package com.zjkj.aigc.application.config;

import com.zjkj.aigc.common.constant.StringPool;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "triton")
public class TritonProperties {

    /**
     * 地址url
     */
    private String endpoint;

    /**
     * 配置列表taskType/modelName, app_name
     */
    private Map<String, String> configMap;

    /**
     * 获取 app-name
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return app-name
     */
    public String getAppName(String taskType, String modelName) {
        if (CollectionUtils.isEmpty(configMap)) {
            return null;
        }
        return configMap.get(taskType + StringPool.DUB_UNDERSCORE + modelName);
    }

    /**
     * 获取type
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return type
     */
    public String getType(String taskType, String modelName) {
        return taskType + StringPool.AT + modelName;
    }

    /**
     * 获取结果地址url
     *
     * @param appName 应用名称
     * @return 结果地址
     */
    public String getResultEndpoint(String appName) {
        if (!StringUtils.hasText(endpoint)) {
            return null;
        }

        // appName _转-
        String domainPrefix = appName.replace(StringPool.UNDERSCORE, StringPool.DASH);
        return String.format(endpoint, domainPrefix, appName);
    }

}
