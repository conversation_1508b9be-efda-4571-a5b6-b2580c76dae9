package com.zjkj.aigc.application.service;

import com.zjkj.aigc.common.dto.ModelTestBriefDTO;
import com.zjkj.aigc.common.dto.ModelTestReportSubmitDTO;
import com.zjkj.aigc.common.req.model.TaskModelQuery;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
public interface ModelTestService {

    /**
     * 测试报告请求
     * @param query 查询参数
     * @return 测试报告
     */
    ModelTestBriefDTO reportRequest(TaskModelQuery query);

    /**
     * 提交测试报告
     * @param reportSubmit 提交信息
     */
    void reportSubmit(ModelTestReportSubmitDTO<ModelTestReportInfo> reportSubmit);
}
