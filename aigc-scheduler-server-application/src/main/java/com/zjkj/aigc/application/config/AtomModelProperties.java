package com.zjkj.aigc.application.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Getter
@Setter
@RefreshScope
@ConfigurationProperties(prefix = "ai.task.atom-model")
public class AtomModelProperties {

    /**
     * 配置列表taskType, modelName
     */
    private Map<String, List<String>> configMap;

    /**
     * 需要移除的key列表taskType, key
     */
    private Map<String, List<String>> remKeyMap;
}
