package com.zjkj.aigc.application.config.dashvector;

import com.aliyun.dashvector.DashVectorClient;
import com.aliyun.dashvector.DashVectorClientConfig;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
public class DashvectorConfig {

    @Value("${dashvector.apiKey}")
    private String apiKey;

    @Value("${dashvector.clusterEndPoint}")
    private String clusterEndPoint;

    @Bean
    @ConditionalOnExpression("${dashvector.enable:false}")
    public DashVectorClient dashVectorClient() {
        // 通过Builder构造DashVectorClientConfig
        DashVectorClientConfig config = DashVectorClientConfig.builder()
                .apiKey(apiKey)
                .endpoint(clusterEndPoint)
                .timeout(10f)
                .build();
        return new DashVectorClient(config);
    }



}
