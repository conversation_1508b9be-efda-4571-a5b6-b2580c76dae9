package com.zjkj.aigc.application.service;

import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.req.node.AigcNodeHeartbeat;
import com.zjkj.aigc.common.req.node.AigcNodeRegisterReq;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcNodeInstanceService {
    /**
     * 节点注册
     * @param register 注册信息
     * @return 注册结果
     */
    AigcRegisterBackDTO register(AigcNodeRegisterReq register);

    /**
     * 节点心跳
     * @param heartbeat 心跳信息
     */
    void heartbeat(AigcNodeHeartbeat heartbeat);
}
