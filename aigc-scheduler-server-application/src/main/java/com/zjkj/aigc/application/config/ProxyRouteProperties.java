package com.zjkj.aigc.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "reverse-proxy.route")
public class ProxyRouteProperties {

    /**
     * 类型
     */
    private List<String> taskTypes;

    /**
     *  目标url
     */
    private String targetUrl;

    /**
     *  前缀
     */
    private String prefix;

}
