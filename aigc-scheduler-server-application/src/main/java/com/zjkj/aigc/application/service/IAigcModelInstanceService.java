package com.zjkj.aigc.application.service;

import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.req.model.AigcModelHeartbeat;
import com.zjkj.aigc.common.req.model.AigcModelRegister;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcModelInstanceService {
    /**
     * 模型注册
     * @param register 注册信息
     * @return AigcRegisterBackDTO
     */
    AigcRegisterBackDTO register(AigcModelRegister register);

    /**
     * 模型心跳
     * @param heartbeat 心跳信息
     */
    void heartbeat(AigcModelHeartbeat heartbeat);
}
