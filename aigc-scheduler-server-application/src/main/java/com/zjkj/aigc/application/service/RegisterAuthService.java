package com.zjkj.aigc.application.service;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
public interface RegisterAuthService {

    /**
     * 获取加密key
     *
     * @return key
     */
    byte[] getCryptKey();

    /**
     * 加密
     *
     * @param plaintext 明文
     * @return 密文
     */
    String encrypt(String plaintext);

    /**
     * 解密
     *
     * @param ciphertext 密文
     * @return 密文
     */
    String decrypt(String ciphertext);
}
