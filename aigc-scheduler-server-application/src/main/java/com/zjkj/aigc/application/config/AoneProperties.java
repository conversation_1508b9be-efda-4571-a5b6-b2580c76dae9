package com.zjkj.aigc.application.config;

import cn.hutool.crypto.SecureUtil;
import com.zjkj.aigc.common.util.Jackson;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "aone")
public class AoneProperties {
    /**
     * 服务端地址
     */
    private String endpoint;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用密钥
     */
    private String appSecret;
    /**
     * 加密key类型
     */
    private Integer keyType;

    /**
     * 生成签名
     * @param body 请求体
     * @return 签名字符串
     */
    public String genSign(Object body, long ts) {
        String str = appName + Jackson.toJSONString(body) + ts + appSecret;
        return SecureUtil.md5(str);
    }
}
