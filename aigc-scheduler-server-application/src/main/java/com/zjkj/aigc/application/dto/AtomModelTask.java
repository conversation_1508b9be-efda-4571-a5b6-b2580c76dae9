package com.zjkj.aigc.application.dto;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Data
public class AtomModelTask<T> implements Serializable {
    private static final long serialVersionUID = -4381172290757723590L;

    /**
     * id
     */
    @Field("_id")
    private String id;
    /**
     * 结果信息
     */
    @Field("infos")
    private T result;
    /**
     * 创建时间 yyyy-MM-dd HH:mm:ss.SSSSSS
     */
    @Field("create_time")
    private String createTimeStr;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 任务id
     */
    private String taskId;

    public LocalDateTime getCreateTime() {
        if (StringUtils.hasText(this.createTimeStr)) {
            try {
                return LocalDateTimeUtil.parse(this.createTimeStr, "yyyy-MM-dd HH:mm:ss.SSSSSS");
            } catch (Exception e) {
                return null;
            }
        }

        return null;
    }
}
