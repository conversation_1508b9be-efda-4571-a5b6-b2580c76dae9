package com.zjkj.aigc.application.service.impl;

import com.zjkj.aigc.application.service.IAigcNodeInstanceService;
import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.req.node.AigcNodeHeartbeat;
import com.zjkj.aigc.common.req.node.AigcNodeRegisterReq;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcNodeInstanceServiceImpl implements IAigcNodeInstanceService {

    private final AigcNodeInstanceService aigcNodeInstanceService;

    @Override
    public AigcRegisterBackDTO register(AigcNodeRegisterReq register) {
        AigcNodeInstance nodeInstance = aigcNodeInstanceService.nodeRegister(register);
        return new AigcRegisterBackDTO()
                .setId(nodeInstance.getId())
                .setNodeIp(nodeInstance.getNodeIp())
                .setTs(System.currentTimeMillis());
    }

    @Override
    public void heartbeat(AigcNodeHeartbeat heartbeat) {
        boolean flag = aigcNodeInstanceService.nodeHeartbeat(heartbeat);
        BaseBizException.isTrue(flag, CustomErrorCode.UNAUTHORIZED);
    }
}
