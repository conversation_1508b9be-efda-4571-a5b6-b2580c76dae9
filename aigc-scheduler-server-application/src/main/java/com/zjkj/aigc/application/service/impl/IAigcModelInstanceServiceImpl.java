package com.zjkj.aigc.application.service.impl;

import com.zjkj.aigc.application.service.IAigcModelInstanceService;
import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.req.model.AigcModelHeartbeat;
import com.zjkj.aigc.common.req.model.AigcModelRegister;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcModelInstanceServiceImpl implements IAigcModelInstanceService {

    private final AigcModelInstanceService aigcModelInstanceService;

    @Override
    public AigcRegisterBackDTO register(AigcModelRegister register) {
        AigcModelInstance modelInstance = aigcModelInstanceService.modelRegister(register);
        return new AigcRegisterBackDTO()
                .setId(modelInstance.getId())
                .setNodeIp(modelInstance.getNodeIp())
                .setTs(System.currentTimeMillis());
    }

    @Override
    public void heartbeat(AigcModelHeartbeat heartbeat) {
        boolean flag = aigcModelInstanceService.modelHeartbeat(heartbeat);
        BaseBizException.isTrue(flag, CustomErrorCode.UNAUTHORIZED);
    }
}
