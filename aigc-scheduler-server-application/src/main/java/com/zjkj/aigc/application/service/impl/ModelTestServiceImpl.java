package com.zjkj.aigc.application.service.impl;

import com.google.common.collect.Lists;
import com.zjkj.aigc.application.service.ModelTestService;
import com.zjkj.aigc.common.dto.ModelTestBriefDTO;
import com.zjkj.aigc.common.dto.ModelTestReportSubmitDTO;
import com.zjkj.aigc.common.enums.ModelAutoTestStatusEnum;
import com.zjkj.aigc.common.req.model.TaskModelQuery;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;
import com.zjkj.aigc.domain.task.service.ModelAutoTestService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelTestServiceImpl implements ModelTestService {

    private final ModelAutoTestService modelAutoTestService;

    @Override
    public ModelTestBriefDTO reportRequest(TaskModelQuery query) {
        ModelAutoTestRecordCondition condition = ModelAutoTestRecordCondition.builder()
                .taskType(query.getTaskType())
                .modelName(query.getModelName())
                .statusList(Lists.newArrayList(ModelAutoTestStatusEnum.SUCCESS.getCode()))
                .build();

        List<ModelAutoTestRecord> records = modelAutoTestService.queryRecordList(condition);
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }

        ModelAutoTestRecord record = records.get(0);

        // 生成简要信息
        ModelTestBriefDTO briefDTO = new ModelTestBriefDTO();
        briefDTO.setTestNo(record.getTestNo());
        briefDTO.setTaskType(record.getTaskType());
        briefDTO.setModelName(record.getModelName());
        briefDTO.setBatchCount(record.getBatchTaskCount());
        briefDTO.setStartTime(record.getTestStartTime());
        briefDTO.setEndTime(record.getTestCompletionTime());
        briefDTO.setModelParams(record.getModelParams());
        return briefDTO;
    }

    @Override
    public void reportSubmit(ModelTestReportSubmitDTO<ModelTestReportInfo> reportSubmit) {
        modelAutoTestService.reportSubmit(reportSubmit);
    }
}
