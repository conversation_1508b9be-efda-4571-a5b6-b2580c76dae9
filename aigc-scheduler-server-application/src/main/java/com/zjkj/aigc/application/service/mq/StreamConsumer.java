package com.zjkj.aigc.application.service.mq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.zjkj.aigc.common.dto.AiTaskCallbackDTO;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
@Slf4j
@Service
public class StreamConsumer {

    private static final long MAX_DELAY = 5 * 60 * 1000;

    private final RestTemplate restTemplate;
    private final AigcTaskService aigcTaskService;
    private final AigcTaskDao aigcTaskDao;

    @Autowired
    public StreamConsumer(@Qualifier("callBackRestTemplate") RestTemplate restTemplate,
                          AigcTaskService aigcTaskService,
                          AigcTaskDao aigcTaskDao) {
        this.restTemplate = restTemplate;
        this.aigcTaskService = aigcTaskService;
        this.aigcTaskDao = aigcTaskDao;
    }
    /**
     * 处理任务回调
     *
     * @param aiTaskCallback 消息体
     * @param headers        消息头
     */
    @StreamListener("aigcSchedulerServerCallbackInput")
    public void handlerAiTaskCallback(@Payload AiTaskCallbackDTO aiTaskCallback, @Headers MessageHeaders headers) {
        Long timestamp = Optional.ofNullable(aiTaskCallback.getTimestamp()).orElse(0L);
        if (System.currentTimeMillis() - timestamp > MAX_DELAY) {
            log.warn("handlerAiTaskCallback. message has expired taskId:{}, timestamp:{} ", aiTaskCallback.getTaskId(), timestamp);
            return;
        }

        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(
                    aiTaskCallback.getNotifyUrl(),
                    new HttpEntity<>(aiTaskCallback.getMessageBody(), httpHeaders),
                    String.class
            );
            HttpStatus statusCode = responseEntity.getStatusCode();
            if (Objects.equals(HttpStatus.OK, statusCode)) {
                log.info("handlerAiTaskCallback. notify success, taskId:{}, url:{}", aiTaskCallback.getTaskId(), aiTaskCallback.getNotifyUrl());
                return;
            }

            log.error("handlerAiTaskCallback. notify failed, statusCode:{}, taskId:{}, url:{}", statusCode.value(), aiTaskCallback.getTaskId(), aiTaskCallback.getNotifyUrl());
        } catch (Exception ex) {
            log.error("handlerAiTaskCallback. notify error, taskId:{}, url:{}, err:{}", aiTaskCallback.getTaskId(), aiTaskCallback.getNotifyUrl(), ex.getMessage());
        }

        // 异常重试
        throw new RuntimeException("handlerAiTaskCallback. notify failed");
    }

    /**
     * 处理任务批量创建
     * @param payload 任务信息
     */
    @StreamListener("batchCreateInput")
    public void handlerAiTaskBatchCreate(@Payload List<Map<String, Object>> payload) {
        if (CollectionUtils.isEmpty(payload)) {
            return;
        }

        List<CreateAiTaskReq<Object>> aiTaskReqList = JSON.parseObject(JSON.toJSONString(payload), new TypeReference<>() {});
        try {
            aigcTaskService.batchSaveAigcTask(aiTaskReqList);
        } catch (DuplicateKeyException ex) {
            log.error("handlerAiTaskBatchCreate. duplicate key, err:{}", ex.getMessage());
            handlerDueToDuplicateKey(aiTaskReqList);
        }

        log.info("handlerAiTaskBatchCreate. done, size:{}", aiTaskReqList.size());
    }

    /**
     *  处理任务重复key
     * @param aiTaskReqList 任务信息
     */
    public void handlerDueToDuplicateKey(List<CreateAiTaskReq<Object>> aiTaskReqList) {
        List<String> businessIds = new ArrayList<>();
        List<String> reqTaskIds = new ArrayList<>();
        aiTaskReqList.forEach(task -> {
            businessIds.add(task.getBusinessId());
            if (StringUtils.hasText(task.getTaskId())) {
                reqTaskIds.add(task.getTaskId());
            }
        });

        if (CollectionUtils.isEmpty(reqTaskIds)) {
            aigcTaskService.batchSaveAigcTask(aiTaskReqList);
            return;
        }

        Set<List<String>> reqTaskIdSet = Set.of(reqTaskIds);
        if (reqTaskIdSet.size() != reqTaskIds.size()) {
            Collection<CreateAiTaskReq<Object>> uniqueList = aiTaskReqList.stream()
                    .collect(Collectors.toMap(
                            CreateAiTaskReq::getTaskId,
                            Function.identity(),
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ))
                    .values();
            aiTaskReqList = new ArrayList<>(uniqueList);
            log.info("handlerDueToDuplicateKey. duplicate size:{}, setSize:{}", reqTaskIds.size(), reqTaskIdSet.size());
        }

        AigcTaskCondition condition = AigcTaskCondition.builder()
                .taskIds(reqTaskIds)
                .businessIds(businessIds)
                .build();
        Map<String, List<String>> existTaskIdMap = aigcTaskDao.queryByCondition(condition).stream()
                .collect(Collectors.groupingBy(
                        AigcTask::getBusinessId,
                        Collectors.mapping(AigcTask::getTaskId, Collectors.toList())
                ));

        if (CollectionUtils.isEmpty(existTaskIdMap)) {
            aigcTaskService.batchSaveAigcTask(aiTaskReqList);
            return;
        }

        aiTaskReqList.removeIf(task ->
                existTaskIdMap.getOrDefault(task.getBusinessId(), Collections.emptyList())
                        .contains(task.getTaskId())
        );

        if (!CollectionUtils.isEmpty(aiTaskReqList)) {
            aigcTaskService.batchSaveAigcTask(aiTaskReqList);
        }

    }
}
