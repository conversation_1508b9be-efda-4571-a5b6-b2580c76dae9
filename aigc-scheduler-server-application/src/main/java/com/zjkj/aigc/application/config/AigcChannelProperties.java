package com.zjkj.aigc.application.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/3/13
 */
@Getter
@Setter
@RefreshScope
@ConfigurationProperties(prefix = "ai.task.channel")
public class AigcChannelProperties {
    /**
     * 配置列表channel, secretKey
     */
    private Map<String, String> configMap;
}
