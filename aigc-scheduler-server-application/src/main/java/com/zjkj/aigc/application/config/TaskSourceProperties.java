package com.zjkj.aigc.application.config;

import com.zjkj.aigc.common.constant.StringPool;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.task.source")
public class TaskSourceProperties {

    /**
     * 默认值
     */
    private String standard = StringPool.EMPTY;
    /**
     * 来源列表
     */
    private List<String> sourceList;
    /**
     * 目标列表
     */
    private List<String> targetList;
    /**
     * 配置
     */
    private Map<String, Config> aiConfigMap;

    @Data
    public static class Config {
        private String endpoint;
        private String appCode;
    }

    /**
     * 获取来源
     *
     * @param businessId 业务ID
     * @param source     源
     * @return 源
     */
    public String getSource(String businessId, String source) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return standard;
        }

        String result = getResult(source);
        if (StringUtils.hasText(result)) {
            return result;
        }

        result = getResult(businessId);
        return StringUtils.hasText(result) ? result : standard;
    }

    private String getResult(String source) {
        if (!StringUtils.hasText(source)) {
            return null;
        }

        String upperCaseSource = source.toUpperCase();
        return sourceList.stream()
                .filter(upperCaseSource::startsWith)
                .findFirst()
                .orElse(null);
    }

    /**
     *  是否是目标
     * @param taskType 任务类型
     * @param modelName 模型名称
     * @return 是否是目标
     */
    public boolean isTarget(String taskType, String modelName) {
        if (CollectionUtils.isEmpty(targetList)) {
            return false;
        }
        return targetList.contains(taskType + StringPool.COMMA + modelName);
    }

    /**
     * 获取ai配置
     *
     * @param businessId 业务ID
     * @param source     源
     * @return 配置
     */
    public Config getAiConfig(String businessId, String source) {
        if (CollectionUtils.isEmpty(aiConfigMap)) {
            return null;
        }

        String resultSource = getSource(businessId, source);
        return aiConfigMap.get(resultSource);
    }
}
