package com.zjkj.aigc.application.config.wrapper;

import org.springframework.util.StreamUtils;
import org.springframework.web.util.WebUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
public class CacheHttpServletRequestWrapper extends HttpServletRequestWrapper {
    private ByteArrayOutputStream cacheContent;

    public CacheHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        this.cacheContent = new ByteArrayOutputStream();
        try {
            ServletInputStream inputStream = super.getInputStream();
            StreamUtils.copy(inputStream, this.cacheContent);
        } catch (IOException e) {
            throw new IllegalStateException("Failed to read the request body", e);
        }
    }

    public void setBody(byte[] body) {
        this.cacheContent = new ByteArrayOutputStream();
        try {
            this.cacheContent.write(body);
        } catch (IOException e) {
            throw new IllegalStateException("Failed to write the request body", e);
        }
    }

    @Override
    public ServletInputStream getInputStream() {
        return new RepeatReadInputStream(cacheContent.toByteArray());
    }

    @Override
    public String getCharacterEncoding() {
        String enc = super.getCharacterEncoding();
        return (enc != null ? enc : WebUtils.DEFAULT_CHARACTER_ENCODING);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
    }

    private static class RepeatReadInputStream extends ServletInputStream {
        private final ByteArrayInputStream inputStream;

        public RepeatReadInputStream(byte[] bytes) {
            this.inputStream = new ByteArrayInputStream(bytes);
        }

        @Override
        public int read() {
            return this.inputStream.read();
        }

        @Override
        public int readLine(byte[] b, int off, int len) {
            return this.inputStream.read(b, off, len);
        }

        @Override
        public boolean isFinished() {
            return this.inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {

        }
    }
}
