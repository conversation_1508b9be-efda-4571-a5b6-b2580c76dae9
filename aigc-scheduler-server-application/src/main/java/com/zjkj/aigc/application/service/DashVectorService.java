package com.zjkj.aigc.application.service;

import com.aliyun.dashvector.models.Doc;
import com.aliyun.dashvector.models.DocOpResult;
import com.zjkj.aigc.dashvector.dto.req.CreateCollectionReq;
import com.zjkj.aigc.dashvector.dto.req.CreateDocReq;
import com.zjkj.aigc.dashvector.dto.req.DeleteDocReq;
import com.zjkj.aigc.dashvector.dto.req.QueryDocReq;
import com.zjkj.aigc.dashvector.dto.resp.DocOperateResp;
import com.zjkj.aigc.dashvector.dto.resp.DocResp;

import java.util.List;

public interface DashVectorService {

    void createCollection(CreateCollectionReq req);

    List<DocOperateResp> createDoc(CreateDocReq req);

    List<DocOperateResp> deleteDoc(DeleteDocReq req);

    List<DocResp> queryDoc(QueryDocReq req);
}




