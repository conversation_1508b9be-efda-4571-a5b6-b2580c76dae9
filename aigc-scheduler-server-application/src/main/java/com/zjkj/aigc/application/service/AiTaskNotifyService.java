package com.zjkj.aigc.application.service;

import com.zjkj.aigc.application.mq.CustomChannelBinder;
import com.zjkj.aigc.common.dto.AiTaskCallbackDTO;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.util.Jackson;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiTaskNotifyService {

    @Resource
    private CustomChannelBinder customChannelBinder;

    /**
     * 回调
     * @param aigcTask 任务
     */
    public void notify(AigcTask aigcTask) {
        String notifyUrl = aigcTask.getNotifyUrl();
        if (!StringUtils.hasText(notifyUrl)) {
            return;
        }

        boolean complete = TaskStateEnum.isComplete(aigcTask.getTaskState());
        if (!complete) {
            return;
        }

        AiTaskCallbackDTO callbackDTO = new AiTaskCallbackDTO();
        callbackDTO.setTaskId(aigcTask.getTaskId());
        callbackDTO.setNotifyUrl(notifyUrl);
        callbackDTO.setTimestamp(System.currentTimeMillis());

        GetAiTaskResp<Object, Object> taskResp = AiTaskService.convertToAiTaskResp(aigcTask);
        callbackDTO.setMessageBody(Jackson.toJSONString(taskResp));

        Message<AiTaskCallbackDTO> message = MessageBuilder.withPayload(callbackDTO)
                .setHeader(MessageHeaders.CONTENT_TYPE, MimeTypeUtils.APPLICATION_JSON)
                .build();
        customChannelBinder.sendAiTaskCallback().send(message);
    }

}
