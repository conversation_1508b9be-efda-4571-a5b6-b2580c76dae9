package com.zjkj.aigc.application.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
public interface CustomChannelBinder {

    /**
     * 发送回调消息到消息中心
     *
     * @return MessageChannel
     */
    @Output("aigcSchedulerServerCallbackOutput")
    MessageChannel sendAiTaskCallback();

    /**
     * 接收回调消息
     *
     * @return MessageChannel
     */
    @Input("aigcSchedulerServerCallbackInput")
    MessageChannel handlerAiTaskCallback();

    /**
     * 发送aigc任务批量创建到消息中心
     *
     * @return MessageChannel
     */
    @Output("batchCreateOutput")
    MessageChannel sendAiTaskBatchCreate();

    /**
     * 接收aigc任务批量创建消息
     *
     * @return MessageChannel
     */
    @Input("batchCreateInput")
    MessageChannel handlerAiTaskBatchCreate();
}
