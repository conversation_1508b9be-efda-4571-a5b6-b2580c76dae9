package com.zjkj.aigc.application.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.zjkj.aigc.application.config.AtomModelProperties;
import com.zjkj.aigc.application.dto.AtomModelTask;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AtomModelService {

    private final MongoTemplate mongoTemplate;
    private final RabbitTemplate rabbitTemplate;
    private final AtomModelProperties atomModelProperties;

    /**
     * 根据id查询
     *
     * @param collectionName 集合名称
     * @param ids            id集合
     * @return 结果
     */
    @SuppressWarnings("unchecked")
    public <T> List<AtomModelTask<T>> queryByIds(String collectionName, List<String> ids) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").in(ids));
        return mongoTemplate.find(query, (Class<AtomModelTask<T>>) (Class<?>) AtomModelTask.class, collectionName);
    }

    /**
     * 推任务
     *
     * @param task      任务
     */
    public void pushTask(CreateAiTaskReq<?> task, Runnable successCallback) {
        rabbitTemplate.convertAndSend(StringPool.EMPTY, task.getModelName(), JSON.toJSONString(task), new CorrelationData() {{
            getFuture().addCallback(
                    result -> {
                        if (Objects.nonNull(result) && result.isAck()) {
                            successCallback.run();
                        } else {
                            log.info("pushTask. Message not acknowledged modelName:{}, taskId:{}, businessId:{}", task.getModelName(), task.getTaskId(), task.getBusinessId());
                        }
                    },
                    ex -> log.error("pushTask. Error sending message modelName:{}, taskId:{}, businessId:{}", task.getModelName(), task.getTaskId(), task.getBusinessId(), ex)
            );
        }});
    }

    /**
     * 批量推任务
     * @param reqList 请求参数
     * @param <T> 请求参数类型
     */
    public <T> void batchPushTask(List<CreateAiTaskReq<T>> reqList) {
        reqList.forEach(req -> pushTask(req, () -> {
        }));
    }

    /**
     *  删除任务
     * @param collectionName 集合名称
     * @param id id
     */
    public void deleteTask(String collectionName, String id) {
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").lt(id));
        mongoTemplate.remove(query, collectionName);
    }

    /**
     * 转换
     * @param atomModelTask 任务
     * @return 结果
     */
    public GetAiTaskResp<Object, Object> convertToAiTaskResp(AtomModelTask<String> atomModelTask) {
        GetAiTaskResp<Object, Object> resp = new GetAiTaskResp<>();
        resp.setTaskId(atomModelTask.getId());
        resp.setState(TaskStateEnum.SUCC.getCode());
        resp.setProgress(100);
        if (JSON.isValid(atomModelTask.getResult())) {
            Map<String, Object> resultMap = JSONObject.parseObject(atomModelTask.getResult(), new TypeReference<>() {
            });
            Integer code = (Integer) resultMap.get("code");
            if (Objects.nonNull(code) && code < 0) {
                resp.setState(TaskStateEnum.FAILED.getCode());
                resp.setMessage((String) resultMap.get("message"));
                resp.setProgress(0);
            }

            // 移除key
            remKey(resultMap, atomModelTask.getTaskType());
            resp.setOutput(resultMap);
        }

        LocalDateTime createTime = atomModelTask.getCreateTime();
        LocalDateTime taskCompletionTime = Objects.nonNull(createTime) ? createTime : LocalDateTime.now();
        resp.setTaskCompletionTime(taskCompletionTime);
        return resp;
    }

    /**
     * 移除key
     * @param resultMap 结果
     * @param taskType 任务类型
     */
    public void remKey(Map<String, Object> resultMap, String taskType) {
        if (CollectionUtils.isEmpty(resultMap) || !StringUtils.hasText(taskType)) {
           return;
        }

        Map<String, List<String>> remKeyMap = atomModelProperties.getRemKeyMap();
        if (CollectionUtils.isEmpty(remKeyMap)) {
            return;
        }

        List<String> remKeys = remKeyMap.get(taskType);
        if (CollectionUtils.isEmpty(remKeys)) {
            return;
        }

        remKeys.forEach(resultMap::remove);
    }

}
