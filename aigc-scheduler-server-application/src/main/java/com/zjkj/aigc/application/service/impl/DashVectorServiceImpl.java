package com.zjkj.aigc.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dashvector.DashVectorClient;
import com.aliyun.dashvector.DashVectorCollection;
import com.aliyun.dashvector.common.ErrorCode;
import com.aliyun.dashvector.models.Doc;
import com.aliyun.dashvector.models.DocOpResult;
import com.aliyun.dashvector.models.Vector;
import com.aliyun.dashvector.models.requests.CreateCollectionRequest;
import com.aliyun.dashvector.models.requests.DeleteDocRequest;
import com.aliyun.dashvector.models.requests.InsertDocRequest;
import com.aliyun.dashvector.models.requests.QueryDocRequest;
import com.aliyun.dashvector.models.responses.Response;
import com.aliyun.dashvector.proto.CollectionInfo;
import com.aliyun.dashvector.proto.FieldType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zjkj.aigc.application.service.DashVectorService;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.dashvector.dto.req.CreateCollectionReq;
import com.zjkj.aigc.dashvector.dto.req.CreateDocReq;
import com.zjkj.aigc.dashvector.dto.req.DeleteDocReq;
import com.zjkj.aigc.dashvector.dto.req.QueryDocReq;
import com.zjkj.aigc.dashvector.dto.resp.DocOperateResp;
import com.zjkj.aigc.dashvector.dto.resp.DocResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DashVectorServiceImpl implements DashVectorService {

    // 使用 Optional 包装依赖
    @Autowired(required = false)
    private DashVectorClient client;

    @Override
    public void createCollection(CreateCollectionReq req) {
        Map<String,Integer> map = req.getFiledsMap();
        CreateCollectionRequest request = null;
        if(MapUtils.isEmpty(map)){
            request = CreateCollectionRequest.builder()
                    .name(req.getCollectionName())  // quickstart
                    .dimension(req.getDimension())
                    .metric(CollectionInfo.Metric.forNumber(req.getMetric()))
                    .dataType(CollectionInfo.DataType.forNumber(req.getDataType()))
                    .build();
        }else{
            Map<String, FieldType> maps = Maps.newHashMap();
            map.entrySet().stream().forEach(m -> {
                maps.put(m.getKey(), FieldType.forNumber(m.getValue()));
            });
            request = CreateCollectionRequest.builder()
                    .name(req.getCollectionName())  // quickstart
                    .dimension(req.getDimension())
                    .metric(CollectionInfo.Metric.forNumber(req.getMetric()))
                    .dataType(CollectionInfo.DataType.forNumber(req.getDataType()))
                    .filedsSchema(maps)
                    .build();
        }

        Response<Void> response = client.create(request);
        if(response.isSuccess()){
            log.info("create dashvector collection success");
        }else{
            log.info("create dashvector collection fail, response:{}", JSON.toJSONString(response));
            throw new BaseBizException(CustomErrorCode.DASHVECTOR_CREATE_COLLECTION_ERROR,String.format("code:%s, msg:%s", response.getCode(), response.getMessage()));
        }
    }

    @Override
    public List<DocOperateResp> createDoc(CreateDocReq req) {
        DashVectorCollection collection = client.get(req.getCollectionName());
        // 构建Vector
        Vector vector = Vector.builder().value(req.getVectorList()).build();
        // 构建Doc
        Doc doc = null;
        if(MapUtils.isNotEmpty(req.getFields())){
            doc = doc.builder().id(req.getId()).vector(vector).fields(req.getFields()).build();
        }else{
            doc = doc.builder().id(req.getId()).vector(vector).build();
        }

        // 插入Doc
        Response<List<DocOpResult>> response = collection.insert(InsertDocRequest.builder().doc(doc).build());
        if(isSuccess(response)){
            log.info("create doc success");
        }else{
            log.info("create doc fail, response:{}", JSON.toJSONString(response));
            throw new BaseBizException(CustomErrorCode.DASHVECTOR_CREATE_DOC_ERROR,String.format("code:%s, msg:%s", response.getCode(), response.getMessage()));
        }

        return createDocOperate(response.getOutput());
    }

    @Override
    public List<DocOperateResp> deleteDoc(DeleteDocReq req) {
        DashVectorCollection collection = client.get(req.getCollectionName());
        List<DocOpResult> list = new ArrayList<>();
        for(String id : req.getIdList()){
            // 发送删除Doc请求
            Response<List<DocOpResult>> response = collection.delete(DeleteDocRequest.builder().id(id).build());
            list.add(response.getOutput().get(0));
        }

        return createDocOperate(list);
    }


    @Override
    public List<DocResp> queryDoc(QueryDocReq req) {
        queryCheck(req);
        DashVectorCollection collection = client.get(req.getCollectionName());
        // 构建QueryDocRequest
        QueryDocRequest request = null;
        if(req.getReturnFields() == null){
            req.setReturnFields(Lists.newArrayList());
        }
        if(req.getFilter() == null){
            req.setFilter("");
        }
        if(Objects.nonNull(req.getId())){
            request = QueryDocRequest.builder().id(req.getId())
                    .topk(req.getTopK())
                    .filter(req.getFilter())
                    .includeVector(req.getIncludeVector())
                    .outputFields(req.getReturnFields())
                    .build();
        }else{
            // 构建Vector
            Vector vector = Vector.builder().value(req.getVectorList()).build();
            request = QueryDocRequest.builder().vector(vector)
                    .topk(req.getTopK())
                    .filter(req.getFilter())
                    .includeVector(req.getIncludeVector())
                    .outputFields(req.getReturnFields())
                    .build();
        }

        // 进行Doc检索
        Response<List<Doc>> response = collection.query(request);
        if(response.isSuccess()){
            log.info("query doc success");
        }else{
            log.info("query doc fail, response:{}", JSON.toJSONString(response));
            throw new BaseBizException(CustomErrorCode.DASHVECTOR_QUERY_DOC_ERROR,String.format("code:%s, msg:%s", response.getCode(), response.getMessage()));
        }

        return createDoc(response.getOutput());
    }

    private void queryCheck(QueryDocReq req){
        if(Objects.isNull(req.getId()) && CollectionUtils.isEmpty(req.getVectorList())){
            throw new BaseBizException(CustomErrorCode.DASHVECTOR_QUERY_PARAMS_ERROR);
        }
    }

    private List<DocOperateResp> createDocOperate(List<DocOpResult> list) {
        List<DocOperateResp> respList = Lists.newArrayList();
        list.stream().forEach(m -> {
            DocOperateResp resp = new DocOperateResp();
            resp.setId(m.getId());
            resp.setCode(m.getCode());
            resp.setMessage(m.getMessage());
            resp.setDocOp(m.getDocOp().name());
            respList.add(resp);
        });
        return respList;
    }

    private List<DocResp> createDoc(List<Doc> list) {
        List<DocResp> respList = Lists.newArrayList();
        list.stream().forEach(m -> {
            DocResp resp = new DocResp();
            resp.setId(m.getId());
            resp.setSparseVector(m.getSparseVector());
            resp.setVectors(JSONObject.parseObject(JSON.toJSONString(m.getVectors()),Map.class));
            resp.setScore(m.getScore());
            DocResp.Vector vector = new DocResp.Vector();
            resp.setVector(JSONObject.parseObject(JSON.toJSONString(m.getVector()),DocResp.Vector.class));
            resp.setFields(m.getFields());
            respList.add(resp);
        });

        return respList;
    }

    private boolean  isSuccess(Response response){
        return response.isSuccess() && ErrorCode.SUCCESS.getMessage().equals(response.getMessage());
    }
}