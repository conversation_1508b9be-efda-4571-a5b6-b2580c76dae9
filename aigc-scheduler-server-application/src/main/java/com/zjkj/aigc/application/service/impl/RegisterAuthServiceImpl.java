package com.zjkj.aigc.application.service.impl;


import com.zjkj.aigc.application.config.AoneProperties;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.common.dto.AoneBaseResponse;
import com.zjkj.aigc.common.util.AESUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Service
@RequiredArgsConstructor
public class RegisterAuthServiceImpl implements RegisterAuthService {

    private final AoneProperties aoneProperties;
    private final RestTemplate restTemplate;
    private byte[] cryptKey;

    /**
     * 获取加密key
     *
     * @return key
     */
    @Override
    public byte[] getCryptKey() {
        if (cryptKey == null) {
            cryptKey = queryCryptKey();
        }
        return cryptKey;
    }

    @Override
    public String encrypt(String plaintext) {
        return AESUtil.encrypt(plaintext, getCryptKey());
    }

    @Override
    public String decrypt(String ciphertext) {
        return AESUtil.decrypt(ciphertext, getCryptKey());
    }

    @SuppressWarnings("unchecked")
    @Retryable(value = {Exception.class}, backoff = @Backoff(delay = 1000, multiplier = 2))
    public byte[] queryCryptKey() {
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("key_type", aoneProperties.getKeyType());

        long ts = System.currentTimeMillis() / 1000;
        String sign = aoneProperties.genSign(bodyMap, ts);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("app", aoneProperties.getAppName());
        httpHeaders.set("ts", String.valueOf(ts));
        httpHeaders.set("sign", sign);

        String url = aoneProperties.getEndpoint() + "/api/v1/secs/get_key";
        AoneBaseResponse<Map<String, Object>> response = restTemplate.postForObject(
                url,
                new HttpEntity<>(bodyMap, httpHeaders),
                AoneBaseResponse.class);
        if (Objects.nonNull(response) && Objects.equals(response.getCode(), 0)) {
            Map<String, Object> result = response.getResult();
            if (!CollectionUtils.isEmpty(result)) {
                String key = (String) result.get("key");
                return Base64Utils.decode(key.getBytes());
            }
        }

        return null;
    }


}
