<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes集群资源监控Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            font-size: 14px;
        }

        select, .refresh-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select:hover, .refresh-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .refresh-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-left: 4px solid #667eea;
            padding-left: 12px;
        }

        .metrics-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
            transition: transform 0.3s ease;
        }

        .metric-card:nth-child(2) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .metric-card:nth-child(3) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-title {
            font-size: 16px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .metric-usage {
            font-size: 14px;
            opacity: 0.8;
        }

        .charts-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .chart {
            width: 100%;
            height: 300px;
        }

        .pod-charts {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .pod-chart-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
        }

        .pod-chart {
            width: 100%;
            height: 250px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: #27ae60;
        }

        .status-warning {
            background-color: #f39c12;
        }

        .status-error {
            background-color: #e74c3c;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .cluster-list-container {
            margin-top: 30px;
        }

        .cluster-table-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
            overflow-x: auto;
        }

        .cluster-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
        }

        .cluster-table th,
        .cluster-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            white-space: nowrap;
        }

        .cluster-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .cluster-table th:hover {
            background: #e9ecef;
        }

        .cluster-table tr:hover {
            background: #f8f9fa;
        }

        .cluster-expand-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
            color: #667eea;
        }

        .cluster-expand-btn:hover {
            background: #f0f0f0;
        }

        .cluster-expand-btn.expanded {
            transform: rotate(90deg);
        }

        .cluster-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-online {
            background-color: #27ae60;
        }

        .status-offline {
            background-color: #e74c3c;
        }

        .cluster-models-row {
            display: none;
            background: #f8f9fa;
        }

        .cluster-models-row.expanded {
            display: table-row;
        }

        .cluster-models-content {
            padding: 16px;
            border-left: 3px solid #667eea;
        }

        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 12px;
        }

        .model-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .model-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .model-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .model-type {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .model-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 12px;
            color: #666;
        }

        .cluster-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            padding: 16px 0;
            border-top: 1px solid #f0f0f0;
        }

        .cluster-pagination-info {
            color: #666;
            font-size: 14px;
        }

        .cluster-pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cluster-pagination-size {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right: 16px;
        }

        .cluster-pagination-size select {
            background: white;
            color: #333;
            border: 1px solid #ddd;
            padding: 6px 8px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }

        .cluster-pagination-nav {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .cluster-pagination-nav button {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cluster-pagination-nav button:hover:not(:disabled) {
            background: #f5f5f5;
            border-color: #ccc;
        }

        .cluster-pagination-nav button.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .cluster-pagination-nav button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f5f5f5;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: flex-start;
            }

            .charts-row {
                grid-template-columns: 1fr;
            }

            .metrics-cards {
                grid-template-columns: 1fr;
            }

            .cluster-table-container {
                overflow-x: auto;
            }

            .cluster-table {
                min-width: 800px;
            }

            .cluster-table th,
            .cluster-table td {
                padding: 8px;
                font-size: 14px;
            }

            .models-grid {
                grid-template-columns: 1fr;
            }

            .cluster-pagination {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }

            .cluster-pagination-controls {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Kubernetes集群资源监控Dashboard</h1>
        <div class="controls">
            <div class="control-group">
                <label>集群:</label>
                <select id="clusterSelect">
                    <option value="cluster-prod-001">生产集群 (cluster-prod-001)</option>
                    <option value="cluster-test-002">测试集群 (cluster-test-002)</option>
                    <option value="cluster-dev-003">开发集群 (cluster-dev-003)</option>
                </select>
            </div>
            <div class="control-group">
                <label>时间范围:</label>
                <select id="timeRangeSelect">
                    <option value="1h">最近1小时</option>
                    <option value="6h">最近6小时</option>
                    <option value="24h" selected>最近24小时</option>
                    <option value="7d">最近7天</option>
                </select>
            </div>
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            <div class="control-group">
                <span class="status-indicator status-online"></span>
                <span>实时监控中</span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 集群监控区域 -->
        <div class="section">
            <h2 class="section-title">📊 集群资源使用概览</h2>
            
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-title">GPU显存使用情况</div>
                    <div class="metric-value">192/320 <span style="font-size: 18px;">GB</span></div>
                    <div class="metric-usage">使用率: 60%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">CPU使用情况</div>
                    <div class="metric-value">45/64 <span style="font-size: 18px;">核</span></div>
                    <div class="metric-usage">使用率: 70%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">内存使用情况</div>
                    <div class="metric-value">180/256 <span style="font-size: 18px;">GB</span></div>
                    <div class="metric-usage">使用率: 70%</div>
                </div>
            </div>

            <div class="charts-row">
                <div class="chart-container">
                    <div class="chart-title">GPU使用率趋势</div>
                    <div id="gpuTrendChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">CPU使用率趋势</div>
                    <div id="cpuTrendChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">内存使用率趋势</div>
                    <div id="memoryTrendChart" class="chart"></div>
                </div>
            </div>

            <!-- 集群信息列表 -->
            <div class="cluster-list-container">
                <div class="chart-title">集群信息列表</div>

                <div class="cluster-table-container">
                    <table class="cluster-table" id="clusterTable">
                        <thead>
                            <tr>
                                <th width="40px"></th>
                                <th onclick="sortClusterTable('clusterId')">集群ID/名称</th>
                                <th onclick="sortClusterTable('status')">集群状态</th>
                                <th onclick="sortClusterTable('nodeCount')">节点数量</th>
                                <th onclick="sortClusterTable('gpuUsage')">GPU显存(已使用/总量)</th>
                                <th onclick="sortClusterTable('cpuUsage')">CPU(已使用/总量)</th>
                                <th onclick="sortClusterTable('memoryUsage')">内存(已使用/总量)</th>
                                <th onclick="sortClusterTable('modelCount')">部署模型数量</th>
                            </tr>
                        </thead>
                        <tbody id="clusterTableBody">
                            <!-- 动态生成集群列表 -->
                        </tbody>
                    </table>

                    <div class="cluster-pagination" id="clusterPagination">
                        <!-- 动态生成分页 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 模型监控区域 -->
        <div class="section">
            <h2 class="section-title">🤖 模型Pod资源监控</h2>
            
            <div class="control-group" style="margin-bottom: 20px;">
                <label>选择模型:</label>
                <select id="modelSelect" style="color: #333; background: white; border: 1px solid #ddd;">
                    <option value="101">LLaMA2-7B (model_id: 101)</option>
                    <option value="102">ChatGLM-6B (model_id: 102)</option>
                    <option value="103">Stable-Diffusion (model_id: 103)</option>
                </select>
            </div>

            <div class="pod-charts">
                <div class="pod-chart-container">
                    <div class="chart-title">Pod GPU使用率监控</div>
                    <div id="podGpuChart" class="pod-chart"></div>
                </div>
                <div class="pod-chart-container">
                    <div class="chart-title">Pod CPU使用率监控</div>
                    <div id="podCpuChart" class="pod-chart"></div>
                </div>
                <div class="pod-chart-container">
                    <div class="chart-title">Pod 内存使用率监控</div>
                    <div id="podMemoryChart" class="pod-chart"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据生成函数
        function generateTimeSeriesData(hours = 24, baseValue = 50, variance = 20) {
            const data = [];
            const now = new Date();
            for (let i = hours * 60; i >= 0; i -= 5) {
                const time = new Date(now.getTime() - i * 60 * 1000);
                const value = Math.max(0, Math.min(100, 
                    baseValue + (Math.random() - 0.5) * variance + Math.sin(i / 60) * 10
                ));
                data.push([time, Math.round(value * 100) / 100]);
            }
            return data;
        }

        // 初始化集群趋势图表
        function initClusterCharts() {
            // GPU趋势图
            const gpuChart = echarts.init(document.getElementById('gpuTrendChart'));
            gpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>GPU使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 60, 25),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#4facfe',
                        width: 1.5
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(79, 172, 254, 0.8)'},
                                {offset: 1, color: 'rgba(79, 172, 254, 0.1)'}
                            ]
                        }
                    },
                    symbol: 'circle',
                    symbolSize: 6,
                    showSymbol: false
                }]
            });

            // CPU趋势图
            const cpuChart = echarts.init(document.getElementById('cpuTrendChart'));
            cpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>CPU使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 70, 20),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#43e97b',
                        width: 1.5
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(67, 233, 123, 0.8)'},
                                {offset: 1, color: 'rgba(67, 233, 123, 0.1)'}
                            ]
                        }
                    },
                    symbol: 'circle',
                    symbolSize: 6,
                    showSymbol: false
                }]
            });

            // 内存趋势图
            const memoryChart = echarts.init(document.getElementById('memoryTrendChart'));
            memoryChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>内存使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 70, 15),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#f093fb',
                        width: 1.5
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(240, 147, 251, 0.8)'},
                                {offset: 1, color: 'rgba(240, 147, 251, 0.1)'}
                            ]
                        }
                    },
                    symbol: 'circle',
                    symbolSize: 6,
                    showSymbol: false
                }]
            });
        }

        // 初始化Pod监控图表
        function initPodCharts() {
            const podNames = ['llama2-7b-001', 'llama2-7b-002', 'llama2-7b-003'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            // Pod GPU使用率图表
            const podGpuChart = echarts.init(document.getElementById('podGpuChart'));
            const gpuSeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 40 + index * 15, 20),
                lineStyle: {
                    color: colors[index],
                    width: 1.5
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            {offset: 0, color: colors[index] + '99'}, // 60% 透明度
                            {offset: 1, color: colors[index] + '1A'}  // 10% 透明度
                        ]
                    }
                },
                symbol: 'circle',
                symbolSize: 4,
                showSymbol: false
            }));

            podGpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: gpuSeries
            });

            // Pod CPU使用率图表
            const podCpuChart = echarts.init(document.getElementById('podCpuChart'));
            const cpuSeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 30 + index * 20, 25),
                lineStyle: {
                    color: colors[index],
                    width: 1.5
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            {offset: 0, color: colors[index] + '99'}, // 60% 透明度
                            {offset: 1, color: colors[index] + '1A'}  // 10% 透明度
                        ]
                    }
                },
                symbol: 'circle',
                symbolSize: 4,
                showSymbol: false
            }));

            podCpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: cpuSeries
            });

            // Pod 内存使用率图表
            const podMemoryChart = echarts.init(document.getElementById('podMemoryChart'));
            const memorySeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 50 + index * 10, 15),
                lineStyle: {
                    color: colors[index],
                    width: 1.5
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            {offset: 0, color: colors[index] + '99'}, // 60% 透明度
                            {offset: 1, color: colors[index] + '1A'}  // 10% 透明度
                        ]
                    }
                },
                symbol: 'circle',
                symbolSize: 4,
                showSymbol: false
            }));

            podMemoryChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: memorySeries
            });
        }

        // 集群列表相关变量
        let clusterCurrentPage = 1;
        let clusterPageSize = 10;
        let clusterData = [];
        let expandedClusters = new Set();

        // 生成集群模拟数据
        function generateClusterData() {
            const clusterNames = ['cluster-prod-001', 'cluster-test-002', 'cluster-dev-003', 'cluster-gpu-004', 'cluster-ai-005'];
            const modelTypes = ['文本生成', '图像生成', '文本分类', '情感分析', '机器翻译'];

            return clusterNames.map((name, index) => {
                const nodeCount = Math.floor(Math.random() * 10) + 3;
                const totalGpuMemory = nodeCount * 16; // 假设每个节点16GB显存
                const usedGpuMemory = Math.floor(Math.random() * totalGpuMemory * 0.8);
                const totalCpu = nodeCount * 8; // 假设每个节点8核
                const usedCpu = Math.floor(Math.random() * totalCpu * 0.7);
                const totalMemory = nodeCount * 32; // 假设每个节点32GB内存
                const usedMemory = Math.floor(Math.random() * totalMemory * 0.6);
                const modelCount = Math.floor(Math.random() * 8) + 2;

                // 生成模型数据
                const models = [];
                for (let i = 0; i < modelCount; i++) {
                    models.push({
                        name: `${modelNames[Math.floor(Math.random() * modelNames.length)]}-${i + 1}`,
                        type: modelTypes[Math.floor(Math.random() * modelTypes.length)],
                        podCount: Math.floor(Math.random() * 5) + 1,
                        cpuUsage: Math.floor(Math.random() * 80) + 10,
                        memoryUsage: Math.floor(Math.random() * 70) + 15,
                        status: Math.random() > 0.1 ? 'running' : 'error'
                    });
                }

                return {
                    id: name,
                    name: name,
                    status: Math.random() > 0.05 ? 'online' : 'offline',
                    nodeCount: nodeCount,
                    gpuUsage: `${usedGpuMemory}GB/${totalGpuMemory}GB`,
                    gpuUsagePercent: Math.round((usedGpuMemory / totalGpuMemory) * 100),
                    cpuUsage: `${usedCpu}核/${totalCpu}核`,
                    cpuUsagePercent: Math.round((usedCpu / totalCpu) * 100),
                    memoryUsage: `${usedMemory}GB/${totalMemory}GB`,
                    memoryUsagePercent: Math.round((usedMemory / totalMemory) * 100),
                    modelCount: modelCount,
                    models: models
                };
            });
        }

        // 初始化集群列表
        function initClusterList() {
            clusterData = generateClusterData();
            renderClusterTable();
        }

        // 渲染集群表格
        function renderClusterTable() {
            const tbody = document.getElementById('clusterTableBody');
            const startIndex = (clusterCurrentPage - 1) * clusterPageSize;
            const endIndex = startIndex + clusterPageSize;
            const pageData = clusterData.slice(startIndex, endIndex);

            let html = '';
            pageData.forEach((cluster, index) => {
                const isExpanded = expandedClusters.has(cluster.id);
                html += `
                    <tr>
                        <td>
                            <button class="cluster-expand-btn ${isExpanded ? 'expanded' : ''}"
                                    onclick="toggleClusterExpand('${cluster.id}')">
                                ▶
                            </button>
                        </td>
                        <td>${cluster.name}</td>
                        <td>
                            <div class="cluster-status">
                                <span class="status-dot ${cluster.status === 'online' ? 'status-online' : 'status-offline'}"></span>
                                ${cluster.status === 'online' ? '在线' : '离线'}
                            </div>
                        </td>
                        <td>${cluster.nodeCount}</td>
                        <td>${cluster.gpuUsage} (${cluster.gpuUsagePercent}%)</td>
                        <td>${cluster.cpuUsage} (${cluster.cpuUsagePercent}%)</td>
                        <td>${cluster.memoryUsage} (${cluster.memoryUsagePercent}%)</td>
                        <td>${cluster.modelCount}</td>
                    </tr>
                `;

                // 添加展开的模型行
                html += `
                    <tr class="cluster-models-row ${isExpanded ? 'expanded' : ''}" id="models-${cluster.id}">
                        <td colspan="8">
                            <div class="cluster-models-content">
                                <div class="models-grid">
                                    ${cluster.models.map(model => `
                                        <div class="model-card">
                                            <div class="model-header">
                                                <span class="model-name">${model.name}</span>
                                                <span class="model-type">${model.type}</span>
                                            </div>
                                            <div class="model-stats">
                                                <div>Pod数量: ${model.podCount}</div>
                                                <div>状态: ${model.status === 'running' ? '运行中' : '异常'}</div>
                                                <div>CPU: ${model.cpuUsage}%</div>
                                                <div>内存: ${model.memoryUsage}%</div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
            renderClusterPagination();
        }

        // 切换集群展开状态
        function toggleClusterExpand(clusterId) {
            if (expandedClusters.has(clusterId)) {
                expandedClusters.delete(clusterId);
            } else {
                expandedClusters.add(clusterId);
            }
            renderClusterTable();
        }

        // 渲染集群分页
        function renderClusterPagination() {
            const container = document.getElementById('clusterPagination');
            const totalPages = Math.ceil(clusterData.length / clusterPageSize);
            const totalItems = clusterData.length;

            let html = `
                <div class="cluster-pagination-info">
                    共 ${totalItems} 个集群
                </div>
                <div class="cluster-pagination-controls">
                    <div class="cluster-pagination-size">
                        <select onchange="changeClusterPageSize(this.value)">
                            <option value="10" ${clusterPageSize === 10 ? 'selected' : ''}>10条/页</option>
                            <option value="20" ${clusterPageSize === 20 ? 'selected' : ''}>20条/页</option>
                            <option value="50" ${clusterPageSize === 50 ? 'selected' : ''}>50条/页</option>
                        </select>
                    </div>
                    <div class="cluster-pagination-nav">
                        <button onclick="changeClusterPage(${clusterCurrentPage - 1})" ${clusterCurrentPage === 1 ? 'disabled' : ''}>‹</button>
            `;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === clusterCurrentPage) {
                    html += `<button class="active">${i}</button>`;
                } else {
                    html += `<button onclick="changeClusterPage(${i})">${i}</button>`;
                }
            }

            html += `
                        <button onclick="changeClusterPage(${clusterCurrentPage + 1})" ${clusterCurrentPage === totalPages ? 'disabled' : ''}>›</button>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // 切换集群页面
        function changeClusterPage(page) {
            const totalPages = Math.ceil(clusterData.length / clusterPageSize);
            if (page >= 1 && page <= totalPages) {
                clusterCurrentPage = page;
                renderClusterTable();
            }
        }

        // 改变集群每页显示数量
        function changeClusterPageSize(size) {
            clusterPageSize = parseInt(size);
            clusterCurrentPage = 1;
            renderClusterTable();
        }

        // 集群表格排序
        function sortClusterTable(column) {
            clusterData.sort((a, b) => {
                if (column === 'status') {
                    return a.status.localeCompare(b.status);
                } else if (column === 'nodeCount' || column === 'modelCount') {
                    return b[column] - a[column];
                } else if (column.includes('Usage')) {
                    return b[column + 'Percent'] - a[column + 'Percent'];
                } else {
                    return a[column].localeCompare(b[column]);
                }
            });
            renderClusterTable();
        }

        // 刷新数据函数
        function refreshData() {
            console.log('刷新数据...');
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.innerHTML = '🔄 刷新中...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.innerHTML = '🔄 刷新数据';
                refreshBtn.disabled = false;
                initClusterCharts();
                initPodCharts();
                initClusterList();
            }, 2000);
        }

        // 集群选择变化处理
        document.getElementById('clusterSelect').addEventListener('change', function(e) {
            console.log('切换集群:', e.target.value);
            refreshData();
        });

        // 模型选择变化处理
        document.getElementById('modelSelect').addEventListener('change', function(e) {
            console.log('切换模型:', e.target.value);
            initPodCharts();
        });

        // 时间范围选择变化处理
        document.getElementById('timeRangeSelect').addEventListener('change', function(e) {
            console.log('切换时间范围:', e.target.value);
            refreshData();
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initClusterCharts();
            initPodCharts();
            initClusterList();

            // 设置自动刷新（每30秒）
            setInterval(function() {
                initClusterCharts();
                initPodCharts();
                initClusterList();
            }, 30000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            setTimeout(function() {
                echarts.getInstanceByDom(document.getElementById('gpuTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('cpuTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('memoryTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podGpuChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podCpuChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podMemoryChart'))?.resize();
            }, 100);
        });
    </script>
</body>
</html>
