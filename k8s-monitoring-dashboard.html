<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes集群资源监控Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            font-size: 14px;
        }

        select, .refresh-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select:hover, .refresh-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .refresh-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-left: 4px solid #667eea;
            padding-left: 12px;
        }

        .metrics-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
            transition: transform 0.3s ease;
        }

        .metric-card:nth-child(2) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .metric-card:nth-child(3) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.3);
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-title {
            font-size: 16px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .metric-usage {
            font-size: 14px;
            opacity: 0.8;
        }

        .charts-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .chart {
            width: 100%;
            height: 300px;
        }

        .pod-charts {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .pod-chart-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
        }

        .pod-chart {
            width: 100%;
            height: 250px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: #27ae60;
        }

        .status-warning {
            background-color: #f39c12;
        }

        .status-error {
            background-color: #e74c3c;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .charts-row {
                grid-template-columns: 1fr;
            }
            
            .metrics-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Kubernetes集群资源监控Dashboard</h1>
        <div class="controls">
            <div class="control-group">
                <label>集群:</label>
                <select id="clusterSelect">
                    <option value="cluster-prod-001">生产集群 (cluster-prod-001)</option>
                    <option value="cluster-test-002">测试集群 (cluster-test-002)</option>
                    <option value="cluster-dev-003">开发集群 (cluster-dev-003)</option>
                </select>
            </div>
            <div class="control-group">
                <label>时间范围:</label>
                <select id="timeRangeSelect">
                    <option value="1h">最近1小时</option>
                    <option value="6h">最近6小时</option>
                    <option value="24h" selected>最近24小时</option>
                    <option value="7d">最近7天</option>
                </select>
            </div>
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            <div class="control-group">
                <span class="status-indicator status-online"></span>
                <span>实时监控中</span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 集群监控区域 -->
        <div class="section">
            <h2 class="section-title">📊 集群资源使用概览</h2>
            
            <div class="metrics-cards">
                <div class="metric-card">
                    <div class="metric-title">GPU使用情况</div>
                    <div class="metric-value">12/20</div>
                    <div class="metric-usage">使用率: 60%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">CPU使用情况</div>
                    <div class="metric-value">45/64</div>
                    <div class="metric-usage">使用率: 70%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">内存使用情况</div>
                    <div class="metric-value">180/256</div>
                    <div class="metric-usage">使用率: 70%</div>
                </div>
            </div>

            <div class="charts-row">
                <div class="chart-container">
                    <div class="chart-title">GPU使用率趋势</div>
                    <div id="gpuTrendChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">CPU使用率趋势</div>
                    <div id="cpuTrendChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">内存使用率趋势</div>
                    <div id="memoryTrendChart" class="chart"></div>
                </div>
            </div>
        </div>

        <!-- 模型监控区域 -->
        <div class="section">
            <h2 class="section-title">🤖 模型Pod资源监控</h2>
            
            <div class="control-group" style="margin-bottom: 20px;">
                <label>选择模型:</label>
                <select id="modelSelect" style="color: #333; background: white; border: 1px solid #ddd;">
                    <option value="101">LLaMA2-7B (model_id: 101)</option>
                    <option value="102">ChatGLM-6B (model_id: 102)</option>
                    <option value="103">Stable-Diffusion (model_id: 103)</option>
                </select>
            </div>

            <div class="pod-charts">
                <div class="pod-chart-container">
                    <div class="chart-title">Pod GPU使用率监控</div>
                    <div id="podGpuChart" class="pod-chart"></div>
                </div>
                <div class="pod-chart-container">
                    <div class="chart-title">Pod CPU使用率监控</div>
                    <div id="podCpuChart" class="pod-chart"></div>
                </div>
                <div class="pod-chart-container">
                    <div class="chart-title">Pod 内存使用率监控</div>
                    <div id="podMemoryChart" class="pod-chart"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据生成函数
        function generateTimeSeriesData(hours = 24, baseValue = 50, variance = 20) {
            const data = [];
            const now = new Date();
            for (let i = hours * 60; i >= 0; i -= 5) {
                const time = new Date(now.getTime() - i * 60 * 1000);
                const value = Math.max(0, Math.min(100, 
                    baseValue + (Math.random() - 0.5) * variance + Math.sin(i / 60) * 10
                ));
                data.push([time, Math.round(value * 100) / 100]);
            }
            return data;
        }

        // 初始化集群趋势图表
        function initClusterCharts() {
            // GPU趋势图
            const gpuChart = echarts.init(document.getElementById('gpuTrendChart'));
            gpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>GPU使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 60, 25),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#f093fb',
                        width: 3
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(240, 147, 251, 0.3)'},
                                {offset: 1, color: 'rgba(240, 147, 251, 0.1)'}
                            ]
                        }
                    }
                }]
            });

            // CPU趋势图
            const cpuChart = echarts.init(document.getElementById('cpuTrendChart'));
            cpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>CPU使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 70, 20),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#4facfe',
                        width: 3
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(79, 172, 254, 0.3)'},
                                {offset: 1, color: 'rgba(79, 172, 254, 0.1)'}
                            ]
                        }
                    }
                }]
            });

            // 内存趋势图
            const memoryChart = echarts.init(document.getElementById('memoryTrendChart'));
            memoryChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>内存使用率: ${params[0].value[1]}%`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    data: generateTimeSeriesData(24, 70, 15),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#43e97b',
                        width: 3
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(67, 233, 123, 0.3)'},
                                {offset: 1, color: 'rgba(67, 233, 123, 0.1)'}
                            ]
                        }
                    }
                }]
            });
        }

        // 初始化Pod监控图表
        function initPodCharts() {
            const podNames = ['llama2-7b-001', 'llama2-7b-002', 'llama2-7b-003'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            // Pod GPU使用率图表
            const podGpuChart = echarts.init(document.getElementById('podGpuChart'));
            const gpuSeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 40 + index * 15, 20),
                lineStyle: {
                    color: colors[index],
                    width: 2
                }
            }));

            podGpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: gpuSeries
            });

            // Pod CPU使用率图表
            const podCpuChart = echarts.init(document.getElementById('podCpuChart'));
            const cpuSeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 30 + index * 20, 25),
                lineStyle: {
                    color: colors[index],
                    width: 2
                }
            }));

            podCpuChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: cpuSeries
            });

            // Pod 内存使用率图表
            const podMemoryChart = echarts.init(document.getElementById('podMemoryChart'));
            const memorySeries = podNames.map((name, index) => ({
                name: name,
                type: 'line',
                smooth: true,
                data: generateTimeSeriesData(24, 50 + index * 10, 15),
                lineStyle: {
                    color: colors[index],
                    width: 2
                }
            }));

            podMemoryChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `${params[0].axisValueLabel}<br/>`;
                        params.forEach(param => {
                            result += `${param.seriesName}: ${param.value[1]}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: podNames,
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{HH}:{mm}'
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: memorySeries
            });
        }

        // 刷新数据函数
        function refreshData() {
            console.log('刷新数据...');
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.innerHTML = '🔄 刷新中...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.innerHTML = '🔄 刷新数据';
                refreshBtn.disabled = false;
                initClusterCharts();
                initPodCharts();
            }, 2000);
        }

        // 集群选择变化处理
        document.getElementById('clusterSelect').addEventListener('change', function(e) {
            console.log('切换集群:', e.target.value);
            refreshData();
        });

        // 模型选择变化处理
        document.getElementById('modelSelect').addEventListener('change', function(e) {
            console.log('切换模型:', e.target.value);
            initPodCharts();
        });

        // 时间范围选择变化处理
        document.getElementById('timeRangeSelect').addEventListener('change', function(e) {
            console.log('切换时间范围:', e.target.value);
            refreshData();
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initClusterCharts();
            initPodCharts();

            // 设置自动刷新（每30秒）
            setInterval(function() {
                initClusterCharts();
                initPodCharts();
            }, 30000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            setTimeout(function() {
                echarts.getInstanceByDom(document.getElementById('gpuTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('cpuTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('memoryTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podGpuChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podCpuChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('podMemoryChart'))?.resize();
            }, 100);
        });
    </script>
</body>
</html>
