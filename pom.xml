<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.6</version>
        <relativePath/>
    </parent>

    <artifactId>aigc-scheduler-server</artifactId>
    <groupId>com.zjkj.aigc.scheduler</groupId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>aigc-scheduler-server-provider</module>
        <module>aigc-scheduler-server-common</module>
        <module>aigc-scheduler-server-infrastructure</module>
        <module>aigc-scheduler-server-client</module>
        <module>aigc-scheduler-server-domain</module>
        <module>aigc-scheduler-server-application</module>
        <module>aigc-scheduler-server-admin</module>
        <module>aigc-scheduler-server-job</module>
    </modules>

    <properties>
        <java.version>11</java.version>
        <saas-admin-sdk-for-microservice.version>1.4.0</saas-admin-sdk-for-microservice.version>
        <hutool-all.version>5.8.32</hutool-all.version>
        <guava.version>33.2.0-jre</guava.version>
        <fastjson2.version>2.0.51</fastjson2.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <spring-cloud-starter-bootstrap.version>3.1.1</spring-cloud-starter-bootstrap.version>
        <spring-cloud-starter-alibaba-nacos-discovery.version>2021.0.1.0
        </spring-cloud-starter-alibaba-nacos-discovery.version>
        <spring-cloud-starter-alibaba-nacos-config.version>2021.0.1.0
        </spring-cloud-starter-alibaba-nacos-config.version>
        <spring-cloud-starter-openfeign.version>3.1.1</spring-cloud-starter-openfeign.version>
        <spring-cloud-starter-loadbalancer.version>3.1.1</spring-cloud-starter-loadbalancer.version>
        <spring-cloud-starter-stream-rocketmq.version>2.2.10</spring-cloud-starter-stream-rocketmq.version>
        <commons.lang.version>2.6</commons.lang.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <com.zjkj.xxl.job>1.0.0-RELEASE</com.zjkj.xxl.job>
        <apm-toolkit-logback-1.x.version>8.8.0</apm-toolkit-logback-1.x.version>
        <spring-cloud-alibaba.version>2.2.5.RELEASE</spring-cloud-alibaba.version>
        <basic-component-clien.version>1.0.0-RELEASE</basic-component-clien.version>
        <scheduler.client.version>1.1.0</scheduler.client.version>
        <scheduler.common.version>1.1.0</scheduler.common.version>
        <spring-boot-web.version>3.0.0</spring-boot-web.version>
        <dingtalk-service.version>2.0.0</dingtalk-service.version>
        <commons-codec.version>1.11</commons-codec.version>
        <javax-mail.version>1.6.2</javax-mail.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <commons-io.version>2.13.0</commons-io.version>
        <opentelemetry.version>2.7.0-alpha</opentelemetry.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <dashvector.version>1.0.18</dashvector.version>
        <redisson.version>3.17.7</redisson.version>
        <zadig-sdk.version>1.0.1</zadig-sdk.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-bootstrap</artifactId>
                <version>${spring-cloud-starter-bootstrap.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-starter-alibaba-nacos-discovery.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-starter-alibaba-nacos-config.version}</version>
            </dependency>
            <!-- openfeign 组件 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring-cloud-starter-openfeign.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot-web.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>${spring-boot-web.version}</version>
            </dependency>
            <!-- openfeign 组件 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>${spring-cloud-starter-loadbalancer.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
                <version>${spring-cloud-starter-stream-rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-common</artifactId>
                <version>${scheduler.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-client</artifactId>
                <version>${scheduler.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-application</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-domain</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zjkj.aigc.scheduler</groupId>
                <artifactId>aigc-scheduler-server-job</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <!-- 集中仓xxl-job-->
            <dependency>
                <groupId>com.zjkj.logistics</groupId>
                <artifactId>xxl-job-spring-boot-starter</artifactId>
                <version>${com.zjkj.xxl.job}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${apm-toolkit-logback-1.x.version}</version>
            </dependency>
            <!-- 引入SSO用户SDK start -->
            <dependency>
                <groupId>com.zjkj.saas</groupId>
                <artifactId>saas-admin-sdk-for-microservice</artifactId>
                <version>${saas-admin-sdk-for-microservice.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>
            <!-- 钉钉相关依赖 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${dingtalk-service.version}</version>
            </dependency>
            <!--邮件相关依赖 -->
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>${javax-mail.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zjkj.aigc</groupId>
                <artifactId>aigc-zadig-sdk</artifactId>
                <version>${zadig-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dashvector-java-sdk</artifactId>
                <version>${dashvector.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 公共jar -->
    <dependencies>
        <!-- spring-boot 测试相关类 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- spring-boot 测试相关类 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>${lombok-mapstruct-binding.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <useDefaultDelimiters>false</useDefaultDelimiters>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <useDefaultDelimiters>true</useDefaultDelimiters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>11</source> <!-- depending on your project -->
                    <target>11</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.22</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>