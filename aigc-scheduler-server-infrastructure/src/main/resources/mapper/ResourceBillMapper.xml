<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ResourceBillMapper">

    <select id="billTotal" resultType="java.math.BigDecimal">
        SELECT
            SUM(payment_amount) AS payment_amount
        FROM
            resource_bill
        WHERE
            is_deleted = 0
        <if test="condition.type != null and condition.type != ''">
            AND type = #{condition.type}
        </if>
        <if test="condition.platform != null and condition.platform != ''">
            AND platform = #{condition.platform}
        </if>
        <if test="condition.month != null">
            AND `month` = #{condition.month}
        </if>
        <if test="condition.instanceId != null and condition.instanceId != ''">
            AND instance_id = #{condition.instanceId}
        </if>
        <if test="condition.productName != null and condition.productName != ''">
            AND product_name LIKE CONCAT('%', #{condition.productName}, '%')
        </if>
    </select>
</mapper>

