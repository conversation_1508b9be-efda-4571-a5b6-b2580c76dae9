<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestRecordMapper">

    <resultMap type="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord" id="ModelAutoTestRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="testNo" column="test_no" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
        <result property="batchTaskCount" column="batch_task_count" jdbcType="INTEGER"/>
        <result property="successTaskCount" column="success_task_count" jdbcType="INTEGER"/>
        <result property="modelParams" column="model_params" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="testStartTime" column="test_start_time" jdbcType="TIMESTAMP"/>
        <result property="testCancelTime" column="test_cancel_time" jdbcType="TIMESTAMP"/>
        <result property="testCompletionTime" column="test_completion_time" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

   
</mapper>

