<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestReportMapper">

    <resultMap type="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestReport" id="ModelAutoTestReportMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="testNo" column="test_no" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
        <result property="serviceName" column="service_name" jdbcType="VARCHAR"/>
        <result property="modelVersion" column="model_version" jdbcType="VARCHAR"/>
        <result property="taskInfo" column="task_info" jdbcType="VARCHAR"/>
        <result property="gpuType" column="gpu_type" jdbcType="VARCHAR"/>
        <result property="gpuDispatchType" column="gpu_dispatch_type" jdbcType="VARCHAR"/>
        <result property="cpuInfo" column="cpu_info" jdbcType="VARCHAR"/>
        <result property="memoryInfo" column="memory_info" jdbcType="VARCHAR"/>
        <result property="gpuInfo" column="gpu_info" jdbcType="VARCHAR"/>
        <result property="resourceLimits" column="resource_limits" jdbcType="VARCHAR"/>
        <result property="reportStatus" column="report_status" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="genTime" column="gen_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

   
</mapper>

