<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.TaskStatMapper">

    <select id="statTaskSummary" resultType="com.zjkj.aigc.common.dto.TaskSummaryStatDTO">
        SELECT
            task_type,
            model_name,
            task_state,
            count(*) AS amount,
            CASE
              WHEN task_state = 2 THEN ROUND(AVG(TIMESTAMPDIFF(SECOND, task_start_time, task_completion_time)), 3)
            END AS avg_time_sec
        FROM aigc_task
        WHERE created_time BETWEEN #{startTime} AND #{endTime}
        <if test="appId != null and appId != ''">
            AND business_id = #{appId}
        </if>
        <if test="taskType != null and taskType != ''">
            AND task_type = #{taskType}
        </if>
        <if test="modelName != null and modelName != ''">
            AND model_name = #{modelName}
        </if>
        AND is_deleted = 0
        GROUP BY task_type,model_name,task_state
    </select>

    <select id="statActiveTask" resultType="com.zjkj.aigc.common.dto.TaskSummaryStatDTO">
        select
            business_id,
            task_type, model_name,
            MAX(created_time) AS lastTime
        FROM aigc_task
        WHERE created_time &gt;= #{condition.startTime} and is_deleted = 0
        GROUP BY business_id, task_type, model_name
    </select>

    <select id="groupModelHourStat" resultType="com.zjkj.aigc.common.dto.TaskSummaryStatDTO">
        select
            task_type, model_name,
            HOUR(created_time) AS task_time_hour,
            count(*) AS amount
        FROM aigc_task
        WHERE created_time BETWEEN #{condition.startTime} AND #{condition.endTime}
        <if test="condition.taskTypes != null and condition.taskTypes.size() > 0">
            AND task_type IN
            <foreach item="item" collection="condition.taskTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.modelNames != null and condition.modelNames.size() > 0">
            AND model_name IN
            <foreach item="item" collection="condition.modelNames" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND is_deleted = 0
        GROUP BY task_type, model_name, task_time_hour
    </select>

    <select id="statRangeModelTasks" resultType="long">
        SELECT
            count(*)
        FROM aigc_task
        WHERE created_time BETWEEN #{condition.startTime} AND #{condition.endTime}
        <if test="condition.taskType != null and condition.taskType != ''">
            AND task_type = #{condition.taskType}
        </if>
        <if test="condition.modelName != null and condition.modelName != ''">
            AND model_name = #{condition.modelName}
        </if>
        AND is_deleted = 0
    </select>
</mapper>

