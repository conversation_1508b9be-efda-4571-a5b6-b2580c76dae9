<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcTaskMapper">

    <resultMap type="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask" id="AigcTaskMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="taskSource" column="task_source" jdbcType="VARCHAR"/>
        <result property="taskProgress" column="task_progress" jdbcType="INTEGER"/>
        <result property="taskState" column="task_state" jdbcType="INTEGER"/>
        <result property="taskStartTime" column="task_start_time" jdbcType="TIMESTAMP"/>
        <result property="taskCancelTime" column="task_cancel_time" jdbcType="TIMESTAMP"/>
        <result property="taskCompletionTime" column="task_completion_time" jdbcType="TIMESTAMP"/>
        <result property="modelId" column="model_id" jdbcType="BIGINT"/>
        <result property="modelName" column="model_name" jdbcType="VARCHAR"/>
        <result property="modelParams" column="model_params" jdbcType="VARCHAR"/>
        <result property="modelOutput" column="model_output" jdbcType="VARCHAR"/>
        <result property="containerId" column="container_id" jdbcType="VARCHAR"/>
        <result property="failMessage" column="fail_message" jdbcType="VARCHAR"/>
        <result property="retryCount" column="retry_count" jdbcType="INTEGER"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>

    <select id="queryTaskStateGroup" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask">
        SELECT task_state, count(*) AS count
        FROM aigc_task
        WHERE is_deleted = 0
        <if test="condition.taskIds != null and condition.taskIds.size() != 0">
            AND task_id in
            <foreach collection="condition.taskIds" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
        <if test="condition.businessId != null and condition.businessId != ''">
            AND business_id = #{condition.businessId}
        </if>
        <if test="condition.taskType != null and condition.taskType != ''">
            AND task_type = #{condition.taskType}
        </if>
        <if test="condition.modelName != null and condition.modelName != ''">
            AND model_name = #{condition.modelName}
        </if>
        GROUP BY task_state
    </select>

    <select id="streamByTaskState" resultSetType="FORWARD_ONLY" fetchSize="-2147483648" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask">
    SELECT *
    FROM aigc_task
    WHERE task_state = #{taskState} AND is_deleted = 0
    <if test="minTime != null">
        AND task_start_time &gt;= #{minTime}
    </if>
    </select>

    <select id="streamByCondition" resultSetType="FORWARD_ONLY" fetchSize="-2147483648" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask">
    SELECT *
    FROM aigc_task
    WHERE is_deleted = 0
    <if test="condition.taskType != null and condition.taskType != ''">
        AND task_type = #{condition.taskType}
    </if>
    <if test="condition.taskState != null">
        AND task_state = #{condition.taskState}
    </if>
    <if test="condition.taskStates != null and condition.taskStates.size() != 0">
        AND task_state in
        <foreach collection="condition.taskStates" item="taskState" open="(" close=")" separator=",">
            #{taskState}
        </foreach>
    </if>
    <if test="condition.businessId != null and condition.businessId != ''">
        AND business_id = #{condition.businessId}
    </if>
    <if test="condition.businessIds != null and condition.businessIds.size() != 0">
        AND business_id in
        <foreach collection="condition.businessIds" item="businessId" open="(" close=")" separator=",">
            #{businessId}
        </foreach>
    </if>
    <if test="condition.modelNameList != null and condition.modelNameList.size() != 0">
        AND model_name in
        <foreach collection="condition.modelNameList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    <if test="condition.modelName != null and condition.modelName != ''">
        AND model_name = #{condition.modelName}
    </if>
    <if test="condition.startTime != null and condition.startTime != ''">
        AND created_time &gt;= #{condition.startTime}
    </if>
    <if test="condition.endTime != null and condition.endTime != ''">
        AND created_time &lt;= #{condition.endTime}
    </if>
    </select>

    <select id="getTaskRank" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask">
        SELECT id, aigc_task_id, `rank` FROM (
            SELECT id, aigc_task_id, ROW_NUMBER() OVER () AS `rank`
            FROM aigc_task
            WHERE is_deleted = 0
            AND task_state = #{condition.taskState}
            <if test="condition.taskType != null and condition.taskType != ''">
                AND task_type = #{condition.taskType}
            </if>
            <if test="condition.modelName != null and condition.modelName != ''">
                AND model_name = #{condition.modelName}
            </if>
            <if test="condition.taskBatch != null">
                AND task_batch = #{condition.taskBatch}
            </if>
            ORDER BY task_priority, id
        ) AS temp
        WHERE aigc_task_id IN
        <foreach collection="condition.aigcTaskIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <delete id="actualDelByBusinessId">
        DELETE FROM aigc_task
        WHERE business_id = #{businessId}
    </delete>

    <delete id="actualDelById">
        DELETE FROM aigc_task
        WHERE id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

</mapper>

