<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestParamMapper">

    <resultMap type="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestParam" id="ModelAutoTestParamMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="testNo" column="test_no" jdbcType="VARCHAR"/>
        <result property="paramName" column="task_type" jdbcType="VARCHAR"/>
        <result property="paramFileUrl" column="model_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
    </resultMap>
</mapper>

