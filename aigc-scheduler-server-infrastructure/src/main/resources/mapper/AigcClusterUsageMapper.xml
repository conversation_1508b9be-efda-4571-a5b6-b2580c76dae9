<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcClusterUsageMapper">


    <select id="statByDate" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage">
        select
            data_date,
            IFNULL(SUM(total_gpu_memory_size), 0) AS total_gpu_memory_size,
            IFNULL(SUM(used_gpu_memory_size), 0) AS used_gpu_memory_size,
            IFNULL(SUM(total_cpu_core), 0) AS total_cpu_core,
            IFNULL(SUM(used_cpu_core), 0) AS used_cpu_core,
            IFNULL(SUM(total_memory_size), 0) AS total_memory_size,
            IFNULL(SUM(used_memory_size), 0) AS used_memory_size
        FROM aigc_cluster_usage
        WHERE is_deleted = 0
        AND data_date BETWEEN #{condition.startDate} AND #{condition.endDate}
        <if test="condition.clusterId != null">
            AND cluster_id = #{condition.clusterId}
        </if>
        <if test="condition.platform != null and condition.platform != ''">
            AND platform = #{condition.platform}
        </if>
        group by data_date
    </select>
   
</mapper>

