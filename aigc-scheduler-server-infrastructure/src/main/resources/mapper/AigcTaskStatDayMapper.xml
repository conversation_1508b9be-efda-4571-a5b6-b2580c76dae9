<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcTaskStatDayMapper">

  <select id="pageByGroupModel" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
    SELECT
        task_type, model_name,
        IFNULL(SUM(success_amount), 0) AS success_amount,
        IFNULL(SUM(fail_amount), 0) AS fail_amount,
        IFNULL(SUM(running_amount), 0) AS running_amount,
        IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
        IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
        IFNULL(SUM(total_amount), 0) AS total_amount,
        ROUND(AVG(avg_elapsed_time)) AS avg_elapsed_time
    FROM aigc_task_stat_day
    WHERE is_deleted = 0
    <if test="query.startDate != null and query.startDate != ''">
      AND stat_date &gt;= #{query.startDate}
    </if>
    <if test="query.endDate != null and query.endDate != ''">
      AND stat_date &lt;= #{query.endDate}
    </if>
    <if test="query.taskType != null and query.taskType != ''">
      AND task_type = #{query.taskType}
    </if>
    <if test="query.modelName != null and query.modelName != ''">
      AND model_name = #{query.modelName}
    </if>
    <if test="query.modelType != null and query.modelType != ''">
      AND model_type = #{query.modelType}
    </if>
    GROUP BY task_type, model_name
    ORDER BY total_amount DESC
  </select>

  <select id="sumByGroupStateDate" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
    SELECT
        stat_date,
        IFNULL(SUM(success_amount), 0) AS success_amount,
        IFNULL(SUM(fail_amount), 0) AS fail_amount,
        IFNULL(SUM(running_amount), 0) AS running_amount,
        IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
        IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
        IFNULL(SUM(total_amount), 0) AS total_amount,
        ROUND(AVG(avg_elapsed_time)) AS avg_elapsed_time
    FROM aigc_task_stat_day
    WHERE is_deleted = 0
    <if test="query.startDate != null and query.startDate != ''">
      AND stat_date &gt;= #{query.startDate}
    </if>
    <if test="query.endDate != null and query.endDate != ''">
      AND stat_date &lt;= #{query.endDate}
    </if>
    <if test="query.taskType != null and query.taskType != ''">
      AND task_type = #{query.taskType}
    </if>
    <if test="query.modelName != null and query.modelName != ''">
      AND model_name = #{query.modelName}
    </if>
    <if test="query.taskTypes != null and query.taskTypes.size() != 0">
      AND task_type in
      <foreach collection="query.taskTypes" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="query.modelNames != null and query.modelNames.size() != 0">
      AND model_name in
      <foreach collection="query.modelNames" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="query.modelType != null and query.modelType != ''">
      AND model_type = #{query.modelType}
    </if>
    GROUP BY stat_date
  </select>

  <select id="sumByGroupModel" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
    SELECT
        <if test="!query.noGroupByModel">
          task_type, model_name,
        </if>
        IFNULL(SUM(success_amount), 0) AS success_amount,
        IFNULL(SUM(fail_amount), 0) AS fail_amount,
        IFNULL(SUM(running_amount), 0) AS running_amount,
        IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
        IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
        IFNULL(SUM(total_amount), 0) AS total_amount,
        ROUND(AVG(avg_elapsed_time)) AS avg_elapsed_time
    FROM aigc_task_stat_day
    WHERE is_deleted = 0
    <if test="query.startDate != null and query.startDate != ''">
      AND stat_date &gt;= #{query.startDate}
    </if>
    <if test="query.endDate != null and query.endDate != ''">
      AND stat_date &lt;= #{query.endDate}
    </if>
    <if test="query.taskType != null and query.taskType != ''">
      AND task_type = #{query.taskType}
    </if>
    <if test="query.modelName != null and query.modelName != ''">
      AND model_name = #{query.modelName}
    </if>
    <if test="query.modelType != null and query.modelType != ''">
      AND model_type = #{query.modelType}
    </if>
    <if test="!query.noGroupByModel">
      GROUP BY task_type, model_name
      ORDER BY total_amount DESC
    </if>
  </select>

  <select id="sumByModelType" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
      SELECT
        model_type,
        IFNULL(SUM(total_amount), 0) AS total_amount,
        COUNT(*) AS modelCount
      FROM aigc_task_stat_day
      WHERE is_deleted = 0
      <if test="query.startDate != null and query.startDate != ''">
          AND stat_date &gt;= #{query.startDate}
      </if>
      <if test="query.endDate != null and query.endDate != ''">
          AND stat_date &lt;= #{query.endDate}
      </if>
      GROUP BY model_type
  </select>

    <select id="sumTotal" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
        SELECT
            IFNULL(SUM(success_amount), 0) AS success_amount,
            IFNULL(SUM(fail_amount), 0) AS fail_amount,
            IFNULL(SUM(running_amount), 0) AS running_amount,
            IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
            IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
            IFNULL(SUM(total_amount), 0) AS total_amount
        FROM aigc_task_stat_day
        WHERE is_deleted = 0
        <if test="query.startDate != null and query.startDate != ''">
            AND stat_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND stat_date &lt;= #{query.endDate}
        </if>
    </select>

    <select id="listByModelStateDate" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
        SELECT
        task_type, model_name,
        IFNULL(SUM(success_amount), 0) AS success_amount,
        IFNULL(SUM(fail_amount), 0) AS fail_amount,
        IFNULL(SUM(running_amount), 0) AS running_amount,
        IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
        IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
        IFNULL(SUM(total_amount), 0) AS total_amount,
        ROUND(AVG(avg_elapsed_time)) AS avg_elapsed_time
        FROM aigc_task_stat_day
        WHERE is_deleted = 0
        <if test="query.startDate != null and query.startDate != ''">
            AND stat_date &gt;= #{query.startDate}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND stat_date &lt;= #{query.endDate}
        </if>
        <if test="query.taskTypes != null and query.taskTypes.size() != 0">
            AND task_type in
            <foreach collection="query.taskTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.modelNames != null and query.modelNames.size() != 0">
            AND model_name in
            <foreach collection="query.modelNames" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY task_type, model_name
        ORDER BY total_amount DESC
    </select>

  <select id="sumByGroupModelStatDate" resultType="com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay">
      SELECT
          stat_date,task_type, model_name,
          IFNULL(SUM(success_amount), 0) AS success_amount,
          IFNULL(SUM(fail_amount), 0) AS fail_amount,
          IFNULL(SUM(running_amount), 0) AS running_amount,
          IFNULL(SUM(waiting_amount), 0) AS waiting_amount,
          IFNULL(SUM(cancel_amount), 0) AS cancel_amount,
          IFNULL(SUM(total_amount), 0) AS total_amount,
          ROUND(AVG(avg_elapsed_time)) AS avg_elapsed_time
      FROM aigc_task_stat_day
      WHERE is_deleted = 0
      <if test="query.startDate != null and query.startDate != ''">
          AND stat_date &gt;= #{query.startDate}
      </if>
      <if test="query.endDate != null and query.endDate != ''">
          AND stat_date &lt;= #{query.endDate}
      </if>
      <if test="query.taskType != null and query.taskType != ''">
          AND task_type = #{query.taskType}
      </if>
      <if test="query.modelName != null and query.modelName != ''">
          AND model_name = #{query.modelName}
      </if>
      <if test="query.taskTypes != null and query.taskTypes.size() != 0">
          AND task_type in
          <foreach collection="query.taskTypes" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="query.modelNames != null and query.modelNames.size() != 0">
          AND model_name in
          <foreach collection="query.modelNames" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="query.modelType != null and query.modelType != ''">
          AND model_type = #{query.modelType}
      </if>
      GROUP BY stat_date, task_type, model_name
    </select>
</mapper>