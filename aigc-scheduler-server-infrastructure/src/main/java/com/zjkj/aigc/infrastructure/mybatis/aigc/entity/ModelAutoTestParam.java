package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模型测试报告表(ModelAutoTestReport)实体类
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("model_auto_test_param")
public class ModelAutoTestParam extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 442112879694654880L;
    /**
     * 测试编码
     */
    private String testNo;
    /**
     * 任务类型
     */
    private String paramName;
    /**
     * AI模型名称
     */
    private String paramFileUrl;
}

