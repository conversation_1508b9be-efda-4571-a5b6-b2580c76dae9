package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstanceAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelInstanceAdjustRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型实例调整记录表(AigcModelInstanceAdjustRecord)Dao层
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelInstanceAdjustRecordDao extends AbstractBaseDao<AigcModelInstanceAdjustRecordMapper, AigcModelInstanceAdjustRecord> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcModelInstanceAdjustRecord> queryPage(AigcModelInstanceAdjustRecordCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }
}
