package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 集群节点查询条件
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AigcClusterNodeCondition {

    /**
     * 集群主键id
     */
    private Long clusterId;

    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * gpu主键id
     */
    private Long gpuId;

    public LambdaQueryWrapper<AigcClusterNode> buildQuery() {
        return Wrappers.<AigcClusterNode>lambdaQuery()
                .eq(Objects.nonNull(clusterId), AigcClusterNode::getClusterId, clusterId)
                .eq(StringUtils.isNotBlank(nodeIp), AigcClusterNode::getNodeIp, nodeIp)
                .eq(StringUtils.isNotBlank(instanceId), AigcClusterNode::getInstanceId, instanceId)
                .eq(Objects.nonNull(gpuId), AigcClusterNode::getGpuId, gpuId)
                ;
    }
}
