package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictDataCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.sys.SysDictDataMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Repository
@RequiredArgsConstructor
public class SysDictDataDao extends AbstractBaseDao<SysDictDataMapper, SysDictData> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<SysDictData> queryPage(SysDictDataCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }
    /**
     * 查询字典数据列表
     *
     * @param condition 查询条件
     * @return 字典数据列表
     */
    public List<SysDictData> queryList(SysDictDataCondition condition) {
        LambdaQueryWrapper<SysDictData> queryWrapper = condition.buildQuery();
        queryWrapper.orderByAsc(SysDictData::getDictSort);
        return list(queryWrapper);
    }


}
