package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Data
@Builder
public class ModelAutoTestRecordCondition implements Serializable {
    private static final long serialVersionUID = -5372648726388134458L;

    /**
     * 名称
     */
    private String name;
    /**
     * 测试编号
     */
    private List<String> testNoList;
    /**
     *  状态
     */
    private List<Integer> statusList;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;

    private Integer limit;

    /**
     * 构建查询条件
     * @return 查询条件
     */
    public LambdaQueryWrapper<ModelAutoTestRecord> buildQuery() {
        LambdaQueryWrapper<ModelAutoTestRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(this.getName()), ModelAutoTestRecord::getName, this.getName());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getTestNoList()), ModelAutoTestRecord::getTestNo, this.getTestNoList());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getStatusList()), ModelAutoTestRecord::getStatus, this.getStatusList());
        queryWrapper.eq(StringUtils.isNotBlank(this.getTaskType()), ModelAutoTestRecord::getTaskType, this.getTaskType());
        queryWrapper.eq(StringUtils.isNotBlank(this.getModelName()), ModelAutoTestRecord::getModelName, this.getModelName());
        queryWrapper.last(Objects.nonNull(this.limit), "LIMIT " + this.limit);
        return queryWrapper;
    }
}
