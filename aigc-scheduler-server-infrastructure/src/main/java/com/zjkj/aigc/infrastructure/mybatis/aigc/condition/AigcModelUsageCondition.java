package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/2/18
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcModelUsageCondition extends BaseCondition<AigcModelUsage>{

    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 集群主键id
     */
    private Collection<Long> clusterIds;
    /**
     * 数据日期
     */
    private LocalDate dataDate;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;

    public LambdaQueryWrapper<AigcModelUsage> buildQuery() {
        LambdaQueryWrapper<AigcModelUsage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(Objects.nonNull(clusterId), AigcModelUsage::getClusterId, clusterId)
                .in(CollectionUtils.isNotEmpty(clusterIds), AigcModelUsage::getClusterId, clusterIds)
                .eq(Objects.nonNull(dataDate), AigcModelUsage::getDataDate, dataDate)
                .ge(Objects.nonNull(startDate), AigcModelUsage::getDataDate, startDate)
                .le(Objects.nonNull(endDate), AigcModelUsage::getDataDate, endDate)
        ;
        return queryWrapper;
    }
}
