package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * GPU资源申请(GpuApply)实体类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
public class GpuApply extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -9093757270859285668L;

    /**
     * 审批编号
     */
    private String code;
    /**
     * 审批类型
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalTypeEnum}
     */
    private String type;
    /**
     * 集群/业务
     */
    private String clusterBusiness;
    /**
     * 云平台
     */
    private String platform;
    /**
     * GPU型号
     * {@link com.zjkj.aigc.common.enums.GpuModelEnum}
     */
    private String gpuModel;
    /**
     * GPU卡数
     */
    private Integer gpuNum;
    /**
     * 单卡显存
     */
    private Integer singleCardMemory;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum}
     */
    private Integer status;
    /**
     * 补充说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 操作说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supplyRemark;
    /**
     * 删除版本号
     */
    private String delVersion;
}
