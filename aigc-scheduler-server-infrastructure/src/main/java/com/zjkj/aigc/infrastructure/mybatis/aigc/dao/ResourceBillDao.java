package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ResourceBillMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 资源账单表(ResourceBill)Dao层
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ResourceBillDao extends AbstractBaseDao<ResourceBillMapper, ResourceBill> {

    private final ResourceBillMapper resourceBillMapper;
    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 资源账单列表
     */
    public List<ResourceBill> queryList(ResourceBillCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 账单总额
     *
     * @param condition 查询条件
     * @return 总额
     */
    public BigDecimal billTotal(ResourceBillCondition condition) {
        BigDecimal billTotal = resourceBillMapper.billTotal(condition);
        return Objects.isNull(billTotal) ? BigDecimal.ZERO : billTotal;
    }
}
