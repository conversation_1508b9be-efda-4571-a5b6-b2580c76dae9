package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.common.dto.TaskSummaryStatDTO;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ai任务表(AigcTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface TaskStatMapper extends BaseMapper<TaskSummaryStatDTO> {

    List<TaskSummaryStatDTO> statTaskSummary(TaskSummaryStatCondition req);

    /**
     * 活跃任务统计
     *
     * @param condition 查询条件
     * @return 活跃任务统计
     */
    List<TaskSummaryStatDTO> statActiveTask(@Param("condition") TaskSummaryStatCondition condition);

    /**
     * 任务统计-按模型小时统计
     *
     * @param condition 查询条件
     * @return 任务统计-按模型统计
     */
    List<TaskSummaryStatDTO> groupModelHourStat(@Param("condition") TaskSummaryStatCondition condition);
    /**
     * 统计模型任务数
     * @param condition 查询条件
     * @return 模型任务数
     */
    long statRangeModelTasks(@Param("condition") TaskSummaryStatCondition condition);
}

