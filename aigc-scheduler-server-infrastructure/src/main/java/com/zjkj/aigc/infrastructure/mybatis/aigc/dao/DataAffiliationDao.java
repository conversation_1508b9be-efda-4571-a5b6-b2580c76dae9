package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.DataAffiliation;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.DataAffiliationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据所属表(DataAffiliation)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DataAffiliationDao extends AbstractBaseDao<DataAffiliationMapper, DataAffiliation> {

    /**
     * 获取数据
     *
     * @param dataType 数量类型
     * @param dataIds  数据id
     * @return 列表
     */
    public List<DataAffiliation> queryByDataTypeId(String dataType, List<String> dataIds) {
        return this.lambdaQuery()
                .eq(DataAffiliation::getDataType, dataType)
                .in(DataAffiliation::getDataId, dataIds)
                .list();
    }
}
