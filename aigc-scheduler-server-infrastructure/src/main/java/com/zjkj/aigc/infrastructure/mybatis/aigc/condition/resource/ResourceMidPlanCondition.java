package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
@Builder
public class ResourceMidPlanCondition implements Serializable {
    private static final long serialVersionUID = 5090330161438222924L;
    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 云平台集合
     */
    private List<String> platforms;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<ResourceMidPlan> buildQuery() {
        LambdaQueryWrapper<ResourceMidPlan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(month), ResourceMidPlan::getMonth, month)
                .eq(StringUtils.isNotBlank(platform), ResourceMidPlan::getPlatform, platform)
                .in(CollUtil.isNotEmpty(platforms), ResourceMidPlan::getPlatform, platforms)
        ;
        return queryWrapper;
    }

    /**
     * 构建删除条件
     *
     * @return 查询条件
     */
    public LambdaUpdateWrapper<ResourceMidPlan> buildDelete() {
        LambdaUpdateWrapper<ResourceMidPlan> updateWrapper = Wrappers.<ResourceMidPlan>lambdaUpdate()
                .set(ResourceMidPlan::getIsDeleted, 1)
                .set(ResourceMidPlan::getDelVersion, System.currentTimeMillis())
                .eq(Objects.nonNull(month), ResourceMidPlan::getMonth, month)
                .eq(StringUtils.isNotBlank(platform), ResourceMidPlan::getPlatform, platform);
        return updateWrapper;
    }
}
