package com.zjkj.aigc.infrastructure.mybatis.aigc.es.event;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
public class AigcTaskBatchEvent extends ApplicationEvent {

    private boolean delete = false;

    public AigcTaskBatchEvent(Object source) {
        super(source);
    }

    public AigcTaskBatchEvent setDelete(boolean delete) {
        this.delete = delete;
        return this;
    }

    public boolean getDelete() {
        return this.delete;
    }

    public List<Long> getIds() {
        List<Long> ids = new ArrayList<>();
        if (source instanceof Collection) {
            ((Collection) source).stream()
                    .forEach(t -> {
                        if (t instanceof Long) {
                            ids.add((Long) t);
                        } else if (t instanceof AigcTask) {
                            ids.add(((AigcTask) t).getId());
                        }
                    });
        }
        return ids;
    }

    public List<String> getAigcTaskIds() {
        List<String> ids = new ArrayList<>();
        if (source instanceof Collection) {
            ((Collection) source).stream()
                    .forEach(t -> {
                        if (t instanceof String) {
                            ids.add((String) t);
                        } else if (t instanceof AigcTask) {
                            ids.add(((AigcTask) t).getAigcTaskId());
                        }
                    });
        }
        return ids;
    }

    public List<AigcTask> getAigcTasks() {
        List<AigcTask> tasks = new ArrayList<>();
        if (source instanceof Collection) {
            ((Collection) source).stream()
                    .forEach(t -> {
                        if (t instanceof AigcTask) {
                            tasks.add((AigcTask) t);
                        }
                    });
        }
        return tasks;
    }
}
