package com.zjkj.aigc.infrastructure.mybatis.aigc.doc;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ai任务表(AigcTask)实体类
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@Accessors(chain = true)
@Document(indexName = "#{@indexNames.active}.#{@indexNames.aigcTask}")
@Setting(shards = 5)
public class AigcTaskDoc implements Serializable {

    private static final long serialVersionUID = 436124028580969512L;

    /**
     * 平台任务ID
     */
    @Id
    private String aigcTaskId;

    /**
     * 表自增ID
     */
    @Field(type = FieldType.Long)
    private Long tableId;

    /**
     * 表
     */
    @Field(type = FieldType.Keyword)
    private String tableSource;

    /**
     * 任务ID
     */
    @Field(type = FieldType.Keyword)
    private String taskId;

    /**
     * 任务类型，一般与AI模型对应
     */
    @Field(type = FieldType.Keyword)
    private String taskType;

    /**
     * 业务系统ID
     */
    @Field(type = FieldType.Keyword)
    private String businessId;

    /**
     * 任务优先级
     * 数字越小优先级越高
     */
    @Field(type = FieldType.Integer)
    private Integer taskPriority;

    /**
     * 是否任务批量0，1
     */
    @Field(type = FieldType.Integer)
    private Integer taskBatch;

    /**
     * 任务来源
     */
    @Field(type = FieldType.Keyword)
    private String taskSource;

    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消
     */
    @Field(type = FieldType.Integer)
    private Integer taskState;

    /**
     * AI模型
     */
    @Field(type = FieldType.Long)
    private Long modelId;

    /**
     * AI模型名称
     */
    @Field(type = FieldType.Keyword)
    private String modelName;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_millis)
    private LocalDateTime createdTime;


}

