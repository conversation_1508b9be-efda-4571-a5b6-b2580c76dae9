package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 告警渠道配置表(AigcAlarmChannel)实体类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
public class AigcAlarmChannel extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -5128494265970211730L;
    /**
     * 标签
     */
    private String tag;
    /**
     * 渠道类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmChannelEnum}
     */
    private Integer channelType;
    /**
     * Webhook地址
     */
    private String webhookUrl;
    /**
     * 签名密钥
     */
    private String sign;
}
