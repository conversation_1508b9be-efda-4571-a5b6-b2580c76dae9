package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据所属表(DataAffiliation)实体类
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataAffiliation extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 392864090083892348L;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 数据类型
     */
    private String dataType;
}

