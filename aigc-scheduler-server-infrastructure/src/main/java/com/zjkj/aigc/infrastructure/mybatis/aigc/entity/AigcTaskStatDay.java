package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * aigc_task_stat_day
 * <AUTHOR>
@Data
public class AigcTaskStatDay implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型类型
     */
    private String modelType;
    /**
     * 任务类型，一般与AI模型对应
     */
    private String taskType;

    /**
     * AI模型名称
     */
    private String modelName;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 成功数量
     */
    private Long successAmount;

    /**
     * 失败数量
     */
    private Long failAmount;

    /**
     * 处理中数量
     */
    private Long runningAmount;

    /**
     * 等待中数量
     */
    private Long waitingAmount;

    /**
     * 取消数量
     */
    private Long cancelAmount;

    /**
     * 总数量
     */
    private Long totalAmount;
    /**
     * 平均耗时ms
     */
    private Long avgElapsedTime;
    /**
     * 统计日期
     */
    private LocalDate statDate;
    /**
     * 模型数量
     */
    @TableField(exist = false)
    private Long modelCount;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;

    /**
     * 逻辑删除 0 否 1是
     */
    @TableLogic(value = "0", delval = "1")
    private Byte isDeleted;

    private static final long serialVersionUID = 1L;
}