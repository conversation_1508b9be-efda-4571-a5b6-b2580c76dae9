package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcNodeInstanceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 节点实例表(AigcNodeInstance)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcNodeInstanceDao extends AbstractBaseDao<AigcNodeInstanceMapper, AigcNodeInstance> {

    /**
     * 根据主机名获取节点实例
     * @param hostname 主机名
     * @return 节点实例
     */
    public AigcNodeInstance getByHostname(String hostname) {
        return this.lambdaQuery()
                .eq(AigcNodeInstance::getHostname, hostname)
                .one();
    }

    /**
     * 根据节点IP查询节点实例
     * @param nodeIp 节点IP
     * @return 节点实例
     */
    public List<AigcNodeInstance> queryByNodeIp(String nodeIp) {
        return this.lambdaQuery()
                .eq(AigcNodeInstance::getNodeIp, nodeIp)
                .list();
    }

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcNodeInstance> queryPage(AigcNodeInstanceCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcNodeInstance> queryList(AigcNodeInstanceCondition condition) {
        return list(condition.buildQuery());
    }

}
