package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应用表(AigcApp)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AigcApp extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 754967769056383591L;

    /**
     * 名称
     */
    private String name;
    /**
     * appid
     */
    private String appId;
    /**
     * 密钥
     */
    private String appSecret;
    /**
     * 状态: 0-未激活,1-激活,2-禁用
     */
    private Integer status;
    /**
     * 回调地址
     */
    private String notifyUrl;
    /**
     * 上次活跃时间
     */
    private LocalDateTime lastActiveTime;
}

