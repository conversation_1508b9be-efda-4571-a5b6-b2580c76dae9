package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Data
@Builder
public class AigcAlarmChannelCondition implements Serializable {
    private static final long serialVersionUID = -7185030848605320971L;
    /**
     * 标签
     */
    private String tag;
    /**
     * 渠道类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmChannelEnum}
     */
    private Integer channelType;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcAlarmChannel> buildQuery() {
        LambdaQueryWrapper<AigcAlarmChannel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(tag), AigcAlarmChannel::getTag, tag)
                .eq(Objects.nonNull(channelType), AigcAlarmChannel::getChannelType, channelType);
        return queryWrapper;
    }
}
