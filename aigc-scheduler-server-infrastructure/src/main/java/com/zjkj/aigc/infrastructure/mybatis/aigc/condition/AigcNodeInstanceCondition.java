package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AigcNodeInstanceCondition extends BaseCondition<AigcNodeInstance> implements Serializable {
    private static final long serialVersionUID = 7640990285330837561L;
    /**
     * 主机名
     */
    private String hostname;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 节点ip
     */
    private List<String> nodeIps;
    /**
     * 合法状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 健康状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcNodeInstance> buildQuery() {
        LambdaQueryWrapper<AigcNodeInstance> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(hostname), AigcNodeInstance::getHostname, hostname)
                .eq(StringUtils.isNotBlank(nodeIp), AigcNodeInstance::getNodeIp, nodeIp)
                .in(CollectionUtils.isNotEmpty(nodeIps), AigcNodeInstance::getNodeIp, nodeIps)
                .eq(Objects.nonNull(status), AigcNodeInstance::getStatus, status)
                .eq(Objects.nonNull(healthStatus), AigcNodeInstance::getHealthStatus, healthStatus)
        ;

        if (StringUtils.isNotBlank(namespace)) {
            queryWrapper.apply("find_in_set({0}, namespace)", namespace);
        }
        return queryWrapper;
    }
}
