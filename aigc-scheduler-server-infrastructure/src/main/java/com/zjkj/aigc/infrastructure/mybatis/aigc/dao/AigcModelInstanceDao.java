package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelInstanceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型实例表(AigcModelInstance)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelInstanceDao extends AbstractBaseDao<AigcModelInstanceMapper, AigcModelInstance> {

    /**
     * 根据podName查询模型实例
     * @param podName podName
     * @return AigcModelInstance
     */
    public AigcModelInstance queryByPodName(String podName) {
        return this.lambdaQuery()
                .eq(AigcModelInstance::getPodName, podName)
                .one();
    }

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcModelInstance> queryPage(AigcModelInstanceCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcModelInstance> queryList(AigcModelInstanceCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 查询在线模型
     * @param condition 查询条件
     * @return 在线模型
     */
    public List<AigcModelInstance> queryOnlineModel(AigcModelInstanceCondition condition) {
        LambdaQueryWrapper<AigcModelInstance> queryWrapper = condition.buildQuery();
        queryWrapper.select(AigcModelInstance::getTaskType, AigcModelInstance::getModelName);
        queryWrapper.groupBy(List.of(AigcModelInstance::getTaskType, AigcModelInstance::getModelName));
        return list(queryWrapper);
    }
}
