package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资源账单表(ResourceBill)实体类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResourceBill extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -45978328317745062L;
    /**
     * 月份
     */
    private Integer month;
    /**
     * 账单类型
     */
    private String type;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品
     */
    private String productName;
    /**
     * GPU型号
     */
    private String gpuModel;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 计费类型
     */
    private String billingType;
    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;
    /**
     * 付款时间
     */
    private LocalDateTime paymentTime;
}

