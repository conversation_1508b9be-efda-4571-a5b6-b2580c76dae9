package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import com.zjkj.aigc.infrastructure.mybatis.config.LongListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态配置表(AigcDynamicConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
public class AigcDynamicConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 672345653674968611L;

    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 最小副本数
     */
    private Integer minReplica;
    /**
     * 最小副本数
     */
    private Integer maxReplica;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 冷却分钟数
     */
    private Integer cooldownMinute;
    /**
     * 扩容维度:分钟数
     */
    private Integer scaleOutMinute;
    /**
     * 扩容负载: 阈值1-100%
     */
    private Integer scaleOutThreshold;
    /**
     * 优先剥夺的模型id
     */
    @TableField(value = "deprived_model_id", typeHandler = LongListTypeHandler.class)
    private List<Long> deprivedModelIds;
    /**
     * 缩容维度:分钟数
     */
    private Integer scaleInMinute;
    /**
     * 缩容负载:阈值1-100%
     */
    private Integer scaleInThreshold;
    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;
    /**
     * 上次伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String lastScaleType;
    /**
     * 上次伸缩时间
     */
    private LocalDateTime lastScaleTime;

    public AigcDynamicConfig buildSnapshot() {
        return new AigcDynamicConfig()
                .setMinReplica(minReplica)
                .setMaxReplica(maxReplica)
                .setPriority(priority)
                .setCooldownMinute(cooldownMinute)
                .setScaleOutMinute(scaleOutMinute)
                .setScaleOutThreshold(scaleOutThreshold)
                .setDeprivedModelIds(deprivedModelIds)
                .setScaleInMinute(scaleInMinute)
                .setScaleInThreshold(scaleInThreshold)
                ;
    }
}

