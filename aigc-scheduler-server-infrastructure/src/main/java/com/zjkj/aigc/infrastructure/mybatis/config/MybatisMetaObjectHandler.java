package com.zjkj.aigc.infrastructure.mybatis.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.zjkj.saas.admin.sdk.dto.SsoUserDTO;
import com.zjkj.saas.admin.sdk.util.SaasSsoContext;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Component
public class MybatisMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATED_TIME = "createdTime";
    private static final String REVISED_TIME = "revisedTime";

    private static final String CREATOR_ID = "creatorId";
    private static final String CREATOR_NAME = "creatorName";

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, CREATED_TIME, LocalDateTime.class, LocalDateTime.now());

        SsoUserDTO ssoUserDTO = SaasSsoContext.getCurrentContext().getSsoUser();
        Optional.ofNullable(ssoUserDTO)
                .ifPresent(user -> {
                    setIfHasSetter(metaObject, CREATOR_ID, ssoUserDTO.getUserId());
                    setIfHasSetter(metaObject, CREATOR_NAME, ssoUserDTO.getUsername());
                });
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, REVISED_TIME, LocalDateTime.class, LocalDateTime.now());
    }

    /**
     * 设置属性值
     *
     * @param metaObject metaObject
     * @param property   属性
     * @param value      值
     */
    public static void setIfHasSetter(MetaObject metaObject, String property, Object value) {
        if (metaObject.hasSetter(property)) {
            metaObject.setValue(property, value);
        }
    }
}
