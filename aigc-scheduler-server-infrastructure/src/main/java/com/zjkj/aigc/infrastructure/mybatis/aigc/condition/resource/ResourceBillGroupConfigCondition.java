package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBillGroupConfig;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/04/09
 */
@Data
@Builder
public class ResourceBillGroupConfigCondition implements Serializable {
    private static final long serialVersionUID = 192023645176702508L;
    /**
     * 支出月份
     */
    private Integer month;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<ResourceBillGroupConfig> buildQuery() {
        LambdaQueryWrapper<ResourceBillGroupConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(month), ResourceBillGroupConfig::getMonth, month)
        ;
        return queryWrapper;
    }
}
