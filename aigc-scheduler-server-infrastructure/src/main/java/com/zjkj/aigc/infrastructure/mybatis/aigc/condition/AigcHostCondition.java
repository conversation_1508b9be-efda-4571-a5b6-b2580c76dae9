package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AigcHostCondition extends BaseCondition<AigcHost> {

    /**
     * 主机ip
     */
    private String hostIp;
    /**
     * 平台
     */
    private List<String> platforms;
    /**
     * 区域
     */
    private List<String> regions;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcHost> buildQuery() {
        LambdaQueryWrapper<AigcHost> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcHost::getId, this.getIds())
                .eq(StringUtils.isNotBlank(hostIp), AigcHost::getHostIp, hostIp)
                .in(CollectionUtils.isNotEmpty(platforms), AigcHost::getPlatform, platforms)
                .in(CollectionUtils.isNotEmpty(regions), AigcHost::getRegion, regions)
                .eq(Objects.nonNull(status), AigcHost::getStatus, status)
        ;
        return queryWrapper;
    }
}
