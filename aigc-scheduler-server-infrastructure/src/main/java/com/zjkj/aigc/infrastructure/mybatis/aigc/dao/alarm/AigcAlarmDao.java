package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.alarm.AigcAlarmMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 模型告警(AigcAlarm)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcAlarmDao extends AbstractBaseDao<AigcAlarmMapper, AigcAlarm> {
    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcAlarm> queryPage(AigcAlarmCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 获取最近一次报警信息
     *
     * @param modelName 模型名称
     * @param taskType  任务类型
     * @param alarmConfigId 告警配ID
     * @return 报警信息
     */
    public AigcAlarm getLastAlarm(String modelName, String taskType, Long alarmConfigId) {
        LambdaQueryWrapper<AigcAlarm> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AigcAlarm::getAlarmConfigId, alarmConfigId);
        wrapper.eq(AigcAlarm::getModelName, modelName);
        wrapper.eq(AigcAlarm::getTaskType, taskType)
                .orderByDesc(AigcAlarm::getAlarmTime).last("limit 1");
        return getOne(wrapper);
    }
}
