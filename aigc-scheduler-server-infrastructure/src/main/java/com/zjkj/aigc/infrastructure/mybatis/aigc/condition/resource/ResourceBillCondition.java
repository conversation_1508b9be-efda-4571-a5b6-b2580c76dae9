package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ResourceBillCondition extends BaseCondition<ResourceBill> {
    /**
     * 账单类型
     */
    private String type;
    /**
     * 云平台
     */
    private String platform;

    /**
     * 实例id
     */
    private String instanceId;
    private Collection<String> instanceIds;

    /**
     * 月份
     */
    private Integer month;
    private Collection<Integer> months;
    /**
     * 产品
     */
    private String productName;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<ResourceBill> buildQuery() {
        return Wrappers.<ResourceBill>lambdaQuery()
                .eq(StrUtil.isNotBlank(type), ResourceBill::getType, type)
                .eq(StrUtil.isNotBlank(platform), ResourceBill::getPlatform, platform)
                .eq(StrUtil.isNotBlank(instanceId), ResourceBill::getInstanceId, instanceId)
                .in(CollUtil.isNotEmpty(instanceIds), ResourceBill::getInstanceId, instanceIds)
                .eq(Objects.nonNull(month), ResourceBill::getMonth, month)
                .in(CollUtil.isNotEmpty(months), ResourceBill::getMonth, months)
                .like(StrUtil.isNotBlank(productName), ResourceBill::getProductName, productName)
                ;
    }
}
