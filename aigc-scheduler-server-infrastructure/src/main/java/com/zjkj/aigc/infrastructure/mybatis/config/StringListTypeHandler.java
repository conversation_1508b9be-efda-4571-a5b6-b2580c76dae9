package com.zjkj.aigc.infrastructure.mybatis.config;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/22
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class StringListTypeHandler extends GenericListTypeHandler<String> {

    public StringListTypeHandler() {
        super(String.class);
    }
}
