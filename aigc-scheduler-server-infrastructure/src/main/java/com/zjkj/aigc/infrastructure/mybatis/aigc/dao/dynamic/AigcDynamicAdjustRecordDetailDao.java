package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.dynamic.AigcDynamicAdjustRecordDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 动态调整记录明细表(AigcDynamicAdjustRecordDetail)Dao层
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcDynamicAdjustRecordDetailDao extends AbstractBaseDao<AigcDynamicAdjustRecordDetailMapper, AigcDynamicAdjustRecordDetail> {

}
