package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcTaskMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcTaskDao extends AbstractBaseDao<AigcTaskMapper, AigcTask> {

    private final AigcTaskMapper aigcTaskMapper;

    /**
     * 根据条件查询
     *
     * @param condition 条件
     * @return 任务列表
     */
    public List<AigcTask> queryByCondition(AigcTaskCondition condition) {
        LambdaQueryWrapper<AigcTask> queryWrapper = condition.buildQuery();
        return list(queryWrapper);
    }

    /**
     * 获取任务排名
     *
     * @param aigcTaskIds 任务id
     * @param taskType    任务类型
     * @param modelName   模型名称
     * @param taskState   任务状态
     * @return 任务排名
     */
    public Map<String, Integer> getTaskRank(List<String> aigcTaskIds, String taskType, String modelName, Integer taskState) {
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .taskState(taskState)
                .taskType(taskType)
                .modelName(modelName)
                .aigcTaskIds(aigcTaskIds)
                .build();
        List<AigcTask> taskList = aigcTaskMapper.getTaskRank(taskCondition);
        if (CollectionUtils.isEmpty(taskList)) {
            return Collections.emptyMap();
        }

        return taskList.stream()
                .collect(Collectors.toMap(AigcTask::getAigcTaskId, AigcTask::getRank, (v1, v2) -> v1));
    }

    /**
     * 获取任务排名
     *
     * @param taskCondition 任务条件
     * @return 任务排名
     */
    public Map<String, Integer> getTaskRankByMemory(AigcTaskCondition taskCondition) {
        if (StringUtils.isBlank(taskCondition.getModelName()) || StringUtils.isBlank(taskCondition.getTaskType())) {
            return Map.of();
        }

        List<String> aigcTaskIds = taskCondition.getAigcTaskIds();
        taskCondition.setAigcTaskIds(null);

        //todo 限制数量
        taskCondition.setLimit(100000);
        taskCondition.setSelectColumns(List.of(AigcTask::getAigcTaskId));
        List<AigcTask> aigcTaskList = list(taskCondition.buildQuery());
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return Map.of();
        }

        // 所有排名
        Map<String, Integer> rankMap = IntStream.range(0, aigcTaskList.size())
                .boxed()
                .collect(Collectors.toMap(i -> aigcTaskList.get(i).getAigcTaskId(), i -> i + 1, (k1, k2) -> k2));

        if (CollectionUtils.isNotEmpty(aigcTaskIds)) {
            return aigcTaskIds.stream()
                    .filter(rankMap::containsKey)
                    .collect(Collectors.toMap(k -> k, rankMap::get, (k1, k2) -> k2));
        }

        return rankMap;
    }

    /**
     * 查询任务状态分组
     *
     * @param taskCondition 任务条件
     * @return 任务状态分组
     */
    public List<AigcTask> queryTaskStateGroup(AigcTaskCondition taskCondition) {
        return aigcTaskMapper.queryTaskStateGroup(taskCondition);
    }

    /**
     * 更新任务状态
     *
     * @param aigcTask         任务
     * @param newTaskStateEnum 新任务状态
     * @param time             时间
     * @return 是否更新成功
     */
    public boolean updateTaskState(AigcTask aigcTask, TaskStateEnum newTaskStateEnum, LocalDateTime time) {

        // 新任务状态
        Integer newTaskState = Optional.ofNullable(newTaskStateEnum).map(TaskStateEnum::getCode).orElse(null);
        LambdaUpdateChainWrapper<AigcTask> updateChainWrapper = this.lambdaUpdate()
                .set(Objects.nonNull(newTaskState), AigcTask::getTaskState, newTaskState)
                .set(StringUtils.isNotBlank(aigcTask.getContainerId()), AigcTask::getContainerId, aigcTask.getContainerId())
                .set(StringUtils.isNotBlank(aigcTask.getFailMessage()), AigcTask::getFailMessage, aigcTask.getFailMessage())
                .set(StringUtils.isNotBlank(aigcTask.getModelOutput()), AigcTask::getModelOutput, aigcTask.getModelOutput())
                .set(Objects.nonNull(aigcTask.getTaskProgress()), AigcTask::getTaskProgress, aigcTask.getTaskProgress())
                .set(Objects.equals(TaskStateEnum.WAITE.getCode(), aigcTask.getTaskState()) && Objects.equals(TaskStateEnum.RUNNING, newTaskStateEnum), AigcTask::getTaskStartTime, time)
                .set(TaskStateEnum.isComplete(newTaskState), AigcTask::getTaskCompletionTime, time)
                .set(AigcTask::getRevisedTime, LocalDateTime.now())
                .set(Objects.equals(TaskStateEnum.CANCEL, newTaskStateEnum), AigcTask::getTaskCancelTime, time)
                .eq(AigcTask::getId, aigcTask.getId())
                .eq(Objects.nonNull(aigcTask.getTaskState()), AigcTask::getTaskState, aigcTask.getTaskState());

        boolean updated = updateChainWrapper.update();
        log.info("change task state. taskId:{}, sourceTaskState:{}, newTaskState:{}, updated:{}", aigcTask.getTaskId(), aigcTask.getTaskState(), newTaskState, updated);
        if (updated) {
            this.backFillTask(aigcTask, newTaskStateEnum, time);
        }
        return updated;
    }

    /**
     * 回填任务信息
     *
     * @param aigcTask         任务
     * @param newTaskStateEnum 新任务状态
     * @param time             时间
     */
    private void backFillTask(AigcTask aigcTask, TaskStateEnum newTaskStateEnum, LocalDateTime time) {
        if (Objects.nonNull(newTaskStateEnum)) {
            aigcTask.setTaskState(newTaskStateEnum.getCode());
        }

        if (TaskStateEnum.isComplete(newTaskStateEnum)) {
            aigcTask.setTaskCompletionTime(time);
        }

        if (Objects.equals(TaskStateEnum.CANCEL, newTaskStateEnum)) {
            aigcTask.setTaskCancelTime(time);
        }

        if (Objects.equals(TaskStateEnum.RUNNING, newTaskStateEnum)) {
            aigcTask.setTaskStartTime(time);
        }

        aigcTask.setRevisedTime(time);
    }

    /**
     * 通过任务id获取任务
     *
     * @param taskId 任务id
     * @return 任务
     */
    public AigcTask getByTaskId(String taskId, String businessId) {
        return this.lambdaQuery()
                .eq(AigcTask::getTaskId, taskId)
                .eq(AigcTask::getBusinessId, businessId)
                .one();
    }

    /**
     * 通过aigc任务id获取任务
     *
     * @param aigcTaskId aigc任务id
     * @return 任务
     */
    public AigcTask getByAigcTaskId(String aigcTaskId) {
        return this.lambdaQuery()
                .eq(AigcTask::getAigcTaskId, aigcTaskId)
                .one();
    }

    /**
     * 重启任务
     *
     * @param aigcTask      任务信息
     * @param taskStateEnum 任务状态
     * @param time          时间
     * @return 是否重启成功
     */
    public boolean restartTask(AigcTask aigcTask, TaskStateEnum taskStateEnum, LocalDateTime time) {
        return restartTask(aigcTask, taskStateEnum, time, Boolean.FALSE);
    }

    /**
     * 重启任务
     *
     * @param aigcTask      任务信息
     * @param taskStateEnum 任务状态
     * @param time          时间
     * @param isRetry       是否重试
     * @return 是否重启成功
     */
    public boolean restartTask(AigcTask aigcTask, TaskStateEnum taskStateEnum, LocalDateTime time, boolean isRetry) {
        int zero = BigDecimal.ZERO.intValue();
        LambdaUpdateChainWrapper<AigcTask> updateChainWrapper = this.lambdaUpdate()
                .set(AigcTask::getTaskState, taskStateEnum.getCode())
                .set(AigcTask::getTaskProgress, zero)
                .set(AigcTask::getContainerId, null)
                .set(AigcTask::getFailMessage, null)
                .set(AigcTask::getModelOutput, null)
                .set(Objects.nonNull(aigcTask.getTaskStartTime()), AigcTask::getTaskStartTime, null)
                .set(Objects.nonNull(aigcTask.getTaskCompletionTime()), AigcTask::getTaskCompletionTime, null)
                .set(Objects.nonNull(aigcTask.getTaskCancelTime()), AigcTask::getTaskCancelTime, null)
                .set(AigcTask::getRevisedTime, time)
                .eq(AigcTask::getId, aigcTask.getId())
                .eq(Objects.nonNull(aigcTask.getTaskState()), AigcTask::getTaskState, aigcTask.getTaskState());

        if (isRetry) {
            updateChainWrapper.setSql("retry_count = retry_count + 1");
        } else {
            updateChainWrapper.set(AigcTask::getRetryCount, zero);
        }

        boolean updated = updateChainWrapper.update();
        log.info("restart task. taskId:{}, isRetry:{}, updated:{}", aigcTask.getTaskId(), isRetry, updated);
        return updated;
    }

    /**
     * 根据任务状态查询任务
     *
     * @param taskState 任务状态
     * @param minTime   最小时间
     * @param handler   handler
     */
    public void streamByTaskState(Integer taskState, LocalDateTime minTime, ResultHandler<AigcTask> handler) {
        aigcTaskMapper.streamByTaskState(taskState, minTime, handler);
    }

    /**
     * 根据条件查询任务
     *
     * @param condition condition
     * @param handler   handler
     */
    public void streamByCondition(AigcTaskCondition condition, ResultHandler<AigcTask> handler) {
        aigcTaskMapper.streamByCondition(condition, handler);
    }

    /**
     * 查询任务参数
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 任务参数
     */
    public String queryModelParam(String taskType, String modelName) {
        LambdaQueryWrapper<AigcTask> queryWrapper = Wrappers.<AigcTask>lambdaQuery()
                .eq(AigcTask::getTaskType, taskType)
                .eq(AigcTask::getModelName, modelName)
                .eq(AigcTask::getTaskState, TaskStateEnum.SUCC.getCode())
                .orderByDesc(AigcTask::getId)
                .last("LIMIT 1");

        AigcTask aigcTask = aigcTaskMapper.selectOne(queryWrapper);
        if (Objects.nonNull(aigcTask)) {
            return aigcTask.getModelParams();
        }

        return null;
    }

    /**
     * 根据业务id删除任务
     * 物理删除
     *
     * @param businessId 业务id
     */
    public void actualDelByBusinessId(String businessId) {
        aigcTaskMapper.actualDelByBusinessId(businessId);
    }

    /**
     * 根据任务id删除任务
     * 物理删除
     *
     * @param ids 任务id
     */
    public void actualDelById(List<Long> ids) {
        aigcTaskMapper.actualDelById(ids);
    }

    /**
     * 查询任务
     *
     * @param condition 查询条件
     * @return 任务分页信息
     */
    public Page<AigcTask> queryPage(AigcTaskCondition condition) {
        LambdaQueryWrapper<AigcTask> queryWrapper = condition.buildQuery();
        return page(condition.getPage(), queryWrapper);
    }

    /**
     * 更新任务
     *
     * @param aigcTask 任务信息
     * @return 是否更新成功
     */
    public boolean updateAigcTask(AigcTask aigcTask) {
        LambdaUpdateWrapper<AigcTask> updateWrapper = Wrappers.<AigcTask>lambdaUpdate()
                .eq(AigcTask::getId, aigcTask.getId())
                .eq(AigcTask::getTaskState, aigcTask.getTaskState());

        return update(aigcTask, updateWrapper);
    }

    /**
     * 更新任务优先级
     *
     * @param ids            任务id集合
     * @param targetPriority 目标优先级
     * @return 是否更新成功
     */
    public boolean updateTaskPriority(Collection<Long> ids, int targetPriority) {
        return this.lambdaUpdate()
                .set(AigcTask::getTaskPriority, targetPriority)
                .in(AigcTask::getId, ids)
                .update();
    }
}
