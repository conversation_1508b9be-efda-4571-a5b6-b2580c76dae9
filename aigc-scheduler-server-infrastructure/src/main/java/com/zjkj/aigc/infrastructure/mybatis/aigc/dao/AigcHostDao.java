package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcHostCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcHostMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 主机表(AigcHost)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcHostDao extends AbstractBaseDao<AigcHostMapper, AigcHost> {

    /**
     * 根据节点IP获取主机信息
     *
     * @param nodeIp 节点IP
     * @return 主机信息
     */
    public AigcHost getByNodeIp(String nodeIp) {
        return this.lambdaQuery()
                .eq(AigcHost::getHostIp, nodeIp)
                .one();
    }

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcHost> queryPage(AigcHostCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcHost> queryList(AigcHostCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 根据主机ip查询
     *
     * @param hostIp 主机ip
     * @return 主机信息
     */
    public AigcHost queryByHostIp(String hostIp) {
        return this.lambdaQuery()
                .eq(AigcHost::getHostIp, hostIp)
                .one();
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void delById(Long id) {
        LambdaUpdateWrapper<AigcHost> updateWrapper = Wrappers.<AigcHost>lambdaUpdate()
                .set(AigcHost::getIsDeleted, 1)
                .set(AigcHost::getDelVersion, System.currentTimeMillis())
                .eq(AigcHost::getId, id);
        update(updateWrapper);
    }
}
