package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@Data
@TableName("heartbeat_check")
public class HeartbeatCheck implements Serializable {
    private static final long serialVersionUID = 767914457593912187L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * seq，4K，aigc
     */
    private String taskType;
    /**
     * 容器id
     */
    @TableField("containe_Id")
    private String containerId;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态 worker状态,running运行中，idle闲置中
     */
    private String status;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
}
