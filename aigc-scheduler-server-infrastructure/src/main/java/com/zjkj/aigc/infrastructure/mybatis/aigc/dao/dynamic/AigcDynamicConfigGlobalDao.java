package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.dynamic.AigcDynamicConfigGlobalMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 动态配置全局表(AigcDynamicConfigGlobal)Dao层
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcDynamicConfigGlobalDao extends AbstractBaseDao<AigcDynamicConfigGlobalMapper, AigcDynamicConfigGlobal> {

    public AigcDynamicConfigGlobal getGlobalConfig() {
        return this.lambdaQuery().one();
    }
}
