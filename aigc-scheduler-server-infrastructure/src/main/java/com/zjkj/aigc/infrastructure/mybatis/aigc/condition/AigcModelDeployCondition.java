package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AigcModelDeployCondition extends BaseCondition<AigcModelDeploy> implements Serializable {
    private static final long serialVersionUID = 8582693462573234775L;

    private String title;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型id集合
     */
    private List<Long> modelIdList;
    /**
     * 加密状态
     */
    private List<Integer> encryptStatusList;
    /**
     * 状态
     */
    private List<Integer> statusList;
    /**
     * 是否加密
     */
    private Boolean isEncrypt;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcModelDeploy> buildQuery() {
        LambdaQueryWrapper<AigcModelDeploy> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(title), AigcModelDeploy::getTitle, title)
                .eq(Objects.nonNull(modelId), AigcModelDeploy::getModelId, modelId)
                .eq(Objects.nonNull(isEncrypt), AigcModelDeploy::getIsEncrypt, isEncrypt)
                .in(CollectionUtils.isNotEmpty(statusList), AigcModelDeploy::getStatus, statusList)
                .in(CollectionUtils.isNotEmpty(encryptStatusList), AigcModelDeploy::getEncryptStatus, encryptStatusList)
                .in(CollectionUtils.isNotEmpty(modelIdList), AigcModelDeploy::getModelId, modelIdList)
        ;
        return queryWrapper;
    }
}
