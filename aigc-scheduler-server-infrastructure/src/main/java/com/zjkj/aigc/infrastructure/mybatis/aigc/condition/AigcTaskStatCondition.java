package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import lombok.Builder;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Data
@Builder
public class AigcTaskStatCondition {
    /**
     * 模型名称
     */
    private String modelName;
    private Collection<String> modelNames;
    /**
     * 任务类型
     */
    private String taskType;
    private Collection<String> taskTypes;
    /**
     * 模型类型
     */
    private String modelType;
    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * 模型分组
     */
    private boolean noGroupByModel;
}
