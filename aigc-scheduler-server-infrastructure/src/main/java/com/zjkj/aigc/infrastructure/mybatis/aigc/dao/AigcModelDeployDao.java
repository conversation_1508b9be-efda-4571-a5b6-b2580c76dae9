package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelDeployMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 模型发布记录表(AigcModelDeploy)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelDeployDao extends AbstractBaseDao<AigcModelDeployMapper, AigcModelDeploy> {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcModelDeploy> queryPage(AigcModelDeployCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcModelDeploy> queryList(AigcModelDeployCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 根据模型发布
     * @param updateModelDeploy 更新的模型发布
     * @return 是否成功
     */
    public boolean updateModelDeploy(AigcModelDeploy updateModelDeploy) {
        LambdaUpdateWrapper<AigcModelDeploy> updateWrapper = Wrappers.<AigcModelDeploy>lambdaUpdate()
                .eq(AigcModelDeploy::getId, updateModelDeploy.getId())
                .eq(Objects.nonNull(updateModelDeploy.getStatus()), AigcModelDeploy::getStatus, updateModelDeploy.getStatus());
        updateModelDeploy.setRevisedTime(LocalDateTime.now());
        return update(updateModelDeploy, updateWrapper);
    }
}

