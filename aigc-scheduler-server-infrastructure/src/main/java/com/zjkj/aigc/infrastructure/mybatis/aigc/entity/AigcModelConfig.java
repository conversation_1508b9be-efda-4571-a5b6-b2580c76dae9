package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler;
import com.zjkj.aigc.common.dto.model.GpuInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 模型配置表(AigcModelConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName(autoResultMap = true)
public class AigcModelConfig extends BaseEntityNoCreator implements Serializable {
    private static final long serialVersionUID = 546153696488004507L;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 项目工程名称
     */
    private String projectName;
    /**
     * 项目git地址
     */
    private String projectGitUrl;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    private String modelFileOssUrl;
    /**
     * 是否加密: 0-不加密,1-加密
     */
    private Boolean isEncrypt;
    /**
     * 基础镜像地址
     */
    private String mirrorImageUrl;
    /**
     * cpu核数
     */
    private Integer cpuCore;
    /**
     * 内存大小GB
     */
    private Integer memorySize;
    /**
     * gpu信息
     */
    @TableField(typeHandler = GsonTypeHandler.class)
    private GpuInfo gpuInfo;
}

