package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.alarm.AigcAlarmChannelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 告警渠道配置表(AigcAlarmChannel)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcAlarmChannelDao extends AbstractBaseDao<AigcAlarmChannelMapper, AigcAlarmChannel> {
}
