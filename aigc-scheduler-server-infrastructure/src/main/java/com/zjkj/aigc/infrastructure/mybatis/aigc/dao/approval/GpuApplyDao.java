package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.approval;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcAppCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval.GpuApplyCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval.GpuApply;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcAppMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.approval.GpuApplyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * GPU资源申请表(GpuApply)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class GpuApplyDao extends AbstractBaseDao<GpuApplyMapper, GpuApply> {
    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<GpuApply> queryPage(GpuApplyCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }
}
