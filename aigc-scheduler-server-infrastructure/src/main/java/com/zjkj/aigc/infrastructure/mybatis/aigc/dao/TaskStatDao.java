package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zjkj.aigc.common.dto.TaskSummaryStatDTO;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.TaskStatMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@DS("slave_0")
@Repository
@RequiredArgsConstructor
public class TaskStatDao extends AbstractBaseDao<TaskStatMapper, TaskSummaryStatDTO> {

    private final TaskStatMapper taskStatMapper;

    public List<TaskSummaryStatDTO> summaryStat(TaskSummaryStatCondition condition) {
        return taskStatMapper.statTaskSummary(condition);
    }

    /**
     * 活跃任务统计
     * @param condition 查询条件
     * @return 活跃任务统计
     */
    public List<TaskSummaryStatDTO> activeTaskStat(TaskSummaryStatCondition condition) {
        return taskStatMapper.statActiveTask(condition);
    }

    /**
     * 任务统计-按模型小时统计
     * @param condition 查询条件
     * @return 任务统计-按模型统计
     */
    public List<TaskSummaryStatDTO> groupModelHourStat(TaskSummaryStatCondition condition) {
        return taskStatMapper.groupModelHourStat(condition);
    }

    /**
     * 统计模型任务数
     * @param statCondition 查询条件
     * @return 模型任务数
     */
    public long statRangeModelTasks(TaskSummaryStatCondition statCondition) {
        return taskStatMapper.statRangeModelTasks(statCondition);
    }
}
