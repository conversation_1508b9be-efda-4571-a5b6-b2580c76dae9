package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.dto.alarm.AlarmConfigThresholdDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmReceiverDTO;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import com.zjkj.aigc.infrastructure.mybatis.config.FastjsonTypeHandler;
import com.zjkj.aigc.infrastructure.mybatis.config.GenericListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 模型告警配置(AigcAlarmConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class AigcAlarmConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 5947477419416497366L;
    /**
     * 标签
     */
    private String tag;
    /**
     * 关联模型ID集合
     */
    @TableField(value = "model_id", typeHandler = FastjsonTypeHandler.class)
    private List<String> modelId;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 告警阈值配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AlarmConfigThresholdDTO alarmConfigThreshold;
    /**
     * 告警时间间隔
     */
    private Integer alarmTimeInterval;
    /**
     * 告警时间间隔单位
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTimeIntervalUnitEnum}
     */
    private Integer alarmTimeIntervalUnit;
    /**
     * 接收人配置
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AlarmReceiverDTO alarmReceiver;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum}
     */
    private Integer status;
}
