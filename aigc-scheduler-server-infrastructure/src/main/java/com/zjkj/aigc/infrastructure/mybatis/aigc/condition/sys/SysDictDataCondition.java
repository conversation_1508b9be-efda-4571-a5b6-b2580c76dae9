package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysDictDataCondition extends BaseCondition<SysDictData> {

    /**
     * 字典类型id
     */
    private Long dictId;

    /**
     * 字典类型id
     */
    private List<Long> dictIds;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;

    public LambdaQueryWrapper<SysDictData> buildQuery() {
        return Wrappers.<SysDictData>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), SysDictData::getId, this.getIds())
                .eq(Objects.nonNull(dictId), SysDictData::getDictId, dictId)
                .in(CollectionUtils.isNotEmpty(dictIds), SysDictData::getDictId, dictIds)
                .eq(Objects.nonNull(status), SysDictData::getStatus, status)
                ;
    }
}
