package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.dto.alarm.AlarmSnapshotDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模型告警(AigcAlarm)实体类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@TableName(autoResultMap = true)
public class AigcAlarm implements Serializable {
    private static final long serialVersionUID = -7338456339058961108L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 告警编号
     */
    private String alarmCode;
    /**
     * 告警配置ID
     */
    private Long alarmConfigId;
    /**
     * 报警快照
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AlarmSnapshotDTO alarmSnapshot;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型类型
     * {@link com.zjkj.aigc.common.enums.model.AigcModelTypeEnum}
     */
    private String modelType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 处理备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 处理时间
     */
    private LocalDateTime handleTime;
}
