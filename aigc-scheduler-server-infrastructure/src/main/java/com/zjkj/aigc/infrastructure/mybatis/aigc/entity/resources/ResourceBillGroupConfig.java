package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 费用分摊信息配置表(ResourceBillGroupConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResourceBillGroupConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 5672067540768028066L;
    /**
     * 月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 中间件费用冗余比例
     */
    private BigDecimal midPriceRedundancy;
    /**
     * 中间件调整费用
     */
    private BigDecimal midAdjPrice;
    /**
     * GPU费用冗余比例
     */
    private BigDecimal gpuPriceRedundancy;
    /**
     * GPU调整费用
     */
    private BigDecimal gpuAdjPrice;

    /**
     * 备注
     */
    private String marks;
}

