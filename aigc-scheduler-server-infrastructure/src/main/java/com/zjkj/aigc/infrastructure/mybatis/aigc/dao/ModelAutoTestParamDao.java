package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestParam;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestParamMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 模型测试批次表(ModelAutoTestRecord)Dao层
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ModelAutoTestParamDao extends AbstractBaseDao<ModelAutoTestParamMapper, ModelAutoTestParam> {
    public List<ModelAutoTestParam> queryByTestNo(String testNo) {
        LambdaQueryWrapper<ModelAutoTestParam> queryWrapper = Wrappers.<ModelAutoTestParam>lambdaQuery()
                .eq(ModelAutoTestParam::getTestNo, testNo);
        return list(queryWrapper);
    }

    public void removeByTestNo(String testNo) {
        LambdaQueryWrapper<ModelAutoTestParam> queryWrapper = Wrappers.<ModelAutoTestParam>lambdaQuery()
                .eq(ModelAutoTestParam::getTestNo, testNo);
        getBaseMapper().delete(queryWrapper);
    }
}
