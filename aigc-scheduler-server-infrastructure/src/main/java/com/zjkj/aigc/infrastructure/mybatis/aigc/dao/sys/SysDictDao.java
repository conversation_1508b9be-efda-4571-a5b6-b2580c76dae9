package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.sys;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.sys.SysDictMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 字典Dao
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Repository
@RequiredArgsConstructor
public class SysDictDao extends AbstractBaseDao<SysDictMapper, SysDict> {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<SysDict> queryPage(SysDictCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<SysDict> queryList(SysDictCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 根据type查询
     *
     * @param type 类型
     * @return 字典信息
     */
    public SysDict queryByType(String type) {
        return this.lambdaQuery()
                .eq(SysDict::getType, type)
                .one();
    }

    /**
     * 根据id删除
     * @param id id
     */
    public void delById(Long id) {
        LambdaUpdateWrapper<SysDict> updateWrapper = Wrappers.<SysDict>lambdaUpdate()
                .set(SysDict::getIsDeleted, 1)
                .set(SysDict::getDelVersion, System.currentTimeMillis())
                .set(SysDict::getRevisedTime, LocalDateTime.now())
                .eq(SysDict::getId, id);
        update(updateWrapper);
    }
}
