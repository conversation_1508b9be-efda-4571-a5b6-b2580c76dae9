package com.zjkj.aigc.infrastructure.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.elasticsearch.RestClientBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.time.Duration;

/**
 * Elasticsearch配置类
 * 解决百度ES证书问题
 */
@Configuration
public class ElasticsearchConfig extends AbstractElasticsearchConfiguration  implements RestClientBuilderCustomizer {

    @Value("${spring.elasticsearch.uris}")
    private String uris;

    @Value("${spring.elasticsearch.username}")
    private String username;

    @Value("${spring.elasticsearch.password}")
    private String password;

    // 空闲存活时间配置
    private final long KeepAliveMillis = Duration.ofMinutes(5).toMillis();

    @Override
    @Bean
    public RestHighLevelClient elasticsearchClient() {
        try {
            // 创建信任所有证书的SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {}

                    @Override
                    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {}

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }
            }, null);

            // 设置认证信息
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password));

            // 解析URI
            String uri = uris.replace("https://", "").replace("http://", "");
            String[] hostAndPort = uri.split(":");
            String host = hostAndPort[0];
            int port = hostAndPort.length > 1 ? Integer.parseInt(hostAndPort[1]) : 443;
            String scheme = uris.startsWith("https") ? "https" : "http";

            // 创建RestClientBuilder
            RestClientBuilder builder = RestClient.builder(
                    new HttpHost(host, port, scheme))
                    .setRequestConfigCallback(requestConfigBuilder ->
                            requestConfigBuilder.setSocketTimeout(30000))
                    .setHttpClientConfigCallback(httpClientBuilder ->
                            httpClientBuilder.setSSLContext(sslContext)
                                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                                    .setDefaultCredentialsProvider(credentialsProvider));

            return new RestHighLevelClient(builder);
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            throw new RuntimeException("Failed to create Elasticsearch client", e);
        }
    }

    /**
     * 不推荐使用这个配置, 其可能会替换原有的RestClientBuilder对象, 但必须实现此方法
     */
    @Override
    public void customize(RestClientBuilder restClientBuilder) {
    }

    /**
     * 增强RestClient构建配置
     */
    @Override
    public void customize(HttpAsyncClientBuilder httpAsyncClientBuilder) {
        RestClientBuilderCustomizer.super.customize(httpAsyncClientBuilder);
        // 配置客户端空闲连接存活时间, 避免因默认的长连接超过服务端超过空闲时间, 导致请求失败
        httpAsyncClientBuilder.setKeepAliveStrategy((response, context) -> KeepAliveMillis);
    }
}