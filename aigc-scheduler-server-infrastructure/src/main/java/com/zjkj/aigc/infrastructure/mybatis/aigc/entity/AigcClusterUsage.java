package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjkj.aigc.infrastructure.mybatis.config.StringListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 集群资源利用表(AigcClusterUsage)实体类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@Accessors(chain = true)
@TableName(value = "aigc_cluster_usage", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class AigcClusterUsage extends BaseEntity {

    private static final long serialVersionUID = 9084950679976927499L;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 平台
     */
    private String platform;
    /**
     * 服务类型
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> serviceType;
    /**
     * 数据日期
     */
    private LocalDate dataDate;
    /**
     * 节点数量
     */
    private Long nodeCount;
    /**
     * gpu型号
     */
    private String gpuModel;
    /**
     * gpu卡数
     */
    private Long gpuCount;
    /**
     * 总gpu显存大小
     */
    private Long totalGpuMemorySize;
    /**
     * 已使用gpu显存大小
     */
    private Long usedGpuMemorySize;
    /**
     * gpu使用率
     */
    private BigDecimal gpuUsageRate;
    /**
     * 总cpu核数
     */
    private Long totalCpuCore;
    /**
     * 已使用cpu核数
     */
    private Long usedCpuCore;
    /**
     * cpu使用率
     */
    private BigDecimal cpuUsageRate;
    /**
     * 总内存大小
     */
    private Long totalMemorySize;
    /**
     * 已使用内存大小
     */
    private Long usedMemorySize;
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsageRate;
    /**
     * 删除版本号 uk
     */
    private String delVersion;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
}
