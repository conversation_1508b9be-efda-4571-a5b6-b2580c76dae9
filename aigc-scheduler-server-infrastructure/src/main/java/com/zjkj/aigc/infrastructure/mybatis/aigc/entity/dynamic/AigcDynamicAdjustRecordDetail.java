package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 动态调整记录明细表(AigcDynamicAdjustRecordDetail)实体类
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicAdjustRecordDetail extends BaseEntity {
    private static final long serialVersionUID = 791583203767423996L;

    /**
     * 调整记录id
     */
    private Long adjustRecordId;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */

    private String scaleType;
    /**
     * 原副本数
     */
    private Integer sourceReplica;
    /**
     * 目标副本数
     */
    private Integer targetReplica;
    /**
     * 差异副本数
     */
    private Integer diffReplica;
    /**
     * gpu内存大小GB
     */
    private Integer gpuMemorySize;
    /**
     * 环境名称
     */
    private String envName;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 伸缩状态
     * @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum
     */
    private String scaleStatus;
    /**
     * 状态：1-生效中 2-结束
     */
    private Integer status;
    /**
     * 伸缩时间
     */
    private LocalDateTime scaleTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 重试次数
     */
    private Integer retryCount;
}

