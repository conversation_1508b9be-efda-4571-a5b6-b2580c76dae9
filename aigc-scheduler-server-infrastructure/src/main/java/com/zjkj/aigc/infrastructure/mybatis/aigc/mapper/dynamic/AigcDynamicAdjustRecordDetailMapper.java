package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.dynamic;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;

/**
 * 动态调整记录明细表(AigcDynamicAdjustRecordDetail)数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface AigcDynamicAdjustRecordDetailMapper extends BaseMapper<AigcDynamicAdjustRecordDetail> {

}

