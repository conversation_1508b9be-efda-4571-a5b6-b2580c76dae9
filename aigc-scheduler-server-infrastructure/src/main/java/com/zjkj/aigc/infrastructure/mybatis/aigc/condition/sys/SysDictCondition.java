package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * 字典查询条件
 *
 * <AUTHOR>
 * @since 2024/12/13
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SysDictCondition extends BaseCondition<SysDict> {

    /**
     * 字典名称
     */
    private String name;

    /**
     * 字典类型
     */
    private String type;

    /**
     * 字典类型
     */
    private List<String> types;

    /**
     * 状态
     */
    private Integer status;

    public LambdaQueryWrapper<SysDict> buildQuery() {
        return Wrappers.<SysDict>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), SysDict::getId, this.getIds())
                .like(StringUtils.isNotBlank(name), SysDict::getName, name)
                .eq(StringUtils.isNotBlank(type), SysDict::getType, type)
                .in(CollectionUtils.isNotEmpty(types), SysDict::getType, types)
                .eq(Objects.nonNull(status), SysDict::getStatus, status)
                ;
    }
}
