package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Builder
public class AigcAlarmConfigCondition implements Serializable {
    private static final long serialVersionUID = 7813237153691696944L;
    /**
     * 标签
     */
    private String tag;
    /**
     * 关联模型ID集合
     */
    private List<String> modelId;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum}
     */
    private Integer status;

    /**
     * 分页信息
     */
    private Page<AigcAlarmConfig> page;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcAlarmConfig> buildQuery() {
        LambdaQueryWrapper<AigcAlarmConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(tag), AigcAlarmConfig::getTag, tag)
                .in(CollUtil.isNotEmpty(modelId), AigcAlarmConfig::getModelId, modelId)
                .eq(Objects.nonNull(alarmType), AigcAlarmConfig::getAlarmType, alarmType)
                .eq(Objects.nonNull(status), AigcAlarmConfig::getStatus, status);
        return queryWrapper;
    }
}
