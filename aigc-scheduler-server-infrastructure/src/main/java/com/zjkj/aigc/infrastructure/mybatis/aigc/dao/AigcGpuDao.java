package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcGpuCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcGpuMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * GPU表(AigcGpu)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcGpuDao extends AbstractBaseDao<AigcGpuMapper, AigcGpu> {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcGpu> queryPage(AigcGpuCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcGpu> queryList(AigcGpuCondition condition) {
        return list(condition.buildQuery());
    }

}
