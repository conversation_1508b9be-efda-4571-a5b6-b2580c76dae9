package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcTaskStatDayMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskStatDayDao extends AbstractBaseDao<AigcTaskStatDayMapper, AigcTaskStatDay> {

    private final AigcTaskStatDayMapper aigcTaskStatDayMapper;

    /**
     * 按模型分组统计
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcTaskStatDay> pageByGroupModel(Page<AigcTaskStatDay> page, AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.pageByGroupModel(page, condition);
    }

    /**
     * 根据日期查询
     *
     * @param statDate 日期
     * @param appId    应用id
     * @return 列表数据
     */
    public List<AigcTaskStatDay> listByStateDate(LocalDate statDate, String appId) {
        return this.lambdaQuery()
                .eq(AigcTaskStatDay::getStatDate, statDate)
                .eq(AigcTaskStatDay::getAppId, appId)
                .list();
    }

    /**
     * 按天合计
     *
     * @param condition 查询条件
     * @return 合计数据
     */
    public List<AigcTaskStatDay> sumByGroupStateDate(AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.sumByGroupStateDate(condition);
    }

    /**
     * 按模型分组合计
     *
     * @param condition 查询条件
     * @return 合计数据
     */
    public List<AigcTaskStatDay> sumByGroupModel(AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.sumByGroupModel(condition);
    }

    /**
     * 查询未完成的任务统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 未完成的任务统计
     */
    public List<AigcTaskStatDay> queryIncompleteStat(String startDate, String endDate) {
        List<AigcTaskStatDay> waitList = this.lambdaQuery()
                .ge(AigcTaskStatDay::getStatDate, startDate)
                .le(AigcTaskStatDay::getStatDate, endDate)
                .gt(AigcTaskStatDay::getWaitingAmount, 0)
                .list();

        List<AigcTaskStatDay> runList = this.lambdaQuery()
                .ge(AigcTaskStatDay::getStatDate, startDate)
                .le(AigcTaskStatDay::getStatDate, endDate)
                .gt(AigcTaskStatDay::getRunningAmount, 0)
                .list();

        if (CollectionUtils.isEmpty(waitList) && CollectionUtils.isEmpty(runList)) {
            return List.of();
        }

        List<Long> collected = waitList.stream()
                .map(AigcTaskStatDay::getId)
                .collect(Collectors.toList());

        runList.stream()
                .filter(stat -> !collected.contains(stat.getId()))
                .forEach(waitList::add);
        return waitList;
    }

    /**
     * 按模型类型合计
     *
     * @param condition 查询条件
     * @return 合计数据
     */
    public List<AigcTaskStatDay> sumByModelType(AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.sumByModelType(condition);
    }

    /**
     * 合计
     *
     * @param condition 查询条件
     * @return 合计数据
     */
    public AigcTaskStatDay sumTotal(AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.sumTotal(condition);
    }

    /**
     * 根据及模型日期查询
     *
     * @param condition   条件
     * @return 列表数据
     */
    public List<AigcTaskStatDay> listByModelStateDate(AigcTaskSummaryCondition condition) {
        return aigcTaskStatDayMapper.listByModelStateDate(condition);
    }

    /**
     * 按模型、数据日期分组合计
     * @param condition 查询条件
     * @return 合计数据
     */
    public List<AigcTaskStatDay> sumByGroupModelStatDate(AigcTaskStatCondition condition) {
        return aigcTaskStatDayMapper.sumByGroupModelStatDate(condition);
    }
}