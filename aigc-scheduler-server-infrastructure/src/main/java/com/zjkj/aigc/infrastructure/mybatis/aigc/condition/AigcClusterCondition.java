package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * 集群查询条件
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcClusterCondition extends BaseCondition<AigcCluster> {

    /**
     * 平台
     */
    private String platform;

    /**
     * 名称
     */
    private String name;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;

    /**
     * 是否不填充节点信息
     */
    private boolean noFillNodes;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 环境类型
     */
    private String envType;

    public LambdaQueryWrapper<AigcCluster> buildQuery() {
        LambdaQueryWrapper<AigcCluster> queryWrapper = Wrappers.<AigcCluster>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcCluster::getId, this.getIds())
                .eq(StringUtils.isNotBlank(platform), AigcCluster::getPlatform, platform)
                .like(StringUtils.isNotBlank(name), AigcCluster::getName, name)
                .eq(Objects.nonNull(status), AigcCluster::getStatus, status)
                .eq(StringUtils.isNotBlank(creatorName), AigcCluster::getCreatorName, creatorName)
                ;
        if (StringUtils.isNotBlank(serviceType)) {
            queryWrapper.apply("FIND_IN_SET({0}, service_type)", serviceType);
        }

        if (StringUtils.isNotBlank(envType)) {
            queryWrapper.apply("FIND_IN_SET({0}, env_type)", envType);
        }
        return queryWrapper;
    }
}
