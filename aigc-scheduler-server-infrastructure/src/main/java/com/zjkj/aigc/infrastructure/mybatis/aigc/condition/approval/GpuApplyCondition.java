package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval.GpuApply;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
@Builder
public class GpuApplyCondition implements Serializable {
    private static final long serialVersionUID = -1905296191428415954L;

    /**
     * 审批编号
     */
    private String code;
    /**
     * 审批类型
     */
    private String type;
    /**
     * 审批状态
     */
    private Integer status;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 发起人
     */
    private String creatorName;
    /**
     * 发起人ID
     */
    private Long creatorId;

    /**
     * 分页信息
     */
    private Page<GpuApply> page;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<GpuApply> buildQuery() {
        LambdaQueryWrapper<GpuApply> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(code), GpuApply::getCode, code)
                .eq(StringUtils.isNotBlank(type), GpuApply::getType, type)
                .eq(Objects.nonNull(status), GpuApply::getStatus, status)
                .eq(StringUtils.isNotBlank(platform), GpuApply::getPlatform, platform)
                .eq(Objects.nonNull(creatorId), GpuApply::getCreatorId, creatorId)
                .like(StringUtils.isNotBlank(creatorName), GpuApply::getCreatorName, creatorName)
        ;
        return queryWrapper;
    }
}
