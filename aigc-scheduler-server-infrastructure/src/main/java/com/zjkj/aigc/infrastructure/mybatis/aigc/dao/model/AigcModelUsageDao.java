package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.model;

import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelUsageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型资源占用表(AigcModelUsage)Dao层
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelUsageDao extends AbstractBaseDao<AigcModelUsageMapper, AigcModelUsage> {

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcModelUsage> queryList(AigcModelUsageCondition condition) {
        return list(condition.buildQuery());
    }
}
