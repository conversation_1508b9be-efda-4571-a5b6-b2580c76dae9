package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AigcAppCondition extends BaseCondition<AigcApp> {

    private Collection<String> appIds;

    private Collection<Integer> statusList;

    private String name;

    public LambdaQueryWrapper<AigcApp> buildQuery() {
        return Wrappers.<AigcApp>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcApp::getId, this.getIds())
                .in(CollectionUtils.isNotEmpty(appIds), AigcApp::getAppId, appIds)
                .in(CollectionUtils.isNotEmpty(statusList), AigcApp::getStatus, statusList)
                .like(StringUtils.isNotBlank(name), AigcApp::getName, name)
                ;
    }
}
