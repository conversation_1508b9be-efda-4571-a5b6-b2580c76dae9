package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * GPU资源规划表(ResourceGpuPlan)实体类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
public class ResourceGpuPlan extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -3996575142857012024L;
    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 型号
     * {@link com.zjkj.aigc.common.enums.GpuModelEnum}
     */
    private String model;
    /**
     * 业务卡需求
     */
    private Integer businessDemand;
    /**
     * 打标卡需求
     */
    private Integer markDemand;
    /**
     * 训练卡需求
     */
    private Integer trainDemand;
    /**
     * 测试卡需求
     */
    private Integer testDemand;
    /**
     * 运维冗余卡需求
     */
    private Integer devopsDemand;
    /**
     * GPU规格/卡
     */
    private Integer norms;
    /**
     * 临时/卡
     */
    private Integer tmp;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 删除版本号
     */
    private String delVersion;
}
