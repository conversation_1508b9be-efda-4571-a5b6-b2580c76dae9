package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 资源账单表(ResourceBill)数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ResourceBillMapper extends BaseMapper<ResourceBill> {

    /**
     * 账单总额
     *
     * @param condition 查询条件
     * @return 总额
     */
    BigDecimal billTotal(@Param("condition") ResourceBillCondition condition);
}

