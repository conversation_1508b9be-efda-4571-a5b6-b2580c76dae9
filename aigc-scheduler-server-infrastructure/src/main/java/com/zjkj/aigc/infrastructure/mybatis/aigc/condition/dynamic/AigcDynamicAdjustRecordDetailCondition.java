package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicAdjustRecordDetailCondition extends BaseCondition<AigcDynamicAdjustRecordDetail> {
    /**
     * 调整记录id
     */
    private Collection<Long> adjustRecordIds;
    /**
     * 模型id
     */
    private Collection<Long> modelIds;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String scaleType;
    /**
     * 伸缩状态
     * @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum
     */
    private String scaleStatus;
    private Collection<String> scaleStatusList;
    /**
     * 状态
     * @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum
     */
    private Integer status;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcDynamicAdjustRecordDetail> buildQuery() {
        return Wrappers.<AigcDynamicAdjustRecordDetail>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcDynamicAdjustRecordDetail::getId, this.getIds())
                .in(CollectionUtils.isNotEmpty(adjustRecordIds), AigcDynamicAdjustRecordDetail::getAdjustRecordId, adjustRecordIds)
                .in(CollectionUtils.isNotEmpty(modelIds), AigcDynamicAdjustRecordDetail::getModelId, modelIds)
                .eq(StringUtils.isNotBlank(scaleType), AigcDynamicAdjustRecordDetail::getScaleType, scaleType)
                .eq(StringUtils.isNotBlank(scaleStatus), AigcDynamicAdjustRecordDetail::getScaleStatus, scaleStatus)
                .in(CollectionUtils.isNotEmpty(scaleStatusList), AigcDynamicAdjustRecordDetail::getScaleStatus, scaleStatusList)
                .eq(Objects.nonNull(status), AigcDynamicAdjustRecordDetail::getStatus, status)
                ;
    }
}
