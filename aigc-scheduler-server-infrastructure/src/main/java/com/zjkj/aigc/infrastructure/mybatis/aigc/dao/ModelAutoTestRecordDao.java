package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 模型测试批次表(ModelAutoTestRecord)Dao层
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ModelAutoTestRecordDao extends AbstractBaseDao<ModelAutoTestRecordMapper, ModelAutoTestRecord> {

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<ModelAutoTestRecord> queryRecordByPage(Page<ModelAutoTestRecord> page, ModelAutoTestRecordCondition condition) {
        LambdaQueryWrapper<ModelAutoTestRecord> queryWrapper = condition.buildQuery();
        return page(page, queryWrapper);
    }

    /**
     * 根据条件查询
     *
     * @param condition 条件
     * @return 结果
     */
    public List<ModelAutoTestRecord> queryByCondition(ModelAutoTestRecordCondition condition) {
        LambdaQueryWrapper<ModelAutoTestRecord> queryWrapper = condition.buildQuery();
        return list(queryWrapper);
    }

    /**
     * 根据编号查询
     * @param testNo 编号
     * @return 结果
     */
    public ModelAutoTestRecord queryByTestNo(String testNo) {
        LambdaQueryWrapper<ModelAutoTestRecord> queryWrapper = Wrappers.<ModelAutoTestRecord>lambdaQuery()
                .eq(ModelAutoTestRecord::getTestNo, testNo);
        return getOne(queryWrapper);
    }

    /**
     * 更新记录
     *
     * @param updateRecord 记录信息
     * @param sourceStatus 原状态
     * @return 是否成功
     */
    public boolean updateRecord(ModelAutoTestRecord updateRecord, Integer sourceStatus) {
        LambdaUpdateWrapper<ModelAutoTestRecord> updateWrapper = Wrappers.<ModelAutoTestRecord>lambdaUpdate()
                .eq(ModelAutoTestRecord::getId, updateRecord.getId())
                .eq(Objects.nonNull(sourceStatus), ModelAutoTestRecord::getStatus, sourceStatus);
        updateRecord.setRevisedTime(LocalDateTime.now());
        return update(updateRecord, updateWrapper);
    }

}
