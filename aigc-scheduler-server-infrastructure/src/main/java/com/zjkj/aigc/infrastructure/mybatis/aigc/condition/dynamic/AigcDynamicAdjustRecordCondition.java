package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicAdjustRecordCondition extends BaseCondition<AigcDynamicAdjustRecord> {
    /**
     * 动态配置id
     */
    private Long dynamicConfigId;
    private Collection<Long> dynamicConfigIds;
    /**
     * 模型id
     */
    private Long modelId;
    private Collection<Long> modelIds;
    /**
     * 伸缩类型: 扩容-SCALE_OUT, 缩容-SCALE_IN
     */
    private String scaleType;
    /**
     * 伸缩状态
     */
    private String scaleStatus;
    private Collection<String> scaleStatusList;
    /**
     * 状态
     * @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum
     */
    private Integer status;
    private List<Integer> statusList;
    /**
     * 是否包含明细
     */
    private boolean includeDetail;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcDynamicAdjustRecord> buildQuery() {
        return Wrappers.<AigcDynamicAdjustRecord>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcDynamicAdjustRecord::getId, this.getIds())
                .eq(Objects.nonNull(dynamicConfigId), AigcDynamicAdjustRecord::getDynamicConfigId, dynamicConfigId)
                .in(CollectionUtils.isNotEmpty(dynamicConfigIds), AigcDynamicAdjustRecord::getDynamicConfigId, dynamicConfigIds)
                .eq(Objects.nonNull(modelId), AigcDynamicAdjustRecord::getModelId, modelId)
                .in(CollectionUtils.isNotEmpty(modelIds), AigcDynamicAdjustRecord::getModelId, modelIds)
                .eq(Objects.nonNull(scaleType), AigcDynamicAdjustRecord::getScaleType, scaleType)
                .eq(StringUtils.isNotBlank(scaleStatus), AigcDynamicAdjustRecord::getScaleStatus, scaleStatus)
                .in(CollectionUtils.isNotEmpty(scaleStatusList), AigcDynamicAdjustRecord::getScaleStatus, scaleStatusList)
                .eq(Objects.nonNull(status), AigcDynamicAdjustRecord::getStatus, status)
                .in(CollectionUtils.isNotEmpty(statusList), AigcDynamicAdjustRecord::getStatus, statusList)
                ;
    }
}
