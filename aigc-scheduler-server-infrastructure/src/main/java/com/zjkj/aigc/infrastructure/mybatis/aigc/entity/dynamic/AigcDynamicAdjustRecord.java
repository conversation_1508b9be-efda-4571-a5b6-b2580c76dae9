package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 动态调整记录表(AigcDynamicAdjustRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicAdjustRecord extends BaseEntity {

    private static final long serialVersionUID = 7888959028669742327L;
    /**
     * 动态配置id
     */
    private Long dynamicConfigId;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String scaleType;
    /**
     * 伸缩状态
     * @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum
     */
    private String scaleStatus;
    /**
     * 状态：1-生效中 2-结束 3-取消
     @see com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum
     */
    private Integer status;
    /**
     * 失败信息
     */
    private String message;
    /**
     * 动态配置快照
     */
    private String dynamicConfigSnapshot;
    /**
     * 关联的id
     */
    private Long relatedId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 调整记录明细列表
     */
    @TableField(exist = false)
    private List<AigcDynamicAdjustRecordDetail> recordDetails;
}

