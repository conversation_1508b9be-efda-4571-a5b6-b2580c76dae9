package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 资源支出(ResourceExpend)实体类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
public class ResourceExpend extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1886720275638867404L;

    /**
     * 支出月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 账号
     */
    private String account;
    /**
     * 上月实际消费
     */
    private BigDecimal lastMonthCost;
    /**
     * 上月余额
     */
    private BigDecimal lastMonthRemain;
    /**
     * 本月已充值
     */
    private BigDecimal thisMonthRecharge;
    /**
     * 备注说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 预算差异说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String budgetDiffRemark;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.resource.ResourceExpendStatusEnum}
     */
    private Integer status;
}
