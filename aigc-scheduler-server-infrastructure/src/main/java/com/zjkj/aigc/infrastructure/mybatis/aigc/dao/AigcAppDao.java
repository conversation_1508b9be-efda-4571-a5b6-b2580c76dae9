package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcAppCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcAppMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 应用表(AigcApp)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcAppDao extends AbstractBaseDao<AigcAppMapper, AigcApp> {

    /**
     * 根据条件查询
     * @param condition 条件
     * @return 应用列表
     */
    public List<AigcApp> queryList(AigcAppCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 查询指定时间之后活跃的应用
     *
     * @param currentTime 当前时间
     * @return 应用列表
     */
    public List<AigcApp> activeAfterTime(String currentTime) {
        return this.lambdaQuery()
                .gt(AigcApp::getLastActiveTime, currentTime)
                .list();
    }
}
