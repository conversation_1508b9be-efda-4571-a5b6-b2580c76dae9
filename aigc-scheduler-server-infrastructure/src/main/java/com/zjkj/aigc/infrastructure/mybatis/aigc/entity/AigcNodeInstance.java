package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.dto.node.NodeSystemInfo;
import com.zjkj.aigc.infrastructure.mybatis.config.StringListTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 节点实例表(AigcNodeInstance)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Data
@Accessors(chain = true)
@TableName(autoResultMap = true)
public class AigcNodeInstance implements Serializable {
    private static final long serialVersionUID = 356901080801899875L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主机名
     */
    private String hostname;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 系统信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private NodeSystemInfo systemInfo;
    /**
     * 命名空间
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> namespace;
    /**
     * 来源ip
     */
    private String sourceIp;
    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
}

