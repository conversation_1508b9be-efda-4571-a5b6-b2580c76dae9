package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 集群资源利用表(AigcClusterUsage)数据库访问层
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcClusterUsageMapper extends BaseMapper<AigcClusterUsage> {
    /**
     * 按日期统计
     *
     * @param condition 查询条件
     * @return 统计数据
     */
    List<AigcClusterUsage> statByDate(@Param("condition") AigcClusterUsageCondition condition);
}
