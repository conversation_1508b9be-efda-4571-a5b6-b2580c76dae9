package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.GsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.dto.ModelConfigChanged;
import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 模型发布记录表(AigcModelDeploy)实体类
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true)
public class AigcModelDeploy extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -77364653909240887L;
    /**
     * 标题
     */
    private String title;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 项目工程名称
     */
    private String projectName;
    /**
     * 项目git地址
     */
    private String projectGitUrl;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    private String modelFileOssUrl;
    /**
     * 基础镜像地址
     */
    private String mirrorImageUrl;
    /**
     * 资源信息,GUP、CPU、内存等信息描述
     */
    @TableField(typeHandler = GsonTypeHandler.class)
    private ModelDeployResource resourceInfo;
    /**
     * 加密状态: 0-未加密,1-加密中,2-加密完成,3-加密失败
     */
    private Integer encryptStatus;
    /**
     * 是否加密: 0-不加密,1-加密
     */
    private Boolean isEncrypt;
    /**
     * 状态: 0-草稿,1-发布中,2-发布成功,3-发布失败
     */
    private Integer status;
    /**
     * 模型配置变更项
     */
    @TableField(typeHandler = GsonTypeHandler.class)
    private ModelConfigChanged changed;
    /**
     * 信息
     */
    private String message;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
}

