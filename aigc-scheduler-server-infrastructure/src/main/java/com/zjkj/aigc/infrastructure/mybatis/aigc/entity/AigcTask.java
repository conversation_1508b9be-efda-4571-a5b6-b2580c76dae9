package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ai任务表(AigcTask)实体类
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@Accessors(chain = true)
@TableName("aigc_task")
public class AigcTask implements Serializable {
    private static final long serialVersionUID = 436124028580969512L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 平台任务ID
     */
    private String aigcTaskId;
    /**
     * 任务类型，一般与AI模型对应
     */
    private String taskType;
    /**
     * 业务系统ID
     */
    private String businessId;
    /**
     * 通知地址
     */
    private String notifyUrl;
    /**
     * 任务优先级
     * 数字越小优先级越高
     */
    private Integer taskPriority;
    /**
     * 是否任务批量0，1
     */
    private Integer taskBatch;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 任务进度
     */
    private Integer taskProgress;
    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消
     */
    private Integer taskState;
    /**
     * 任务开始时间
     */
    private LocalDateTime taskStartTime;
    /**
     * 任务取消时间
     */
    private LocalDateTime taskCancelTime;
    /**
     * 任务完成时间
     */
    private LocalDateTime taskCompletionTime;
    /**
     * AI模型
     */
    private Long modelId;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 模型参数，与任务类型有关
     */
    private String modelParams;
    /**
     * 输出图片{多张以英文逗号分隔}
     */
    private String modelOutput;
    /**
     * 运行容器id
     */
    private String containerId;
    /**
     * 失败信息
     */
    private String failMessage;
    /**
     * 重试次数
     */
    private Integer retryCount;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
//    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    @TableField(exist = false)
    private Integer rank;

    @TableField(exist = false)
    private Long count;

}

