package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcClusterNodeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 集群节点数据访问对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Repository
@RequiredArgsConstructor
public class AigcClusterNodeDao extends AbstractBaseDao<AigcClusterNodeMapper, AigcClusterNode> {

    /**
     * 根据集群ID删除节点
     *
     * @param clusterId 集群ID
     */
    public void removeByClusterId(Long clusterId) {
        LambdaQueryWrapper<AigcClusterNode> queryWrapper = Wrappers.<AigcClusterNode>lambdaQuery()
                .eq(AigcClusterNode::getClusterId, clusterId);
        remove(queryWrapper);
    }

    /**
     * 根据集群ID查询节点列表
     *
     * @param clusterIds 集群ID
     * @return 节点列表
     */
    public List<AigcClusterNode> listByClusterId(List<Long> clusterIds) {
        LambdaQueryWrapper<AigcClusterNode> queryWrapper = Wrappers.<AigcClusterNode>lambdaQuery()
                .in(AigcClusterNode::getClusterId, clusterIds);
        return list(queryWrapper);
    }

    /**
     * gpu是否被使用
     * @param gpuId gpuId
     * @return 是否被使用
     */
    public boolean gpuIsInUsed(Long gpuId) {
        AigcClusterNode clusterNode = this.lambdaQuery()
                .eq(AigcClusterNode::getGpuId, gpuId)
                .last("LIMIT 1")
                .one();
        return Objects.nonNull(clusterNode);
    }
}
