package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型基础信息表(AigcModelInfo)Dao层
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelInfoDao extends AbstractBaseDao<AigcModelInfoMapper, AigcModelInfo> {

    /**
     * 查询分页
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcModelInfo> queryPage(AigcModelInfoCondition condition) {
        LambdaQueryWrapper<AigcModelInfo> queryWrapper = condition.buildQuery();
        return page(condition.getPage(), queryWrapper);
    }

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcModelInfo> queryList(AigcModelInfoCondition condition) {
        LambdaQueryWrapper<AigcModelInfo> queryWrapper = condition.buildQuery();
        return list(queryWrapper);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void delById(Long id) {
        LambdaUpdateWrapper<AigcModelInfo> updateWrapper = Wrappers.<AigcModelInfo>lambdaUpdate()
                .set(AigcModelInfo::getIsDeleted, 1)
                .set(AigcModelInfo::getDelVersion, System.currentTimeMillis())
                .eq(AigcModelInfo::getId, id);
        update(updateWrapper);
    }

    /**
     * 更改状态
     *
     * @param ids    id列表
     * @param status 状态
     */
    public void changeStatus(List<Long> ids, Integer status) {
        LambdaUpdateWrapper<AigcModelInfo> updateWrapper = Wrappers.<AigcModelInfo>lambdaUpdate()
                .set(AigcModelInfo::getStatus, status)
                .in(AigcModelInfo::getId, ids);
        update(updateWrapper);
    }
}
