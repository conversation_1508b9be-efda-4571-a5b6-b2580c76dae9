package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelTestTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 */
public interface AigcModelTestTaskMapper extends BaseMapper<AigcModelTestTask> {

}

