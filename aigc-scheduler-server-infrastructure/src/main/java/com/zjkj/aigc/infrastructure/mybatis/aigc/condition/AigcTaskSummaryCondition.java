package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/12/26
 */
@Data
@Builder
public class AigcTaskSummaryCondition {
    /**
     * 任务类型
     */
    private Set<String> taskTypes;
    /**
     * 模型名称
     */
    private Set<String> modelNames;
    /**
     * 统计日期-开始日期
     */
    private String startDate;
    /**
     * 统计日期-结束日期
     */
    private String endDate;

    public static AigcTaskSummaryCondition recently(long minusDays) {
        LocalDate now = LocalDate.now();
        return AigcTaskSummaryCondition.builder()
                .startDate(now.minusDays(minusDays).toString())
                .endDate(now.toString())
                .build();
    }
}
