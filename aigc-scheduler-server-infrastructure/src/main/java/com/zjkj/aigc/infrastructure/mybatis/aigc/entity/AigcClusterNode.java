package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 集群节点实体
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@TableName("aigc_cluster_node")
@EqualsAndHashCode(callSuper = true)
public class AigcClusterNode extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 成本
     */
    private BigDecimal cost;
    /**
     * gpu主键id
     */
    private Long gpuId;
    /**
     * cpu核数
     */
    private Integer cpuCore;
    /**
     * 内存大小GB
     */
    private Integer memorySize;
}
