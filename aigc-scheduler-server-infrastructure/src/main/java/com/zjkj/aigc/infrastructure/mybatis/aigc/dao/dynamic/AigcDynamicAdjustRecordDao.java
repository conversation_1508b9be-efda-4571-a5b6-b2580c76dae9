package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.dynamic.AigcDynamicAdjustRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * 动态调整记录表(AigcDynamicAdjustRecord)Dao层
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcDynamicAdjustRecordDao extends AbstractBaseDao<AigcDynamicAdjustRecordMapper, AigcDynamicAdjustRecord> {

    /**
     * 更新动态调整记录状态
     *
     * @param ids        动态调整记录id列表
     * @param statusEnum 动态调整状态
     */
    public void updateStatusById(Collection<Long> ids, DynamicAdjustStatusEnum statusEnum) {
        LambdaUpdateWrapper<AigcDynamicAdjustRecord> updateWrapper = Wrappers.<AigcDynamicAdjustRecord>lambdaUpdate()
                .set(AigcDynamicAdjustRecord::getStatus, statusEnum.getStatus())
                .set(AigcDynamicAdjustRecord::getRevisedTime, LocalDateTime.now())
                .in(AigcDynamicAdjustRecord::getId, ids);
        update(updateWrapper);
    }

    /**
     * 更新动态调整记录伸缩状态
     * @param id 动态调整记录ID
     * @param source 源状态
     * @param target 目标状态
     * @return 是否成功
     */
    public boolean changeScaleStatus(Long id, String source, String target) {
        LambdaUpdateWrapper<AigcDynamicAdjustRecord> updateWrapper = Wrappers.<AigcDynamicAdjustRecord>lambdaUpdate()
                .set(AigcDynamicAdjustRecord::getScaleStatus, target)
                .eq(AigcDynamicAdjustRecord::getId, id)
                .eq(AigcDynamicAdjustRecord::getScaleStatus, source);
        return update(updateWrapper);
    }

    /**
     * 更新动态调整记录状态
     * @param id 动态调整记录ID
     * @param source 源状态
     * @param target 目标状态
     * @return 是否成功
     */
    public boolean changeStatus(Long id, Integer source, Integer target) {
        LambdaUpdateWrapper<AigcDynamicAdjustRecord> updateWrapper = Wrappers.<AigcDynamicAdjustRecord>lambdaUpdate()
                .set(AigcDynamicAdjustRecord::getStatus, target)
                .eq(AigcDynamicAdjustRecord::getId, id)
                .eq(AigcDynamicAdjustRecord::getStatus, source);
        return update(updateWrapper);
    }
}
