package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ai任务表(AigcTask)实体类
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@Accessors(chain = true)
@TableName("aigc_model_test_task")
public class AigcModelTestTask implements Serializable {
    private static final long serialVersionUID = 436124028580969512L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 测试编码
     */
    private String testCode;
    /**
     * 任务进度
     */
    private Integer taskProgress;
    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消
     */
    private Integer taskState;
    /**
     * 任务开始时间
     */
    private LocalDateTime taskStartTime;
    /**
     * 任务取消时间
     */
    private LocalDateTime taskCancelTime;
    /**
     * 任务完成时间
     */
    private LocalDateTime taskCompletionTime;
    /**
     * AI模型
     */
    private Long modelId;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 任务类型，一般与AI模型对应
     */
    private String taskType;
    /**
     * 模型参数，与任务类型有关
     */
    private String modelParams;
    /**
     * 输出图片{多张以英文逗号分隔}
     */
    private String modelOutput;
    /**
     * 输出图片{多张以英文逗号分隔}
     */
    private String modelNewOutput;
    /**
     *
     */
    private Long evaluatorId;
    /**
     *
     */
    private String evaluator;
    /**
     *
     */
    private Integer isAcceptable;
    /**
     *
     */
    private String acceptOutput;
    /**
     *
     */
    private String evaluation;
    /**
     * 运行容器id
     */
    private String containerId;
    /**
     * 失败信息
     */
    private String failMessage;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
//    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

}

