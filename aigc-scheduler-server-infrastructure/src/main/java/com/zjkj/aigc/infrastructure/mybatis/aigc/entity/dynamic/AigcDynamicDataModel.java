package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic;

import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.common.util.MathUtil;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
@Data
@Accessors(chain = true)
public class AigcDynamicDataModel {

    private List<Model> modelList;

    private Map<Long, AigcModelInfo> deprivedModelMap;

    private Map<Long, GpuInfo> modelConfigMap;

    Map<Long, AigcDynamicConfig> dynamicConfigMap;

    @Data
    @Accessors(chain = true)
    public static class Model {
        /**
         * 模型ID
         */
        private Long modelId;
        /**
         * 模型中午名称
         */
        private String name;
        /**
         * 任务类型
         */
        private String taskType;
        /**
         * 模型名称
         */
        private String modelName;
        /**
         * 未处理任务数
         */
        private Long unprocessedTasks;
        /**
         * 平均耗时ms
         */
        private Long avgElapsedTime;
        /**
         * 在线实例
         */
        private long instance;
        /**
         * GPU内存大小
         */
        private Integer gpuMemorySize;
        /**
         * 优先级
         */
        private Integer priority;
        private AigcDynamicConfig config;
        /**
         * 是否扩容
         *
         * @param scaleOutMinute    扩容维度:分钟数
         * @param scaleOutThreshold 扩容负载: 阈值1-100%
         * @param maxReplica        最大副本数
         * @return 是否扩容
         */
       public boolean shouldBeScaleOut(int scaleOutMinute, int scaleOutThreshold, int maxReplica) {
            if (instance >= maxReplica || Objects.isNull(avgElapsedTime) || avgElapsedTime <= 0) {
                return false;
            }

            long processCount = (scaleOutMinute * 60000L) / avgElapsedTime;
            long threshold = (processCount * instance * scaleOutThreshold) / 100;
            return unprocessedTasks > threshold;
        }

        /**
         * 计算实例数变化
         *
         * @param targetQuantity  目标处理量
         * @param targetThreshold 目标负载
         * @param instance        当前实例数
         * @param avgElapsedTime  平均耗时ms
         * @return 实例数(正值表示增加, 负值表示减少)
         */
        public long calInstanceChange(Long targetQuantity, Integer targetThreshold, Long instance, Long avgElapsedTime) {
            if (MathUtil.anyLessOrEqual(0, targetQuantity, avgElapsedTime) || MathUtil.anyLessOrEqual(0, targetThreshold)) {
                return 0;
            }

            // 单个实例每小时可处理的数量
            double hourQuantity = 3600000.0 / avgElapsedTime;

            // 目标总处理量
            double preQuantity = targetQuantity * (100.0 / targetThreshold);
            long needInstance = (long) Math.ceil(preQuantity / hourQuantity);
            return needInstance - instance;
        }
    }

    /**
     * 排序
     * @param modelList 模型列表
     */
    public static void adjustSort(List<AigcDynamicDataModel.Model> modelList) {
        // 按priority升序 avgElapsedTime升序
        modelList.sort(Comparator.comparing(
                AigcDynamicDataModel.Model::getPriority).thenComparing(m -> Objects.nonNull(m.getAvgElapsedTime()) ? m.getAvgElapsedTime() : Long.MAX_VALUE)
        );
    }
}
