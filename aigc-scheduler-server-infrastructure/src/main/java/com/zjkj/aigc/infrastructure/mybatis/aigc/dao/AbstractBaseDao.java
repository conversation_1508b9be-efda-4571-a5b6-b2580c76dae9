package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/9/8
 */
@Slf4j
public abstract class AbstractBaseDao<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> {


    public List<T> selectList(T record) {
        if (isAllPropertyNull(record)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new QueryWrapper<>(record));
    }

    /**
     * 空字符串属性转为null
     *
     * @param t   待处理对象
     * @param <T> 待处理对象的类型
     */
    public static <T> void blankStringPropertyToNull(T t) {
        if (null == t) {
            return;
        }

        getFields(t).forEach(field -> {
            field.setAccessible(true);
            if (field.getType().equals(String.class)) {
                try {
                    if (!StringUtils.hasText((String) field.get(t))) {
                        field.set(t, null);
                    }
                } catch (IllegalAccessException e) {
                    log.error("字段解析错误", e);
                }
            }
        });
    }


    /**
     * 判断对象T中的所有字段值都为null (排除某些字段)
     *
     * @param t                  入参对象
     * @param exceptPropertyName 排除的属性名称
     * @param <T>                入参对象类型
     * @return true-所有字段值都为null, false-反之
     */
    public static <T> boolean isAllPropertyNull(T t, String... exceptPropertyName) {
        if (t == null) {
            return true;
        }

        if (exceptPropertyName == null) {
            return isAllPropertyNull(t);
        }

        return getFields(t).stream()
                .filter(field -> !Arrays.asList(exceptPropertyName).contains(field.getName()))
                .noneMatch(fieldPredicate(t));
    }

    /**
     * 判断所有属性是否为空
     *
     * @param t t
     * @return boolean
     */
    protected static <T> boolean isAllPropertyNull(T t) {
        if (t == null) {
            return true;
        }
        return getFields(t).stream().noneMatch(fieldPredicate(t));
    }

    /**
     * 获取字段
     *
     * @param t t
     * @return {@link List<Field>}
     */
    private static <T> List<Field> getFields(T t) {
        List<Field> fieldList = new ArrayList<>();
        Class<?> aClass = t.getClass();
        while (aClass != null) {
            fieldList.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
        }
        return fieldList;
    }

    /**
     * 字段判断
     *
     * @param t t
     * @return {@link Predicate<Field>}
     */
    private static <T> Predicate<Field> fieldPredicate(T t) {
        return field -> {
            try {
                field.setAccessible(true);
                return field.get(t) != null;
            } catch (IllegalAccessException e) {
                return false;
            }
        };
    }

    /**
     * 获取第一个
     *
     * @param records 记录
     * @return {@link T}
     */
    public <T> T getFirst(List<T> records) {
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        return records.get(0);
    }

    /**
     * 过滤、去重
     *
     * @param params 入参列表
     * @return List<T>
     */
    protected List<T> filterAndDistinct(Collection<T> params, Predicate<? super T> filterPredict) {
        if (params == null) {
            return Collections.emptyList();
        }

        return params.stream()
                .filter(filterPredict)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 修改状态
     *
     * @param idField     id 字段
     * @param ids         id列表
     * @param statusField 状态字段
     * @param status      状态
     */
    public void changeStatus(SFunction<T, ?> idField, Collection<Long> ids, SFunction<T, ?> statusField, Integer status) {
        LambdaUpdateWrapper<T> updateWrapper = Wrappers.<T>lambdaUpdate()
                .set(statusField, status)
                .in(idField, ids);
        update(updateWrapper);
    }
}
