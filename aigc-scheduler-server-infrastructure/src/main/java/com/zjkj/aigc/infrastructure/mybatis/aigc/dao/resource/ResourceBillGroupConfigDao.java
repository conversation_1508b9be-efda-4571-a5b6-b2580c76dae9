package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBillGroupConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.resources.ResourceBillGroupConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 费用分摊信息配置表(ResourceBillGroupConfig)Dao层
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ResourceBillGroupConfigDao extends AbstractBaseDao<ResourceBillGroupConfigMapper, ResourceBillGroupConfig> {

}
