package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 字典数据表
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SysDictData extends BaseEntity {
    private static final long serialVersionUID = -8959475738572401535L;

    /**
     * 字典类型id
     */
    private Long dictId;

    /**
     * 字典标签
     */
    private String label;

    /**
     * 字典键值
     */
    @TableField(value = "`value`")
    private String value;

    /**
     * 字典排序
     */
    private Integer dictSort;
    /**
     * 样式属性
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cssClass;
    /**
     * 颜色属性
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String colorClass;
    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
}
