package com.zjkj.aigc.infrastructure.mybatis.aigc.es.event;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import org.springframework.context.ApplicationEvent;

/**
 *
 */
public class AigcTaskEvent extends ApplicationEvent {

    private boolean delete = false;

    public AigcTaskEvent(Object source) {
        super(source);
    }

    public AigcTaskEvent setDelete(boolean delete) {
        this.delete = delete;
        return this;
    }

    public boolean getDelete(){
        return this.delete;
    }

    public Long getId() {
        if (source instanceof Long) {
            return (Long) source;
        } else if (source instanceof AigcTask) {
            return ((AigcTask) source).getId();
        }
        return null;
    }

    public String getAigcTaskId() {
        if (source instanceof String) {
            return (String) source;
        } else if (source instanceof AigcTask) {
            return ((AigcTask) source).getAigcTaskId();
        }
        return null;
    }

    public AigcTask getAigcTask() {
        if(source instanceof AigcTask) {
            return (AigcTask) source;
        }
        return null;
    }
}
