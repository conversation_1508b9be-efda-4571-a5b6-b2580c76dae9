package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import cn.hutool.core.lang.func.LambdaUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.doc.AigcTaskDoc;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import lombok.Builder;
import lombok.Data;
import org.apache.http.client.utils.DateUtils;
import org.elasticsearch.action.update.UpdateHelper;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/31
 */
@Data
@Builder
public class AigcTaskCondition {
    /**
     * id
     */
    private Collection<Long> ids;
    /**
     * 任务id
     */
    private List<String> taskIds;

    /**
     * 任务id
     */
    private List<String> aigcTaskIds;

    /**
     * 业务id
     */
    private String businessId;
    /**
     * 业务id
     */
    private List<String> businessIds;

    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 任务状态
     */
    private Integer taskState;
    private List<Integer> taskStates;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 任务名称
     */
    private String modelName;
    /**
     * 任务名称
     */
    private List<String> modelNameList;
    /**
     * 查询数量
     */
    private Integer limit;
    /**
     * 是否按优先级排序
     */
    private boolean orderByPriority;
    /**
     * 是否任务批量0，1
     */
    private Integer taskBatch;
    /**
     * 分页信息
     */
    private Page<AigcTask> page;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 查询字段
     */
    private List<SFunction<AigcTask, ?>> selectColumns;

    /**
     * 失败信息
     */
    private String failMessage;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcTask> buildQuery() {
        LambdaQueryWrapper<AigcTask> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getIds()), AigcTask::getId, this.getIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getAigcTaskIds()), AigcTask::getAigcTaskId, this.getAigcTaskIds());
        queryWrapper.eq(StringUtils.isNotBlank(this.getBusinessId()), AigcTask::getBusinessId, this.getBusinessId());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getBusinessIds()), AigcTask::getBusinessId, this.getBusinessIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getTaskIds()), AigcTask::getTaskId, this.getTaskIds());
        queryWrapper.ge(StringUtils.isNotBlank(this.getStartTime()), AigcTask::getCreatedTime, this.getStartTime());
        queryWrapper.le(StringUtils.isNotBlank(this.getEndTime()), AigcTask::getCreatedTime, this.getEndTime());
        queryWrapper.eq(Objects.nonNull(this.getTaskState()), AigcTask::getTaskState, this.getTaskState());
        queryWrapper.in(CollectionUtils.isNotEmpty(this.getTaskStates()), AigcTask::getTaskState, this.getTaskStates());
        queryWrapper.eq(StringUtils.isNotBlank(this.getTaskType()), AigcTask::getTaskType, this.getTaskType());
        queryWrapper.eq(StringUtils.isNotBlank(this.getModelName()), AigcTask::getModelName, this.getModelName());
        queryWrapper.eq(StringUtils.isNotBlank(this.getTaskSource()), AigcTask::getTaskSource, this.getTaskSource());
        queryWrapper.eq(Objects.nonNull(this.getTaskBatch()), AigcTask::getTaskBatch, this.getTaskBatch());
        queryWrapper.eq(Objects.nonNull(this.getFailMessage()),AigcTask::getFailMessage,this.getFailMessage());
        queryWrapper.last(Objects.nonNull(this.getLimit()), "LIMIT " + this.getLimit());
        queryWrapper.orderByAsc(this.isOrderByPriority(), AigcTask::getTaskPriority);
        queryWrapper.orderByAsc(this.isOrderByPriority(), AigcTask::getId);
        if (CollectionUtils.isNotEmpty(this.selectColumns)) {
            queryWrapper.select(selectColumns);
        }
        return queryWrapper;
    }

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public NativeSearchQuery buildESQuery() {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder();
        BoolQueryBuilder bqb = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(this.getIds())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTableId), this.getIds()));
        }
        if (CollectionUtils.isNotEmpty(this.getAigcTaskIds())) {
            bqb.filter(QueryBuilders.termsQuery(UpdateHelper.ContextFields.ID, this.getAigcTaskIds()));
        }
        if (StringUtils.isNotBlank(this.getBusinessId())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getBusinessId), this.getBusinessId()));
        }
        if (CollectionUtils.isNotEmpty(this.getBusinessIds())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getBusinessId), this.getBusinessIds()));
        }
        if (CollectionUtils.isNotEmpty(this.getTaskIds())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskId), this.getTaskIds()));
        }
        if (StringUtils.isNotBlank(this.getStartTime()) || StringUtils.isNotBlank(this.getEndTime())) {
            RangeQueryBuilder rq = QueryBuilders.rangeQuery(LambdaUtil.getFieldName(AigcTaskDoc::getCreatedTime));
            if (StringUtils.isNotBlank(this.getStartTime())) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime st = LocalDateTime.parse(this.getStartTime(), formatter);
                rq.gte(st.format(DateTimeFormatter.ofPattern(DateFormat.date_hour_minute_second_millis.getPattern())));
            }
            if (StringUtils.isNotBlank(this.getEndTime())) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime et = LocalDateTime.parse(this.getEndTime(), formatter);
                rq.lt(et.format(DateTimeFormatter.ofPattern(DateFormat.date_hour_minute_second_millis.getPattern())));

            }
            bqb.filter(rq);
        }
        if (Objects.nonNull(this.getTaskState())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskState), new Integer[]{this.getTaskState()}));
        }
        if (CollectionUtils.isNotEmpty(this.getTaskStates())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskState), this.getTaskStates().toArray(new Integer[0])));
        }
        if (StringUtils.isNotBlank(this.getTaskType())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskType), this.getTaskType()));
        }
        if (StringUtils.isNotBlank(this.getModelName())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getModelName), this.getModelName()));
        }
        if (StringUtils.isNotBlank(this.getTaskSource())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskSource), this.getTaskSource()));
        }
        if (Objects.nonNull(this.getTaskBatch())) {
            bqb.filter(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskBatch), new Integer[]{this.getTaskBatch()}));
        }
        Collection<SortBuilder<?>> sbs = new ArrayList<>();
        if (this.isOrderByPriority()) {
            sbs.add(SortBuilders.fieldSort(LambdaUtil.getFieldName(AigcTaskDoc::getTaskPriority)).order(SortOrder.ASC));
        }
        sbs.add(SortBuilders.fieldSort(LambdaUtil.getFieldName(AigcTaskDoc::getTableId)).order(SortOrder.DESC));
        Pageable p = null;
        if (Objects.nonNull(this.getPage())) {
            p = PageRequest.of((int) this.getPage().getCurrent() - 1, (int) this.getPage().getSize());
        } else {
            p = PageRequest.of(0, 100);
        }
        builder.withQuery(bqb)
                .withTrackTotalHits(true)
                .withSorts(sbs)
                .withPageable(p);
        return builder.build();
    }
}
