package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.dto.ModelInstanceAdjustDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmConfigThresholdDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 模型实例调整记录表(AigcModelInstanceAdjustRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@TableName(autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class AigcModelInstanceAdjustRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -1588861369940303098L;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ModelInstanceAdjustDTO> modelInfo;
}

