package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.zjkj.aigc.common.util.MathUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 模型基础信息表(AigcModelInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(autoResultMap = true)
public class AigcModelInfo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -60769260615603771L;
    /**
     * 模型中文名称
     */
    private String name;
    /**
     * 类型: BIZ-业务模型,ATOM-原子模型
     */
    private String type;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 环境实例数
     * envType,实例数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Long> envInstance;
    /**
     * 是否在线
     */
    private Boolean online;
    /**
     * 预测负载百分比
     * 小时计
     */
    private Long preThreshold;
    /**
     * 预测数量
     */
    private Long preQuantity;
    /**
     * 平均耗时ms
     */
    private Long avgElapsedTime;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.model.AigcModelInfoEnum}
     */
    private Integer status;
    /**
     * 删除版本号 uk
     */
    private String delVersion;
    /**
     * 备注
     */
    private String remark;
    /**
     * 上次活跃时间
     */
    private LocalDateTime lastActiveTime;
    /**
     * 上次注册时间
     */
    private LocalDateTime lastRegisterTime;

    public long getInstanceByEnv(String envType) {
        if (!CollectionUtils.isEmpty(envInstance)) {
            return Convert.convert(Long.class, envInstance.getOrDefault(envType, 0L));
        }
        return 0L;
    }

    /**
     * 计算负载百分比
     *
     * @param preQuantity    预测数量
     * @param instance       实例数
     * @param avgElapsedTime 平均耗时ms
     * @return 负载百分比
     */
    public long calPreThreshold(Long preQuantity, Long instance, Long avgElapsedTime) {
        if (MathUtil.anyLessOrEqual(0, preQuantity, instance, avgElapsedTime)) {
            return 0;
        }

        // 按小时计: 3600000ms
        double hourCapacity = (3600000.0 / avgElapsedTime) * instance;

        // 计算负载百分比，避免整型溢出
        return hourCapacity <= 0 ? 0 : (long) (preQuantity * 100.0 / hourCapacity);
    }

    /**
     * 计算负载百分比
     *
     * @param preQuantity    预测数量
     * @param preMinute      预测分钟数
     * @param instance       实例数
     * @param avgElapsedTime 平均耗时ms
     * @return 负载百分比
     */
    public long calPreThreshold(Long preQuantity, Integer preMinute, Long instance, Long avgElapsedTime) {
        if (MathUtil.anyLessOrEqual(0, preQuantity, instance, avgElapsedTime) || MathUtil.anyLessOrEqual(0, preMinute)) {
            return 0;
        }

        double hourCapacity = ((double) (preMinute * 60000L) / avgElapsedTime) * instance;

        // 计算负载百分比，避免整型溢出
        return hourCapacity <= 0 ? 0 : (long) (preQuantity * 100.0 / hourCapacity);
    }

    /**
     * 计算实例数变化
     *
     * @param targetQuantity  目标处理量
     * @param targetThreshold 目标负载
     * @param instance        当前实例数
     * @param avgElapsedTime  平均耗时ms
     * @return 实例数(正值表示增加, 负值表示减少)
     */
    public long calInstanceChange(Long targetQuantity, Long targetThreshold, Long instance, Long avgElapsedTime) {
        if (MathUtil.anyLessOrEqual(0, targetQuantity, targetThreshold, instance, avgElapsedTime)) {
            return 0;
        }

        // 单个实例每小时可处理的数量
        double hourQuantity = 3600000.0 / avgElapsedTime;

        // 目标总处理量
        double preQuantity = targetQuantity * (100.0 / targetThreshold);
        long needInstance = (long) Math.ceil(preQuantity / hourQuantity);
        return needInstance - instance;
    }
}

