package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcClusterUsageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 集群资源利用表(AigcClusterUsage)数据访问层
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcClusterUsageDao extends AbstractBaseDao<AigcClusterUsageMapper, AigcClusterUsage> {

    private final AigcClusterUsageMapper aigcClusterUsageMapper;

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcClusterUsage> queryPage(AigcClusterUsageCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcClusterUsage> queryList(AigcClusterUsageCondition condition) {
        return list(condition.buildQuery());
    }

    /**
     * 按日期统计
     *
     * @param condition 查询条件
     * @return 统计数据
     */
    public List<AigcClusterUsage> statByDate(AigcClusterUsageCondition condition) {
        return aigcClusterUsageMapper.statByDate(condition);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void delById(Long id) {
        LambdaUpdateWrapper<AigcClusterUsage> updateWrapper = Wrappers.<AigcClusterUsage>lambdaUpdate()
                .set(AigcClusterUsage::getIsDeleted, 1)
                .set(AigcClusterUsage::getDelVersion, System.currentTimeMillis())
                .set(AigcClusterUsage::getRevisedTime, LocalDateTime.now())
                .eq(AigcClusterUsage::getId, id);
        update(updateWrapper);
    }
}
