package com.zjkj.aigc.infrastructure.mybatis.config;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class GenericListTypeHandler<T> extends BaseTypeHandler<List<T>> {

    private final Class<T> elementType;
    public GenericListTypeHandler(Class<T> elementType) {
        this.elementType = elementType;
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<T> list, JdbcType jdbcType) throws SQLException {
        if (CollectionUtils.isEmpty(list)) {
            preparedStatement.setString(i, StringPool.EMPTY);
            return;
        }

        String value = list.stream()
                .map(Object::toString)
                .collect(Collectors.joining(StringPool.COMMA));
        preparedStatement.setString(i, value);
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        String value = resultSet.getString(columnName);
        return convertToList(value);
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        String value = resultSet.getString(columnIndex);
        return convertToList(value);
    }

    @Override
    public List<T> getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        String value = callableStatement.getString(columnIndex);
        return convertToList(value);
    }

    private List<T> convertToList(String value) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }

        return Arrays.stream(value.split(StringPool.COMMA))
                .map(v -> Convert.convert(elementType, v))
                .collect(Collectors.toList());
    }

}
