package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceGpuPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.resources.ResourceGpuPlanMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * GPU资源规划(ResourceGpuPlan)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ResourceGpuPlanDao extends AbstractBaseDao<ResourceGpuPlanMapper, ResourceGpuPlan> {
}
