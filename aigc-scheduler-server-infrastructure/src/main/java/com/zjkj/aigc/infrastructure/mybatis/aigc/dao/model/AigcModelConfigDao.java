package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.model;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcModelConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模型配置表(AigcModelConfig)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcModelConfigDao extends AbstractBaseDao<AigcModelConfigMapper, AigcModelConfig> {

    /**
     * 根据模型id获取
     *
     * @param modelId 模型id
     * @return 模型配置
     */
    public AigcModelConfig getByModelId(Long modelId) {
        return this.lambdaQuery()
                .eq(AigcModelConfig::getModelId, modelId)
                .one();
    }

    /**
     * 根据模型id集合获取
     *
     * @param modelIds 模型id集合
     * @return 模型配置 集合
     */
    public List<AigcModelConfig> getByModelIds(List<Long> modelIds) {
        return this.lambdaQuery()
                .in(AigcModelConfig::getModelId, modelIds).list();
    }
}
