package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntityNoCreator;
import com.zjkj.aigc.infrastructure.mybatis.config.LongListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 动态配置全局表(AigcDynamicConfigGlobal)实体类
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(autoResultMap = true)
public class AigcDynamicConfigGlobal extends BaseEntityNoCreator implements Serializable {
    private static final long serialVersionUID = -53331829969332058L;
    /**
     * 动态调度启用
     */
    private Boolean dynamicEnabled;
    /**
     * 白名单启用
     */
    private Boolean whitelistEnabled;
    /**
     * 模型白名单
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Long> modelWhitelist;
    /**
     * 冷却分钟数
     */
    private Integer cooldownMinute;
    /**
     * zadig 端点地址
     */
    private String zadigEndpoint;
    /**
     * zadig api token
     */
    private String zadigApiToken;
}

