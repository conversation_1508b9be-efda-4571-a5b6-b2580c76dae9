package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Builder
public class AigcAlarmCondition implements Serializable {
    private static final long serialVersionUID = 7813237153691696944L;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型类型
     * {@link com.zjkj.aigc.common.enums.model.AigcModelTypeEnum}
     */
    private String modelType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 分页信息
     */
    private Page<AigcAlarm> page;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcAlarm> buildQuery() {
        LambdaQueryWrapper<AigcAlarm> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(taskType), AigcAlarm::getTaskType, taskType)
                .eq(StringUtils.isNotBlank(modelType), AigcAlarm::getModelType, modelType)
                .eq(StringUtils.isNotBlank(modelName), AigcAlarm::getModelName, modelName)
                .eq(Objects.nonNull(alarmType), AigcAlarm::getAlarmType, alarmType)
                .eq(Objects.nonNull(status), AigcAlarm::getStatus, status)
                .ge(StringUtils.isNotBlank(startTime), AigcAlarm::getAlarmTime, startTime)
                .le(StringUtils.isNotBlank(endTime), AigcAlarm::getAlarmTime, endTime);
        return queryWrapper;
    }
}
