package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestReport;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.ModelAutoTestReportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 模型测试报告表(ModelAutoTestReport)Dao层
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ModelAutoTestReportDao extends AbstractBaseDao<ModelAutoTestReportMapper, ModelAutoTestReport> {

    /**
     * 根据测试编号查询
     * @param testNo 测试编号
     */
    public ModelAutoTestReport queryByTestNo(String testNo) {
        return this.lambdaQuery()
                .eq(ModelAutoTestReport::getTestNo, testNo)
                .one();
    }
}
