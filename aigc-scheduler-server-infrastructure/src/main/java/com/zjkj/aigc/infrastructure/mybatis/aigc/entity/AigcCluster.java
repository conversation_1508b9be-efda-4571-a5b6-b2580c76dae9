package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zjkj.aigc.infrastructure.mybatis.config.StringListTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 集群实体
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@TableName(value = "aigc_cluster", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class AigcCluster extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    private String platform;

    /**
     * 名称
     */
    private String name;

    /**
     * 服务类型
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> serviceType;

    /**
     * 环境类型
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> envType;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;

    /**
     * 节点列表
     */
    @TableField(exist = false)
    private List<AigcClusterNode> nodes;
}
