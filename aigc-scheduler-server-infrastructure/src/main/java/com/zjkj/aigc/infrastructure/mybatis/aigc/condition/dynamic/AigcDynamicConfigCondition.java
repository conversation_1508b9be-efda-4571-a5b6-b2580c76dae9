package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicConfigCondition extends BaseCondition<AigcDynamicConfig> {

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 模型ID
     */
    private Collection<Long> modelIds;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;

    /**
     * 构建查询条件
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcDynamicConfig> buildQuery() {
        return Wrappers.<AigcDynamicConfig>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcDynamicConfig::getId, this.getIds())
                .eq(Objects.nonNull(modelId), AigcDynamicConfig::getModelId, modelId)
                .in(CollectionUtils.isNotEmpty(modelIds), AigcDynamicConfig::getModelId, modelIds)
                .eq(Objects.nonNull(status), AigcDynamicConfig::getStatus, status)
                ;
    }
}
