package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.alarm.AigcAlarmConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 模型告警配置(AigcAlarmConfig)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcAlarmConfigDao extends AbstractBaseDao<AigcAlarmConfigMapper, AigcAlarmConfig> {
    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcAlarmConfig> queryPage(AigcAlarmConfigCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }
}
