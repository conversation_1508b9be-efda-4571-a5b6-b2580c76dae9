package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.resources.ResourceMidPlanMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 中间件资源规划(ResourceMidPlan)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ResourceMidPlanDao extends AbstractBaseDao<ResourceMidPlanMapper, ResourceMidPlan> {

}
