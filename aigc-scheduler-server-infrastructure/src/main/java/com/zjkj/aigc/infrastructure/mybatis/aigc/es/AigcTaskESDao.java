package com.zjkj.aigc.infrastructure.mybatis.aigc.es;

import cn.hutool.core.lang.func.LambdaUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.doc.AigcTaskDoc;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.event.AigcTaskBatchEvent;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.event.AigcTaskEvent;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcTaskMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class AigcTaskESDao {

    private AigcTaskMapper mapper;

    private AigcTaskRepo repo;

    private ElasticsearchRestTemplate template;

    private static String TABLE_SOURCE_DEF = "ai_inference_server.aigc_task";

    /**
     * 查询任务
     *
     * @param condition 查询条件
     * @return 任务分页信息
     */
    public Page<AigcTaskDoc> queryPage(AigcTaskCondition condition) {
        NativeSearchQuery query = condition.buildESQuery();

        // 打印详细的ES查询条件日志
        log.info("=== ES Query Details ===");
        log.info("Query: {}", query.getQuery() != null ? query.getQuery().toString() : "null");
        log.info("Filter: {}", query.getFilter() != null ? query.getFilter().toString() : "null");
        log.info("Sort: {}", query.getSort() != null ? query.getSort().toString() : "null");
        log.info("Pageable: page={}, size={}",
                query.getPageable().getPageNumber(),
                query.getPageable().getPageSize());
        log.info("=== End ES Query Details ===");

        SearchHits<AigcTaskDoc> hits = template.search(query, AigcTaskDoc.class);
        Page<AigcTaskDoc> page = new Page<>(query.getPageable().getPageNumber(), query.getPageable().getPageSize(), hits.getTotalHits());
        page.setRecords(hits.getSearchHits().stream().map(t -> t.getContent()).collect(Collectors.toList()));

        // 打印查询结果统计
        log.info("ES Query Result: totalHits={}, returnedRecords={}", hits.getTotalHits(), page.getRecords().size());

        return page;
    }

    /**
     * @param e
     * @return
     */
    public AigcTaskDoc save(AigcTask e) {
        AigcTaskDoc d = new AigcTaskDoc();
        BeanUtils.copyProperties(e, d);
        d.setTableId(e.getId());
        d.setTableSource(TABLE_SOURCE_DEF);
        d = this.repo.save(d);
        return d;
    }

    /**
     * @param iter
     * @return
     */
    public Iterable<AigcTaskDoc> saveAll(Iterable<AigcTask> iter) {
        List<AigcTaskDoc> docIter = new ArrayList<>();
        iter.forEach(e -> {
            AigcTaskDoc d = new AigcTaskDoc();
            BeanUtils.copyProperties(e, d);
            d.setTableId(e.getId());
            d.setTableSource(TABLE_SOURCE_DEF);
            docIter.add(d);
        });
        Iterable<AigcTaskDoc> r = this.repo.saveAll(docIter);
        return r;
    }

    /**
     * @param aigcTaskId
     */
    public void deleteByAigcTaskId(String aigcTaskId) {
        this.repo.deleteById(aigcTaskId);
    }

    /**
     * @param aigcTaskIds
     */
    public void deleteByAigcTaskIds(List<String> aigcTaskIds) {
        this.repo.deleteAllById(aigcTaskIds);
    }

    /**
     * @param id
     */
    public void deleteById(Long id) {
        Query query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTableId), id))
                        .must(QueryBuilders.matchQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskSource), TABLE_SOURCE_DEF))
                )
                .build();
        this.template.delete(query, AigcTaskDoc.class);
    }

    /**
     * @param ids
     */
    public void deleteByIds(List<Long> ids) {
        Query query = new NativeSearchQueryBuilder()
                .withQuery(QueryBuilders.boolQuery()
                        .must(QueryBuilders.termsQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTableId), ids))
                        .must(QueryBuilders.matchQuery(LambdaUtil.getFieldName(AigcTaskDoc::getTaskSource), TABLE_SOURCE_DEF))
                )
                .build();
        this.template.delete(query, AigcTaskDoc.class);
    }


    /**
     * @param aigcTaskId
     * @return
     */
    public Optional<AigcTaskDoc> findById(String aigcTaskId) {
        return this.repo.findById(aigcTaskId);
    }

    @EventListener
    @Async("esExecutor") // 指定线程池
    @Retryable(
            value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(multiplier = 2, maxDelay = 30_000))
    public void handleTaskEvent(AigcTaskEvent event) {
        Long id = event.getId();
        String aigcTaskId = event.getAigcTaskId();
        AigcTask task = event.getAigcTask();
        try {
            if (!event.getDelete()) {
                if (Objects.isNull(task)) {
                    if (Objects.nonNull(id)) {
                        task = mapper.selectById(id);
                    } else if (StringUtils.hasText(aigcTaskId)) {
                        LambdaQueryWrapper<AigcTask> q = Wrappers.lambdaQuery();
                        q.eq(AigcTask::getAigcTaskId, aigcTaskId);
                        q.last("limit 1");
                        task = mapper.selectOne(q);
                    }
                }

                if (Objects.nonNull(task)) {
                    this.save(task);
                    log.info("task es save succ, id[{}], aigcTaskId[{}]", id, aigcTaskId);
                } else {
                    log.info("task es save fail id not exists, id[{}], aigcTaskId[{}]", id, aigcTaskId);
                }
            } else {
                if (Objects.nonNull(id)) {
                    this.deleteById(id);
                } else if (StringUtils.hasText(aigcTaskId)) {
                    this.deleteByAigcTaskId(aigcTaskId);
                }
                log.info("task es delete succ, id[{}], aigcTaskId[{}]", id, aigcTaskId);
            }
        } catch (Exception e) {
            int attempt = RetrySynchronizationManager.getContext().getRetryCount();
            log.warn("task es sync excep, attempt[{}], id[{}], aigcTaskId[{}]", attempt, id, aigcTaskId, e);
            throw e;
        }
    }

    @EventListener
    @Async("esExecutor")
    @Retryable(
            value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(multiplier = 2, maxDelay = 30_000))// 指定线程池
    public void handleTaskEvent(AigcTaskBatchEvent event) {
        List<Long> ids = event.getIds();
        List<String> aigcTaskIds = event.getAigcTaskIds();
        List<AigcTask> tasks = event.getAigcTasks();
        try {
            if (!event.getDelete()) {
                if (Objects.isNull(tasks)) {
                    if (!CollectionUtils.isEmpty(ids)) {
                        LambdaQueryWrapper<AigcTask> q = Wrappers.lambdaQuery();
                        q.select(AigcTask.class, tfi -> !tfi.getColumn().equalsIgnoreCase("model_params")
                                && !tfi.getColumn().equalsIgnoreCase("model_output"));
                        q.in(AigcTask::getId, ids);
                        tasks = mapper.selectList(q);
                    } else if (!CollectionUtils.isEmpty(aigcTaskIds)) {
                        LambdaQueryWrapper<AigcTask> q = Wrappers.lambdaQuery();
                        q.select(AigcTask.class, tfi -> !tfi.getColumn().equalsIgnoreCase("model_params")
                                && !tfi.getColumn().equalsIgnoreCase("model_output"));
                        q.in(AigcTask::getAigcTaskId, aigcTaskIds);
                        tasks = mapper.selectList(q);
                    }
                }
                if (!CollectionUtils.isEmpty(tasks)) {
                    this.saveAll(tasks);
                    log.info("task es batch save succ, ids[{}], aigcTaskIds[{}]",
                            org.apache.commons.lang.StringUtils.join(ids, ","),
                            org.apache.commons.lang.StringUtils.join(aigcTaskIds, ","));
                } else {
                    log.info("task es batch fail list is empty, ids[{}], aigcTaskIds[{}]",
                            org.apache.commons.lang.StringUtils.join(ids, ","),
                            org.apache.commons.lang.StringUtils.join(aigcTaskIds, ","));
                }
            } else {
                if (!CollectionUtils.isEmpty(ids)) {
                    this.deleteByIds(ids);
                } else if (!CollectionUtils.isEmpty(aigcTaskIds)) {
                    this.deleteByAigcTaskIds(aigcTaskIds);
                }
                log.info("task es batch delete succ, ids[{}], aigcTaskIds[{}]",
                        org.apache.commons.lang.StringUtils.join(ids, ","),
                        org.apache.commons.lang.StringUtils.join(aigcTaskIds, ","));
            }
        } catch (Exception e) {
            int attempt = RetrySynchronizationManager.getContext().getRetryCount();
            log.warn("task es batch sync excep, attempt[{}], ids[{}], aigcTaskIds[{}]", attempt,
                    org.apache.commons.lang.StringUtils.join(ids, ","),
                    org.apache.commons.lang.StringUtils.join(aigcTaskIds, ","), e);
            throw e;
        }
    }
}
