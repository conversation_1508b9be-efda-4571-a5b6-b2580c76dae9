package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstanceAdjustRecord;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/01/17
 */
@Data
@Builder
public class AigcModelInstanceAdjustRecordCondition implements Serializable {
    private static final long serialVersionUID = -895170079074745156L;

    /**
     * 分页信息
     */
    private Page<AigcModelInstanceAdjustRecord> page;
    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcModelInstanceAdjustRecord> buildQuery() {
        LambdaQueryWrapper<AigcModelInstanceAdjustRecord> queryWrapper = Wrappers.lambdaQuery();
        ;
        return queryWrapper;
    }
}
