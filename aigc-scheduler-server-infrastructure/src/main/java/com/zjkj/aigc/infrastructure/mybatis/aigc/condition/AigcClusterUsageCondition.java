package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 集群资源利用查询条件
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcClusterUsageCondition extends BaseCondition<AigcClusterUsage> {

    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 集群主键id
     */
    private List<Long> clusterIds;

    /**
     * 平台
     */
    private String platform;
    /**
     * 平台集合
     */
    private List<String> platforms;
    /**
     * 数据日期
     */
    private LocalDate dataDate;

    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;

    public LambdaQueryWrapper<AigcClusterUsage> buildQuery() {
        LambdaQueryWrapper<AigcClusterUsage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .eq(Objects.nonNull(clusterId), AigcClusterUsage::getClusterId, clusterId)
                .in(CollectionUtils.isNotEmpty(clusterIds), AigcClusterUsage::getClusterId, clusterIds)
                .eq(StringUtils.isNotBlank(platform), AigcClusterUsage::getPlatform, platform)
                .eq(Objects.nonNull(dataDate), AigcClusterUsage::getDataDate, dataDate)
                .ge(Objects.nonNull(startDate), AigcClusterUsage::getDataDate, startDate)
                .le(Objects.nonNull(endDate), AigcClusterUsage::getDataDate, endDate)
                .in(CollectionUtils.isNotEmpty(platforms), AigcClusterUsage::getPlatform, platforms)
        ;
        return queryWrapper;
    }
}
