package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class AigcModelInfoCondition extends BaseCondition<AigcModelInfo> implements Serializable {
    private static final long serialVersionUID = 364716793745048382L;

    private Long id;

    private Collection<Long> idList;
    /**
     * 名称
     */
    private String name;
    /**
     * 类型
     */
    private String type;
    /**
     * 任务类型
     */
    private String taskType;
    private Collection<String> taskTypes;
    /**
     * 模型名称
     */
    private String modelName;
    private Collection<String> modelNames;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态列表
     */
    private List<Integer> statusList;
    /**
     * 是否在线
     */
    private Boolean online;
    /**
     * 环境类型
     */
    private String envType;
    /**
     * <=预测负载
     */
    private Integer lePreThreshold;
    /**
     * >=预测负载
     */
    private Integer gePreThreshold;

    private Integer limit;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcModelInfo> buildQuery() {
        LambdaQueryWrapper<AigcModelInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(id), AigcModelInfo::getId, id)
                .in(CollectionUtils.isNotEmpty(idList), AigcModelInfo::getId, idList)
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcModelInfo::getId, this.getIds())
                .like(StringUtils.isNotBlank(name), AigcModelInfo::getName, name)
                .eq(StringUtils.isNotBlank(type), AigcModelInfo::getType, type)
                .eq(StringUtils.isNotBlank(taskType), AigcModelInfo::getTaskType, taskType)
                .in(CollectionUtils.isNotEmpty(taskTypes), AigcModelInfo::getTaskType, taskTypes)
                .eq(StringUtils.isNotBlank(modelName), AigcModelInfo::getModelName, modelName)
                .in(CollectionUtils.isNotEmpty(modelNames), AigcModelInfo::getModelName, modelNames)
                .eq(Objects.nonNull(status), AigcModelInfo::getStatus, status)
                .in(CollectionUtils.isNotEmpty(statusList), AigcModelInfo::getStatus, statusList)
                .eq(Objects.nonNull(online), AigcModelInfo::getOnline, online)
                .le(Objects.nonNull(lePreThreshold), AigcModelInfo::getPreThreshold, lePreThreshold)
                .ge(Objects.nonNull(gePreThreshold), AigcModelInfo::getPreThreshold, gePreThreshold)
                .last(Objects.nonNull(limit), "LIMIT " + limit)
        ;
        return queryWrapper;
    }
}
