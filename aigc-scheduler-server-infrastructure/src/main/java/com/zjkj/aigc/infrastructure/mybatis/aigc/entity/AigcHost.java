package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主机表(AigcHost)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcHost extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -23265715830099080L;
    /**
     * 主机ip
     */
    private String hostIp;
    /**
     * 平台
     */
    private String platform;
    /**
     * 区域
     */
    private String region;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 删除版本
     */
    private String delVersion;
}

