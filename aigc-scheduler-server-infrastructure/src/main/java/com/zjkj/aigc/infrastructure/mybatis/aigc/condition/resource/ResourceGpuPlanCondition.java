package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
@Builder
public class ResourceGpuPlanCondition implements Serializable {
    private static final long serialVersionUID = -6363315327299270528L;
    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 云平台集合
     */
    private List<String> platforms;
    /**
     * 不能删除的ID集合
     */
    private List<Long> notDelIds;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<ResourceGpuPlan> buildQuery() {
        LambdaQueryWrapper<ResourceGpuPlan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(month), ResourceGpuPlan::getMonth, month)
                .eq(StringUtils.isNotBlank(platform), ResourceGpuPlan::getPlatform, platform)
                .in(CollUtil.isNotEmpty(platforms), ResourceGpuPlan::getPlatform, platforms)
        ;
        return queryWrapper;
    }

    /**
     * 构建删除条件
     *
     * @return 查询条件
     */
    public LambdaUpdateWrapper<ResourceGpuPlan> buildDelete() {
        LambdaUpdateWrapper<ResourceGpuPlan> updateWrapper = Wrappers.<ResourceGpuPlan>lambdaUpdate()
                .set(ResourceGpuPlan::getIsDeleted, 1)
                .set(ResourceGpuPlan::getDelVersion, System.currentTimeMillis())
                .eq(Objects.nonNull(month), ResourceGpuPlan::getMonth, month)
                .eq(StringUtils.isNotBlank(platform), ResourceGpuPlan::getPlatform, platform)
                .notIn(CollUtil.isNotEmpty(notDelIds), ResourceGpuPlan::getId, notDelIds);;
        return updateWrapper;
    }
}
