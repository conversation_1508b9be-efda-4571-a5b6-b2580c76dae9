package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/11/22
 */
@Accessors(chain = true)
@Data
public class TaskSummaryStatCondition implements Serializable {
    private static final long serialVersionUID = -6938487486823135919L;
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 模型名称
     */
    private String modelName;
    private Collection<String> modelNames;

    /**
     * 任务类型
     */
    private String taskType;
    private Collection<String> taskTypes;

    /**
     * 应用ID
     */
    private String appId;
}
