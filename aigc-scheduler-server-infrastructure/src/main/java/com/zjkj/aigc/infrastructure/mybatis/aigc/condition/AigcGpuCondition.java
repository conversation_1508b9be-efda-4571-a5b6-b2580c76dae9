package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * GPU查询条件
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcGpuCondition extends BaseCondition<AigcGpu> {

    /**
     * 平台
     */
    private String platform;

    /**
     * 型号
     */
    private String model;

    /**
     * 卡数
     */
    private Integer cardCount;

    /**
     * 单卡显存
     */
    private Integer singleCardMemory;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcGpu> buildQuery() {
        LambdaQueryWrapper<AigcGpu> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .in(CollectionUtils.isNotEmpty(this.getIds()), AigcGpu::getId, this.getIds())
                .eq(StringUtils.hasText(platform), AigcGpu::getPlatform, platform)
                .eq(StringUtils.hasText(model), AigcGpu::getModel, model)
                .eq(Objects.nonNull(cardCount), AigcGpu::getCardCount, cardCount)
                .eq(Objects.nonNull(singleCardMemory), AigcGpu::getSingleCardMemory, singleCardMemory)
                .eq(StringUtils.hasText(creatorName), AigcGpu::getCreatorName, creatorName);
        return queryWrapper;
    }
}
