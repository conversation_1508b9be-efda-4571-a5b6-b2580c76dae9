package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * ai任务表(AigcTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
public interface AigcTaskMapper extends BaseMapper<AigcTask> {

    /**
     * 获取任务状态
     *
     * @param condition 条件
     * @return 返回结果
     */
    List<AigcTask> queryTaskStateGroup(@Param("condition") AigcTaskCondition condition);

    /**
     * 根据任务状态查询任务
     *
     * @param taskState 任务状态
     * @param minTime   最小时间
     * @param handler   handler
     */
    void streamByTaskState(@Param("taskState") Integer taskState,
                           @Param("minTime") LocalDateTime minTime,
                           ResultHandler<AigcTask> handler);

    /**
     * 根据条件查询任务
     *
     * @param condition 条件
     * @param handler   handler
     */
    void streamByCondition(@Param("condition") AigcTaskCondition condition, ResultHandler<AigcTask> handler);

    /**
     * 获取任务排名
     *
     * @param condition condition
     * @return 返回结果
     */
    List<AigcTask> getTaskRank(@Param("condition") AigcTaskCondition condition);

    /**
     * 根据业务id删除
     *
     * @param businessId 业务id
     */
    void actualDelByBusinessId(@Param("businessId") String businessId);

    /**
     * 根据id删除
     *
     * @param ids id
     */
    void actualDelById(@Param("ids") List<Long> ids);
}

