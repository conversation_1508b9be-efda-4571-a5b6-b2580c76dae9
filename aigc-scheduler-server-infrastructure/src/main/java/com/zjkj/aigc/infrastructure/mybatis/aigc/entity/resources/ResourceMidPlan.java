package com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 中间件资源规划表(ResourceMidPlan)实体类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Data
public class ResourceMidPlan extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 5846937276535464329L;

    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 价格说明
     */
    private String priceRemark;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;
    /**
     * 删除版本号
     */
    private String delVersion;
}
