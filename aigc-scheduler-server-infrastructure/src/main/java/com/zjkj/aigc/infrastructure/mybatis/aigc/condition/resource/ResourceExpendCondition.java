package com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceExpend;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Data
@Builder
public class ResourceExpendCondition implements Serializable {
    private static final long serialVersionUID = -5320292554943536386L;
    /**
     * 支出月份
     */
    private Integer month;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<ResourceExpend> buildQuery() {
        LambdaQueryWrapper<ResourceExpend> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(month), ResourceExpend::getMonth, month)
        ;
        return queryWrapper;
    }
}
