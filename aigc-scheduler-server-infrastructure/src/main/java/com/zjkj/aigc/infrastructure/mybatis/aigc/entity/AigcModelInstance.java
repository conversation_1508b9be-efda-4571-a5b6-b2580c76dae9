package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.zjkj.aigc.common.constant.StringPool;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模型实例表(AigcModelInstance)实体类
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Data
@Accessors(chain = true)
public class AigcModelInstance implements Serializable {
    private static final long serialVersionUID = 821885515833076612L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * pod名称
     */
    private String podName;
    /**
     * pod命名空间
     */
    private String podNamespace;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态: 0-空闲，1-运行
     */
    private Integer podStatus;
    /**
     * 状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * sdk版本
     */
    private String sdkVersion;
    /**
     * 来源ip
     */
    private String sourceIp;
    /**
     * 环境类型
     */
    private String envType;
    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 获取pod环境
     * @param splitChar 分隔符
     * @return 项目名称和环境名称
     */
    public String[] getPodEnv(String splitChar) {
        if (!StringUtils.hasText(podNamespace) || !StringUtils.hasText(splitChar)) {
            return null;
        }
        String[] split = podNamespace.split(splitChar);
        if (split.length < 2) {
            return null;
        }
        return split;
    }

    /**
     * 获取服务名称
     * @return pod名称
     */
    public String getServiceName() {
        if (!StringUtils.hasText(podName)) {
            return null;
        }

        int secondLastHyphenIndex = podName.lastIndexOf(StringPool.DASH, podName.lastIndexOf(StringPool.DASH) - 1);
        return podName.substring(0, secondLastHyphenIndex);
    }
}

