package com.zjkj.aigc.infrastructure.mybatis.aigc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.AigcClusterMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 集群数据访问对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Repository
@RequiredArgsConstructor
public class AigcClusterDao extends AbstractBaseDao<AigcClusterMapper, AigcCluster> {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcCluster> queryPage(AigcClusterCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }
}
