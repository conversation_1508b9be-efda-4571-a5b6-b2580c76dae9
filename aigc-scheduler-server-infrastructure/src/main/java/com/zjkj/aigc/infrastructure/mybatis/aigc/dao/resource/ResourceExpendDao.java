package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource;

import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceExpend;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.resources.ResourceExpendMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 资源支出(ResourceExpend)Dao层
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ResourceExpendDao extends AbstractBaseDao<ResourceExpendMapper, ResourceExpend> {

}
