package com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AbstractBaseDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.dynamic.AigcDynamicConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 动态配置表(AigcDynamicConfig)Dao层
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AigcDynamicConfigDao extends AbstractBaseDao<AigcDynamicConfigMapper, AigcDynamicConfig> {

    /**
     * 根据模型ID查询
     *
     * @param modelId 模型ID
     * @return 动态配置
     */
    public AigcDynamicConfig queryByModelId(Long modelId) {
        return this.lambdaQuery()
                .eq(AigcDynamicConfig::getModelId, modelId)
                .one();
    }

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    public Page<AigcDynamicConfig> queryPage(AigcDynamicConfigCondition condition) {
        return page(condition.getPage(), condition.buildQuery());
    }

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    public List<AigcDynamicConfig> queryList(AigcDynamicConfigCondition condition) {
        return list(condition.buildQuery());
    }


}
