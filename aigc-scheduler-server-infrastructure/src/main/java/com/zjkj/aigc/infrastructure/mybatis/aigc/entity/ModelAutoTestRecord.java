package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 模型测试批次表(ModelAutoTestRecord)实体类
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("model_auto_test_record")
public class ModelAutoTestRecord extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -68144282610931786L;
    /**
     * 测试编号
     */
    private String name;
    /**
     * 测试编码
     */
    private String testNo;
    /**
     * 测试业务ID
     */
    private String businessId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 创建的任务数
     */
    private Long batchTaskCount;
    /**
     * 成功的任务数
     */
    private Long successTaskCount;
    /**
     * 平均耗时ms
     */
    private Long avgElapsedTime;
    /**
     * 保留任务
     */
    @TableField(value = "is_retain_task")
    private Boolean retainTask;
    /**
     * 参数类型: single-单个，batch-批量
     */
    private String paramType;
    /**
     * 模型参数
     */
    private String modelParams;
    /**
     * 状态，0：待执行，1：执行中，2：已完成，3：报告已生成，4:取消，5:失败
     */
    private Integer status;
    /**
     * 测试开始时间
     */
    private LocalDateTime testStartTime;
    /**
     * 测试取消时间
     */
    private LocalDateTime testCancelTime;
    /**
     * 测试完成时间
     */
    private LocalDateTime testCompletionTime;
}

