package com.zjkj.aigc.infrastructure.mybatis.aigc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ai任务表统计表(天)(AigcTaskStatDay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface AigcTaskStatDayMapper extends BaseMapper<AigcTaskStatDay> {

    /**
     * 按日期分组统计
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return Page<AigcTaskStatDay>
     */
    Page<AigcTaskStatDay> pageByGroupModel(Page<AigcTaskStatDay> page, @Param("query") AigcTaskStatCondition query);

    /**
     * 按天合计
     *
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByGroupStateDate(@Param("query") AigcTaskStatCondition query);

    /**
     * 按模型分组合计
     *
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByGroupModel(@Param("query") AigcTaskStatCondition query);

    /**
     * 按模型类型合计
     *
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByModelType(@Param("query") AigcTaskStatCondition query);

    /**
     * 合计
     *
     * @param query 查询条件
     * @return AigcTaskStatDay
     */
    AigcTaskStatDay sumTotal(@Param("query") AigcTaskStatCondition query);

    /**
     * 按模型集合
     *
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> listByModelStateDate(@Param("query") AigcTaskSummaryCondition query);

    /**
     * 按模型类型合计
     *
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByGroupModelStatDate(@Param("query") AigcTaskStatCondition query);
}

