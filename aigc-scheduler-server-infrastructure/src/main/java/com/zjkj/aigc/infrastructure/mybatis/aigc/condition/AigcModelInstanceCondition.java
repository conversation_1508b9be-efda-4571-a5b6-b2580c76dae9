package com.zjkj.aigc.infrastructure.mybatis.aigc.condition;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AigcModelInstanceCondition extends BaseCondition<AigcModelInstance> implements Serializable {
    private static final long serialVersionUID = 6933581498125856773L;
    /**
     * pod名称
     */
    private String podName;
    /**
     * pod命名空间
     */
    private List<String> podNamespaces;
    /**
     * 节点ip
     */
    private List<String> nodeIps;
    /**
     * 任务类型
     */
    private String taskType;
    private Collection<String> taskTypes;
    /**
     * 模型名称
     */
    private String modelName;
    private Collection<String> modelNames;
    /**
     * 模型id
     */
    private Collection<Long> modelIds;
    /**
     * 环境类型
     */
    private String envType;
    /**
     * pod状态: 0-空闲，1-运行
     */
    private Integer podStatus;
    /**
     * 合法状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 健康状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 集群id
     */
    private Collection<Long> clusterIds;
    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public LambdaQueryWrapper<AigcModelInstance> buildQuery() {
        LambdaQueryWrapper<AigcModelInstance> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(podName), AigcModelInstance::getPodName, podName)
                .in(CollectionUtils.isNotEmpty(podNamespaces), AigcModelInstance::getPodNamespace, podNamespaces)
                .in(CollectionUtils.isNotEmpty(nodeIps), AigcModelInstance::getNodeIp, nodeIps)
                .in(CollectionUtils.isNotEmpty(taskTypes), AigcModelInstance::getTaskType, taskTypes)
                .in(CollectionUtils.isNotEmpty(modelNames), AigcModelInstance::getModelName, modelNames)
                .eq(StringUtils.isNotBlank(taskType), AigcModelInstance::getTaskType, taskType)
                .eq(StringUtils.isNotBlank(modelName), AigcModelInstance::getModelName, modelName)
                .in(CollectionUtils.isNotEmpty(modelIds), AigcModelInstance::getModelId, modelIds)
                .eq(StringUtils.isNotBlank(envType), AigcModelInstance::getEnvType, envType)
                .eq(Objects.nonNull(podStatus), AigcModelInstance::getPodStatus, podStatus)
                .eq(Objects.nonNull(status), AigcModelInstance::getStatus, status)
                .eq(Objects.nonNull(healthStatus), AigcModelInstance::getHealthStatus, healthStatus)
                .eq(Objects.nonNull(modelId), AigcModelInstance::getModelId, modelId)
                .eq(Objects.nonNull(clusterId), AigcModelInstance::getClusterId, clusterId)
                .in(CollectionUtils.isNotEmpty(clusterIds), AigcModelInstance::getClusterId, clusterIds)
        ;
        return queryWrapper;
    }
}
