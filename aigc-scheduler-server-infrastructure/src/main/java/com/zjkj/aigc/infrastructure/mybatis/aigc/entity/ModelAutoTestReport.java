package com.zjkj.aigc.infrastructure.mybatis.aigc.entity;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 模型测试报告表(ModelAutoTestReport)实体类
 *
 * <AUTHOR>
 * @since 2024-10-17
 */
@Data
@TableName("model_auto_test_report")
public class ModelAutoTestReport implements Serializable {
    private static final long serialVersionUID = 442112346894654880L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 测试编码
     */
    private String testNo;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 模型服务名称
     */
    private String serviceName;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 任务信息平均任务时间 、平均1h任务数，josn格式
     */
    private String taskInfo;
    /**
     * 显卡型号
     */
    private String gpuType;
    /**
     * 显卡调度类型：独占、虚拟
     */
    private String gpuDispatchType;
    /**
     * CPU均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式
     */
    private String cpuInfo;
    /**
     * 内存均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式
     */
    private String memoryInfo;
    /**
     * GPU均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式
     */
    private String gpuInfo;
    /**
     * GPU 信息明细
     */
    private String gpuInfoDetails;
    /**
     * 资源限制，josn格式
     */
    private String resourceLimits;
    /**
     * 状态，0：待生成；1：已生成
     */
    private Integer reportStatus;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    /**
     * 生成时间
     */
    private LocalDateTime genTime;
    /**
     * 逻辑删除 0 否 1是
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;
}

