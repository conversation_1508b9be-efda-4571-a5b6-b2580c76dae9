# AIGC SDK 国际化使用指南

## 概述

AIGC SDK 支持国际化功能，可以根据语言设置返回对应语言的错误消息和提示信息。

## 支持的语言

- `zh_CN`: 中文（简体）- 默认语言
- `en_US`: 英文

## 使用方式

### 1. 设置全局默认语言

适用于整个应用使用同一种语言的场景：

```java
import com.zjkj.aigc.client.AigcClientHelper;

// 设置全局默认语言为英文
AigcClientHelper.setGlobalLocale("en_US");

// 后续所有SDK调用都会使用英文
DataResponse<CreateAiTaskResp> response = aiTaskClient.createAiTask(request);
```

### 2. 设置当前线程语言

适用于多线程环境，不同线程需要使用不同语言的场景：

```java
// 设置当前线程使用中文
AigcClientHelper.setCurrentLocale("zh_CN");

// 当前线程的SDK调用会使用中文
DataResponse<CreateAiTaskResp> response = aiTaskClient.createAiTask(request);

// 建议在线程结束时清理
AigcClientHelper.clearCurrentLocale();
```

### 3. 临时指定语言执行操作（推荐）

适用于需要临时切换语言的场景，自动管理语言环境：

```java
// 单个操作
DataResponse<CreateAiTaskResp> response = AigcClientHelper.executeWithLocale("en_US", 
    () -> aiTaskClient.createAiTask(request));

// 批量操作
AigcClientHelper.executeWithLocale("zh_CN", () -> {
    aiTaskClient.createAiTask(request1);
    aiTaskClient.getAiTask(request2);
    aiTaskClient.cancelAiTask(request3);
});
```

### 4. 便捷方法

```java
// 使用中文执行操作
DataResponse<CreateAiTaskResp> response = AigcClientHelper.executeWithChinese(
    () -> aiTaskClient.createAiTask(request));

// 使用英文执行操作
DataResponse<CreateAiTaskResp> response = AigcClientHelper.executeWithEnglish(
    () -> aiTaskClient.createAiTask(request));
```

## 语言优先级

SDK会按以下优先级确定使用的语言：

1. **临时语言设置**：`executeWithLocale()` 方法中指定的语言
2. **线程级语言设置**：`setCurrentLocale()` 设置的语言
3. **全局默认语言**：`setGlobalLocale()` 设置的语言
4. **系统默认语言**：`zh_CN`（中文）

## 完整示例

```java
public class AigcSdkExample {
    
    @Autowired
    private AiTaskClient aiTaskClient;
    
    public void example() {
        // 1. 设置全局默认语言
        AigcClientHelper.setGlobalLocale("en_US");
        
        // 2. 使用全局语言（英文）
        DataResponse<CreateAiTaskResp> response1 = aiTaskClient.createAiTask(request);
        
        // 3. 临时使用中文
        DataResponse<GetAiTaskResp> response2 = AigcClientHelper.executeWithChinese(
            () -> aiTaskClient.newGetAiTask(getRequest));
        
        // 4. 批量操作使用指定语言
        AigcClientHelper.executeWithLocale("zh_CN", () -> {
            aiTaskClient.createAiTask(request1);
            aiTaskClient.createAiTask(request2);
            aiTaskClient.getTaskStatus(statusRequest);
        });
        
        // 5. 为当前线程设置特定语言
        AigcClientHelper.setCurrentLocale("en_US");
        DataResponse<String> response3 = aiTaskClient.resetTaskState(resetRequest);
        
        // 6. 清理当前线程语言设置
        AigcClientHelper.clearCurrentLocale();
    }
}
```

## 注意事项

1. **线程安全**：语言设置是线程安全的，不同线程的语言设置互不影响
2. **内存管理**：建议在长期运行的线程中使用 `clearCurrentLocale()` 清理语言设置
3. **Web环境兼容**：在Web应用中使用SDK时，会自动从HTTP请求头获取语言信息
4. **默认行为**：如果不设置任何语言，默认使用中文（zh_CN）

## 错误处理

如果设置了不支持的语言，SDK会自动回退到默认语言（中文）：

```java
// 设置不支持的语言
AigcClientHelper.setGlobalLocale("ja_JP"); // 日文（不支持）

// SDK会自动使用默认语言（中文）
DataResponse<CreateAiTaskResp> response = aiTaskClient.createAiTask(request);
```
