package com.zjkj.aigc.task.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/7/16
 */
@Data
@Accessors(chain = true)
public class AiTaskStatusReq implements Serializable {
    private static final long serialVersionUID = 3597400989795661086L;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 业务id
     */
    @NotBlank(message = "{task.priority.business.id.required}")
    private String businessId;
}
