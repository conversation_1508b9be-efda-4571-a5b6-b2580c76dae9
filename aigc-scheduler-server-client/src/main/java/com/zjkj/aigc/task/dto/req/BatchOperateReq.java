package com.zjkj.aigc.task.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/16
 */
@Data
@Accessors(chain = true)
public class BatchOperateReq implements Serializable {
    private static final long serialVersionUID = -4514996670474230289L;
    /**
     * 任务id
     */
    @NotEmpty(message = "{validation.task.ids.empty}")
    private List<String> taskIds;
    /**
     * 业务id
     */
    @NotNull(message = "{task.priority.business.id.required}")
    private String businessId;
}
