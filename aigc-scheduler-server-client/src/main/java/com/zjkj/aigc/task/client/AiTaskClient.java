package com.zjkj.aigc.task.client;

import com.zjkj.aigc.config.FeignClientConfiguration;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.task.dto.req.AiTaskStatusReq;
import com.zjkj.aigc.task.dto.req.BaseAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchOperateReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;
import com.zjkj.aigc.task.dto.resp.AiTaskStatusResp;
import com.zjkj.aigc.task.dto.resp.OperateFailedResp;
import com.zjkj.aigc.task.dto.resp.CreateAiTaskResp;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 任务客户端
 * <AUTHOR>
 * @since 2024/5/30
 */
@FeignClient(name = "AiTaskClient",
        url = "${ai.csrs.server.url:}",
        path = "/aigc/v1/task",
        configuration = FeignClientConfiguration.class)
public interface AiTaskClient {

    /**
     * 创建任务
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 请求参数类型
     */
    @PostMapping("/createAiTask")
    <T> DataResponse<CreateAiTaskResp> createAiTask(@RequestBody CreateAiTaskReq<T> req);

    /**
     * 异步批量创建任务
     * @param reqList 请求参数
     * @return 返回结果
     * @param <T> 请求参数类型
     */
    @PostMapping("/batch/createAiTask/async")
    <T> DataResponse<String> asyncBatchCreateAiTask(@RequestBody List<CreateAiTaskReq<T>> reqList);

    /**
     * 查询任务信息
     * @param req 请求参数
     * @return 返回结果
     *  单个任务查询，请求taskId:"taskId"，data返回{}: GetAiTaskResp<T, U>
     *  多个任务查询，请求taskId:"taskId1,taskId2"，data返回[{},{}]: List<GetAiTaskResp<T, U>>
     * @param <T> 返回参数类型
     */
    @Deprecated
    @PostMapping("/getAiTask")
    <T> DataResponse<T> getAiTask(@RequestBody GetAiTaskReq req);

    /**
     * 查询任务信息
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 模型入参类型
     * @param <U> 模型出参类型
     */
    @PostMapping("/new/getAiTask")
    <T, U> DataResponse<GetAiTaskResp<T, U>> newGetAiTask(@RequestBody GetAiTaskReq req);

    /**
     * 批量查询任务信息
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 返回参数类型
     */
    @PostMapping("/batch/getAiTask")
    <T, U> DataResponse<List<GetAiTaskResp<T, U>>> batchGetAiTask(@RequestBody BatchGetAiTaskReq req);

    /**
     * 获取任务状态统计
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/getTaskStatus")
    DataResponse<List<AiTaskStatusResp>> getTaskStatus(@RequestBody AiTaskStatusReq req);

    /**
     *  取消任务
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/cancelAiTask")
    DataResponse<OperateFailedResp> cancelAiTask(@RequestBody BaseAiTaskReq req);

    /**
     * 重置任务状态
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/resetTaskState")
    DataResponse<String> resetTaskState(@RequestBody BaseAiTaskReq req);

    /**
     * 批量重置任务状态
     *
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/batch/resetTaskState")
    DataResponse<OperateFailedResp> batchResetTaskState(@RequestBody BatchOperateReq req);

    /**
     * 同步推理任务
     * 实时返回推理结果
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 请求参数类型
     * @param <U> 返回参数类型
     */
    @PostMapping("/sync/inference")
    <T, U> DataResponse<GetAiTaskResp<T, U>> syncInferenceTask(@RequestBody CreateAiTaskReq<T> req);
}
