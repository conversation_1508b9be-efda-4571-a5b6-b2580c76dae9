package com.zjkj.aigc.task.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/16
 */
@Data
@Accessors(chain = true)
public class BatchGetAiTaskReq implements Serializable {
    private static final long serialVersionUID = -3147837734013423036L;
    /**
     * 任务id
     */
    @NotEmpty(message = "{validation.task.ids.empty}")
    private List<String> taskIds;
    /**
     * 业务id
     */
    @NotBlank(message = "{task.priority.business.id.required}")
    private String businessId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     *  批量开启
     */
    @JsonIgnore
    private boolean batchOpen;
}
