package com.zjkj.aigc.task.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiTaskStatusResp implements Serializable {
    private static final long serialVersionUID = -2007366264473987273L;

    /**
     *  任务状态 0-待执行，1-执行中，2-成功，3-失败，4-取消，5-超时失败
     */
    private Integer status;
    /**
     * 数量
     */
    private Long count;
}
