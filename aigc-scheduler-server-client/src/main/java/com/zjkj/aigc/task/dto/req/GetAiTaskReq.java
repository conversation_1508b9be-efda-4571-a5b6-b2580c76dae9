package com.zjkj.aigc.task.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/7/16
 */
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class GetAiTaskReq extends BaseAiTaskReq implements Serializable {
    private static final long serialVersionUID = 3289900586915959200L;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     *  批量开启
     */
    @JsonIgnore
    private boolean batchOpen;
}
