package com.zjkj.aigc.task.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
@Accessors(chain = true)
public class BaseAiTaskReq implements Serializable {
    private static final long serialVersionUID = 8351419809403830623L;
    /**
     * 任务id
     */
    @NotBlank(message = "{task.model.callback.task.id.required}")
    private String taskId;
    /**
     * 业务id
     */
    @NotBlank(message = "{task.priority.business.id.required}")
    private String businessId;
}
