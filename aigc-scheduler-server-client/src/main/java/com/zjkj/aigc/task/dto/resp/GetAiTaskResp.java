package com.zjkj.aigc.task.dto.resp;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zjkj.aigc.config.serializer.TimestampDeserializer;
import com.zjkj.aigc.config.serializer.TimestampSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetAiTaskResp<T, U> implements Serializable {
    private static final long serialVersionUID = 3282190713423081881L;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 业务id
     */
    private String businessId;
    /**
     *任务状态 0-待执行，1-执行中，2-成功，3-失败，4-取消，5-超时失败
     */
    private Integer state;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 进度
     */
    private Integer progress;

    /**
     * 队列排名
     */
    private Integer rank;
    /**
     * 输入参数
     */
    private T inputParams;
    /**
     * 输出参数
     */
    private U output;
    /**
     *  信息
     */
    private String message;
    /**
     * 创建时间
     */
    @JsonSerialize(using = TimestampSerializer.class)
    @JsonDeserialize(using = TimestampDeserializer.LocalDateTimeDeserializer.class)
    private LocalDateTime createdTime;
    /**
     * 任务开始时间
     */
    @JsonSerialize(using = TimestampSerializer.class)
    @JsonDeserialize(using = TimestampDeserializer.LocalDateTimeDeserializer.class)
    private LocalDateTime taskStartTime;
    /**
     * 任务完成时间
     */
    @JsonSerialize(using = TimestampSerializer.class)
    @JsonDeserialize(using = TimestampDeserializer.LocalDateTimeDeserializer.class)
    private LocalDateTime taskCompletionTime;

}
