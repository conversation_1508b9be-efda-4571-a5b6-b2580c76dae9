package com.zjkj.aigc.task.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Data
@Accessors(chain = true)
public class CreateAiTaskReq<T>  implements Serializable {
    private static final long serialVersionUID = 5124471980312448604L;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 业务id
     */
    @NotBlank(message = "{task.priority.business.id.required}")
    private String businessId;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;
    /**
     * 模型参数
     */
    @NotNull(message = "{validation.params.null}")
    private T params;
    /**
     * 任务来源（可选）
     */
    private String taskSource;
    /**
     * 优先级（可选）
     * 数字越小优先级越高，默认 0
     */
    private Integer taskPriority;
    /**
     * 是否任务批量（可选）
     */
    @Range(min = 0, max = 1, message = "{validation.task.batch.range}")
    private Integer taskBatch;
    /**
     * 回调地址(可选)
     */
    private String notifyUrl;
}
