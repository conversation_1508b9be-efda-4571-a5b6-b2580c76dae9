package com.zjkj.aigc.base.dto;

import lombok.Data;
import java.io.Serializable;

/**
 *  数据返回包装类
 * <AUTHOR>
 * @since 2024/5/29
 */
@Data
public class DataResponse<T> implements Serializable {
    private static final long serialVersionUID = -7720475100814037917L;
    private boolean successful;
    private Integer code;
    private String message;
    private T data;

    public static final Integer OK_CODE = 2000000;

    public static final Integer FAIL_CODE = 5000000;

    private static final String OK_MSG = "Request successful";

    public DataResponse() {
    }

    public static <T> DataResponse<T> unAuthorized() {
        DataResponse<T> rsp = new DataResponse<>();
        rsp.setSuccessful(Boolean.FALSE);
        rsp.setCode(401);
        rsp.setMessage("Unauthorized");
        return rsp;
    }

    public static <T> DataResponse<T> ok() {
        DataResponse<T> rsp = new DataResponse<>();
        rsp.setSuccessful(Boolean.TRUE);
        rsp.setCode(OK_CODE);
        rsp.setMessage(OK_MSG);
        return rsp;
    }

    public static <T> DataResponse<T> ok(T data) {
        DataResponse<T> rsp = new DataResponse<>();
        rsp.setSuccessful(Boolean.TRUE);
        rsp.setCode(OK_CODE);
        rsp.setMessage(OK_MSG);
        rsp.setData(data);
        return rsp;
    }

    public static <T> DataResponse<T> fail(String failMsg) {
        return fail(FAIL_CODE, failMsg);
    }

    public static <T> DataResponse<T> fail(Integer code, String failMsg) {
        DataResponse<T> rsp = new DataResponse<>();
        rsp.setSuccessful(Boolean.FALSE);
        rsp.setCode(code);
        rsp.setMessage(failMsg);
        return rsp;
    }


}

