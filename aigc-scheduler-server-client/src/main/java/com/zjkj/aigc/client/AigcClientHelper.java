package com.zjkj.aigc.client;

import com.zjkj.aigc.config.AigcClientLocaleContext;
import org.springframework.util.StringUtils;

import java.util.function.Supplier;

/**
 * AIGC SDK客户端工具类
 * 提供便捷的语言环境管理功能
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class AigcClientHelper {

    /**
     * 设置全局默认语言
     * 影响所有未单独设置语言的SDK调用
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     */
    public static void setGlobalLocale(String locale) {
        AigcClientLocaleContext.setGlobalLocale(locale);
    }

    /**
     * 获取全局默认语言
     * 
     * @return 全局默认语言标识
     */
    public static String getGlobalLocale() {
        return AigcClientLocaleContext.getGlobalLocale();
    }

    /**
     * 设置当前线程的语言环境
     * 只影响当前线程的SDK调用
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     */
    public static void setCurrentLocale(String locale) {
        AigcClientLocaleContext.setLocale(locale);
    }

    /**
     * 获取当前线程的语言环境
     * 
     * @return 当前线程的语言标识
     */
    public static String getCurrentLocale() {
        return AigcClientLocaleContext.getLocale();
    }

    /**
     * 清除当前线程的语言环境设置
     * 建议在线程结束时调用，避免内存泄漏
     */
    public static void clearCurrentLocale() {
        AigcClientLocaleContext.clear();
    }

    /**
     * 在指定语言环境下执行操作（有返回值）
     * 自动管理语言环境的设置和清理
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作的返回值
     * 
     * @example
     * <pre>
     * DataResponse<CreateAiTaskResp> response = AigcClientHelper.executeWithLocale("en_US", 
     *     () -> aiTaskClient.createAiTask(request));
     * </pre>
     */
    public static <T> T executeWithLocale(String locale, Supplier<T> operation) {
        if (!StringUtils.hasText(locale)) {
            // 如果语言为空，直接执行操作
            return operation.get();
        }

        // 保存原始语言环境
        String originalLocale = AigcClientLocaleContext.hasThreadLocale() 
            ? AigcClientLocaleContext.getLocale() 
            : null;
        
        try {
            // 设置临时语言环境
            AigcClientLocaleContext.setLocale(locale);
            
            // 执行操作
            return operation.get();
            
        } finally {
            // 恢复原始语言环境
            if (originalLocale != null) {
                AigcClientLocaleContext.setLocale(originalLocale);
            } else {
                AigcClientLocaleContext.clear();
            }
        }
    }

    /**
     * 在指定语言环境下执行操作（无返回值）
     * 自动管理语言环境的设置和清理
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     * @param operation 要执行的操作
     * 
     * @example
     * <pre>
     * AigcClientHelper.executeWithLocale("zh_CN", () -> {
     *     aiTaskClient.createAiTask(request1);
     *     aiTaskClient.getAiTask(request2);
     * });
     * </pre>
     */
    public static void executeWithLocale(String locale, Runnable operation) {
        executeWithLocale(locale, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * 在中文环境下执行操作（有返回值）
     * 
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作的返回值
     */
    public static <T> T executeWithChinese(Supplier<T> operation) {
        return executeWithLocale("zh_CN", operation);
    }

    /**
     * 在中文环境下执行操作（无返回值）
     * 
     * @param operation 要执行的操作
     */
    public static void executeWithChinese(Runnable operation) {
        executeWithLocale("zh_CN", operation);
    }

    /**
     * 在英文环境下执行操作（有返回值）
     * 
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作的返回值
     */
    public static <T> T executeWithEnglish(Supplier<T> operation) {
        return executeWithLocale("en_US", operation);
    }

    /**
     * 在英文环境下执行操作（无返回值）
     * 
     * @param operation 要执行的操作
     */
    public static void executeWithEnglish(Runnable operation) {
        executeWithLocale("en_US", operation);
    }
}
