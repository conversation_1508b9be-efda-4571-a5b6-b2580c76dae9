package com.zjkj.aigc.dashvector.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 根据向量检索相似性doc信息请求类
 *
 * <AUTHOR>
 * @since 2025/04/22
 */
@Data
@Accessors(chain = true)
public class QueryDocReq implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 向量collectionName
     */
    @NotBlank(message = "collectionName not be empty")
    private String collectionName;

    /**
     * 主键ID，
     */
    private String id;

    /**
     * topK排名信息，默认返回top 10
     */
    @NotNull(message = "topK not be NULL")
    private Integer topK = 10;

    /**
     * 是否返回向量信息，默认不返回，如果需要返回请设置为true
     */
    private Boolean includeVector = false;

    /**
     * 向量数组，当主键id字段传了值，则会以主键ID对应的doc的向量进行相似性检测
     * 只有当id字段为空时，次字段有值，才会以该字段进行相似性检测
     */
    private List<Float> vectorList;

    /**
     * 条件过滤列表，其中的值需要自行拼接,例如： age > 10 and name = 'xxx'
     */
    private String filter;

    /**
     * 返回字段集合，默认返回所有字段；如果需要按需查询
     * 请填写需要返回的字段信息
     */
    private List<String> returnFields = new ArrayList<>();
}
