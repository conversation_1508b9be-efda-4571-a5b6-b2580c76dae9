package com.zjkj.aigc.dashvector.dto.resp;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Setter
@Getter
public class DocResp {
    private String id;
    private Vector vector;
    private Map<String, Object> fields;
    private float score;
    private Map<Long, Float> sparseVector;
    private Map<String, Vector> vectors;

    @Setter
    @Getter
    public static class Vector{
        private List<? extends Number> value;
    }
}
