package com.zjkj.aigc.dashvector.client;


import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.config.FeignClientConfiguration;
import com.zjkj.aigc.dashvector.dto.req.CreateCollectionReq;
import com.zjkj.aigc.dashvector.dto.req.CreateDocReq;
import com.zjkj.aigc.dashvector.dto.req.DeleteDocReq;
import com.zjkj.aigc.dashvector.dto.req.QueryDocReq;
import com.zjkj.aigc.dashvector.dto.resp.DocOperateResp;
import com.zjkj.aigc.dashvector.dto.resp.DocResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 阿里云向量检索服务客户端
 * <AUTHOR>
 * @since 2025/4/24
 */
@FeignClient(name = "DashVectorClient",
        url = "${ai.csrs.server.url:}",
        path = "/aigc/dashvector",
        configuration = FeignClientConfiguration.class)
public interface DashVectorClient {

    @PostMapping("/createCollection")
    DataResponse<Void> createCollection(@RequestBody CreateCollectionReq req);

    @PostMapping("/createDoc")
    DataResponse<List<DocOperateResp>> createDoc(@RequestBody CreateDocReq req);

    @PostMapping("/deleteDoc")
    DataResponse<List<DocOperateResp>> createDoc(@RequestBody DeleteDocReq req);

    @PostMapping("/queryDoc")
    DataResponse<List<DocResp>> queryDoc(@RequestBody QueryDocReq req);

}
