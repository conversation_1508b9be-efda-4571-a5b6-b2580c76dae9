package com.zjkj.aigc.dashvector.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */
@Data
@Accessors(chain = true)
public class CreateCollectionReq  implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 向量collectionName
     */
    @NotBlank(message = "collectionName not be empty")
    private String collectionName;

    /**
     * 向量维度，默认为1792
     */
    private int dimension = 1792;

    /**
     * 距离度量方式,  0:euclidean, 1:dotproduct, 2:cosine,
     * 默认 dotproduct
     */
    private int metric = 2;

    /**
     * 数据类型，0:FLOAT, 1:INT
     */
    private int  dataType = 0;

    /**
     * 字段集合map，
     * key为字段名称，
     * value为字段类型 0:BOOL, 1:STRING, 2:INT, 3:FLOAT, 4:LONG
     */
    private Map<String,Integer> filedsMap;
}
