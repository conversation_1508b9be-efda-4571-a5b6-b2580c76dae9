package com.zjkj.aigc.dashvector.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  用于dashvector向量检索查询的 filter条件拼接
 *
 *  <AUTHOR>
 *  @since 2025/4/24
 * 支持分组的SQL条件构建器（直接拼接值）
 *
 *  dashvector本身只支持 大于，大于等于，小于，小于等于，等于，
 *  多个条件之间支持AND、OR和()进行连接，最大允许10240个字符。
 */
public class FilterConditionBuilder {
    private final List<Object> segments = new ArrayList<>();
    private boolean useOr = false;

    private static class ConditionNode {
        final String condition;

        ConditionNode(String condition) {
            this.condition = condition;
        }
    }

    public synchronized FilterConditionBuilder and() {
        this.useOr = false;
        return this;
    }

    public synchronized FilterConditionBuilder or() {
        this.useOr = true;
        return this;
    }

    public synchronized FilterConditionBuilder eq(String column, Object value) {
        if (value != null) {
            return addCondition(column + " = " + formatValue(value));
        }
        return this;
    }

    public synchronized FilterConditionBuilder ne(String column, Object value) {
        if (value != null) {
            return addCondition(column + " != " + formatValue(value));
        }
        return this;
    }

    public synchronized FilterConditionBuilder gt(String column, Object value) {
        if (value != null) {
            return addCondition(column + " > " + formatValue(value));
        }
        return this;
    }

    public synchronized FilterConditionBuilder lt(String column, Object value) {
        if (value != null) {
            return addCondition(column + " < " + formatValue(value));
        }
        return this;
    }

    public synchronized FilterConditionBuilder ge(String column, Object value) {
        if (value != null) {
            return addCondition(column + " >= " + formatValue(value));
        }
        return this;
    }

    public synchronized FilterConditionBuilder le(String column, Object value) {
        if (value != null) {
            return addCondition(column + " <= " + formatValue(value));
        }
        return this;
    }

    @Deprecated
    public synchronized FilterConditionBuilder like(String column, String value) {
        if (value != null && !value.isEmpty()) {
            return addCondition(column + " LIKE '%" + escapeSql(value) + "%'");
        }
        return this;
    }

    @Deprecated
    public synchronized FilterConditionBuilder in(String column, List<?> values) {
        if (values != null && !values.isEmpty()) {
            String valuesStr = values.stream()
                    .map(this::formatValue)
                    .collect(Collectors.joining(", "));
            return addCondition(column + " IN (" + valuesStr + ")");
        }
        return this;
    }

    @Deprecated
    public synchronized FilterConditionBuilder between(String column, Object start, Object end) {
        if (start != null && end != null) {
            return addCondition(column + " BETWEEN " + formatValue(start) + " AND " + formatValue(end));
        }
        return this;
    }

    @Deprecated
    public synchronized FilterConditionBuilder isNull(String column) {
        return addCondition(column + " IS NULL");
    }

    @Deprecated
    public synchronized FilterConditionBuilder isNotNull(String column) {
        return addCondition(column + " IS NOT NULL");
    }

    public synchronized FilterConditionBuilder group(FilterConditionBuilder subGroup) {
        String inner = subGroup.build();
        if (!inner.isEmpty()) {
            segments.add(new ConditionNode("(" + inner + ")"));
        }
        return this;
    }

    private synchronized FilterConditionBuilder addCondition(String condition) {
        segments.add(new ConditionNode(condition));
        return this;
    }

    public synchronized String build() {
        if (segments.isEmpty()) return "";

        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Object obj : segments) {
            if (!first) {
                sb.append(useOr ? " OR " : " AND ");
            }

            if (obj instanceof ConditionNode) {
                sb.append(((ConditionNode) obj).condition);
            } else {
                sb.append(obj.toString());
            }

            first = false;
        }

        return sb.toString();
    }


    public synchronized void clear() {
        segments.clear();
        useOr = false;
    }

    private String formatValue(Object value) {
        if (value == null) {
            return "NULL";
        }
        if (value instanceof Number) {
            return value.toString();
        }

        return "'" + escapeSql(value.toString()) + "'";
    }

    private String escapeSql(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''");
    }

    // 示例
    public static void main(String[] args) {
        // 测试0: 使用OR 分组
        FilterConditionBuilder builder = new FilterConditionBuilder()
                .group(new FilterConditionBuilder()
                        .or()
                        .eq("name", "张三")
                        .eq("name", "李四"))
                .and()
                .group(new FilterConditionBuilder()
                        .gt("age", 20)
                        .lt("age", 40));

        System.out.println(builder.build());
        // 输出: (name = '张三' OR name = '李四') AND (age > 20 AND age < 40)

        // 测试1: 默认AND连接
        FilterConditionBuilder builder1 = new FilterConditionBuilder()
                .eq("name", "张三")
                .eq("age", 25)
                .eq("status", 1);

        System.out.println(builder1.build());
        // 输出: name = '张三' AND age = 25 AND status = 1

        // 测试2: 使用OR连接
        FilterConditionBuilder builder2 = new FilterConditionBuilder()
                .eq("name", "张三")
                .or() // 切换到OR
                .eq("name", "李四")
                .eq("age", 30)
                ; // 继续使用OR

        System.out.println(builder2.build());
        // 输出: name = '张三' OR name = '李四' OR age = 30


        // 测试3: 混合使用
        FilterConditionBuilder builder3 = new FilterConditionBuilder()
                .group(new FilterConditionBuilder()
                        .or()
                        .eq("dept", "IT")
                        .eq("dept", "HR")
                        .group(new FilterConditionBuilder()
                                .and()
                                .eq("age",1000)
                                .ge("size", 200)))
                .and() // 切换回AND
                .gt("salary", 5000)
                .lt("salary", 10000)
                .in("id", Arrays.asList(1, 2))
                .between("created_time","2025-04-22 00:00:00","2025-04-23 00:00:00")
                .like("phone", "15827298060");

        System.out.println(builder3.build());
        // 输出: (dept = 'IT' OR dept = 'HR' OR (age = 1000 AND size >= 200)) AND salary > 5000 AND salary < 10000 AND id IN (1, 2) AND created_time BETWEEN '2025-04-22 00:00:00' AND '2025-04-23 00:00:00' AND phone LIKE '%15827298060%'

    }
}