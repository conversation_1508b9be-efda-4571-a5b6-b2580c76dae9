package com.zjkj.aigc.dashvector.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */
@Data
@Accessors(chain = true)
public class DeleteDocReq implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 向量collectionName
     */
    @NotBlank(message = "collectionName not be empty")
    private String collectionName;

    /**
     * idList
     */
    @NotNull(message = "idList not be empty")
    private List<String> idList;
}
