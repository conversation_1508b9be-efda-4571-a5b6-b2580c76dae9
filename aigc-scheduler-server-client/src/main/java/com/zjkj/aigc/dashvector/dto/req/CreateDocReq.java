package com.zjkj.aigc.dashvector.dto.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/04/22
 */
@Data
@Accessors(chain = true)
public class CreateDocReq  implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 向量collectionName
     */
    @NotBlank(message = "collectionName not be empty")
    private String collectionName;

    /**
     * 自带doc id
     */
    @NotBlank(message = "id not be empty")
    private String id;

    /**
     * 向量数组
     */
    @NotNull(message = "vectorList not be NULL")
    private List<Float> vectorList;

    /**
     * 字段集合，key字段属性，value为对应的值
     * value类型 0:BOOL, 1:STRING, 2:INT, 3:FLOAT, 4:LONG
     */
    private Map<String, Object> fields;
}
