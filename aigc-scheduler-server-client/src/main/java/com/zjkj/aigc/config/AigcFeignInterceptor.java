package com.zjkj.aigc.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

/**
 * AIGC Feign请求拦截器
 * 负责添加认证信息和语言环境信息
 *
 * <AUTHOR>
 * @since 2025/3/13
 */
public class AigcFeignInterceptor implements RequestInterceptor {

    @Setter
    private AigcSdkProperties properties;
    private static final String X_SECRET_KEY = "secretKey";

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 添加认证信息
        if (StringUtils.hasText(properties.getSecretKey())) {
            requestTemplate.header(X_SECRET_KEY, properties.getSecretKey());
        }

        // 添加语言环境信息
        configLocale(requestTemplate);
    }

    /**
     * 配置语言环境
     * 从SDK客户端上下文获取语言设置，并添加到请求头中
     *
     * @param requestTemplate Feign请求模板
     */
    private void configLocale(RequestTemplate requestTemplate) {
        String locale = AigcClientLocaleContext.getLocale();
        if (StringUtils.hasText(locale)) {
            // 添加Accept-Language头，服务端的LocaleInterceptor会自动处理
            requestTemplate.header(HttpHeaders.ACCEPT_LANGUAGE, locale);
        }
    }
}
