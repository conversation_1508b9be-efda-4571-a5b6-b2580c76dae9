package com.zjkj.aigc.config;

import com.fasterxml.jackson.core.type.TypeReference;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
public class TypeReferenceHolder {

    private static final ThreadLocal<TypeReference<?>> TYPE_REFERENCE_THREAD_LOCAL = new ThreadLocal<>();

    public static void setTypeReference(TypeReference<?> typeReference) {
        TYPE_REFERENCE_THREAD_LOCAL.set(typeReference);
    }

    public static TypeReference<?> getTypeReference() {
        return TYPE_REFERENCE_THREAD_LOCAL.get();
    }

    public static void clear() {
        TYPE_REFERENCE_THREAD_LOCAL.remove();
    }
}
