package com.zjkj.aigc.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Client;
import feign.RequestInterceptor;
import feign.Response;
import feign.codec.Decoder;
import feign.codec.Encoder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.lang.reflect.Type;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @since 2024/7/5
 */
public class FeignClientConfiguration {

    /**
     * 信任所有证书
     * @return Client
     */
    @Bean
    public Client feignClient() {
        try {
            SSLContext ctx = SSLContext.getInstance("SSL");
            X509TrustManager tm = new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain,String authType) {}

                @Override
                public void checkServerTrusted(X509Certificate[] chain,String authType) {}

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            };
            ctx.init(null, new TrustManager[]{tm}, null);
            return new Client.Default(ctx.getSocketFactory(), (hostname, session) -> true);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * feign编码器
     * @return Encoder
     */
    @Bean
    public Encoder feignEncoder() {
        return new SpringEncoder(() -> new HttpMessageConverters(new MappingJackson2HttpMessageConverter(customObjectMapper())));
    }

    /**
     * feign解码器
     * @return Decoder
     */
    @Bean
    public Decoder feignDecoder() {
        return new ResponseEntityDecoder(new GenericJackson2Decoder(customObjectMapper()));
    }

    @Bean
    @ConditionalOnProperty(value = "ai.csrs.server.url")
    public RequestInterceptor interceptor(AigcSdkProperties properties) {
        AigcFeignInterceptor interceptor = new AigcFeignInterceptor();
        interceptor.setProperties(properties);
        return interceptor;
    }

    /**
     * 自定义ObjectMapper
     * @return ObjectMapper
     */
    public ObjectMapper customObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    /**
     * 自定义解码器
     */
    public static class GenericJackson2Decoder implements Decoder {
        private final ObjectMapper objectMapper;

        public GenericJackson2Decoder(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        /**
         * 解码
         * @param response 响应
         * @param type 类型
         * @return 解码结果
         * @throws IOException 异常
         */
        @Override
        public Object decode(Response response, Type type) throws IOException {
            JavaType javaType = getJavaType(type);
            return objectMapper.readValue(response.body().asInputStream(), javaType);
        }

        /**
         * 获取JavaType
         * @param type 类型
         * @return JavaType
         */
        private JavaType getJavaType(Type type) {
            TypeReference<?> typeReference = TypeReferenceHolder.getTypeReference();
            if (typeReference != null) {
                type = typeReference.getType();
                TypeReferenceHolder.clear();
            }
            return objectMapper.getTypeFactory().constructType(type);
        }

    }


}