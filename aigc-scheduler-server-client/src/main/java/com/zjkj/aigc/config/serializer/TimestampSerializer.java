package com.zjkj.aigc.config.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.Temporal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
public class TimestampSerializer extends JsonSerializer<Temporal> {

    @Override
    public void serialize(Temporal value, JsonGenerator jsonGenerator, SerializerProvider serializers) throws IOException {
        if (Objects.nonNull(value)) {
            long timestamp;
            if (value instanceof LocalDateTime) {
                timestamp = ((LocalDateTime) value).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else if (value instanceof LocalDate) {
                timestamp = ((LocalDate) value).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else if (value instanceof LocalTime) {
                timestamp = LocalDateTime.of(LocalDate.now(), (LocalTime) value).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else {
                throw new IllegalArgumentException("Unsupported type: " + value.getClass());
            }
            jsonGenerator.writeNumber(timestamp);
        }
    }
}
