package com.zjkj.aigc.config;

import org.springframework.util.StringUtils;

/**
 * SDK客户端语言上下文管理器
 * 用于在非Web环境下管理语言环境
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class AigcClientLocaleContext {

    /**
     * 线程本地语言环境
     */
    private static final ThreadLocal<String> LOCALE_CONTEXT = new ThreadLocal<>();
    
    /**
     * 全局默认语言环境
     */
    private static String globalLocale = "zh_CN";

    /**
     * 设置当前线程的语言环境
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     */
    public static void setLocale(String locale) {
        if (StringUtils.hasText(locale)) {
            LOCALE_CONTEXT.set(locale);
        }
    }

    /**
     * 获取当前线程的语言环境
     * 优先返回线程级设置，如果没有则返回全局默认语言
     * 
     * @return 语言标识
     */
    public static String getLocale() {
        String locale = LOCALE_CONTEXT.get();
        return locale != null ? locale : globalLocale;
    }

    /**
     * 设置全局默认语言环境
     * 
     * @param locale 语言标识 (如: zh_CN, en_US)
     */
    public static void setGlobalLocale(String locale) {
        if (StringUtils.hasText(locale)) {
            globalLocale = locale;
        }
    }

    /**
     * 获取全局默认语言环境
     * 
     * @return 全局默认语言标识
     */
    public static String getGlobalLocale() {
        return globalLocale;
    }

    /**
     * 清除当前线程的语言环境设置
     * 建议在线程结束时调用，避免内存泄漏
     */
    public static void clear() {
        LOCALE_CONTEXT.remove();
    }

    /**
     * 检查当前线程是否设置了语言环境
     * 
     * @return true表示当前线程有语言设置，false表示使用全局默认
     */
    public static boolean hasThreadLocale() {
        return LOCALE_CONTEXT.get() != null;
    }
}
