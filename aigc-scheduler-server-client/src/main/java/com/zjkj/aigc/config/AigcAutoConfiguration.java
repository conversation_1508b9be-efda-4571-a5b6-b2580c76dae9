package com.zjkj.aigc.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/7/17
 */
@ConditionalOnProperty(value = "ai.csrs.server.url")
@Configuration
@EnableFeignClients(basePackages = {"com.zjkj.aigc.**.client"})
@EnableConfigurationProperties(AigcSdkProperties.class)
public class AigcAutoConfiguration {

}
