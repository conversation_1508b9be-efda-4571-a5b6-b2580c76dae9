
ALTER TABLE `model_auto_test_record`
    ADD COLUMN `is_retain_task` int NOT NULL DEFAULT '0' COMMENT '保留测试任务: 0-否，1-是' AFTER `avg_elapsed_time`,
    ADD COLUMN `param_type` varchar(64) NOT NULL DEFAULT 'single' COMMENT '参数类型: single-单个，batch-批量' AFTER `is_retain_task`,
    ADD COLUMN `creator_id` bigint DEFAULT NULL COMMENT '创建人id' AFTER `test_completion_time`,
    ADD COLUMN `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称' AFTER `creator_id`;