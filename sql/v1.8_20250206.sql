ALTER TABLE `aigc_cluster_node`
    ADD COLUMN `cpu_core` int NOT NULL DEFAULT '0' COMMENT 'cpu核数' AFTER `gpu_id`,
    ADD COLUMN `memory_size` int NOT NULL DEFAULT '0' COMMENT '内存大小GB' AFTER `cpu_core`;


ALTER TABLE `model_auto_test_record`
    MODIFY COLUMN `status` int NOT NULL COMMENT '状态，0：待执行，1：执行中，2：已完成，3：报告已生成，4:取消，5:失败' AFTER `model_params`;


ALTER TABLE `aigc_cluster_usage`
    ADD COLUMN `total_gpu_memory_size` bigint NOT NULL DEFAULT '0' COMMENT '总gpu显存大小GB' AFTER `gpu_count`,
    ADD COLUMN `used_gpu_memory_size` bigint NOT NULL DEFAULT '0' COMMENT '使用的gpu显存大小GB' AFTER `total_gpu_memory_size`,
    ADD COLUMN `total_cpu_core` bigint NOT NULL DEFAULT '0' COMMENT '总cpu核数' AFTER `gpu_usage_rate`,
    ADD COLUMN `used_cpu_core` bigint NOT NULL DEFAULT '0' COMMENT '使用的cpu核数' AFTER `total_cpu_core`,
    ADD COLUMN `cpu_usage_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT 'cpu使用率' AFTER `used_cpu_core`,
    ADD COLUMN `total_memory_size` bigint NOT NULL DEFAULT '0' COMMENT '总内存大小GB' AFTER `cpu_usage_rate`,
    ADD COLUMN `used_memory_size` bigint NOT NULL DEFAULT '0' COMMENT '使用的内存大小GB' AFTER `total_memory_size`,
    ADD COLUMN `memory_usage_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '内存使用率' AFTER `used_memory_size`;
