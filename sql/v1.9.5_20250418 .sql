CREATE TABLE `resource_bill_group_config` (
                                              `id` BIGINT NOT NULL AUTO_INCREMENT,
                                              `month` INT NOT NULL COMMENT '月份',
                                              `platform` VARCHAR ( 32 ) CHARACTER
                                                       SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云平台',
                                              `mid_price_redundancy` DECIMAL ( 10, 2 ) NOT NULL COMMENT '中间件费用冗余比例',
                                              `mid_adj_price` DECIMAL ( 10, 2 ) NULL DEFAULT NULL COMMENT '中间件调整费用',
                                              `gpu_price_redundancy` DECIMAL ( 10, 2 ) NOT NULL COMMENT 'GPU费用冗余比例',
                                              `gpu_adj_price` DECIMAL ( 10, 2 ) NULL DEFAULT NULL COMMENT 'GPU调整费用',
                                              `creator_id` BIGINT NULL DEFAULT NULL COMMENT '创建人id',
                                              `creator_name` VARCHAR ( 64 ) CHARACTER
                                                       SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
                                              `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                              `revised_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
                                              `is_deleted` TINYINT NULL DEFAULT 0 COMMENT '逻辑删除 0 否 1是',
                                              PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 5 CHARACTER
SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '费用分摊信息配置表' ROW_FORMAT = DYNAMIC;