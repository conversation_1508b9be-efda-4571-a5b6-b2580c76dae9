
ALTER TABLE `aigc_cluster_usage`
    ADD COLUMN `node_count` bigint NOT NULL DEFAULT '0' COMMENT '节点数量' AFTER `data_date`,
    ADD COLUMN `gpu_model` varchar(128) DEFAULT '' COMMENT 'gpu型号' AFTER `node_count`;

ALTER TABLE `resource_bill`
    ADD COLUMN `type` varchar(64) NOT NULL COMMENT '类型:gpu, mid-中间件' AFTER `month`,
    ADD COLUMN `product_code` varchar(128) DEFAULT '' COMMENT '产品编号' AFTER `platform`,
    ADD COLUMN `product_name` varchar(128) DEFAULT '' COMMENT '产品名称' AFTER `product_code`;

ALTER TABLE `resource_bill`
    DROP INDEX `idx_month`,
    ADD INDEX `idx_month_type`(`month`, `type`);

ALTER TABLE `model_auto_test_record`
    ADD COLUMN `avg_elapsed_time` bigint DEFAULT NULL COMMENT '平均耗时ms' AFTER `success_task_count`;


CREATE TABLE `aigc_model_usage` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `model_name_zh` varchar(64) DEFAULT '' COMMENT '模型中文名称',
    `task_type` varchar(128) DEFAULT '' COMMENT '任务类型',
    `model_name` varchar(128) DEFAULT '' COMMENT '模型名称',
    `model_type` varchar(64) DEFAULT '' COMMENT '模型类型',
    `cluster_id` bigint NOT NULL COMMENT '集群主键id',
    `data_date` date NOT NULL COMMENT '数据日期',
    `pod_count` bigint NOT NULL DEFAULT '0' COMMENT 'pod数量',
    `gpu_memory_size` bigint NOT NULL DEFAULT '0' COMMENT 'gpu显存大小GB',
    `gpu_memory_size_ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '集群gpu显存占比',
    `cpu_core` bigint NOT NULL DEFAULT '0' COMMENT 'cpu核数',
    `cpu_core_ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '集群cpu核数占比',
    `memory_size` bigint NOT NULL DEFAULT '0' COMMENT '总内存大小GB',
    `memory_size_ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '集群内存占比',
    `created_time` datetime NOT NULL COMMENT '创建时间',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
    PRIMARY KEY (`id`),
    KEY `idx_dt_cid_mtype` (`data_date`, `cluster_id`, `model_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型资源占用表';



