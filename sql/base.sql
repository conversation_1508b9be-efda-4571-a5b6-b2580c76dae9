-- ----------------------------
-- Table structure for aigc_alarm
-- ----------------------------
CREATE TABLE `aigc_alarm` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `alarm_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '告警编号',
      `alarm_snapshot` json DEFAULT NULL COMMENT '告警快照',
      `task_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务类型',
      `model_type` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型类型',
      `model_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型名称',
      `model_name_zh` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型中文名称',
      `alarm_type` int NOT NULL DEFAULT '0' COMMENT '告警类型: 0-积压告警,1-成功率告警',
      `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-未处理,1-不处理,2-已处理',
      `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '处理备注',
      `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
      `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
      `alarm_config_id` bigint DEFAULT NULL COMMENT '告警配ID',
      PRIMARY KEY (`id`),
      KEY `idx_modelname_tasktype_alarmtype` (`model_name`,`task_type`,`alarm_type`) USING BTREE,
      KEY `idx_alarm_code` (`alarm_code`),
      KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型告警表';

-- ----------------------------
-- Table structure for aigc_alarm_channel
-- ----------------------------
CREATE TABLE `aigc_alarm_channel` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `tag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '标签',
      `channel_type` int NOT NULL DEFAULT '0' COMMENT '告警渠道: 0-钉钉webhook,1-飞书webhook',
      `webhook_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Webhook地址',
      `sign` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签名密钥',
      `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
      `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='告警渠道配置表';

-- ----------------------------
-- Table structure for aigc_alarm_config
-- ----------------------------
CREATE TABLE `aigc_alarm_config` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `tag` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '标签',
     `model_id` json NOT NULL COMMENT '关联模型ID集合',
     `alarm_type` int NOT NULL DEFAULT '0' COMMENT '告警类型: 0-积压告警,1-成功率告警',
     `alarm_config_threshold` json DEFAULT NULL COMMENT '告警阈值配置',
     `alarm_time_interval` int NOT NULL DEFAULT '1' COMMENT '告警时间间隔',
     `alarm_time_interval_unit` int NOT NULL DEFAULT '0' COMMENT '告警时间间隔单位：0-分钟，1-小时',
     `alarm_receiver` json DEFAULT NULL COMMENT '接收人配置',
     `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-禁用,1-启用',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型告警配置表';

-- ----------------------------
-- Table structure for aigc_app
-- ----------------------------
CREATE TABLE `aigc_app` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
    `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'appid',
    `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密钥',
    `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-未激活,1-激活,2-禁用',
    `notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '回调地址',
    `created_time` datetime NOT NULL COMMENT '创建时间',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_appid` (`app_id`) USING BTREE,
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='应用表';

-- ----------------------------
-- Table structure for aigc_cluster
-- ----------------------------
CREATE TABLE `aigc_cluster` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `platform` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台',
        `name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
        `service_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务类型',
        `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
        `status` int NOT NULL DEFAULT '0' COMMENT '状态: 1-启用,0-禁用',
        `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
        `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
        `created_time` datetime NOT NULL COMMENT '创建时间',
        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
        `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
        PRIMARY KEY (`id`),
        KEY `idx_platform_servicetype` (`platform`,`service_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='集群表';

-- ----------------------------
-- Table structure for aigc_cluster_node
-- ----------------------------
CREATE TABLE `aigc_cluster_node` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `cluster_id` bigint NOT NULL COMMENT '集群主键id',
     `node_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ip',
     `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
     `gpu_id` bigint NOT NULL COMMENT 'gpu主键id',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `idx_cluster_id` (`cluster_id`),
     KEY `idx_gpu_id` (`gpu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='集群节点表';

-- ----------------------------
-- Table structure for aigc_cluster_usage
-- ----------------------------
CREATE TABLE `aigc_cluster_usage` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `cluster_id` bigint NOT NULL COMMENT '集群主键id',
      `cluster_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '集群名称',
      `platform` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '平台',
      `service_type` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '服务类型',
      `data_date` date NOT NULL COMMENT '数据日期',
      `gpu_count` bigint NOT NULL DEFAULT '0' COMMENT 'gpu数量',
      `gpu_usage_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT 'gpu使用率',
      `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
      `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
      `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
      `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_datadate_clusterid` (`data_date`,`cluster_id`,`del_version`) USING BTREE,
      KEY `idx_platform_servicetype` (`platform`,`service_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='集群资源利用表';

-- ----------------------------
-- Table structure for aigc_gpu
-- ----------------------------
CREATE TABLE `aigc_gpu` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `platform` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台',
        `model` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '型号',
        `card_count` int unsigned NOT NULL COMMENT '卡数',
        `single_card_memory` int NOT NULL COMMENT '单卡显存',
        `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
        `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
        `created_time` datetime NOT NULL COMMENT '创建时间',
        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
        `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='gpu表';

-- ----------------------------
-- Table structure for aigc_host
-- ----------------------------
CREATE TABLE `aigc_host` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `host_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主机ip',
     `platform` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '平台',
     `region` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '区域',
     `status` int NOT NULL DEFAULT '0' COMMENT '状态: 1-启用,0-禁用',
     `remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `del_version` varchar(24) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_hostip_delversion` (`host_ip`,`del_version`) USING BTREE,
     KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='主机表';

-- ----------------------------
-- Table structure for aigc_model_config
-- ----------------------------
CREATE TABLE `aigc_model_config` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `model_id` bigint NOT NULL COMMENT '模型id',
     `project_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '项目工程名称',
     `project_git_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '项目git地址',
     `model_version` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型版本',
     `deploy_sdk_version` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部署sdk版本',
     `model_file_oss_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型文件OSS地址',
     `is_encrypt` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密: 0-不加密,1-加密',
     `mirror_image_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '基础镜像地址',
     `cpu_core` int DEFAULT NULL COMMENT 'cpu核数',
     `memory_size` int DEFAULT NULL COMMENT '内存大小GB',
     `gpu_info` varchar(512) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'gpu信息',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `uk_model_id` (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型配置表';

-- ----------------------------
-- Table structure for aigc_model_deploy
-- ----------------------------
DROP TABLE IF EXISTS `aigc_model_deploy`;
CREATE TABLE `aigc_model_deploy` (
         `id` bigint NOT NULL AUTO_INCREMENT,
         `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
         `model_id` bigint NOT NULL COMMENT '模型id',
         `project_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '项目工程名称',
         `project_git_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '项目git地址',
         `model_version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型版本',
         `deploy_sdk_version` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部署sdk版本',
         `model_file_oss_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型文件OSS地址',
         `mirror_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '基础镜像地址',
         `resource_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '资源信息,GUPCPU内存等信息描述',
         `encrypt_status` int NOT NULL DEFAULT '0' COMMENT '模型文件加密状态: 0-未加密,1-加密中,2-加密完成,3-加密失败',
         `is_encrypt` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密: 0-不加密,1-加密',
         `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-草稿,1-发布中,2-发布成功,3-发布失败',
         `changed` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '变更项',
         `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '信息',
         `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
         `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
         `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
         `created_time` datetime NOT NULL COMMENT '创建时间',
         `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
         `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
         PRIMARY KEY (`id`),
         KEY `idx_status_encrypt_status` (`status`,`encrypt_status`) USING BTREE,
         KEY `idx_model_id` (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型发布记录表';

-- ----------------------------
-- Table structure for aigc_model_info
-- ----------------------------
CREATE TABLE `aigc_model_info` (
       `id` bigint NOT NULL AUTO_INCREMENT,
       `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型中文名称',
       `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型: BIZ-业务模型,ATOM-原子模型',
       `task_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务类型',
       `model_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型名称',
       `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-未部署,1-启用,2-禁用',
       `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
       `last_active_time` datetime DEFAULT NULL COMMENT '上次活跃时间',
       `last_register_time` datetime DEFAULT NULL COMMENT '上次注册时间',
       `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
       `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
       `created_time` datetime NOT NULL COMMENT '创建时间',
       `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
       `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
       `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
       PRIMARY KEY (`id`),
       UNIQUE KEY `uk_modelname_tasktype` (`model_name`,`task_type`,`del_version`) USING BTREE,
       KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=100000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型基础信息表';

-- ----------------------------
-- Table structure for aigc_model_instance
-- ----------------------------
CREATE TABLE `aigc_model_instance` (
       `id` bigint NOT NULL AUTO_INCREMENT,
       `pod_name` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'pod名称',
       `pod_namespace` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'pod命名空间',
       `node_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ip',
       `node_name` varchar(128) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '节点名称',
       `task_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务类型',
       `model_name` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '模型名称',
       `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-非法，1-合法',
       `pod_status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-空闲，1-运行',
       `health_status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-离线，1-在线',
       `model_version` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型版本',
       `sdk_version` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'sdk版本',
       `source_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源ip',
       `last_heartbeat_time` datetime DEFAULT NULL COMMENT '最后心跳时间',
       `created_time` datetime NOT NULL COMMENT '创建时间',
       `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
       `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
       PRIMARY KEY (`id`),
       UNIQUE KEY `uk_podname` (`pod_name`) USING BTREE,
       KEY `idx_nodeip` (`node_ip`),
       KEY `idx_status_healthstatus` (`status`,`health_status`),
       KEY `idx_modelname_tasktype` (`model_name`,`task_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型实例表';

-- ----------------------------
-- Table structure for aigc_node_instance
-- ----------------------------
CREATE TABLE `aigc_node_instance` (
      `id` bigint NOT NULL AUTO_INCREMENT,
      `hostname` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主机名',
      `node_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ip',
      `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-非法，1-合法',
      `health_status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-离线，1-在线',
      `system_info` json DEFAULT NULL COMMENT '系统信息',
      `namespace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '命名空间',
      `source_ip` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源ip',
      `last_heartbeat_time` datetime DEFAULT NULL COMMENT '最后心跳时间',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uk_hostname` (`hostname`),
      KEY `idx_status_healthstatus` (`status`,`health_status`) USING BTREE,
      KEY `idx_nodeip` (`node_ip`)
) ENGINE=InnoDB AUTO_INCREMENT=1000000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='节点实例表';

-- ----------------------------
-- Table structure for aigc_task
-- ----------------------------
CREATE TABLE `aigc_task` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
     `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务ID',
     `aigc_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '平台任务id',
     `task_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型，一般与AI模型对应',
     `business_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '业务系统ID',
     `task_priority` int NOT NULL DEFAULT '0' COMMENT '任务优先级',
     `task_batch` int NOT NULL DEFAULT '0' COMMENT '任务批量',
     `notify_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '回调地址',
     `task_source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务来源',
     `task_progress` int DEFAULT '0' COMMENT '任务进度',
     `task_state` int NOT NULL COMMENT '状态，0：待执行，1：执行中，2：成功，3：失败，4：取消，5：超时失败',
     `task_start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
     `task_cancel_time` datetime DEFAULT NULL COMMENT '任务取消时间',
     `task_completion_time` datetime DEFAULT NULL COMMENT '任务完成时间',
     `model_id` bigint DEFAULT NULL COMMENT 'AI模型',
     `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'AI模型名称',
     `model_params` json DEFAULT NULL COMMENT '模型参数，与任务类型有关',
     `model_output` json DEFAULT NULL COMMENT '输出图片{多张以英文逗号分隔}',
     `container_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '运行容器id',
     `fail_message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败信息',
     `retry_count` int DEFAULT '0' COMMENT '重试次数',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE KEY `uk_aigc_task_id` (`aigc_task_id`) USING BTREE,
     UNIQUE KEY `uk_business_task_id` (`business_id`,`task_id`) USING BTREE,
     KEY `idx_created_time` (`created_time`) USING BTREE,
     KEY `idx_task_type_model_name_priority` (`task_type`,`model_name`,`task_priority`),
     KEY `idx_task_state` (`task_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='ai任务表';

-- ----------------------------
-- Table structure for aigc_task_archive
-- ----------------------------
CREATE TABLE `aigc_task_archive` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
     `task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务ID',
     `aigc_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '平台任务id',
     `task_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型，一般与AI模型对应',
     `business_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '业务系统ID',
     `task_priority` int NOT NULL DEFAULT '0' COMMENT '任务优先级',
     `task_batch` int NOT NULL DEFAULT '0' COMMENT '任务批量',
     `notify_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '回调地址',
     `task_source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务来源',
     `task_progress` int DEFAULT '0' COMMENT '任务进度',
     `task_state` int NOT NULL COMMENT '状态，0：待执行，1：执行中，2：成功，3：失败，4：取消，5：超时失败',
     `task_start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
     `task_cancel_time` datetime DEFAULT NULL COMMENT '任务取消时间',
     `task_completion_time` datetime DEFAULT NULL COMMENT '任务完成时间',
     `model_id` bigint DEFAULT NULL COMMENT 'AI模型',
     `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'AI模型名称',
     `model_params` json DEFAULT NULL COMMENT '模型参数，与任务类型有关',
     `model_output` json DEFAULT NULL COMMENT '输出图片{多张以英文逗号分隔}',
     `container_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '运行容器id',
     `fail_message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败信息',
     `retry_count` int DEFAULT '0' COMMENT '重试次数',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE KEY `uk_aigc_task_id` (`aigc_task_id`) USING BTREE,
     UNIQUE KEY `uk_business_task_id` (`business_id`,`task_id`) USING BTREE,
     KEY `idx_tasktype_modelname_taskstate_isdeleted` (`task_type`,`model_name`,`task_state`,`is_deleted`),
     KEY `idx_created_time` (`created_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='ai任务归档表';

-- ----------------------------
-- Table structure for aigc_task_stat_day
-- ----------------------------
CREATE TABLE `aigc_task_stat_day` (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
      `model_type` varchar(64) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模型类型',
      `task_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型，一般与AI模型对应',
      `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'AI模型名称',
      `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '应用ID',
      `success_amount` bigint NOT NULL DEFAULT '0' COMMENT '成功数量',
      `fail_amount` bigint NOT NULL DEFAULT '0' COMMENT '失败数量',
      `running_amount` bigint NOT NULL DEFAULT '0' COMMENT '处理中数量',
      `waiting_amount` bigint NOT NULL DEFAULT '0' COMMENT '等待中数量',
      `cancel_amount` bigint NOT NULL DEFAULT '0' COMMENT '取消数量',
      `total_amount` bigint NOT NULL DEFAULT '0' COMMENT '总数',
      `avg_elapsed_time` bigint DEFAULT NULL COMMENT '平均耗时ms',
      `stat_date` date NOT NULL COMMENT '统计日期',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`) USING BTREE,
      KEY `idx_statdate_tasktype_modelname` (`stat_date`,`task_type`,`model_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='任务统计表(天)';

-- ----------------------------
-- Table structure for data_affiliation
-- ----------------------------
CREATE TABLE `data_affiliation` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `data_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据id',
        `data_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型',
        `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
        `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
        `created_time` datetime NOT NULL COMMENT '创建时间',
        `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
        `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
        PRIMARY KEY (`id`),
        KEY `idx_data_type_id` (`data_type`,`data_id`) USING BTREE,
        KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据所属表';

-- ----------------------------
-- Table structure for gpu_apply
-- ----------------------------
CREATE TABLE `gpu_apply` (
         `id` bigint NOT NULL AUTO_INCREMENT,
         `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审批编号',
         `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审批类型',
         `cluster_business` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '集群/业务',
         `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云平台',
         `gpu_model` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GPU型号',
         `gpu_num` int NOT NULL DEFAULT '0' COMMENT 'GPU卡总数',
         `single_card_memory` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单卡显存',
         `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-待审批,1-审批通过，2-审批不通过',
         `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '补充说明',
         `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
         `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
         `created_time` datetime NOT NULL COMMENT '创建时间',
         `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
         `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
         `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
         `supply_remark` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作说明',
         PRIMARY KEY (`id`),
         KEY `idx_code` (`code`),
         KEY `idx_type` (`type`),
         KEY `idx_status` (`status`),
         KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='GPU申请表';

-- ----------------------------
-- Table structure for heartbeat_check
-- ----------------------------
CREATE TABLE `heartbeat_check` (
       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
       `task_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'seq，4K，aigc',
       `containe_Id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '容器id',
       `model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型名称',
       `status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态 worker状态,running运行中，idle闲置中',
       `created_time` datetime NOT NULL COMMENT '创建时间',
       `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
       `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='心跳检查表';

-- ----------------------------
-- Table structure for model_auto_test_record
-- ----------------------------
CREATE TABLE `model_auto_test_record` (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
      `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '测试编号',
      `test_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '测试编码',
      `business_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '测试业务ID',
      `task_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型',
      `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'AI模型名称',
      `batch_task_count` bigint DEFAULT '0' COMMENT '创建的任务数',
      `success_task_count` bigint DEFAULT '0' COMMENT '成功的任务数',
      `model_params` json DEFAULT NULL COMMENT '模型参数',
      `status` int NOT NULL COMMENT '状态，0：待执行，1：执行中，2：已完成，3：报告已生成，4:取消',
      `test_start_time` datetime DEFAULT NULL COMMENT '测试开始时间',
      `test_cancel_time` datetime DEFAULT NULL COMMENT '测试取消时间',
      `test_completion_time` datetime DEFAULT NULL COMMENT '测试完成时间',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE KEY `uk_test_no` (`test_no`) USING BTREE,
      KEY `idx_tasktype_modelname_isdeleted` (`task_type`,`model_name`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='模型测试批次表';

-- ----------------------------
-- Table structure for model_auto_test_report
-- ----------------------------
CREATE TABLE `model_auto_test_report` (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
      `test_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '测试编码',
      `task_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务类型',
      `model_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'AI模型名称',
      `service_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型服务名称',
      `model_version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型版本',
      `task_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务信息平均任务时间 、平均1h任务数，josn格式',
      `gpu_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '显卡型号',
      `gpu_dispatch_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '显卡调度类型：独占、虚拟',
      `cpu_info` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'CPU均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式',
      `memory_info` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '内存均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式',
      `gpu_info` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'GPU均值、峰值、大于 limit 的80% 90% 的时间占比，josn格式',
      `gpu_info_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'gpu明细',
      `resource_limits` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '资源限制，josn格式',
      `report_status` int NOT NULL DEFAULT '0' COMMENT '状态，0：待生成；1：已生成',
      `created_time` datetime NOT NULL COMMENT '创建时间',
      `gen_time` datetime DEFAULT NULL COMMENT '生成时间',
      `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE KEY `uk_test_no` (`test_no`) USING BTREE,
      KEY `idx_tasktype_modelname_isdeleted` (`task_type`,`model_name`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='模型测试报告表';

-- ----------------------------
-- Table structure for resource_expend
-- ----------------------------
CREATE TABLE `resource_expend` (
       `id` bigint NOT NULL AUTO_INCREMENT,
       `month` int NOT NULL COMMENT '资源支出月份',
       `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云平台',
       `account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
       `last_month_cost` decimal(10,2) DEFAULT '0.00' COMMENT '上月实际消费',
       `last_month_remain` decimal(10,2) DEFAULT '0.00' COMMENT '上月余额',
       `this_month_recharge` decimal(10,2) DEFAULT '0.00' COMMENT '本月已充值',
       `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注说明',
       `budget_diff_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '预算差异说明',
       `status` int NOT NULL DEFAULT '0' COMMENT '状态: 0-急需充值,1-充值时间待定，2-本月无需充值',
       `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
       `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
       `created_time` datetime NOT NULL COMMENT '创建时间',
       `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
       `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
       `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
       PRIMARY KEY (`id`),
       KEY `idx_month` (`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资源支出表';

-- ----------------------------
-- Table structure for resource_gpu_plan
-- ----------------------------
CREATE TABLE `resource_gpu_plan` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `month` int NOT NULL COMMENT '规划月份',
     `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云平台',
     `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '型号',
     `business_demand` int NOT NULL DEFAULT '0' COMMENT '业务卡需求',
     `mark_demand` int NOT NULL DEFAULT '0' COMMENT '打标卡需求',
     `train_demand` int NOT NULL DEFAULT '0' COMMENT '训练卡需求',
     `test_demand` int NOT NULL DEFAULT '0' COMMENT '测试卡需求',
     `devops_demand` int NOT NULL DEFAULT '0' COMMENT '运维冗余卡需求',
     `norms` int NOT NULL DEFAULT '0' COMMENT 'GPU规格/卡',
     `tmp` int NOT NULL DEFAULT '0' COMMENT '临时/卡',
     `price` decimal(10,2) DEFAULT '0.00' COMMENT '单价',
     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `idx_month_platform` (`month`,`platform`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='GPU资源规划表';

-- ----------------------------
-- Table structure for resource_mid_plan
-- ----------------------------
CREATE TABLE `resource_mid_plan` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `month` int NOT NULL COMMENT '规划月份',
     `platform` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '云平台',
     `price_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '价格说明',
     `total_price` decimal(10,2) DEFAULT '0.00' COMMENT '总价',
     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注说明',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `del_version` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `idx_month_platform` (`month`,`platform`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='中间件资源规划表';

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
CREATE TABLE `sys_dict` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典名称',
    `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
    `status` int DEFAULT '1' COMMENT '状态:0-停用 1-启用',
    `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
    `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
    `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
    `created_time` datetime NOT NULL COMMENT '创建时间',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `del_version` varchar(24) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除版本',
    `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_type_delversion` (`type`,`del_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典表';

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
CREATE TABLE `sys_dict_data` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
     `dict_id` bigint NOT NULL COMMENT '字典类型id',
     `label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典标签',
     `value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典键值',
     `dict_sort` int DEFAULT '0' COMMENT '字典排序',
     `status` int DEFAULT '1' COMMENT '状态:0-停用 1-启用',
     `css_class` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '样式属性',
     `color_class` varchar(100) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '颜色属性',
     `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `idx_dictid` (`dict_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';


-- 字典表数据
INSERT INTO `sys_dict`
(`id`, `name`, `type`, `status`, `remark`, `creator_id`, `creator_name`, `created_time`, `revised_time`, `del_version`, `is_deleted`)
VALUES
(103, 'GPU型号', 'GPU_SERIES', 1, '', 191137482, '龙土新', '2024-12-16 11:51:18', '2024-12-17 16:33:05', '0', 0),
(106, '部署SDK版本', 'deploy_sdk_version', 1, '', 190591045, '杨振华', '2024-12-23 12:00:29', NULL, '0', 0),
(107, '模型类型', 'model_type', 1, '', 191137482, '龙土新', '2024-12-25 17:19:21', NULL, '0', 0);


INSERT INTO `sys_dict_data`
(`id`, `dict_id`, `label`, `value`, `dict_sort`, `status`, `css_class`, `color_class`, `remark`, `creator_id`, `creator_name`, `created_time`, `revised_time`, `is_deleted`)
VALUES
(113, 103, 'L20', 'L20', 1, 1, '', '', '', 191137482, '龙土新', '2024-12-16 11:51:47', '2024-12-18 10:05:11', 0),
(114, 103, '4090', '4090', 2, 1, '', '', '', 191137482, '龙土新', '2024-12-16 11:52:00', '2024-12-16 11:52:32', 0),
(115, 103, 'H20', 'H20', 3, 1, '', '', '', 191137482, '龙土新', '2024-12-16 11:52:18', NULL, 0),
(116, 103, 'A100', 'A100', 4, 1, '', '', '', 191137482, '龙土新', '2024-12-16 11:52:47', '2024-12-16 14:10:43', 0),
(127, 106, '0.1.5', '0.1.5', 0, 1, '', '', '', 190591045, '杨振华', '2024-12-23 12:01:38', NULL, 0),
(128, 106, '0.1.4', '0.1.4', 0, 1, '', '', '', 190591045, '杨振华', '2024-12-23 14:08:05', '2024-12-23 14:11:37', 0),
(129, 106, '0.1.3', '0.1.3', 0, 1, '', '', '', 190591045, '杨振华', '2024-12-23 14:08:30', NULL, 0),
(130, 106, '未使用', '-', 0, 1, '', '', '', 190591045, '杨振华', '2024-12-25 14:55:25', '2024-12-25 14:56:17', 0),
(131, 107, '标注', 'TAG', 2, 1, '', '#f53f3f', '', 191137482, '龙土新', '2024-12-25 17:19:40', '2024-12-25 18:17:29', 0),
(132, 107, '业务', 'BIZ', 1, 1, '', '#5b9df9', '', 191137482, '龙土新', '2024-12-25 17:19:51', '2024-12-25 18:17:06', 0),
(133, 107, '原子', 'ATOM', 3, 1, '', '#34c38f', '', 191137482, '龙土新', '2024-12-25 17:20:07', '2024-12-25 18:16:55', 0),
(134, 107, 'BI', 'BI', 4, 1, '', '#f3af3d', '', 191137482, '龙土新', '2024-12-25 17:20:18', '2024-12-25 18:17:15', 0);
