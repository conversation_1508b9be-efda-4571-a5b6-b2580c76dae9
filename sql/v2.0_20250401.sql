ALTER TABLE `aigc_model_instance`
    ADD COLUMN `cluster_id` bigint DEFAULT NULL COMMENT '集群主键id' AFTER `id`,
    ADD COLUMN `model_id` bigint DEFAULT NULL COMMENT '模型id' AFTER `node_name`;

ALTER TABLE `aigc_model_instance`
    ADD INDEX `idx_model_id`(`model_id`),
    ADD INDEX `idx_cluster_id`(`cluster_id`);

ALTER TABLE `aigc_model_info`
    ADD COLUMN `env_instance` varchar(255) DEFAULT '{}' COMMENT '环境实例数' AFTER `status`,
    ADD COLUMN `online` tinyint DEFAULT '0' COMMENT '是否在线' AFTER `env_instance`,
    ADD COLUMN `pre_threshold` int DEFAULT '0' COMMENT '预测负载百分比' AFTER `online`,
    ADD COLUMN `pre_quantity` int DEFAULT '0' COMMENT '预测数量' AFTER `pre_threshold`,
    ADD COLUMN `avg_elapsed_time` bigint DEFAULT NULL COMMENT '平均耗时ms' AFTER `pre_threshold`;

ALTER TABLE `aigc_model_info`
    ADD INDEX `idx_online_prethreshold`(`online`, `pre_threshold`);

CREATE TABLE `aigc_dynamic_config_global` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `dynamic_enabled` tinyint NOT NULL COMMENT '动态调度启用',
    `zadig_endpoint` varchar(255) DEFAULT '' COMMENT 'zadig端点地址',
    `zadig_api_token` varchar(512) DEFAULT '' COMMENT 'zadig api token',
    `whitelist_enabled` tinyint NOT NULL COMMENT '白名单启用',
    `model_whitelist` varchar(2000) DEFAULT '' COMMENT '模型白名单',
    `cooldown_minute` int unsigned NOT NULL DEFAULT '0' COMMENT '冷却分钟数',
    `created_time` datetime NOT NULL COMMENT '创建时间',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='动态配置全局表';

INSERT INTO `aigc_dynamic_config_global`
    (`dynamic_enabled`, `zadig_endpoint`, `zadig_api_token`, `whitelist_enabled`, `model_whitelist`, `cooldown_minute`, `created_time`, `revised_time`)
    VALUES
    (0, 'http://zadig.xxxxx.site/','eyJhxxxx',0, '', 15, '2025-04-01 00:00:00', NULL);

CREATE TABLE `aigc_dynamic_config` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `model_id` bigint NOT NULL COMMENT '模型id',
    `min_replica` int unsigned DEFAULT '0' COMMENT '最小副本数',
    `max_replica` int unsigned DEFAULT '0' COMMENT '最大副本数',
    `priority` int NOT NULL DEFAULT '0' COMMENT '优先级',
    `cooldown_minute` int unsigned NOT NULL DEFAULT '0' COMMENT '冷却分钟数',
    `scale_out_minute` int NOT NULL COMMENT '扩容维度:分钟数',
    `scale_out_threshold` int NOT NULL COMMENT '缩容负载: 阈值1-100%',
    `deprived_model_id` varchar(2000) DEFAULT '' COMMENT '优先剥夺的模型id',
    `scale_in_minute` int NOT NULL COMMENT '缩容维度:分钟数',
    `scale_in_threshold` int NOT NULL COMMENT '缩容负载:阈值1-100%',
    `status` int DEFAULT '1' COMMENT '状态:0-停用 1-启用',
    `last_scale_type` varchar(16) DEFAULT NULL COMMENT '上次伸缩类型:SCALE_OUT/SCALE_IN',
    `last_scale_time` datetime DEFAULT NULL COMMENT '上次伸缩时间',
    `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
    `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称',
    `created_time` datetime NOT NULL COMMENT '创建时间',
    `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
    PRIMARY KEY (`id`),
    KEY `idx_model_id` (`model_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='动态配置表';

CREATE TABLE `aigc_dynamic_adjust_record` (
   `id` bigint NOT NULL AUTO_INCREMENT,
   `dynamic_config_id` bigint DEFAULT NULL COMMENT '动态配置id',
   `model_id` bigint DEFAULT NULL COMMENT '模型id',
   `model_name_zh` varchar(128) DEFAULT '' COMMENT '模型中文名称',
   `scale_type` varchar(16) NOT NULL COMMENT '伸缩类型:SCALE_OUT/SCALE_IN',
   `scale_status` varchar(16) NOT NULL COMMENT '伸缩状态：PENDING-待伸缩 SCALING-伸缩中 SUCCESS-伸缩成功 FAILED-伸缩失败 CANCEL-取消',
   `status` int DEFAULT '1' COMMENT '状态：1-生效中 2-结束 3-取消',
   `message` varchar(256) DEFAULT NULL COMMENT '失败信息',
   `dynamic_config_snapshot` varchar(1024) DEFAULT NULL COMMENT '动态配置快照',
   `related_id` bigint DEFAULT NULL COMMENT '关联的id',
   `remark` varchar(256) DEFAULT NULL COMMENT '备注',
   `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
   `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称',
   `created_time` datetime NOT NULL COMMENT '创建时间',
   `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
   `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
   PRIMARY KEY (`id`),
   KEY `idx_dynamic_config_id` (`dynamic_config_id`),
   KEY `idx_model_id` (`model_id`),
   KEY `idx_status_scale_type_status` (`status`, `scale_type`, `scale_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='动态调整记录表';

CREATE TABLE `aigc_dynamic_adjust_record_detail` (
   `id` bigint NOT NULL AUTO_INCREMENT,
   `adjust_record_id` bigint NOT NULL COMMENT '调整记录id',
   `model_id` bigint NOT NULL COMMENT '模型id',
   `model_name_zh` varchar(128) DEFAULT '' COMMENT '模型中文名称',
   `scale_type` varchar(16) NOT NULL COMMENT '伸缩类型:SCALE_OUT/SCALE_IN',
   `source_replica` int unsigned NOT NULL COMMENT '原副本数',
   `target_replica` int unsigned NOT NULL COMMENT '目标副本数',
   `diff_replica` int unsigned NOT NULL COMMENT '差异副本数',
   `gpu_memory_size` int NOT NULL COMMENT 'gpu内存大小GB',
   `env_name` varchar(128) NOT NULL COMMENT '环境名称',
   `project_name` varchar(128) NOT NULL COMMENT '项目名称',
   `service_name` varchar(128) NOT NULL COMMENT '服务名称',
   `scale_status` varchar(16) NOT NULL COMMENT '伸缩状态：PENDING-待伸缩 SCALING-伸缩中 SUCCESS-伸缩成功 FAILED-伸缩失败 CANCEL-取消 ROLLBACK-伸缩回退',
   `status` int DEFAULT '1' COMMENT '状态：1-生效中 2-结束',
   `scale_time` datetime DEFAULT NULL COMMENT '伸缩时间',
   `remark` varchar(256) DEFAULT NULL COMMENT '备注',
   `retry_count` int DEFAULT '0' COMMENT '重试次数',
   `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
   `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称',
   `created_time` datetime NOT NULL COMMENT '创建时间',
   `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
   `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
   PRIMARY KEY (`id`),
   KEY `idx_model_id_scale_type` (`model_id`, `scale_type`),
   KEY `idx_adjust_record_id` (`adjust_record_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='动态调整记录明细表';


