
ALTER TABLE `aigc_cluster_node` ADD COLUMN `instance_id` varchar(64) NULL DEFAULT '' COMMENT '实例id' AFTER `cluster_id`;
ALTER TABLE `aigc_cluster` ADD COLUMN `env_type` varchar(64) NULL DEFAULT '' COMMENT '环境类型' AFTER `service_type`;

ALTER TABLE `aigc_model_instance` ADD COLUMN `env_type` varchar(64) NULL DEFAULT '' COMMENT '环境类型' AFTER `source_ip`;

ALTER TABLE `aigc_app`
    ADD COLUMN `last_active_time` datetime NULL COMMENT '上次活跃时间' AFTER `notify_url`,
    ADD COLUMN `creator_id` bigint DEFAULT NULL COMMENT '创建人id' AFTER `last_active_time`,
    ADD COLUMN `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称' AFTER `creator_id`;

ALTER TABLE `aigc_model_info`
    MODIFY COLUMN `status` int NOT NULL DEFAULT 0 COMMENT '状态: 1-启用,2-禁用' AFTER `model_name`;

CREATE TABLE `resource_bill` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `month` int NOT NULL COMMENT '月份',
     `platform` varchar(32) DEFAULT '' COMMENT '云平台',
     `gpu_model` varchar(64) DEFAULT '' COMMENT 'GPU型号',
     `instance_id` varchar(64) NOT NULL COMMENT '实例id',
     `billing_type` varchar(32) NOT NULL COMMENT '计费类型',
     `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付款金额',
     `payment_time` datetime DEFAULT NULL COMMENT '付款时间',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`),
     KEY `idx_month` (`month`),
     KEY `idx_platform` (`platform`),
     KEY `instance_id` (`instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='资源账单表';