CREATE TABLE `aigc_model_instance_adjust_record` (
     `id` bigint NOT NULL AUTO_INCREMENT,
     `model_info` json COMMENT '模型信息',
     `creator_id` bigint DEFAULT NULL COMMENT '创建人id',
     `creator_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建人名称',
     `created_time` datetime NOT NULL COMMENT '创建时间',
     `revised_time` datetime DEFAULT NULL COMMENT '更新时间',
     `is_deleted` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除 0 否 1是',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模型实例调整记录表';