package com.zjkj.aigc.controller.task;

import com.zjkj.aigc.application.dto.AtomModelTask;
import com.zjkj.aigc.application.service.AtomModelService;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/2
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/atom/task")
@RequiredArgsConstructor
public class AtomAiTaskController {

    private final AtomModelService atomModelService;

    /**
     * 批量创建任务
     * @param reqList 请求参数
     * @param <T> 请求参数类型
     */
    @PostMapping("/batch/createAiTask")
    public <T> void batchCreateAiTask(@RequestBody List<CreateAiTaskReq<T>> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.info.empty"));
        }

        try {
            atomModelService.batchPushTask(reqList);
        } catch (Exception e) {
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR, MessageUtils.getMessage("business.task.create.failed"));
        }
    }

    /**
     * 查询任务信息
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/batch/getAiTask")
    public List<GetAiTaskResp<Object, Object>> batchGetAiTask(@Validated @RequestBody BatchGetAiTaskReq req) {
        if (!StringUtils.hasText(req.getModelName()) || CollectionUtils.isEmpty(req.getTaskIds())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.model.name.ids.empty"));
        }

        List<AtomModelTask<String>> atomModelTasks = atomModelService.queryByIds(req.getModelName(), req.getTaskIds());
        if (CollectionUtils.isEmpty(atomModelTasks)) {
            return Collections.emptyList();
        }

        return atomModelTasks.stream().map(atomModelService::convertToAiTaskResp).collect(Collectors.toList());
    }

    /**
     * 范围删除任务
     * @param req 请求参数
     */
    @PostMapping("/deleteAiTask")
    public void deleteAiTask(@RequestBody GetAiTaskReq req) {
        if (!StringUtils.hasText(req.getTaskId()) || !StringUtils.hasText(req.getModelName())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.id.model.name.empty"));
        }

        atomModelService.deleteTask(req.getModelName(), req.getTaskId());

    }

}
