package com.zjkj.aigc.controller.dashvector;

import com.aliyun.dashvector.models.Doc;
import com.aliyun.dashvector.models.DocOpResult;
import com.zjkj.aigc.application.service.DashVectorService;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.dashvector.dto.req.CreateCollectionReq;
import com.zjkj.aigc.dashvector.dto.req.CreateDocReq;
import com.zjkj.aigc.dashvector.dto.req.DeleteDocReq;
import com.zjkj.aigc.dashvector.dto.req.QueryDocReq;
import com.zjkj.aigc.dashvector.dto.resp.DocOperateResp;
import com.zjkj.aigc.dashvector.dto.resp.DocResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestLog
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/dashvector/")
public class DashVectorController {

    private final DashVectorService dashVectorService;

    @PostMapping("createCollection")
    public DataResponse<Void> createCollection(@Validated @RequestBody CreateCollectionReq req) {
        dashVectorService.createCollection(req);
        return DataResponse.ok();
    }

    @PostMapping("createDoc")
    public DataResponse<List<DocOperateResp>> createDoc(@Validated @RequestBody CreateDocReq req){
        return DataResponse.ok(dashVectorService.createDoc(req));
    }

    @PostMapping("deleteDoc")
    public DataResponse<List<DocOperateResp>> createDoc(@Validated @RequestBody DeleteDocReq req){
        return DataResponse.ok(dashVectorService.deleteDoc(req));
    }

    @PostMapping("queryDoc")
    public DataResponse<List<DocResp>> queryDoc(@Validated @RequestBody QueryDocReq req){
        return DataResponse.ok(dashVectorService.queryDoc(req));
    }
}
