package com.zjkj.aigc.controller.task;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.zjkj.aigc.application.service.AiTaskService;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.task.AigcTaskPriorityReq;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.task.client.AiTaskClient;
import com.zjkj.aigc.task.dto.req.AiTaskStatusReq;
import com.zjkj.aigc.task.dto.req.BaseAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.BatchOperateReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;
import com.zjkj.aigc.task.dto.resp.AiTaskStatusResp;
import com.zjkj.aigc.task.dto.resp.CreateAiTaskResp;
import com.zjkj.aigc.task.dto.resp.GetAiTaskResp;
import com.zjkj.aigc.task.dto.resp.OperateFailedResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 *  AI任务接口
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@RefreshScope
@RequestLog
@RestController
@RequestMapping("/aigc/v1/task")
@RequiredArgsConstructor
public class AiTaskController implements AiTaskClient {

    @Value("${ai.task.priority.request-key:}")
    private String requestKey;

    private static final String X_CHANNEL_KEY = "x-channel";
    private static final String X_REQUEST_KEY = "x-request-key";
    private final AiTaskService aiTaskService;

    /**
     * 获取渠道
     * @return 请求头
     */
    public String channel() {
        String channel = null;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            channel = request.getHeader(X_CHANNEL_KEY);
        } catch (Exception e) {
            log.error("获取请求头失败", e);
        }
        return channel;
    }
    /**
     * 创建任务
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 请求参数类型
     */
    @Override
    public <T> DataResponse<CreateAiTaskResp> createAiTask(@Validated @RequestBody CreateAiTaskReq<T> req) {
        if (!StringUtils.hasText(req.getTaskSource())) {
            req.setTaskSource(channel());
        }

        CreateAiTaskResp result = aiTaskService.createAiTask(req);
        return DataResponse.ok(result);
    }


    @Override
    public <T> DataResponse<String> asyncBatchCreateAiTask(@Validated @RequestBody List<CreateAiTaskReq<T>> reqList) {
        aiTaskService.asyncBatchCreateAiTask(reqList);
        return DataResponse.ok();
    }

    /**
     * 查询任务信息
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 返回参数类型
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> DataResponse<T> getAiTask(@Validated @RequestBody GetAiTaskReq req) {
        BatchGetAiTaskReq taskReq = BeanUtil.copyProperties(req, BatchGetAiTaskReq.class);
        List<String> taskIds = StringUtils.hasText(req.getTaskId()) ? Splitter.on(StringPool.COMMA).splitToList(req.getTaskId()) : Lists.newArrayList();
        taskReq.setTaskIds(taskIds);
        List<GetAiTaskResp<Object, Object>> resultList = aiTaskService.batchGetAiTask(taskReq);
        if (CollectionUtils.isEmpty(resultList)) {
            return DataResponse.ok();
        }

        return DataResponse.ok(taskIds.size() > 1 ? (T) resultList : (T) CollectionUtils.firstElement(resultList));
    }

    @Override
    public <T, U> DataResponse<GetAiTaskResp<T, U>> newGetAiTask(@Validated @RequestBody GetAiTaskReq req) {
        GetAiTaskResp<T, U> result = aiTaskService.newGetAiTask(req);
        return DataResponse.ok(result);
    }

    /**
     * 批量获取任务信息
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 返回参数类型
     */
    @Override
    public <T, U> DataResponse<List<GetAiTaskResp<T, U>>> batchGetAiTask(@Validated @RequestBody BatchGetAiTaskReq req) {
        List<GetAiTaskResp<T, U>> resultList = aiTaskService.batchGetAiTask(req);
        return DataResponse.ok(resultList);
    }

    /**
     * 获取任务状态统计
     * @param req 请求参数
     * @return 返回结果
     */
    @Override
    public DataResponse<List<AiTaskStatusResp>> getTaskStatus(@Validated @RequestBody AiTaskStatusReq req) {
        List<AiTaskStatusResp> resultList = aiTaskService.queryTaskStatusGroup(req);
        return DataResponse.ok(resultList);
    }
    /**
     *  取消任务
     * @param req 请求参数
     * @return 返回结果
     */
    @Override
    public DataResponse<OperateFailedResp> cancelAiTask(@Validated @RequestBody BaseAiTaskReq req) {
        List<String> taskIdlist = aiTaskService.cancelAiTask(req);
        OperateFailedResp result = new OperateFailedResp();
        result.setFailed(taskIdlist);
        return DataResponse.ok(result);
    }
    /**
     * 重置任务状态
     * @param req 请求参数
     * @return 返回结果
     */
    @Override
    public DataResponse<String> resetTaskState(@Validated @RequestBody BaseAiTaskReq req) {
        aiTaskService.resetTaskState(req);
        return DataResponse.ok();
    }

    /**
     * 批量重置任务状态
     *
     * @param req 请求参数
     * @return 返回结果
     */
    @Override
    public DataResponse<OperateFailedResp> batchResetTaskState(@Validated @RequestBody BatchOperateReq req) {
        List<String> taskIdlist = aiTaskService.batchResetTaskState(req);
        OperateFailedResp result = new OperateFailedResp();
        result.setFailed(taskIdlist);
        return DataResponse.ok(result);
    }

    /**
     * 同步推理任务
     * 实时返回推理结果
     * @param req 请求参数
     * @return 返回结果
     * @param <T> 请求参数类型
     * @param <U> 返回参数类型
     */
    @Override
    public <T, U> DataResponse<GetAiTaskResp<T, U>> syncInferenceTask(@Validated @RequestBody CreateAiTaskReq<T> req) {
        GetAiTaskResp<T, U> result = aiTaskService.syncInferenceTask(req);
        return DataResponse.ok(result);
    }

    /**
     * 任务优先级
     * @param req 请求参数
     * @return 返回结果
     */
    @PostMapping("/priority")
    public DataResponse<Integer> priority(@Validated @RequestBody AigcTaskPriorityReq req,
                                         @RequestHeader(value = X_REQUEST_KEY, required = false) String reqRequestKey) {
        BaseBizException.isTrue(StringUtils.hasText(requestKey), CustomErrorCode.NOT_SUPPORTED);
        BaseBizException.isTrue(Objects.equals(reqRequestKey, requestKey), CustomErrorCode.NOT_SUPPORTED, MessageUtils.getMessage("business.request.key.mismatch"));

        int count = aiTaskService.priority(req);
        return DataResponse.ok(count);
    }
}
