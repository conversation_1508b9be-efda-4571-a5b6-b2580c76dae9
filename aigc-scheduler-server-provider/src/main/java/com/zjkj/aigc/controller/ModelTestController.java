package com.zjkj.aigc.controller;

import com.zjkj.aigc.application.service.ModelTestService;
import com.zjkj.aigc.common.dto.ModelTestBriefDTO;
import com.zjkj.aigc.common.dto.ModelTestReportSubmitDTO;
import com.zjkj.aigc.common.req.model.TaskModelQuery;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模型测试
 *
 * <AUTHOR>
 * @since 2024/10/17
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/model-test")
@RequiredArgsConstructor
public class ModelTestController extends BaseController {

    private final ModelTestService modelTestService;

    /**
     * 测试报告请求
     *
     * @param query 查询参数
     */
    @PostMapping("/report/request")
    public ModelTestBriefDTO reportRequest(@Validated @RequestBody TaskModelQuery query) {
        return modelTestService.reportRequest(query);
    }

    /**
     * 测试报告提交
     *
     * @param reportSubmit 报告提交参数
     */
    @PostMapping("/report/submit")
    public void reportSubmit(@Validated @RequestBody ModelTestReportSubmitDTO<ModelTestReportInfo> reportSubmit) {
        modelTestService.reportSubmit(reportSubmit);
    }


}
