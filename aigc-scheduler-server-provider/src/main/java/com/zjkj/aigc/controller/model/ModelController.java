package com.zjkj.aigc.controller.model;

import com.zjkj.aigc.application.service.AiTaskService;
import com.zjkj.aigc.application.service.IAigcModelInstanceService;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.req.model.AigcModelHeartbeat;
import com.zjkj.aigc.common.req.model.AigcModelRegister;
import com.zjkj.aigc.common.util.Jackson;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.common.req.model.HeartbeatCheckReq;
import com.zjkj.aigc.common.req.model.TaskModelQuery;
import com.zjkj.aigc.common.req.model.TaskModelResultCallbackReq;
import com.zjkj.aigc.common.dto.AbstractTaskDTO;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.domain.task.service.ModelHeartbeatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 算法模型接口
 *
 * <AUTHOR>
 * @since 2024/6/3
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/v1/model")
public class ModelController {

    private final AiTaskService aiTaskService;
    private final ModelHeartbeatService modelHeartbeatService;
    private final RegisterAuthService registerAuthService;
    private final IAigcModelInstanceService aigcModelInstanceService;
    /**
     * 查询任务信息
     *
     * @param query 查询参数
     * @return AbstractTaskDTO
     */
    @PostMapping("/queryAigcTask")
    public AbstractTaskDTO queryAigcTask(@Validated @RequestBody TaskModelQuery query) {
        return aiTaskService.getWaitTaskOfQueue(query);
    }

    /**
     * 任务结果回调
     *
     * @param req 请求参数
     */
    @PostMapping("/taskResultCallback")
    public void taskModelResultCallback(@Validated @RequestBody TaskModelResultCallbackReq<Map<String, Object>> req) {
        aiTaskService.taskResultCallback(req);
    }

    /**
     * 任务模型心跳接口
     *
     * @param req 回调参数
     */
    @Deprecated
    @PostMapping("/checkHeartbeat")
    public void checkHeartbeat(@Validated @RequestBody HeartbeatCheckReq req) {
//        modelHeartbeatService.saveHeartbeatCheck(req);
    }

    /**
     * 模型注册
     * @param register 注册参数
     */
    @PostMapping("/register")
    public String register(@Validated @RequestBody AigcModelRegister register) {
        AigcRegisterBackDTO registerDTO = aigcModelInstanceService.register(register);
        return registerAuthService.encrypt(Jackson.toJSONString(registerDTO));
    }

    /**
     * 模型心跳
     * @param heartbeat 心跳参数
     */
    @PostMapping("/heartbeat")
    public void heartbeat(@Validated @RequestBody AigcModelHeartbeat heartbeat) {
        aigcModelInstanceService.heartbeat(heartbeat);
    }

}
