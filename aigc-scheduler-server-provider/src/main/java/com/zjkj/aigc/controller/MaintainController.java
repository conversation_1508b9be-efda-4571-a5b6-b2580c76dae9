package com.zjkj.aigc.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 *  维护控制器
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/maintain")
@RequiredArgsConstructor
public class MaintainController {

    private final AigcTaskService aigcTaskService;

    /**
     * 维护任务队列
     *
     * @param endTime 结束时间
     */
    @PostMapping("/taskRefreshQueue")
    public void taskRefreshQueue(String endTime) {
        LocalDateTime localDateTime = LocalDateTimeUtil.of(DateUtil.parse(endTime));
        aigcTaskService.taskRefreshQueue(localDateTime);
    }
}
