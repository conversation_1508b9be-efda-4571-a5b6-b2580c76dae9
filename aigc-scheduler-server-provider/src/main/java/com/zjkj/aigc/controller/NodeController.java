package com.zjkj.aigc.controller;

import com.zjkj.aigc.application.service.IAigcNodeInstanceService;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.common.dto.node.AigcRegisterBackDTO;
import com.zjkj.aigc.common.req.node.AigcNodeHeartbeat;
import com.zjkj.aigc.common.req.node.AigcNodeRegisterReq;
import com.zjkj.aigc.common.util.Jackson;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 节点管理
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/v1/node")
public class NodeController {

    private final IAigcNodeInstanceService aigcNodeInstanceService;
    private final RegisterAuthService registerAuthService;

    /**
     * 节点注册
     */
    @PostMapping("/register")
    public String register(@Validated @RequestBody AigcNodeRegisterReq register) {
        AigcRegisterBackDTO registerDTO = aigcNodeInstanceService.register(register);
        return registerAuthService.encrypt(Jackson.toJSONString(registerDTO));
    }

    /**
     * 节点心跳
     */
    @PostMapping("/heartbeat")
    public void heartbeat(@Validated @RequestBody AigcNodeHeartbeat heartbeat) {
        aigcNodeInstanceService.heartbeat(heartbeat);
    }


}
