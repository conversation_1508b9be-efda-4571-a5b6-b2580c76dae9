package com.zjkj.aigc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableDiscoveryClient
@MapperScan({"com.zjkj.aigc.infrastructure.**.mapper"})
@SpringBootApplication(scanBasePackages = {"com.zjkj.aigc.**"})
public class AigcSchedulerServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(AigcSchedulerServerApplication.class);
    }
}
