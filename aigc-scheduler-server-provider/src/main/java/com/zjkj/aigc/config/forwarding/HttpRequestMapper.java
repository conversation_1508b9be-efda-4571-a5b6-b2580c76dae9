package com.zjkj.aigc.config.forwarding;

import org.apache.http.message.BasicHeader;
import org.apache.http.message.HeaderGroup;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;

import static java.net.URI.create;
import static java.util.Collections.list;
import static org.springframework.http.HttpMethod.valueOf;
/**
 *
 * <AUTHOR>
 * @since 2024/6/6
 */
public class HttpRequestMapper {

    public RequestEntity<byte[]> map(HttpServletRequest request, String targetUrl, String prefix) throws IOException {
        HttpMethod method = extractMethod(request);
        URI uri = extractUri(request, targetUrl, prefix);
        HttpHeaders headers = extractHeaders(request);
        byte[] body = extractBody(request);
        return new RequestEntity<>(body, headers, method, uri);
    }

    private URI extractUri(HttpServletRequest request, String targetUrl, String prefix) {
        String contextPath = request.getContextPath();
        String uri = request.getRequestURI();
        if (StringUtils.hasText(contextPath)) {
            uri = uri.replace(contextPath, "");
        }
        if (StringUtils.hasText(prefix)) {
            uri = uri.replace(prefix, "");
        }

        String query = request.getQueryString() == null ? "" : "?" + request.getQueryString();
        return create(targetUrl + uri + query);
    }

    private HttpMethod extractMethod(HttpServletRequest request) {
        return valueOf(request.getMethod());
    }

    protected static final HeaderGroup HOP_BY_HOP_HEADERS;
    static {
        HOP_BY_HOP_HEADERS = new HeaderGroup();
        String[] hopHeaders = new String[] {
                HttpHeaders.CONTENT_LENGTH, HttpHeaders.ACCEPT_ENCODING, HttpHeaders.ACCEPT,
                HttpHeaders.CONNECTION, HttpHeaders.PROXY_AUTHENTICATE, HttpHeaders.PROXY_AUTHORIZATION,
                HttpHeaders.HOST, HttpHeaders.TE, HttpHeaders.TRANSFER_ENCODING, HttpHeaders.UPGRADE };
        for (String header : hopHeaders) {
            HOP_BY_HOP_HEADERS.addHeader(new BasicHeader(header, null));
        }
    }

    private HttpHeaders extractHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if (HOP_BY_HOP_HEADERS.containsHeader(headerName)) {
                continue;
            }

            List<String> headerValues = list(request.getHeaders(headerName));
            headers.put(headerName, headerValues);
        }
        return headers;
    }

    private byte[] extractBody(HttpServletRequest request) throws IOException {
        byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
        return body.length != 0 ? body : null;
    }
}
