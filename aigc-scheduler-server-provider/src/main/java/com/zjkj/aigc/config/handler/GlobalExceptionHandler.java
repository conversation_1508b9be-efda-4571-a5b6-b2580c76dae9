package com.zjkj.aigc.config.handler;

import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.exception.WithErrorCodeException;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@RestControllerAdvice(basePackages = {"com.zjkj.aigc.controller"})
public class GlobalExceptionHandler {

    /**
     * 业务异常处理
     *
     * @param ex 异常
     * @return 返回结果
     */
    @ExceptionHandler(BaseBizException.class)
    public DataResponse<?> baseBizExceptionHandler(BaseBizException ex) {
        log.warn("业务异常，错误码:{}, 错误信息描述:{}", ex.getErrorCode().getCode(), ex.getMessage());
        return DataResponse.fail(ex.getErrorCode().getCode(), ex.getMessage());
    }

    /**
     * 业务异常处理
     *
     * @param ex 异常
     * @return 返回结果
     */
    @ExceptionHandler(WithErrorCodeException.class)
    public DataResponse<?> withErrorCodeExceptionHandler(WithErrorCodeException ex) {
        log.warn("错误码:{}, 错误信息描述:{}", ex.getErrorCode().getCode(), ex.getMessage(), ex);
        return DataResponse.fail(ex.getErrorCode().getCode(), ex.getMessage());
    }

    /**
     * 请求参数不合法异常处理
     *
     * @param ex 异常
     * @return 返回结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public DataResponse<?> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        List<String> errMsg = new ArrayList<>(bindingResult.getErrorCount());
        bindingResult.getFieldErrors().forEach(s -> errMsg.add(s.getDefaultMessage()));
        log.warn("参数校验异常, 异常信息:{}", errMsg);
        return DataResponse.fail(CustomErrorCode.PARAM_ERROR.getCode(), String.join(StringPool.SEMICOLON, errMsg));
    }

    /**
     * 缺少请求体异常处理
     *
     * @param ex 异常
     * @return 返回结果
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public DataResponse<?> httpMessageNotReadableExceptionHandler(HttpMessageNotReadableException ex) {
        log.warn("缺少请求体异常,异常信息", ex);
        return DataResponse.fail(CustomErrorCode.PARAM_ERROR.getCode(), "缺少请求体/参数格式不合法");
    }

    /**
     * 忽略参数错误异常
     *
     * @param ex 异常
     * @return 返回结果
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public DataResponse<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        log.warn("missingServletRequestParameterExceptionHandler", ex);
        return DataResponse.fail(CustomErrorCode.PARAM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * feign请求失败异常处理
     *
     * @param request 请求
     * @param ex      异常
     * @return 返回结果
     */
    @ExceptionHandler(FeignException.class)
    public DataResponse<?> feignExceptionHandler(HttpServletRequest request, FeignException ex) {
        String reqBody = "";
        if (ex.hasRequest() && Objects.nonNull(ex.request().body())) {
            reqBody = new String(ex.request().body(), Optional.ofNullable(ex.request().charset()).orElse(Charset.defaultCharset()));
        }

        log.error("feign请求失败异常, path[{}], reqBody[{}]", request.getRequestURI(), reqBody, ex);
        return DataResponse.fail(CustomErrorCode.FEIGN_INVOKE_ERROR.getCode(), CustomErrorCode.FEIGN_INVOKE_ERROR.getMessage());
    }

    /**
     * 数据重复异常处理
     *
     * @param request 请求
     * @param ex      异常
     * @return 返回结果
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public DataResponse<?> duplicateKeyExceptionHandler(HttpServletRequest request, Exception ex) {
        log.error("数据库数据重复异常, path[{}]", request.getRequestURI(), ex);
        return DataResponse.fail(CustomErrorCode.DB_DATA_ALREADY_EXISTS.getCode(), CustomErrorCode.DB_DATA_ALREADY_EXISTS.getMessage());
    }

    /**
     * 未知异常处理
     *
     * @param request 请求
     * @param ex      异常
     * @return 返回结果
     */
    @ExceptionHandler(Exception.class)
    public DataResponse<?> exceptionHandler(HttpServletRequest request, Exception ex) {
        log.error("系统内部异常, path[{}]", request.getRequestURI(), ex);
        return DataResponse.fail(CustomErrorCode.UNKNOWN_ERROR.getCode(), CustomErrorCode.UNKNOWN_ERROR.getMessage());
    }

}
