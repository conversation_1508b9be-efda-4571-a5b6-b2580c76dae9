package com.zjkj.aigc.config;

import com.zjkj.aigc.common.i18n.LocaleUtils;
import com.zjkj.aigc.config.interceptor.LocaleInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

/**
 * Web MVC配置类 - 包含国际化配置
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 语言环境解析器
     * 用于确定当前请求的语言环境
     */
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver resolver = new SessionLocaleResolver();
        // 设置默认语言环境
        resolver.setDefaultLocale(LocaleUtils.DEFAULT_LOCALE);
        return resolver;
    }

    /**
     * 语言环境变更拦截器
     * 支持通过URL参数切换语言：?lang=zh_CN
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        // 设置语言参数名
        interceptor.setParamName("lang");
        return interceptor;
    }

    /**
     * 自定义语言环境拦截器
     * 支持从请求头获取语言信息
     */
    @Bean
    public LocaleInterceptor customLocaleInterceptor() {
        return new LocaleInterceptor();
    }

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加Spring自带的语言切换拦截器
        registry.addInterceptor(localeChangeInterceptor());
        
        // 添加自定义的语言环境拦截器
        registry.addInterceptor(customLocaleInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/static/**", "/error");
    }
}
