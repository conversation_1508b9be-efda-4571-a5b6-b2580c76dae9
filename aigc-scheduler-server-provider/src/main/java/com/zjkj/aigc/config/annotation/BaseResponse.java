package com.zjkj.aigc.config.annotation;

import com.zjkj.aigc.config.handler.ResponseResultHandler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 统一响应注解
 * 添加注解后，统一响应体才能生效
 * @see ResponseResultHandler
 * <AUTHOR>
 * @since 2024/5/30
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface BaseResponse {
}
