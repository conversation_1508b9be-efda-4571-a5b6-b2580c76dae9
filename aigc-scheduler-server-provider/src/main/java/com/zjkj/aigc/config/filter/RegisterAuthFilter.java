package com.zjkj.aigc.config.filter;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.zjkj.aigc.application.config.wrapper.CacheHttpServletRequestWrapper;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Slf4j
public class RegisterAuthFilter extends OncePerRequestFilter {

    private static final Long MAX_INTERVAL = 60 * 1000L;
    private static final Set<String> ENV_SET= Set.of("dev", "sit", "uat");
    private final RegisterAuthService registerAuthService;

    public RegisterAuthFilter(RegisterAuthService registerAuthService) {
        this.registerAuthService = registerAuthService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        request = new CacheHttpServletRequestWrapper(request);
        boolean legal = isLegal(request, response);
        if (!legal) {
            return;
        }
        filterChain.doFilter(request, response);
    }

    /**
     * 是否合法
     *
     * @param request  请求
     * @param response 响应
     * @return boolean
     * @throws IOException 异常
     */
    public boolean isLegal(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String body = ServletUtil.getBody(request);
        if (!JSON.isValidObject(body)) {
            log.info("RegisterAuthFilter. 请求体不合法 req:{}", body);
            writeUnAuthResponse(response);
            return false;
        }

        Map<String, Object> enBodyMap = JSON.parseObject(body, new TypeReference<>() {});
        String data = (String) enBodyMap.get("data");
        if (!StringUtils.hasText(data)) {
            log.info("RegisterAuthFilter. 请求体不合法-data为空 req:{}", body);
            writeUnAuthResponse(response);
            return false;
        }

        Map<String, Object> bodyMap;
        try {
            String decryptStr = registerAuthService.decrypt(data);
            bodyMap = JSON.parseObject(decryptStr, new TypeReference<>() {});
        } catch (Exception e) {
            log.error("RegisterAuthFilter. 解密异常 req:{}, err:{}", body, e.getMessage(), e);
            writeUnAuthResponse(response);
            return false;
        }

        Long ts = Optional.ofNullable(bodyMap.get("ts"))
                .filter(obj -> obj instanceof Number)
                .map(obj -> {
                    long timestamp = ((Number) obj).longValue();
                    return timestamp < 100000000000L ? timestamp * 1000 : timestamp;
                })
                .orElse(0L);
        long compare = Math.abs(System.currentTimeMillis() - ts);
        if (compare > MAX_INTERVAL) {
            writeUnAuthResponse(response);
            log.info("RegisterAuthFilter. 请求时间超过限制 req:{}", JSON.toJSONString(bodyMap));
            return false;
        }

        // 设置额外信息
        setExtra(request, bodyMap);

        String newBody = JSON.toJSONString(bodyMap);
        ((CacheHttpServletRequestWrapper) request).setBody(newBody.getBytes(StandardCharsets.UTF_8));
        return true;
    }

    /**
     * 设置额外信息
     *
     * @param request 请求
     * @param bodyMap 请求体
     */
    private void setExtra(HttpServletRequest request, Map<String, Object> bodyMap) {
        try {
            // 获取请求IP
            String clientIp = ServletUtil.getClientIP(request);
            bodyMap.put("sourceIp", clientIp);

            // 获取环境类型
            String endpoint = (String) bodyMap.get("endpoint");
            String envType = getEnvType(endpoint);
            if (StringUtils.hasText(envType)) {
                bodyMap.put("envType", envType);
            }
        } catch (Exception e) {
            log.error("RegisterAuthFilter. 设置额外信息异常 err:{}", e.getMessage(), e);
        }

    }

    /**
     * 获取环境类型
     * @param endpoint 端点
     * @return 环境类型
     */
    private String getEnvType(String endpoint) {
        String currentEnvType = EnvUtil.getCurrentEnvType();
        if (!StringUtils.hasText(endpoint)) {
            return currentEnvType;
        }

        try {
            String host = new URL(endpoint).getHost();
            String firstHostPart = host.split("\\.")[0];
            String[] envParts = firstHostPart.split(StringPool.DASH);
            if (envParts.length > 1) {
                String reqEnv = envParts[0];
                return ENV_SET.stream().anyMatch(reqEnv::startsWith) ? reqEnv : currentEnvType;
            }
        } catch (MalformedURLException | ArrayIndexOutOfBoundsException ex) {
            log.error("getEnvType(). endpoint:{}, err:{}", endpoint, ex.getMessage(), ex);
        }
        return currentEnvType;
    }

    /**
     * 未授权返回
     *
     * @param response 响应
     * @throws IOException 异常
     */
    public void writeUnAuthResponse(HttpServletResponse response) throws IOException {
        String resp = Jackson.toJSONString(DataResponse.unAuthorized());
        response.getOutputStream().write(resp.getBytes(StandardCharsets.UTF_8));
    }
}
