package com.zjkj.aigc.config;

import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.zjkj.aigc.application.config.AigcChannelProperties;
import com.zjkj.aigc.application.config.AtomModelProperties;
import com.zjkj.aigc.application.config.ProxyRouteProperties;
import com.zjkj.aigc.application.config.TaskSourceProperties;
import com.zjkj.aigc.application.config.TritonProperties;
import com.zjkj.aigc.application.mq.CustomChannelBinder;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.config.filter.AigcChannelFilter;
import com.zjkj.aigc.config.filter.RegisterAuthFilter;
import com.zjkj.aigc.config.filter.ReverseProxyFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;

import javax.servlet.Filter;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Optional;


/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@EnableRetry
@Configuration
@RequiredArgsConstructor
@EnableBinding({CustomChannelBinder.class})
@EnableConfigurationProperties({
        ProxyRouteProperties.class,
        AtomModelProperties.class,
        AigcChannelProperties.class,
        TritonProperties.class,
        TaskSourceProperties.class,
})
public class WebAutoConfiguration {

    private final RestTemplate restTemplate;
    private final RegisterAuthService registerAuthService;
    private final AigcChannelProperties aigcChannelProperties;

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> {
            builder.simpleDateFormat(DATE_TIME_FORMAT);
            builder.serializers(new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        };
    }

    /**
     *  反向代理过滤器
     * @param proxyRouteProperties 代理路由配置
     * @return 过滤器注册
     */
//    @Bean
    public FilterRegistrationBean<Filter> reverseProxyInjectFilter(ProxyRouteProperties proxyRouteProperties) {
        FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>();
        ReverseProxyFilter reverseProxyFilter = new ReverseProxyFilter(Ordered.LOWEST_PRECEDENCE, restTemplate, proxyRouteProperties);
        filterRegistrationBean.setFilter(reverseProxyFilter);
        filterRegistrationBean.addUrlPatterns("/aigc/v1/task/*", "/aigc/v1/model/*");
        return filterRegistrationBean;
    }

    /**
     * 节点&模型注册过滤器
     *
     * @return 过滤器注册
     */
    @Bean
    public FilterRegistrationBean<Filter> registerAuthFilter() {
        FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>();
        RegisterAuthFilter registerAuthFilter = new RegisterAuthFilter(registerAuthService);
        filterRegistrationBean.setFilter(registerAuthFilter);
        filterRegistrationBean.addUrlPatterns(
                "/aigc/v1/node/register",
                "/aigc/v1/node/heartbeat",
                "/aigc/v1/model/register",
                "/aigc/v1/model/heartbeat"
        );
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean<Filter> aigcChannelFilter() {
        FilterRegistrationBean<Filter> filterRegistrationBean = new FilterRegistrationBean<>();
        Map<String, String> configMap = Optional.ofNullable(aigcChannelProperties.getConfigMap())
                .orElse(Map.of());
        AigcChannelFilter aigcChannelFilter = new AigcChannelFilter(configMap);
        filterRegistrationBean.setFilter(aigcChannelFilter);
        filterRegistrationBean.addUrlPatterns("/aigc/v1/task/*");
        return filterRegistrationBean;
    }
}
