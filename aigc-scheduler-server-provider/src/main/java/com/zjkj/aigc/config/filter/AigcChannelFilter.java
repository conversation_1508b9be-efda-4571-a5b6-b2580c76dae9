package com.zjkj.aigc.config.filter;

import cn.hutool.extra.servlet.ServletUtil;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/13
 */
@Slf4j
public class AigcChannelFilter extends OncePerRequestFilter {

    private static final String X_SECRET_KEY = "secretKey";
    private static final String X_CHANNEL_KEY = "x-channel";

    private final Map<String, String> channelMap;
    public AigcChannelFilter(Map<String, String> channelMap) {
        this.channelMap = channelMap;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String channel = ServletUtil.getHeader(request, X_CHANNEL_KEY, StandardCharsets.UTF_8);
        if (StringUtils.hasText(channel) &&  channelMap.containsKey(channel)) {
            String reqSecretKey = ServletUtil.getHeader(request, X_SECRET_KEY, StandardCharsets.UTF_8);
            String secretKey = channelMap.get(channel);
            if (!Objects.equals(secretKey, reqSecretKey)) {
                String resp = Jackson.toJSONString(DataResponse.unAuthorized());
                response.getOutputStream().write(resp.getBytes(StandardCharsets.UTF_8));
                return;
            }
        }

        filterChain.doFilter(request, response);
    }
}
