package com.zjkj.aigc.config.filter;


import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.zjkj.aigc.application.config.ProxyRouteProperties;
import com.zjkj.aigc.application.config.wrapper.CacheHttpServletRequestWrapper;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.config.forwarding.HttpRequestMapper;
import com.zjkj.aigc.config.forwarding.HttpResponseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
/**
 *
 * <AUTHOR>
 * @since 2024/6/6
 */
@Slf4j
public class ReverseProxyFilter extends OncePerRequestFilter implements Ordered {

    private final int order;
    private final RestTemplate restTemplate;
    private final ProxyRouteProperties proxyRouteProperties;
    private final HttpRequestMapper httpRequestMapper;
    private final HttpResponseMapper httpResponseMapper;

    public ReverseProxyFilter(int order, RestTemplate restTemplate, ProxyRouteProperties proxyRouteProperties) {
        this.order = order;
        this.restTemplate = restTemplate;
        this.proxyRouteProperties = proxyRouteProperties;
        this.httpRequestMapper = new HttpRequestMapper();
        this.httpResponseMapper = new HttpResponseMapper();
    }

    @Override
    public int getOrder() {
        return order;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {

        boolean proxy = false;
        if (!CollectionUtils.isEmpty(proxyRouteProperties.getTaskTypes())) {
            // 包装请求
            request = new CacheHttpServletRequestWrapper(request);
            proxy = isProxy((CacheHttpServletRequestWrapper) request);
        }

        if (proxy) {
            try {
                // 代理转发
                exchange(request, response);
            } catch (Exception ex) {
                log.error("reverse proxy error. uri: {}", request.getRequestURI(), ex);
                response.setStatus(HttpStatus.OK.value());
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.setContentType(MediaType.APPLICATION_JSON_VALUE);
                String resp = JSON.toJSONString(DataResponse.fail("访问外部异常: " + ex.getMessage()));
                response.getOutputStream().write(resp.getBytes());
            }
            return;
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 代理转发
     * @param request 请求
     * @param response 响应
     * @throws IOException IO异常
     */
    private void exchange(HttpServletRequest request, HttpServletResponse response) throws IOException {
        RequestEntity<byte[]> requestEntity = httpRequestMapper.map(request, proxyRouteProperties.getTargetUrl(), proxyRouteProperties.getPrefix());
        ResponseEntity<byte[]> responseEntity = restTemplate.exchange(requestEntity, byte[].class);
        httpResponseMapper.map(responseEntity, response);
    }

    /**
     * 是否代理
     * @param request 请求
     * @return 是否代理
     */
    public boolean isProxy(CacheHttpServletRequestWrapper request) {

        if (!HttpMethod.POST.matches(request.getMethod())) {
            return false;
        }

        String contentType = request.getContentType();
        if (!StringUtils.hasText(contentType) || !contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
            return false;
        }

        String body = ServletUtil.getBody(request);
        if (!JSON.isValidObject(body)) {
            return false;
        }

        Map<String, Object> bodyMap = JSONObject.parseObject(body, new TypeReference<>() {
        });

        String taskType = (String) bodyMap.getOrDefault("taskType", null);
        boolean proxy = StringUtils.hasText(taskType) && proxyRouteProperties.getTaskTypes().contains(taskType);

        log.info("reverse proxy. uri: {}, taskType: {}, isProxy: {}", request.getRequestURI(), taskType, proxy);
        return proxy;
    }
}
