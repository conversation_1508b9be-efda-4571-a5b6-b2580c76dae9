package com.zjkj.aigc.config.handler;

import com.zjkj.aigc.common.util.Jackson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class RequestLogAspect {

    private static final int MAX_LENGTH = 4096;

    @Pointcut("within(@com.zjkj.aigc.config.annotation.RequestLog *)")
    public void point() {
    }

    @Around("point()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Map<String, Object> requestParams = getRequestParams(joinPoint);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 执行方法
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            stopWatch.stop();
            printLog(requestParams, "执行异常: " + throwable.getMessage(), stopWatch.getTotalTimeMillis());
            throw throwable;
        }

        stopWatch.stop();
        printLog(requestParams, result, stopWatch.getTotalTimeMillis());
        return result;
    }

    public void printLog(Map<String, Object> requestParams, Object result, long elapsedTime) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            String body = "";
            if (Objects.nonNull(result) && !isSpecialResponse(result)) {
                body = Jackson.toJSONString(result);
                if (StringUtils.hasText(body) && body.length() > MAX_LENGTH) {
                    body = body.substring(0, MAX_LENGTH) + "...";
                }
            }
            log.info("请求路径: {}, 耗时: {}ms, 请求参数: {}, 返回结果: {}", request.getRequestURI(), elapsedTime, Jackson.toJSONString(requestParams.values()), body);
        } catch (Exception ex) {
            log.warn("RequestLogAspect print log error. It does not affect the main process ", ex);
        }
    }

    /**
     *  是特殊响应
     * @param result 响应体
     * @return 是否特殊响应
     */
    public boolean isSpecialResponse(Object result) {
        return result instanceof Resource
                || result instanceof byte[]
                || result instanceof InputStream
                || result instanceof OutputStream
                || result instanceof MultipartFile;
    }

    /**
     * 获取入参
     *
     * @param joinPoint 切点
     * @return 入参
     */
    private Map<String, Object> getRequestParams(JoinPoint joinPoint) {
        Map<String, Object> requestParams = new HashMap<>();
        try {
            String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
            Object[] paramValues = joinPoint.getArgs();
            for (int i = 0; i < paramNames.length; i++) {
                Object value = paramValues[i];
                if (value instanceof HttpServletRequest || value instanceof HttpServletResponse) {
                    continue;
                }

                if (value instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) value;
                    value = file.getOriginalFilename();
                }
                requestParams.put(paramNames[i], value);
            }
        } catch (Exception ex) {
            log.warn("RequestLogAspect get request params error. It does not affect the main process ", ex);
        }
        return requestParams;
    }

}
