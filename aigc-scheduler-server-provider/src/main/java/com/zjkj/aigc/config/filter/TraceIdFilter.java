package com.zjkj.aigc.config.filter;

import com.zjkj.aigc.common.context.TraceId;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 *  TraceId 过滤器
 * <AUTHOR>
 * @since 2024/6/6
 */
public class TraceIdFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {

            String traceId = request.getHeader(TraceId.KEY_TRACE_ID);
            if (!StringUtils.hasText(traceId)) {
                traceId = TraceId.newTraceId();
            }

            //将 traceId 放入MDC中
            TraceId.setId(traceId);

            //将 traceId 写入响应头
            response.addHeader(TraceId.KEY_TRACE_ID, traceId);

            filterChain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }
}
