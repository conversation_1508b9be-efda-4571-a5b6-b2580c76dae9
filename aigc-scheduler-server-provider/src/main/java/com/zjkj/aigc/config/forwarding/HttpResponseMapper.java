package com.zjkj.aigc.config.forwarding;

import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 *
 * <AUTHOR>
 * @since 2024/6/6
 */
public class HttpResponseMapper {

    public void map(ResponseEntity<byte[]> responseEntity, HttpServletResponse response) throws IOException {
        setStatus(responseEntity, response);
        setHeaders(responseEntity, response);
        setBody(responseEntity, response);
    }

    private void setStatus(ResponseEntity<byte[]> responseEntity, HttpServletResponse response) {
        response.setStatus(responseEntity.getStatusCode().value());
    }

    private void setHeaders(ResponseEntity<byte[]> responseEntity, HttpServletResponse response) {
        responseEntity.getHeaders().forEach((name, values) -> values.forEach(value -> response.addHeader(name, value)));
    }

    private void setBody(ResponseEntity<byte[]> responseEntity, HttpServletResponse response) throws IOException {
        if (responseEntity.getBody() != null) {
            response.getOutputStream().write(responseEntity.getBody());
        }
    }
}
