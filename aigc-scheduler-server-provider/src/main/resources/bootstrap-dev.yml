#server:
#  tomcat:
#    uri-encoding: utf-8
#
#spring:
#  redis:
#    redisson:
#      config: |
#        singleServerConfig:
#          address: redis://redis.xggrydetmtdt.publicscs.bj.baidubce.com:6379
#          password: 1Qaz2Wsx3Edc
#          database: 12
#        codec: !<org.redisson.codec.JsonJacksonCodec> {}
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: **************************************************************************************************************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: ODBNy7aJscIX
#    hikari:
#      connection-test-query: SELECT 1
#      maximum-pool-size: 100
#      minimum-idle: 10
#    dynamic:
#      enabled: false
#  data:
#    mongodb:
#      uri: mongodb://aigc-dev:vMiQLl!<EMAIL>:27017/aigc-dev
#  rabbitmq:
#    host: rabbitmq-iiDMs4eR6LnxdOdw9dye.rabbitmq.bj.baidubce.com
#    port: 5672
#    username: admin
#    password: x30p%5MMqYTJoRbEegND9lZs
#    virtual-host: agic-dataset
#    publisher-confirm-type: correlated
#    publisher-confirms: true
#    publisher-returns: true
#    template:
#      mandatory: true
#  cloud:
#    stream:
#      rocketmq:
#        binder:
#          name-server: rocketmq-zjNTAQwW4Dt6ftjCzM9J.rocketmq.bj.baidubce.com:9876
#          access-key: prod_robot
#          secret-key: Z$NLzPSPibL6FKBq
#        default:
#          consumer:
#            push.maxReconsumeTimes: -1
#        bindings:
#          aigcSchedulerServerCallbackInput:
#            consumer:
#              push.maxReconsumeTimes: 1
#      bindings:
#        aigcSchedulerServerCallbackOutput:
#          destination: aigc-scheduler-server-callback-topic
#        aigcSchedulerServerCallbackInput:
#          destination: aigc-scheduler-server-callback-topic
#          group: aigc-scheduler-server-callback-group
#          consumer:
#            maxAttempts: 2
#            concurrency: 5
#            backOffInitialInterval: 4000
#            backOffMultiplier: 2.0
##        batchCreateOutput:
##          destination: aigc-scheduler-server-batch-create-topic
##        batchCreateInput:
##          destination: aigc-scheduler-server-batch-create-topic
##          group: aigc-scheduler-server-batch-create-group
##          consumer:
##            maxAttempts: 1
##            concurrency: 2
#
#  elasticsearch:
#    uris: https://**************:8200
#    username: superuser
#    password: 1tMP41Fux0lo%0Kc0y!ED%Vb
#
#mybatis-plus:
#  mapper-locations: classpath*:mapper/*.xml
#  type-aliases-package: com.zjkj.aigc.infrastructure.mybatis.aigc.entity*,;
#  type-handlers-package: com.zjkj.aigc.infrastructure.mybatis.aigc.mapper*,;
#  configuration:
#    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
#
#xxl:
#  job:
#    enabled: false
#    admin-addresses: ${aigc.xxl_job.admin-addresses}
#    appname: ${spring.profiles.active}-aigc-scheduler-server-executor
#    port: ${aigc.xxl_job.port}
#    log-path: ${aigc.xxl_job.log-path}
#    log-retention-days: ${aigc.xxl_job.log-retention-days}
#
#nacos:
#  addr: nacos.robot-sew.com:8848
#  namespace: dev-base
#  produce: false
#
#reverse-proxy:
#  route:
#    prefix:
#    target-url: https://dev-ai.textile-story.com
#    task-types:
#    # - aigc2
#    # - type2
#
#saas:
#  sso:
#    sdk:
#      admin:
#        enabled: false
#        enable: ${saas.sso.sdk.admin.enabled}
#
#aone:
#  endpoint: http://aone.tiangong.tech
#  app-name: inference
#  app-secret: 8782d5f6ac5e453a319cfa0677b2c584
#  key-type: 3
#
#triton:
#  endpoint: https://dev-%s.tiangong.tech/%s/api/v1
#  config-map:
#    "fg_cons_face_det__yolo_face": face_det
#
#dashvector:
#  enable: false
#  apiKey: sk-3IV3qrClvj6az0nHqa8AzJcc1sCxW59B7E4D71F4A11F0BF8D5A2EF940B540
#  # clusterEndPoint: vrs-cn-lf648anyd00039.dashvector.cn-hangzhou.aliyuncs.com
#  clusterEndPoint: vrs-cn-lf648anyd00039.vpc.cn-hangzhou.dashvector.aliyuncs.com
#
#ai:
#  task:
#    priority:
#      request-key: 0B4F5FB9F07745E388B5E1B491FE6C0E
#    channel:
#      config-map:
#        ZJ: 2F594908DA5674516AD79E2FB3E63FB3
#    schedule:
#      global-config:
#        - maxRetry: 3
#          timeout: 10m
#      config-list:
#      # - taskType: aigc
#      #   maxRetry: 3
#      #   timeout: 10m
#    batch-model:
#      model-prefix: BATCH_
#      priority: 1
#      model-list:
#      # - 4k,4k
#    atom-model:
#      rem-key-map:
#        duplicate:
#          - input_imgvector
#      config-map:
#        image_valid:
#          - valid_and_text
#          - tt_valid_and_text
#          - aidc_valid_and_text
#    source:
#      standard: JV
#      source-list: JV,ZJ,ROBOT
#      target-list:
#        - fg20_cons_gen,aigc-fg20-gen
#      ai-config-map:
#        JV:
#          endpoint: https://fashion-ai.jzcang.cn/openapi
#          appCode: 211e208d2816fc98e8e6fe1358b7122f
#        ZJ:
#          endpoint: https://fashion-ai.jzcang.cn/openapi
#          appCode: 6403ae4e64ea6ba281e6b14e0dff3583
#        ROBOT:
#          endpoint: https://fashion-ai.jzcang.cn/openapi
#          appCode: 76a32b77ce7642ad6a75d2cedc6b97e5