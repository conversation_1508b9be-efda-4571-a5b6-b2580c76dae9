<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="log.context.name" value="aigc-scheduler-server"/>
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID} --- [%15.15t] %-40.40logger{39} : [%tid] [%X{trace_id}] %m%n"/>

    <contextName>${log.context.name}</contextName>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>${log.pattern}</Pattern>
        </encoder>
    </appender>
    <appender name="infoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/info/${log.context.name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/info/${log.context.name}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${log.pattern}</Pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="errorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/error/${log.context.name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/error/${log.context.name}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>${log.pattern}</Pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 上报 apm 平台 -->
    <appender name="skywalking-grpc-log"
              class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>${log.pattern}</Pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="OTEL" class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
        <appender-ref ref="console"/>
    </appender>

    <!-- 设置日志级别 -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="infoLog"/>
        <appender-ref ref="errorLog"/>
        <appender-ref ref="skywalking-grpc-log"/>
    </root>
    <logger name="com.alibaba.nacos.client.naming" level="OFF"/>
</configuration>
