spring:
  application:
    name: aigc-scheduler-server
  main:
    allow-circular-references: true
  cloud:
    nacos:
      server-addr: nacos.robot-sew.com:8848
      config:
        namespace: ${spring.profiles.active}
        file-extension: yaml
        group: aigc-server
        shared-configs:
          - data-id: public.yaml
            group: aigc
            refresh: true
        refresh:
          enabled: true
      discovery:
        namespace: ${spring.cloud.nacos.config.namespace}
        group: ${spring.cloud.nacos.config.shared-configs[0].group}