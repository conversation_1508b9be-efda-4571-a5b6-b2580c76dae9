package com.zjkj.aigc.service;

import com.alibaba.fastjson.JSON;
import com.zjkj.aigc.application.service.RegisterAuthService;
import com.zjkj.aigc.common.vo.TaskSummaryStatResp;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 18:03
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@TestPropertySource(properties = {"spring.profiles.active=dev-aigc-csrs"})
@SpringBootTest
public class StatServiceTest {

    @Autowired
    private AigcTaskStatDayService taskStatService;
    @Autowired
    private RegisterAuthService registerAuthService;

    @Test
    public void summaryTest() {
        TaskSummaryStatCondition req = new TaskSummaryStatCondition();
        req.setStartTime("2024-10-01 00:00:00");
        req.setEndTime("2024-10-18 00:00:00");
        List<TaskSummaryStatResp> list = taskStatService.summary(req);
        log.info(JSON.toJSONString(list));
    }

    @Test
    public void encTest() {
        System.out.println(registerAuthService.encrypt("Hello world!"));

        String str = "d416lLfFJ+5DsFueMXbvrZSsuy8GvYndv8pdTiN4sIcKg3QWT7WC1Q==";
        String decrypt = registerAuthService.decrypt(str);
        System.out.println(decrypt);
    }
}
