package com.zjkj.aigc.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zjkj.aigc.application.service.DashVectorService;
import com.zjkj.aigc.dashvector.dto.req.CreateCollectionReq;
import com.zjkj.aigc.dashvector.dto.req.CreateDocReq;
import com.zjkj.aigc.dashvector.dto.req.DeleteDocReq;
import com.zjkj.aigc.dashvector.dto.req.QueryDocReq;
import com.zjkj.aigc.dashvector.dto.resp.DocOperateResp;
import com.zjkj.aigc.dashvector.dto.resp.DocResp;
import com.zjkj.aigc.dashvector.util.FilterConditionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@TestPropertySource(properties = {"spring.profiles.active=dev-aigc-csrs"})
@SpringBootTest
public class DashVectorServiceTest {

    @Resource
    private DashVectorService dashVectorService;

    @Test
    public void testCreateColl(){
        CreateCollectionReq req = new CreateCollectionReq();
        req.setCollectionName("quickstart");
//        Map<String,Integer> map = new HashMap<>();
//        map.put("myLove",0);
//        map.put("name",1);
//        map.put("age",2);
//        map.put("amount",3);
//        map.put("size",4);
//        req.setFiledsMap(map);
        dashVectorService.createCollection(req);
    }

    @Test
    public void testCreateDoc(){
        for(int i=1; i<=5; i++){
            CreateDocReq req = new CreateDocReq();
            req.setCollectionName("quickstart");
            List<Float> list = Lists.newArrayList(0.2f,0.2f,0.3f,0.4f);
            req.setVectorList(list);
            req.setId(String.valueOf(i));
            Map<String,Object> map = new HashMap<>();
            map.put("myLove",true);
            map.put("name","ywj"+i);
            map.put("age",20+i);
            map.put("amount",100f);
            map.put("size",400L);
            req.setFields(map);
            List<DocOperateResp> results = dashVectorService.createDoc(req);
            log.info(JSON.toJSONString(results));
        }
    }

    @Test
    public void testDeleteDoc(){
        DeleteDocReq req = new DeleteDocReq();
        req.setCollectionName("quickstart");
        List<String> list = Lists.newArrayList("1","2");
        req.setIdList(list);
        List<DocOperateResp> results = dashVectorService.deleteDoc(req);
        log.info(JSON.toJSONString(results));
        System.out.println(results);
    }

    @Test
    public void testQueryDocByVector(){
        QueryDocReq req = new QueryDocReq();
        req.setCollectionName("quickstart");
        List<Float> vector = Lists.newArrayList(0.2f,0.2f,0.3f,0.4f);
        req.setVectorList(vector);
        FilterConditionBuilder builder = new FilterConditionBuilder()
//                .lt("age", 22)
                .like("name","ywj");
        req.setFilter(builder.build());
        req.setIncludeVector(true);
        List<String> retFields = Lists.newArrayList("name","age");
        req.setReturnFields(retFields);
        List<DocResp> results = dashVectorService.queryDoc(req);
        log.info(JSON.toJSONString(results));
        System.out.println(results);
    }
}
