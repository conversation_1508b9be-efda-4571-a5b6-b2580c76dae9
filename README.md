# AIGC 调度服务器

## 项目概述

AIGC 调度服务器是一个基于Spring Boot的微服务架构项目，主要用于AI生成内容（AIGC）的任务调度和管理。该项目采用DDD（领域驱动设计）思想进行设计，使用分层架构实现业务逻辑的解耦和系统的可维护性。

## 技术栈

- **Java版本**: 11
- **构建工具**: Maven
- **核心框架**: Spring Boot 2.6.6
- **微服务框架**: Spring Cloud + Alibaba Nacos
- **消息队列**: RocketMQ
- **ORM框架**: MyBatis-Plus
- **数据库**: MySQL
- **缓存**: Redis (Redisson)
- **定时任务**: XXL-Job
- **工具库**: Hutool, Guava, FastJson2
- **向量数据库**: Aliyun DashVector

## 项目架构

项目采用典型的DDD架构风格，模块划分清晰，各层次职责分明：

```
aigc-scheduler-server
├── aigc-scheduler-server-admin        # 管理后台模块
├── aigc-scheduler-server-application  # 应用服务层，协调领域对象完成业务功能
├── aigc-scheduler-server-client       # 对外提供的客户端API模块
├── aigc-scheduler-server-common       # 公共工具类和通用组件
├── aigc-scheduler-server-domain       # 领域层，包含核心业务逻辑和规则
├── aigc-scheduler-server-infrastructure # 基础设施层，提供技术实现
├── aigc-scheduler-server-job          # 任务调度模块
└── aigc-scheduler-server-provider     # 服务提供者模块，实现对外API接口
```

### 架构分层说明

1. **展现层(UI)**: admin模块
   - 负责用户界面和接口展示
   - 处理HTTP请求并返回响应

2. **应用层(Application)**: application模块
   - 协调领域对象以完成业务用例
   - 不包含业务规则
   - 代表系统能力和用例

3. **领域层(Domain)**: domain模块
   - 包含业务概念、业务规则和业务逻辑
   - 是系统的核心

4. **基础设施层(Infrastructure)**: infrastructure模块
   - 提供技术实现
   - 包含数据库访问、消息发送等技术细节

5. **客户端层(Client)**: client模块
   - 对外提供API接口和SDK

## 模块详细说明

### 1. aigc-scheduler-server-admin

管理后台模块，提供系统管理界面和API接口，是系统的交互入口。主要职责包括：
- 提供用户界面
- 处理HTTP请求
- 权限控制和认证
- 集成SSO单点登录

### 2. aigc-scheduler-server-application

应用服务层，负责协调领域对象以完成特定业务用例。主要职责包括：
- 实现业务用例和场景
- 事务管理
- 调用领域服务

### 3. aigc-scheduler-server-client

客户端API模块，提供给其他服务调用的接口。主要职责包括：
- 定义API接口
- 提供SDK给其他服务使用
- 版本管理

### 4. aigc-scheduler-server-common

公共工具类和通用组件模块，被其他模块共同依赖。主要职责包括：
- 提供工具类
- 定义常量
- 通用异常处理
- 公共DTO和VO对象

### 5. aigc-scheduler-server-domain

领域层，包含核心业务逻辑和规则。主要职责包括：
- 定义领域模型
- 实现领域逻辑
- 定义领域事件
- 包含业务规则和约束

### 6. aigc-scheduler-server-infrastructure

基础设施层，提供技术实现。主要职责包括：
- 数据库访问实现
- 消息队列实现
- 缓存实现
- 第三方服务集成
- 向量数据库访问(DashVector)

### 7. aigc-scheduler-server-job

任务调度模块，负责定时任务和异步任务的调度。主要职责包括：
- 集成XXL-Job
- 定义和实现定时任务
- 任务监控和管理

### 8. aigc-scheduler-server-provider

服务提供者模块，实现对外API接口。主要职责包括：
- 实现Client模块定义的接口

## 集成的主要外部服务

1. **Nacos**: 服务注册发现与配置中心
2. **RocketMQ**: 消息队列，用于异步通信和事件驱动
3. **XXL-Job**: 分布式任务调度平台
4. **SkyWalking/OpenTelemetry**: 分布式追踪系统
5. **SSO用户认证系统**: 集成单点登录
6. **钉钉API**: 集成钉钉通知
7. **邮件服务**: 用于发送邮件通知
8. **Zadig**: 持续交付平台

## 开发指南

### 开发环境搭建
1. 克隆项目代码
2. 安装必要的开发工具(JDK 11, Maven, IDE等)
3. 启动本地开发环境的中间件(MySQL, Redis, Nacos等)
4. 导入项目到IDE中，配置开发环境参数

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化工具
- 注释清晰，特别是领域模型和业务规则

### 版本管理
- 遵循语义化版本规范
- 分支管理策略：主干开发，特性分支