package com.zjkj.aigc.job.config;

import org.apache.http.client.HttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate(clientHttpRequestFactory());
    }

    @Bean
    public RestTemplate callBackRestTemplate() {
        return new RestTemplate(clientHttpRequestFactory());
    }

    /**
     * 配置 RestTemplate 的请求工厂
     *
     * @return ClientHttpRequestFactory
     */
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        try {
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(200);
            connectionManager.setDefaultMaxPerRoute(100);
            // 创建 HttpClient，并设置 SSLContext 和连接池管理器
            HttpClient httpClient = HttpClients.custom()
                    .setSSLContext(SSLContextBuilder.create().loadTrustMaterial((chain, authType) -> true).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .setConnectionManager(connectionManager)
                    .build();

            // 创建一个基于 HttpClient 的请求工厂
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
            factory.setConnectTimeout(3000);
            factory.setReadTimeout(5000);
            return factory;
        } catch (Exception ex) {
            throw new RuntimeException("clientHttpRequestFactory init error");
        }
    }

}
