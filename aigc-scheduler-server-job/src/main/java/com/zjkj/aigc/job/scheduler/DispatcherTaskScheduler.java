package com.zjkj.aigc.job.scheduler;

import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.job.config.TaskScheduleProperties;
import com.zjkj.aigc.job.service.JAigcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/7
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DispatcherTaskScheduler {

    private final JAigcTaskService aigcTaskService;
    private final TaskScheduleProperties taskScheduleProperties;

    /**
     * 检查并重试任务
     */
    @XxlJob("checkRunningTask")
    public void checkRunningTask() {
        TaskScheduleProperties.Config globalConfig = taskScheduleProperties.globalConfig();
        Map<String, TaskScheduleProperties.Config> configMap = taskScheduleProperties.configMapByTaskType();
        if (Objects.isNull(globalConfig) && CollectionUtils.isEmpty(configMap)) {
            log.info("checkRunningTask end, not config");
            return;
        }

        aigcTaskService.checkRunningTask(null, globalConfig, configMap);
    }


}
