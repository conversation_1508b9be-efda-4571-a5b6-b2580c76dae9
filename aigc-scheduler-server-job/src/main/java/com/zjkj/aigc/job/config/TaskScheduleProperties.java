package com.zjkj.aigc.job.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/11
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.task.schedule")
public class TaskScheduleProperties {

    /**
     * 全局配置
     */
    private List<Config> globalConfig;

    /**
     * 任务配置
     */
    private List<Config> configList = new ArrayList<>();

    /**
     * 任务配置
     */
    @Getter
    @Setter
    public static class Config {
        /**
         * 任务类型
         */
        private String taskType;
        /**
         * 最大重试次数
         */
        private Integer maxRetry = 0;
        /**
         * 超时时间
         */
        private Duration timeout = Duration.ofMinutes(10L);
    }

    /**
     * 获取全局配置
     * @return 全局配置
     */
    public Config globalConfig() {
        if (CollectionUtils.isEmpty(this.globalConfig)) {
            return null;
        }
        return CollectionUtils.firstElement(this.globalConfig);
    }

    /**
     * 转换为Map
     * @return 配置Map
     */
    public Map<String, Config> configMapByTaskType() {
        if (CollectionUtils.isEmpty(configList)) {
            return new HashMap<>();
        }
        return configList.stream()
                .filter(config -> StringUtils.hasText(config.getTaskType()))
                .collect(Collectors.toMap(Config::getTaskType, Function.identity(), (v1, v2) -> v1));
    }
}
