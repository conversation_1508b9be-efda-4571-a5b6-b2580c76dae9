package com.zjkj.aigc.job.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskArchiveDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskArchive;
import com.zjkj.aigc.job.service.AigcTaskArchiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskArchiveServiceImpl implements AigcTaskArchiveService {

    private final AigcTaskService aigcTaskService;
    private final AigcTaskArchiveDao aigcTaskArchiveDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(List<AigcTask> aigcTasks) {
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return;
        }

        List<AigcTaskArchive> aigcTaskArchives = BeanUtil.copyToList(aigcTasks, AigcTaskArchive.class);
        aigcTaskArchiveDao.saveBatch(aigcTaskArchives);

        List<Long> ids = aigcTasks.stream()
                .map(AigcTask::getId)
                .collect(Collectors.toList());

        aigcTaskService.actualDelById(ids);
    }
}
