package com.zjkj.aigc.job.scheduler;

import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.enums.ModelAutoTestStatusEnum;
import com.zjkj.aigc.domain.task.service.ModelAutoTestService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/23
 */
@Component
@RequiredArgsConstructor
public class ModelTestScheduler extends AbstractJobLog {

    private final ModelAutoTestService modelAutoTestService;

    @XxlJob("modelTestCheckDoneJob")
    public void modelTestCheckDoneJob() {
        ModelAutoTestRecordCondition condition = ModelAutoTestRecordCondition.builder()
                .statusList(Lists.newArrayList(ModelAutoTestStatusEnum.RUNNING.getCode()))
                .build();

        List<ModelAutoTestRecord> records = modelAutoTestService.queryRecordList(condition);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.forEach(record -> {
            try {
                modelAutoTestService.checkDone(record);
            } catch (Exception ex) {
                this.error("检查模型测试完成异常. testNo:{}", record.getTestNo(), ex);
            }
        });
    }
}
