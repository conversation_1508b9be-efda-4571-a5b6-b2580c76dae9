package com.zjkj.aigc.job.scheduler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.enums.ClusterStatusEnum;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcClusterUsageService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Component
@RequiredArgsConstructor
public class AigcClusterUsageScheduler extends AbstractJobLog {

    private final AigcClusterUsageService aigcClusterUsageService;
    private final AigcClusterService aigcClusterService;

    /**
     * 集群使用情况统计
     */
    @XxlJob("clusterUsageStat")
    public void clusterUsageStat() {
        AigcClusterCondition condition = new AigcClusterCondition()
                .setStatus(ClusterStatusEnum.ENABLE.getCode());
        List<AigcCluster> aigcClusters = aigcClusterService.queryList(condition);
        String jobParam = XxlJobHelper.getJobParam();
        LocalDate date = StringUtils.hasText(jobParam) ? LocalDate.parse(jobParam) : LocalDate.now();
        aigcClusters.forEach(cluster -> {
            try {
                aigcClusterUsageService.clusterUsageStat(cluster, date);
            } catch (Exception e) {
                this.error("clusterUsageStat() 集群使用情况统计异常 date:{}, clusterId:{}", date, cluster.getId(), e);
            }
        });

        this.info("clusterUsageStat() 集群使用情况统计完成 date:{}, size:{}", date, aigcClusters.size());
    }
}
