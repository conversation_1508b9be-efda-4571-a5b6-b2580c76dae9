package com.zjkj.aigc.job.scheduler;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.constant.AigcConstant;
import com.zjkj.aigc.common.dto.TaskSummaryStatDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.domain.task.service.AigcAppService;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.TaskStatDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/22 16:04
 */
@Component
@RequiredArgsConstructor
public class AigcTaskStatDayScheduler extends AbstractJobLog {

    private final AigcTaskStatDayService taskStatService;
    private final AigcAppService aigcAppService;
    private final TaskStatDao taskStatDao;
    private final AigcModelInfoService aigcModelInfoService;

    /**
     * 统计任务-今日
     */
    @XxlJob("aigcTaskStatDayJob")
    public void aigcTaskStatDayJob() {
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate;
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)) {
            JSONObject jsonObject = JSONObject.parseObject(jobParam);
            startDate = LocalDate.parse(jsonObject.getString("startTime"));
            endDate = LocalDate.parse(jsonObject.getString("endTime"));
            checkParams(startDate, endDate);
            this.info("aigcTaskStatDayJob jobParam: {}", jobParam);
        }

        List<AigcApp> aigcAppList = aigcAppService.activeAfterTime(startDate.toString());
        aigcTaskStatDayJob(aigcAppList, startDate, endDate);
    }

    /**
     * 统计任务-指定时间范围
     *
     * @param aigcAppList 应用列表
     * @param startDate   开始时间
     * @param endDate     结束时间
     */
    public void aigcTaskStatDayJob(List<AigcApp> aigcAppList, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(aigcAppList)) {
            this.info("aigcTaskStatDayJob aigcAppList is empty");
            return;
        }

        // 计算需要处理的总天数
        long totalDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        aigcAppList.parallelStream().forEach(aigcApp -> {
            String appId = aigcApp.getAppId();
            Stream.iterate(startDate, date -> date.plusDays(1))
                    .limit(totalDays)
                    .forEach(currentDate -> {
                        try {
                            taskStatService.statDay(appId, currentDate);
                        } catch (Exception ex) {
                            this.error("aigcTaskStatDayJob appId:{}, currentDate:{}", appId, currentDate, ex);
                        }
                    });
        });
    }

    /**
     * 统计任务-昨日
     */
    @XxlJob("aigcTaskYesterdayStatJob")
    public void aigcTaskYesterdayStatJob() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<AigcApp> aigcAppList = aigcAppService.activeAfterTime(yesterday.toString());
        aigcTaskStatDayJob(aigcAppList, yesterday, yesterday);
    }

    /**
     * 统计任务-过往未完成的任务
     */
    @XxlJob("aigcTaskPastRangStatJob")
    public void aigcTaskPastRangStatJob() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        int days = 7;
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)) {
            days = Integer.parseInt(jobParam);
            this.info("aigcTaskPastRangStatJob jobParam: {}", jobParam);
        }

        LocalDate startDate = yesterday.minusDays(days);
        List<AigcTaskStatDay> aigcTaskStatDayList = taskStatService.queryIncompleteStat(startDate.toString(), yesterday.toString());
        if (CollectionUtils.isEmpty(aigcTaskStatDayList)) {
            return;
        }

        aigcTaskStatDayList.forEach(stat -> {
            try {
                taskStatService.recalculateStat(stat);
            } catch (Exception e) {
                this.error("aigcTaskPastRangStatJob recalculateStat error. statDate:{}, taskType:{}, modelName:{}", stat.getStatDate(), stat.getTaskType(), stat.getModelName(), e);
            }
        });

        this.info("aigcTaskPastRangStatJob statSize:{}", aigcTaskStatDayList.size());
    }

    /**
     * 检查时间范围
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    private void checkParams(LocalDate startTime, LocalDate endTime) {
        if (ChronoUnit.MONTHS.between(startTime, endTime) > 3) {
            throw new BaseBizException(CustomErrorCode.TIME_RANGE_TO_LONG, CustomErrorCode.TIME_RANGE_TO_LONG.getMessage("three months"));
        }
    }

    /**
     * 活跃的任务
     */
    @XxlJob("aigcTaskActiveJob")
    public void aigcTaskActiveJob() {
        LocalDateTime localDateTime = LocalDateTime.now().minusHours(2);
        String time = LocalDateTimeUtil.formatNormal(localDateTime);
        TaskSummaryStatCondition condition = new TaskSummaryStatCondition()
                .setStartTime(time);
        List<TaskSummaryStatDTO> activeTaskList = taskStatDao.activeTaskStat(condition)
                .stream()
                .filter(task -> !task.getBusinessId().startsWith(AigcConstant.BUSINESS_ID_PREFIX))
                .collect(Collectors.toList());
        this.info("aigcTaskActiveJob afterTime: {}, activeTaskList:{}", time, activeTaskList.size());
        if (CollectionUtils.isEmpty(activeTaskList)) {
            return;
        }

        //按businessId分组，取最大的lastTime
        List<AigcApp> aigcAppList = activeTaskList.stream()
                .collect(Collectors.toMap(
                        TaskSummaryStatDTO::getBusinessId,
                        TaskSummaryStatDTO::getLastTime,
                        (time1, time2) -> time1.isAfter(time2) ? time1 : time2
                ))
                .entrySet()
                .stream()
                .map(entry -> new AigcApp()
                        .setAppId(entry.getKey())
                        .setLastActiveTime(entry.getValue())
                )
                .collect(Collectors.toList());

        // 刷新活跃的应用
        aigcAppService.refreshActiveApp(aigcAppList);

        //按taskType 和 modelName 分组，取最大的lastTime
        Map<Map.Entry<String, String>, LocalDateTime> statMap = activeTaskList.stream()
                .collect(Collectors.toMap(
                        task -> Map.entry(task.getTaskType(), task.getModelName()),
                        TaskSummaryStatDTO::getLastTime,
                        (time1, time2) -> time1.isAfter(time2) ? time1 : time2
                ));

        // 刷新活跃的模型
        aigcModelInfoService.refreshActiveTime(statMap);
    }

}
