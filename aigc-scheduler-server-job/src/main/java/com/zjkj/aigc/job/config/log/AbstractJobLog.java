package com.zjkj.aigc.job.config.log;

import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: TODO
 * @author：EDY
 * @date: 2022/4/21 9:54
 * @version: 1.0
 */
@Slf4j
public abstract class AbstractJobLog {

    protected void info(String message) {
        AbstractJobLog.log.info(message);
        XxlJobHelper.log(message);
    }

    protected void info(String message, Object... objects) {
        AbstractJobLog.log.info(message, objects);
        XxlJobHelper.log(message, objects);
    }

    protected void error(String message, Throwable throwable) {
        AbstractJobLog.log.error(message, throwable);
        XxlJobHelper.log(throwable);
    }

    protected void error(String message, Object... objects) {
        AbstractJobLog.log.error(message, objects);
        XxlJobHelper.log(message, objects);
    }

}
