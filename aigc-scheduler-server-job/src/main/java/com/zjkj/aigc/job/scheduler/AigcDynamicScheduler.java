package com.zjkj.aigc.job.scheduler;

import cn.hutool.core.collection.CollStreamUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicScaleTypeEnum;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicConfigService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import com.zjkj.aigc.zadig.client.ZadigClient;
import com.zjkj.aigc.zadig.config.ZadigClientFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
@Component
@RequiredArgsConstructor
public class AigcDynamicScheduler extends AbstractJobLog {

    private final AigcDynamicService aigcDynamicService;
    private final AigcDynamicConfigService aigcDynamicConfigService;
    private final AigcTaskStatDayService aigcTaskStatDayService;
    private final AigcModelInfoService aigcModelInfoService;
    private final AigcDynamicAdjustRecordService aigcDynamicAdjustRecordService;

    /**
     * 刷新平均耗时
     * @param jobParam 任务参数
     */
    private void refreshAvgElapsedTime(String jobParam) {
        int days = 5;
        if (StringUtils.hasText(jobParam)) {
            days = Integer.parseInt(jobParam);
            this.info("refreshAvgElapsedTime jobParam: {}", jobParam);
        }

        LocalDate startDate = LocalDate.now().minusDays(days);
        AigcTaskSummaryCondition condition = AigcTaskSummaryCondition.builder()
                .startDate(startDate.toString())
                .build();
        List<AigcTaskStatDay> aigcTaskStatDays = aigcTaskStatDayService.listByModelStateDate(condition);
        aigcModelInfoService.refreshAvgElapsedTime(aigcTaskStatDays);
    }

    /**
     * 动态预测模型负载
     */
    @XxlJob("dynamicModelPreThresholdJob")
    public void dynamicModelPreThreshold() {
        // 刷新平均耗时
        refreshAvgElapsedTime(XxlJobHelper.getJobParam());
        AigcModelInfoCondition infoCondition = AigcModelInfoCondition.builder()
                .online(true)
                .envType(EnvUtil.getCurrentEnvType())
                .build();
        List<AigcModelInfo> aigcModelInfos = aigcModelInfoService.queryList(infoCondition);
        aigcDynamicService.modelPreThreshold(aigcModelInfos);
    }

    /**
     * 动态扩容-预测
     */
    public void dynamicScaleOutByPrediction() {

    }

    /**
     * 动态扩容-任务
     */
    @XxlJob("dynamicScaleOutByTaskJob")
    public void dynamicScaleOutByTask() {
        AigcDynamicConfigGlobal globalConfig = aigcDynamicConfigService.getGlobalConfig();
        if (Objects.isNull(globalConfig) || Objects.equals(globalConfig.getDynamicEnabled(), Boolean.FALSE)) {
            return;
        }

        AigcTaskSummaryCondition condition = AigcTaskSummaryCondition.recently(2);
        // 最近3天有任务积累的数据
        List<AigcTaskStatDay> statDayList = aigcTaskStatDayService.listByModelStateDate(condition)
                .stream()
                .filter(v -> (v.getWaitingAmount() > 0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(statDayList)) {
            dynamicScaleInByDeprivedLowThreshold(new HashMap<>());
            return;
        }

        aigcDynamicService.scaleOutByTask(globalConfig, statDayList);
        Map<Map.Entry<String, String>, Long> statModeMap = CollStreamUtil.toMap(statDayList, v -> Map.entry(v.getTaskType(), v.getModelName()), AigcTaskStatDay::getWaitingAmount);
        dynamicScaleInByDeprivedLowThreshold(statModeMap);
    }

    /**
     * 动态缩容-低负载剥夺伸缩
     * @param statModeMap 任务统计
     */
    public void dynamicScaleInByDeprivedLowThreshold(Map<Map.Entry<String, String>, Long> statModeMap) {
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setScaleType(DynamicScaleTypeEnum.SCALE_OUT.getType())
                .setIncludeDetail(Boolean.TRUE);
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryList(condition);
        aigcDynamicService.scaleInByLowThresholdDeprived(statModeMap, records);
    }

    /**
     * 动态伸缩实例
     */
    @XxlJob("dynamicScaleInstanceJob")
    public void dynamicScaleInstance() {
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setScaleStatusList(List.of(DynamicAdjustScaleStatusEnum.PENDING.getStatus(), DynamicAdjustScaleStatusEnum.SCALING.getStatus()))
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setIncludeDetail(Boolean.TRUE)
                ;
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryList(condition);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        AigcDynamicConfigGlobal globalConfig = aigcDynamicConfigService.getGlobalConfig();
        ZadigClient zadigClient = ZadigClientFactory.create(globalConfig.getZadigEndpoint(), globalConfig.getZadigApiToken());
        records.forEach(record -> {
            try {
                aigcDynamicService.scaleInstance(zadigClient, record);
                info("dynamicScaleInstance, recordId: {}, scaleStatus: {}", record.getId(), record.getScaleStatus());
            } catch (Exception e) {
                error("dynamicScaleInstance error, recordId: {}, err: {}", record.getId(), e.getMessage());
            }
        });
    }
}
