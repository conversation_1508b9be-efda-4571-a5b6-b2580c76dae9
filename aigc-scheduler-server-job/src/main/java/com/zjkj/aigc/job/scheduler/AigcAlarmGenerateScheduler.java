package com.zjkj.aigc.job.scheduler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.dto.alarm.AlarmConfigThresholdDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmReceiverDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmSnapshotDTO;
import com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum;
import com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum;
import com.zjkj.aigc.common.enums.alarm.AlarmTimeIntervalUnitEnum;
import com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum;
import com.zjkj.aigc.common.enums.model.AigcModelTypeEnum;
import com.zjkj.aigc.common.enums.msg.DingTalkMsgTypeEnum;
import com.zjkj.aigc.common.enums.msg.FeishuMsgTypeEnum;
import com.zjkj.aigc.common.msg.DingTalkMsgService;
import com.zjkj.aigc.common.msg.EmailService;
import com.zjkj.aigc.common.msg.FeishuMsgService;
import com.zjkj.aigc.common.msg.dto.DingTalkMsgDTO;
import com.zjkj.aigc.common.msg.dto.EmailMsgDTO;
import com.zjkj.aigc.common.msg.dto.FeishuMsgDTO;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.util.PreciseTimeNumberGenerator;
import com.zjkj.aigc.common.util.RetryUtil;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelInfoDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskStatDayDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmChannelDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmConfigDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/12/18
 */
@Component
@RefreshScope
@RequiredArgsConstructor
public class AigcAlarmGenerateScheduler extends AbstractJobLog {

    @Value("${ai.model.alarm.detail-page:https://test-aip-admin.tiangong.tech/#/model/alarm/detail/%d}")
    private String detailPage;

    private Map<Long, AigcModelInfo> modelInfoMap;

    private Map<String, AigcTaskStatDay> statMap;

    private Map<String, AigcTaskStatDay> atomStatMap;

    private Long now;

    private LocalDateTime localDateTime;

    private final AigcAlarmConfigDao alarmConfigDao;

    private final AigcModelInfoDao modelInfoDao;

    private final AigcAlarmDao alarmDao;

    private final AigcTaskStatDayDao taskStatDayDao;

    private final AigcAlarmChannelDao aigcAlarmChannelDao;

    private final DingTalkMsgService dingTalkMsgService;

    private final FeishuMsgService feishuMsgService;

    private final EmailService emailService;

    private final RabbitTemplate rabbitTemplate;

    /**
     * 生成模型告警
     */
    @XxlJob("aigcAlarmGenerateJob")
    @SuppressWarnings("unchecked")
    public void aigcAlarmGenerateJob() {
        this.now = System.currentTimeMillis();
        this.localDateTime = LocalDateTime.now();
        //查询告警配置
        LambdaQueryWrapper<AigcAlarmConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AigcAlarmConfig::getStatus, AlarmConfigStatusEnum.ENABLE.getStatus());
        List<AigcAlarmConfig> configList = alarmConfigDao.getBaseMapper().selectList(queryWrapper);
        if (CollUtil.isNotEmpty(configList)) {
            Set<String> modelIds = configList.stream()
                    .filter(config -> CollUtil.isNotEmpty(config.getModelId()))
                    .flatMap(config -> config.getModelId().stream())
                    .collect(Collectors.toSet());
            //查询关联模型信息
            List<AigcModelInfo> modelInfoList = modelInfoDao.listByIds(modelIds);
            if (CollUtil.isNotEmpty(modelInfoList)) {
                Map<Integer, List<AigcAlarmConfig>> alarmConfigMap = configList.stream()
                        .collect(Collectors.groupingBy(AigcAlarmConfig::getAlarmType));
                //模型信息处理
                this.modelInfoMap = modelInfoList.stream()
                        .collect(Collectors.toMap(AigcModelInfo::getId, modelInfo -> modelInfo));
                Set<String> taskTypes = modelInfoList.stream()
                        .map(info -> info.getTaskType())
                        .collect(Collectors.toSet());
                Set<String> modelNames = modelInfoList.stream()
                        .map(info -> info.getModelName())
                        .collect(Collectors.toSet());
                List<String> atomModelNames = modelInfoList.stream()
                        .filter(info -> info.getType().equals(AigcModelTypeEnum.DATA_MQ.getCode()))
                        .map(info -> info.getModelName())
                        .collect(Collectors.toList());
                //近3天任务统计
                LocalDate today = LocalDate.now();
                LocalDate formerDay = today.minusDays(2);
                List<AigcTaskStatDay> statDayList = taskStatDayDao.listByModelStateDate(AigcTaskSummaryCondition.builder()
                        .startDate(formerDay.toString())
                        .endDate(today.toString())
                        .taskTypes(taskTypes)
                        .modelNames(modelNames)
                        .build());
                this.statMap = statDayList.stream()
                        .collect(Collectors.toMap(
                                stat -> stat.getModelName() + stat.getTaskType(),
                                stat -> stat, (k1, k2) -> k2)
                        );
                this.atomStatMap = queryAtomModelSummary(atomModelNames);
                //分类处理
                overStack(alarmConfigMap.get(AlarmTypeEnum.OVERSTOCK.getCode()));
                successRate(alarmConfigMap.get(AlarmTypeEnum.SUCCESSRATE.getCode()));
            }
        }
    }

    /**
     * 积压处理
     *
     * @param configList 配置信息
     */
    private void overStack(List<AigcAlarmConfig> configList) {
        if (CollUtil.isNotEmpty(configList)) {
            //并行处理
            configList.parallelStream().forEach(config -> {
                config.getModelId().parallelStream().forEach(id -> {
                    AigcModelInfo modelInfo = this.modelInfoMap.get(Long.valueOf(id));
                    if (Objects.nonNull(modelInfo)) {
                        AigcTaskStatDay statDay;
                        if (modelInfo.getType().equals(AigcModelTypeEnum.DATA_MQ.getCode())) {
                            statDay = this.atomStatMap.get(modelInfo.getModelName());
                        } else {
                            statDay = this.statMap.get(modelInfo.getModelName() + modelInfo.getTaskType());
                        }
                        if (Objects.nonNull(statDay)) {
                            AlarmConfigThresholdDTO configThresholdDTO = config.getAlarmConfigThreshold();
                            //命中积压条件
                            if (Objects.nonNull(configThresholdDTO) && Objects.nonNull(configThresholdDTO.getOverstackNum())
                                    && configThresholdDTO.getOverstackNum() < statDay.getWaitingAmount()) {
                                AigcAlarm alarm = alarmDao.getLastAlarm(modelInfo.getModelName(), modelInfo.getTaskType(), config.getId());
                                if (Objects.isNull(alarm) || checkTime(alarm.getAlarmTime(), now, config.getAlarmTimeInterval(), config.getAlarmTimeIntervalUnit())) {
                                    AlarmSnapshotDTO alarmSnapshot = new AlarmSnapshotDTO();
                                    alarmSnapshot.setConfigId(config.getId());
                                    alarmSnapshot.setOverstackNumThreshold(configThresholdDTO.getOverstackNum());
                                    alarmSnapshot.setOverstackNum(statDay.getWaitingAmount());
                                    AigcAlarm aigcAlarm = saveAlarm(config, modelInfo, alarmSnapshot);
                                    ThreadUtil.execAsync(() -> {
                                        LocalDateTime finishTime = calculateFinishTime(modelInfo, statDay);
                                        msgPush(modelInfo, config, alarmSnapshot, aigcAlarm.getId(), finishTime);
                                    });
                                }
                            }
                        }
                    }
                });
            });
        }
    }

    /**
     * 计算完成时间
     *
     * @param modelInfo 模型信息
     * @param statDay   统计信息
     * @return 完成时间
     */
    public LocalDateTime calculateFinishTime(AigcModelInfo modelInfo, AigcTaskStatDay statDay) {
        if (Objects.isNull(statDay.getAvgElapsedTime()) || statDay.getAvgElapsedTime() <= 0 || !modelInfo.getOnline()) {
            return null;
        }

        String currentEnvType = EnvUtil.getCurrentEnvType();
        Long instance = modelInfo.getInstanceByEnv(currentEnvType);
        if (instance <= 0) {
            return null;
        }

        long mills = (statDay.getWaitingAmount() / instance) * statDay.getAvgElapsedTime();
        return LocalDateTime.now().plus(Duration.ofMillis(mills));
    }

    /**
     * 成功率处理
     *
     * @param configList 配置信息
     */
    private void successRate(List<AigcAlarmConfig> configList) {
        if (CollUtil.isNotEmpty(configList) && !this.statMap.isEmpty()) {
            //并行处理
            configList.parallelStream().forEach(config -> {
                AlarmConfigThresholdDTO configThresholdDTO = config.getAlarmConfigThreshold();
                config.getModelId().parallelStream().forEach(id -> {
                    AigcModelInfo modelInfo = this.modelInfoMap.get(Long.valueOf(id));
                    if (Objects.nonNull(modelInfo)) {
                        //当日统计
                        AigcTaskStatDay day = this.statMap.get(modelInfo.getModelName() + modelInfo.getTaskType());
                        if (Objects.nonNull(day) && Objects.nonNull(day.getTotalAmount()) && Objects.nonNull(day.getFailAmount())
                                && day.getTotalAmount() > 0 && day.getFailAmount() > 0) {
                            //当前成功率
                            Long successRate = ((day.getTotalAmount() - day.getFailAmount()) * 100) / day.getTotalAmount();
                            //成功率小于阈值
                            if (successRate.intValue() < configThresholdDTO.getSuccessRate()) {
                                AigcAlarm alarm = alarmDao.getLastAlarm(modelInfo.getModelName(), modelInfo.getTaskType(), config.getId());
                                boolean flag = false;
                                if (Objects.nonNull(alarm)) {
                                    //已经存在告警，判断时间间隔及任务总数变化
                                    AlarmSnapshotDTO alarmSnapshotDTO = BeanUtil.copyProperties(alarm.getAlarmSnapshot(), AlarmSnapshotDTO.class);
                                    if (!alarmSnapshotDTO.getTaskTotalAmount().equals(day.getTotalAmount())
                                            && checkTime(alarm.getAlarmTime(), now, config.getAlarmTimeInterval(), config.getAlarmTimeIntervalUnit())) {
                                        flag = true;
                                    }
                                } else {
                                    flag = true;
                                }
                                if (flag) {
                                    AlarmSnapshotDTO alarmSnapshot = new AlarmSnapshotDTO();
                                    alarmSnapshot.setConfigId(config.getId());
                                    alarmSnapshot.setSuccessRateThreshold(configThresholdDTO.getSuccessRate());
                                    alarmSnapshot.setSuccessRate(successRate.intValue());
                                    alarmSnapshot.setTaskTotalAmount(day.getTotalAmount());
                                    alarmSnapshot.setTaskFailAmount(day.getFailAmount());
                                    AigcAlarm aigcAlarm = saveAlarm(config, modelInfo, alarmSnapshot);
                                    ThreadUtil.execAsync(() -> {
                                        msgPush(modelInfo, config, alarmSnapshot, aigcAlarm.getId(), null);
                                    });
                                }
                            }
                        }
                    }
                });
            });
        }
    }

    /**
     * 查询原子模型队列信息
     *
     * @return 原子模型队列信息
     */
    private Map<String, AigcTaskStatDay> queryAtomModelSummary(List<String> atomModelNames) {
        Map<String, AigcTaskStatDay> map = new ConcurrentHashMap<>();
        if (CollUtil.isNotEmpty(atomModelNames)) {
            RabbitAdmin rabbitAdmin = new RabbitAdmin(rabbitTemplate);
            atomModelNames.parallelStream().forEach(modelName -> {
                QueueInformation information = rabbitAdmin.getQueueInfo(modelName);
                if (Objects.nonNull(information)) {
                    AigcTaskStatDay day = new AigcTaskStatDay();
                    day.setModelName(modelName);
                    day.setTaskType(AigcModelTypeEnum.DATA_MQ.getCode());
                    day.setWaitingAmount(Long.valueOf(information.getMessageCount()));
                    map.put(modelName, day);
                }
            });
        }
        return map;
    }

    /**
     * 消息推送
     *
     * @param modelInfo     模型信息
     * @param config        告警配置
     * @param alarmSnapshot 告警快照
     * @param alarmId       告警ID
     */
    private void msgPush(AigcModelInfo modelInfo, AigcAlarmConfig config, AlarmSnapshotDTO alarmSnapshot, Long alarmId, LocalDateTime finishTime) {
        if (Objects.nonNull(config.getAlarmReceiver())) {
            AlarmReceiverDTO receiverDTO = config.getAlarmReceiver();
            List<String> phone = new ArrayList<>();
            if (CollUtil.isNotEmpty(receiverDTO.getSms())) {
                for (AlarmReceiverDTO.Sms sms : receiverDTO.getSms()) {
                    phone.add("@" + sms.getPhone());
                    //TODO 消息推送
                }
            }
            if (CollUtil.isNotEmpty(receiverDTO.getEmail())) {
                for (AlarmReceiverDTO.Email email : receiverDTO.getEmail()) {
                    ThreadUtil.execAsync(() -> {
                        RetryUtil.retryProcess(
                                () -> emailService.send(buildEmailMsgDTO(config.getAlarmType(),
                                        modelInfo, email.getEmail(), alarmSnapshot, alarmId, finishTime)),  // 重试函数
                                result -> result.getIsSuccess() ? new RetryUtil.RetryResult(false) : new RetryUtil.RetryResult(true, result.getErrorMsg()),  // 判断是否重试
                                ex -> ex instanceof Exception ? new RetryUtil.RetryResult(true, ex.getMessage()) : new RetryUtil.RetryResult(false),  // 异常判断
                                result -> result,  // 结果函数
                                2,  // 最大重试次数
                                1000,  // 每次重试间隔时间（毫秒）
                                TimeUnit.MILLISECONDS,  // 时间单位
                                true  // 是否采用倍增间隔策略
                        );
                    });
                }
            }
            if (CollUtil.isNotEmpty(receiverDTO.getDingtalk())) {
                for (AlarmReceiverDTO.WebHook dingTalk : receiverDTO.getDingtalk()) {
                    if (Objects.nonNull(dingTalk.getChannelId())) {
                        AigcAlarmChannel channel = aigcAlarmChannelDao.getById(dingTalk.getChannelId());
                        if (Objects.nonNull(channel)) {
                            ThreadUtil.execAsync(() -> {
                                RetryUtil.retryProcess(
                                        () -> dingTalkMsgService.sendWebHook(buildDingTalkMsgDTO(config.getAlarmType(),
                                                modelInfo, channel, phone, alarmSnapshot, alarmId, finishTime)),  // 重试函数
                                        result -> result.getIsSuccess() ? new RetryUtil.RetryResult(false) : new RetryUtil.RetryResult(true, result.getErrorMsg()),  // 判断是否重试
                                        ex -> ex instanceof Exception ? new RetryUtil.RetryResult(true, ex.getMessage()) : new RetryUtil.RetryResult(false),  // 异常判断
                                        result -> result,  // 结果函数
                                        2,  // 最大重试次数
                                        1000,  // 每次重试间隔时间（毫秒）
                                        TimeUnit.MILLISECONDS,  // 时间单位
                                        true  // 是否采用倍增间隔策略
                                );
                            });
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(receiverDTO.getFeishu())) {
                for (AlarmReceiverDTO.WebHook feishu : receiverDTO.getFeishu()) {
                    if (Objects.nonNull(feishu.getChannelId())) {
                        AigcAlarmChannel channel = aigcAlarmChannelDao.getById(feishu.getChannelId());
                        if (Objects.nonNull(channel)) {
                            ThreadUtil.execAsync(() -> {
                                RetryUtil.retryProcess(
                                        () -> feishuMsgService.sendWebHook(buildFeishuMsgDTO(config.getAlarmType(),
                                                modelInfo, channel, alarmSnapshot, alarmId, finishTime)),  // 重试函数
                                        result -> result.getIsSuccess() ? new RetryUtil.RetryResult(false) : new RetryUtil.RetryResult(true, result.getErrorMsg()),  // 判断是否重试
                                        ex -> ex instanceof Exception ? new RetryUtil.RetryResult(true, ex.getMessage()) : new RetryUtil.RetryResult(false),  // 异常判断
                                        result -> result,  // 结果函数
                                        2,  // 最大重试次数
                                        1000,  // 每次重试间隔时间（毫秒）
                                        TimeUnit.MILLISECONDS,  // 时间单位
                                        true); // 是否采用倍增间隔策略
                            });
                        }
                    }
                }
            }
        }
    }

    /**
     * 构建钉钉消息体
     *
     * @param alarmType     告警类型
     * @param modelInfo     模型信息
     * @param channel       消息通道
     * @param phone         @手机号
     * @param alarmSnapshot 告警快照
     * @param alramId       告警ID
     * @param finishTime    完成时间
     * @return 钉钉消息载体
     */
    private DingTalkMsgDTO buildDingTalkMsgDTO(Integer alarmType, AigcModelInfo modelInfo, AigcAlarmChannel channel,
                                               List<String> phone, AlarmSnapshotDTO alarmSnapshot, Long alramId, LocalDateTime finishTime) {
        String text = "";
        String type = Optional.ofNullable(AigcModelTypeEnum.getByCode(modelInfo.getType()))
                .map(AigcModelTypeEnum::getDesc)
                .orElse(modelInfo.getType());
        if (AlarmTypeEnum.OVERSTOCK.getCode().equals(alarmType)) {
            text = "### 【调度平台】模型任务-任务积压告警" + " \n\n " +
                    "> #### 模型类型：" + type + " \n\n " +
                    "> #### 任务类型：" + modelInfo.getTaskType() + " \n\n " +
                    "> #### 模型名称：" + modelInfo.getModelName() + " \n\n " +
                    "> #### 积压数量阈值：" + alarmSnapshot.getOverstackNumThreshold() + " \n\n " +
                    "> #### 当前积压数量：" + alarmSnapshot.getOverstackNum() + " \n\n ";
            if (Objects.nonNull(finishTime)) {
                text += "> #### 预计完成时间：" + LocalDateTimeUtil.formatNormal(finishTime) + " \n\n ";
            }
        } else if (AlarmTypeEnum.SUCCESSRATE.getCode().equals(alarmType)) {
            text = "### 【调度平台】模型任务-任务成功率偏低告警" + " \n\n " +
                    "> #### 模型类型：" + type + " \n\n " +
                    "> #### 任务类型：" + modelInfo.getTaskType() + " \n\n " +
                    "> #### 模型名称：" + modelInfo.getModelName() + " \n\n " +
                    "> #### 成功率阈值：" + alarmSnapshot.getSuccessRateThreshold() + "% \n\n " +
                    "> #### 近3日当前成功率：" + alarmSnapshot.getSuccessRate() + "% \n\n " +
                    "> #### 近3日当前任务总数量：" + alarmSnapshot.getTaskTotalAmount() + " \n\n " +
                    "> #### 近3日当前任务失败数量：" + alarmSnapshot.getTaskFailAmount() + " \n\n ";
        }
        if (CollUtil.isNotEmpty(phone)) {
            text = text + "> #### " + phone.stream().collect(Collectors.joining(""));
        }
        DingTalkMsgDTO.ActionCard actionCard = DingTalkMsgDTO.ActionCard.builder()
                .title("模型任务告警")
                .text(text)
                .singleTitle("查看告警详情")
                .singleUrl(DingTalkMsgDTO.getPcOutSlideUrl(String.format(detailPage, alramId)))
                .build();
        return DingTalkMsgDTO.builder()
                .type(DingTalkMsgTypeEnum.ACTIONCARD.getCode())
                .atMobiles(phone)
                .webHookUrl(channel.getWebhookUrl())
                .secret(channel.getSign())
                .actionCard(actionCard).build();
    }

    /**
     * 构建飞书消息体
     *
     * @param alarmType     告警类型
     * @param modelInfo     模型信息
     * @param channel       消息渠道
     * @param alarmSnapshot 告警快照
     * @param alramId       告警ID
     * @param finishTime    完成时间
     * @return 飞书消息载体
     */
    private FeishuMsgDTO buildFeishuMsgDTO(Integer alarmType, AigcModelInfo modelInfo, AigcAlarmChannel channel,
                                           AlarmSnapshotDTO alarmSnapshot, Long alramId, LocalDateTime finishTime) {
        String text = "";
        String type = Optional.ofNullable(AigcModelTypeEnum.getByCode(modelInfo.getType()))
                .map(AigcModelTypeEnum::getDesc)
                .orElse(modelInfo.getType());
        if (AlarmTypeEnum.OVERSTOCK.getCode().equals(alarmType)) {
            text = "**【调度平台】模型任务-任务积压告警**" + " \n\n " +
                    "**模型类型：**" + type + " \n\n " +
                    "**任务类型：**" + modelInfo.getTaskType() + " \n\n " +
                    "**模型名称：**" + modelInfo.getModelName() + " \n\n " +
                    "**积压数量阈值：**" + alarmSnapshot.getOverstackNumThreshold() + " \n\n " +
                    "**当前积压数量：**" + alarmSnapshot.getOverstackNum() + " \n\n ";
            if (Objects.nonNull(finishTime)) {
                text += "**预计完成时间：**" + LocalDateTimeUtil.formatNormal(finishTime) + " \n\n ";
            }
        } else if (AlarmTypeEnum.SUCCESSRATE.getCode().equals(alarmType)) {
            text = "**【调度平台】模型任务-任务成功率偏低告警**" + " \n\n " +
                    "**模型类型：**" + type + " \n\n " +
                    "**任务类型：**" + modelInfo.getTaskType() + " \n\n " +
                    "**模型名称：**" + modelInfo.getModelName() + " \n\n " +
                    "**成功率阈值：**" + alarmSnapshot.getSuccessRateThreshold() + "% \n\n " +
                    "**近3日当前成功率：**" + alarmSnapshot.getSuccessRate() + "% \n\n " +
                    "**近3日当前任务总数量：**" + alarmSnapshot.getTaskTotalAmount() + " \n\n " +
                    "**近3日当前任务失败数量：**" + alarmSnapshot.getTaskFailAmount() + " \n\n ";
        }
        JSONObject button = new JSONObject();
        button.put("tag", "button");
        button.put("type", "danger_filled");
        button.put("size", "medium");

        JSONObject buttonText = new JSONObject();
        buttonText.put("tag", "plain_text");
        buttonText.put("content", "查看告警详情");
        button.put("text", buttonText);

        JSONObject behavior = new JSONObject();
        behavior.put("type", "open_url");
        behavior.put("default_url", String.format(detailPage, alramId));
        button.put("behaviors", Arrays.asList(behavior));

        JSONObject action = new JSONObject();
        action.put("tag", "action");
        action.put("actions", Arrays.asList(button));

        JSONObject markdown = new JSONObject();
        markdown.put("tag", "markdown");
        markdown.put("content", text);

        JSONArray elements = new JSONArray();
        elements.add(markdown);
        elements.add(action);
        FeishuMsgDTO.HeaderTitle headerTitle = FeishuMsgDTO.HeaderTitle.builder()
                .tag("plain_text")
                .content("模型任务告警")
                .build();
        FeishuMsgDTO.Header header = FeishuMsgDTO.Header.builder()
                .template("red")
                .title(headerTitle)
                .build();
        FeishuMsgDTO.Card card = FeishuMsgDTO.Card.builder()
                .elements(elements)
                .header(header)
                .build();
        return FeishuMsgDTO.builder()
                .type(FeishuMsgTypeEnum.INTERACTIVE.getCode())
                .webHookUrl(channel.getWebhookUrl())
                .secret(channel.getSign())
                .card(card).build();
    }

    /**
     * 邮件消息载体
     *
     * @param alarmType     告警类型
     * @param modelInfo     模型类型
     * @param email         邮箱
     * @param alarmSnapshot 快照信息
     * @param alramId       告警ID
     * @param finishTime    完成时间
     * @return
     */
    private EmailMsgDTO buildEmailMsgDTO(Integer alarmType, AigcModelInfo modelInfo, String email,
                                         AlarmSnapshotDTO alarmSnapshot, Long alramId, LocalDateTime finishTime) {
        String text = "";
        String tilte = "";
        String type = Optional.ofNullable(AigcModelTypeEnum.getByCode(modelInfo.getType()))
                .map(AigcModelTypeEnum::getDesc)
                .orElse(modelInfo.getType());
        if (AlarmTypeEnum.OVERSTOCK.getCode().equals(alarmType)) {
            tilte = "【调度平台】模型任务积压告警";
            text = "<b>【调度平台】模型任务积压告警" + "</b>" +
                    "<p><b>模型类型</b>：" + type + "</p>" +
                    "<p><b>任务类型</b>：" + modelInfo.getTaskType() + "</p>" +
                    "<p><b>模型名称</b>：" + modelInfo.getModelName() + "</p>" +
                    "<p><b>积压数量阈值</b>：" + alarmSnapshot.getOverstackNumThreshold() + "</p>" +
                    "<p><b>当前积压数量</b>：<b style=\"color: #FF0000;\">" + alarmSnapshot.getOverstackNum() + "</b></p>";
            if (Objects.nonNull(finishTime)) {
                text += "<p><b>预计完成时间</b>：" + LocalDateTimeUtil.formatNormal(finishTime) + "</p>";
            }
        } else if (AlarmTypeEnum.SUCCESSRATE.getCode().equals(alarmType)) {
            tilte = "【调度平台】模型任务成功率偏低告警";
            text = "<b>【调度平台】模型任务成功率偏低告警" + "</b>" +
                    "<p><b>模型类型</b>：" + type + "</p>" +
                    "<p><b>任务类型</b>：" + modelInfo.getTaskType() + "</p>" +
                    "<p><b>模型名称</b>：" + modelInfo.getModelName() + "</p>" +
                    "<p><b>成功率阈值</b>：" + alarmSnapshot.getSuccessRateThreshold() + "%</p>" +
                    "<p><b>近3日当前成功率</b>：<b style=\"color: #FF0000;\">" + alarmSnapshot.getSuccessRate() + "%</b></p>" +
                    "<p><b>近3日当前任务总数量</b>：" + alarmSnapshot.getTaskTotalAmount() + "</p>" +
                    "<p><b>近3日当前任务失败数量</b>：<b style=\"color: #FF0000;\">" + alarmSnapshot.getTaskFailAmount() + "</b></p>";
        }
        text = text + "<p><a href=\"" + String.format(detailPage, alramId) + "\">查看告警详情</a></p>";
        return EmailMsgDTO.builder()
                .email(email).content(text).title(tilte)
                .build();
    }

    /**
     * 构建告警
     *
     * @param alarmConfig   告警配置
     * @param modelInfo     模型消息
     * @param alarmSnapshot 告警快照消息
     * @return 模型告警
     */
    private AigcAlarm saveAlarm(AigcAlarmConfig alarmConfig, AigcModelInfo modelInfo, AlarmSnapshotDTO alarmSnapshot) {
        AigcAlarm aigcAlarm = new AigcAlarm();
        aigcAlarm.setAlarmConfigId(alarmConfig.getId());
        aigcAlarm.setAlarmType(alarmConfig.getAlarmType());
        aigcAlarm.setTaskType(modelInfo.getTaskType());
        aigcAlarm.setModelType(modelInfo.getType());
        aigcAlarm.setModelName(modelInfo.getModelName());
        aigcAlarm.setModelNameZh(modelInfo.getName());
        aigcAlarm.setAlarmSnapshot(alarmSnapshot);
        aigcAlarm.setStatus(AlarmStatusEnum.UNPROCESSED.getStatus());
        aigcAlarm.setAlarmCode(PreciseTimeNumberGenerator.generateNumber());
        aigcAlarm.setAlarmTime(localDateTime);
        alarmDao.save(aigcAlarm);
        return aigcAlarm;
    }

    /**
     * 校验时间间隔
     *
     * @param alarmTime 上次告警时间
     * @param now       当前时间
     * @param time      间隔时间
     * @param timeUnit  间隔时间单位
     * @return false-未超过间隔时间，true-超过间隔时间
     */
    private boolean checkTime(LocalDateTime alarmTime, Long now, Integer time, Integer timeUnit) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = alarmTime.atZone(zone).toInstant();
        long alarmTimeMillis = instant.getEpochSecond() * 1000L;
        long t = 0;
        if (AlarmTimeIntervalUnitEnum.MIN.getCode() == timeUnit) {
            t = time * 60 * 1000L;
        } else if (AlarmTimeIntervalUnitEnum.HOUR.getCode() == timeUnit) {
            t = time * 60 * 60 * 1000L;
        }
        if (alarmTimeMillis + t < now) {
            return true;
        }
        return false;
    }
}
