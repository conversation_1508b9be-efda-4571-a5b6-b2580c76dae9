package com.zjkj.aigc.job.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/2
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AtomModelTaskResultScheduler {

//    private final AtomModelProperties atomModelProperties;
//    private final AiTaskService aiTaskService;
//
//    /**
//     * 原子模型任务结果调度
//     */
//    @XxlJob("atomModelResultTask")
//    public void taskResultScheduler() {
//        Map<String, List<String>> configMap = atomModelProperties.getConfigMap();
//        if (CollectionUtils.isEmpty(configMap)) {
//            log.info("taskResultScheduler end, not config");
//            return;
//        }
//
//        configMap.keySet().parallelStream()
//                .forEach(taskType -> aiTaskService.atomModelTaskResultCheck(taskType, configMap.get(taskType)));
//    }
//
//    /**
//     * 原子模型任务创建调度
//     */
//    @XxlJob("atomModelLaunchTask")
//    public void taskLaunchScheduler() {
//        Map<String, List<String>> configMap = atomModelProperties.getConfigMap();
//        if (CollectionUtils.isEmpty(configMap)) {
//            log.info("atomModelLaunchTask end, not config");
//            return;
//        }
//
//        configMap.keySet().parallelStream()
//                .forEach(taskType -> aiTaskService.atomModelTaskLaunch(taskType, configMap.get(taskType)));
//    }


}
