package com.zjkj.aigc.job.scheduler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 自动重试失败任务调度器
 * 每分钟检查最近两分钟内执行失败且错误信息包含"img output error"的任务，并自动调用重试接口
 *
 * <AUTHOR>
 * @since 2024/11/20
 */
@Component
@RefreshScope
@RequiredArgsConstructor
public class AigcTaskAutoRetryScheduler extends AbstractJobLog {

    private final AigcTaskDao aigcTaskDao;
    private final AigcTaskService iAigcTaskService;

    /**
     * 自动重试失败任务
     * 每分钟执行一次，查询最近两分钟内执行失败且错误信息包含"img output error"的任务，并自动调用重试接口
     */
    @XxlJob("aigcTaskAutoRetryJob")
    public void aigcTaskAutoRetryJob() {
        this.info("aigcTaskAutoRetryJob start");
        
        // 查询最近两分钟内执行失败的任务
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twoMinutesAgo = now.minusMinutes(2);
        String startTime = twoMinutesAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .taskStates(List.of(TaskStateEnum.FAILED.getCode()))
                .failMessage("img output error")
                .startTime(startTime)
                .endTime(endTime)
                .build();
        List<AigcTask> aigcTasks = aigcTaskDao.queryByCondition(condition);
        this.info("两分钟内需重试任务数: {}", aigcTasks.size());
        
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return;
        }

        for (AigcTask aigcTask : aigcTasks) {
            boolean b = iAigcTaskService.retryTask(aigcTask);
            this.info("任务 {} 重试结果:{}",aigcTask.getTaskId(), b);
        }

    }
}