package com.zjkj.aigc.job.service.impl;

import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.job.config.TaskScheduleProperties;
import com.zjkj.aigc.job.service.JAigcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JAigcTaskServiceImpl implements JAigcTaskService {

    private final AigcTaskDao aigcTaskDao;
    private final AigcTaskService aigcTaskService;

    @Override
    public void checkRunningTask(LocalDateTime startTime, TaskScheduleProperties.Config globalConfig, Map<String, TaskScheduleProperties.Config> configMap) {
        aigcTaskDao.streamByTaskState(TaskStateEnum.RUNNING.getCode(), startTime, resultContext -> {
            AigcTask aigcTask = resultContext.getResultObject();
            try {
                TaskScheduleProperties.Config taskConfig = configMap.getOrDefault(aigcTask.getTaskType(), globalConfig);
                checkTask(aigcTask, taskConfig);
            } catch (Exception e) {
                log.error("checkAndRetryTask() 任务异常, taskId:{}, taskType:{}", aigcTask.getTaskId(), aigcTask.getTaskType(), e);
            }
        });
    }

    /**
     * 检查任务
     *
     * @param aigcTask 任务
     * @param config   配置
     */
    public void checkTask(AigcTask aigcTask, TaskScheduleProperties.Config config) {
        if (Objects.isNull(config) || Objects.isNull(aigcTask)) {
            return;
        }

        LocalDateTime taskStartTime = aigcTask.getTaskStartTime();
        if (Objects.isNull(taskStartTime)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(taskStartTime, now);
        boolean isTimeout = duration.compareTo(config.getTimeout()) > 0;

        // 会有进度更新，所以判断更新是否也超时
        if (isTimeout && Objects.nonNull(aigcTask.getRevisedTime())) {
            duration = Duration.between(aigcTask.getRevisedTime(), now);
            isTimeout = duration.compareTo(config.getTimeout()) > 0;
        }

        if (!isTimeout) {
            return;
        }

        // 最大重试次数
        if (aigcTask.getRetryCount() >= config.getMaxRetry()) {
            aigcTask.setFailMessage(TaskStateEnum.TIMEOUT_FAILED.getDesc());
            aigcTaskService.timeoutFailTask(aigcTask);
            log.info("checkRunningTask() 任务超时失败, taskId:{}, taskType:{}", aigcTask.getTaskId(), aigcTask.getTaskType());
            return;
        }

        boolean retry = aigcTaskService.retryTask(aigcTask);
        log.info("checkRunningTask() 任务重试, taskId: {}, taskType: {}, retry: {}", aigcTask.getTaskId(), aigcTask.getTaskType(), retry);
    }
}
