package com.zjkj.aigc.job.service;

import com.zjkj.aigc.job.config.TaskScheduleProperties;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
public interface JAigcTaskService {
    /**
     * 检查运行中的任务
     * @param localDateTime 时间
     * @param globalConfig 全局配置
     * @param configMap 任务配置
     */
    void checkRunningTask(LocalDateTime localDateTime, TaskScheduleProperties.Config globalConfig, Map<String, TaskScheduleProperties.Config> configMap);
}
