package com.zjkj.aigc.job.scheduler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/27
 */
@Component
@RequiredArgsConstructor
public class AigcInstanceScheduler extends AbstractJobLog {

    private final AigcNodeInstanceService aigcNodeInstanceService;
    private final AigcModelInstanceService aigcModelInstanceService;
    private final AigcModelInfoService aigcModelInfoService;
    /**
     * 检查节点实例健康状态
     */
    @XxlJob("checkNodeInstanceHealthStatus")
    public void checkNodeInstanceHealthStatus() {
        Duration timeout = Duration.ofMinutes(5L);
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)) {
            this.info("checkNodeInstanceHealthStatus jobParam:{}", jobParam);
            timeout = Duration.ofMinutes(Long.parseLong(jobParam));
        }

        aigcNodeInstanceService.checkHealthStatus(timeout);
    }

    /**
     * 检查模型实例健康状态
     */
    @XxlJob("checkModelInstanceHealthStatus")
    public void checkModelInstanceHealthStatus() {
        Duration timeout = Duration.ofMinutes(2L);
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)) {
            this.info("checkModelInstanceHealthStatus jobParam:{}", jobParam);
            timeout = Duration.ofMinutes(Long.parseLong(jobParam));
        }

        aigcModelInstanceService.checkHealthStatus(timeout);

        // 查询模型在线实例
        Map<Map.Entry<String, String>, List<AigcModelInstance>> instanceMap = aigcModelInstanceService.queryInstance(new AigcModelInstanceCondition());

        // 刷新模型实例信息
        aigcModelInfoService.refreshInstance(instanceMap);

        // 缺少modelId的模型实例
        List<AigcModelInstance> missModelIdInstances = instanceMap.values().stream()
                .flatMap(List::stream)
                .filter(instance -> Objects.isNull(instance.getModelId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(missModelIdInstances)) {
            aigcModelInstanceService.fillModelId(missModelIdInstances);
            info("checkModelInstanceHealthStatus fill modelId. size:{}", missModelIdInstances.size());
        }
    }

}
