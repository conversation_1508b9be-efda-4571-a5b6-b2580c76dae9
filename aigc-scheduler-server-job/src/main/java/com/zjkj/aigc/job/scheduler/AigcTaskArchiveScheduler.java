package com.zjkj.aigc.job.scheduler;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.domain.batch.BatchProcessor;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.job.config.log.AbstractJobLog;
import com.zjkj.aigc.job.service.AigcTaskArchiveService;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/20
 */
@Component
@RefreshScope
@RequiredArgsConstructor
public class AigcTaskArchiveScheduler extends AbstractJobLog {

    private static final List<Integer> TASK_STATE = List.of(
            TaskStateEnum.SUCC.getCode(),
            TaskStateEnum.FAILED.getCode(),
            TaskStateEnum.CANCEL.getCode(),
            TaskStateEnum.TIMEOUT_FAILED.getCode()
    );

    private final AigcTaskDao aigcTaskDao;
    private final AigcTaskArchiveService aigcTaskArchiveService;

    /**
     * 归档aigc任务
     */
    @XxlJob("aigcTaskArchiveJob")
    @SuppressWarnings("unchecked")
    public void aigcTaskArchiveJob() {
        String jobParam = XxlJobHelper.getJobParam();
        this.info("aigcTaskArchiveJob. jobParam:{}", jobParam);
        if (!StringUtils.hasText(jobParam)) {
            this.info("aigcTaskArchiveJob skip. jobParam is empty");
            return;
        }

        Map<String, Object> paramMap = JSON.parseObject(jobParam, new TypeReference<>() {});
        Integer archiveDays = (Integer) paramMap.getOrDefault("days", -1);
        List<String> businessIds = (List<String>) paramMap.getOrDefault("businessIds", List.of());
        if (archiveDays < 0) {
            this.info("aigcTaskArchiveJob skip. archiveDays:{}", archiveDays);
            return;
        }

        LocalDate endDate = LocalDate.now().minusDays(archiveDays + 1);
        this.info("aigcTaskArchiveJob start. archiveDays:{}, endDate:{}, businessIds:{}", archiveDays, endDate, businessIds);
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .taskStates(TASK_STATE)
                .businessIds(businessIds)
                .endTime(endDate.toString())
                .build();

        BatchProcessor<AigcTask> batchProcessor = new BatchProcessor<>(500, aigcTaskArchiveService::archive);
        aigcTaskDao.streamByCondition(condition, resultContext -> batchProcessor.add(resultContext.getResultObject()));
        batchProcessor.flush();
        this.info("aigcTaskArchiveJob end. totalProcessed:{}", batchProcessor.getTotalProcessed());
    }
}
