<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zjkj.aigc.scheduler</groupId>
        <artifactId>aigc-scheduler-server</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>aigc-scheduler-server-job</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zjkj.logistics</groupId>
            <artifactId>xxl-job-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zjkj.aigc.scheduler</groupId>
            <artifactId>aigc-scheduler-server-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
    </dependencies>

</project>