<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多维度任务调用量统计 - AIGC调度器</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            font-size: 14px;
        }

        select, .refresh-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select:hover, .refresh-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-left: 4px solid #667eea;
            padding-left: 12px;
        }

        .overview-section {
            display: grid;
            grid-template-columns: 60% 40%;
            gap: 24px;
            margin-bottom: 24px;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e8ed;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .chart {
            width: 100%;
            height: 350px;
        }

        .ranking-list {
            padding: 16px;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .ranking-item:last-child {
            border-bottom: none;
        }

        .ranking-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ranking-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .ranking-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .model-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .model-stats {
            font-size: 12px;
            color: #7f8c8d;
        }

        .progress-bar {
            width: 100px;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }

        .analysis-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .analysis-controls select {
            background: white;
            color: #333;
            border: 1px solid #ddd;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .data-table-container {
            margin-top: 20px;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            flex-wrap: wrap;
            gap: 12px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .data-table th:hover {
            background: #e9ecef;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: #27ae60;
        }

        @media (max-width: 1024px) {
            .overview-section {
                grid-template-columns: 1fr;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .analysis-controls {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .table-controls {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .data-table {
                font-size: 14px;
            }
            
            .data-table th,
            .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 多维度任务调用量统计</h1>
        <div class="controls">
            <div class="control-group">
                <label>统计周期:</label>
                <select id="periodSelect">
                    <option value="today">今日</option>
                    <option value="7d">近7天</option>
                    <option value="30d" selected>近30天</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            <div class="control-group">
                <label>统计粒度:</label>
                <select id="granularitySelect">
                    <option value="hour">按小时</option>
                    <option value="day" selected>按天</option>
                    <option value="month">按月</option>
                </select>
            </div>
            <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
            <div class="control-group">
                <span class="status-indicator status-online"></span>
                <span>数据已更新</span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 概览统计区域 -->
        <div class="section">
            <h2 class="section-title">📈 调用概览统计</h2>
            <div class="overview-section">
                <div class="chart-container">
                    <div class="chart-title">调用来源分布</div>
                    <div id="sourceDistributionChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">热门模型排行榜 TOP5</div>
                    <div class="ranking-list" id="modelRanking">
                        <!-- 动态生成排行榜内容 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细分析区域 -->
        <div class="section">
            <h2 class="section-title">📋 详细分析</h2>
            
            <div class="analysis-controls">
                <div class="control-group">
                    <label>时间范围:</label>
                    <select id="timeRangeSelect">
                        <option value="today">今日</option>
                        <option value="7d">近7天</option>
                        <option value="30d" selected>近30天</option>
                        <option value="custom">自定义时间</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>统计粒度:</label>
                    <select id="timeGranularitySelect">
                        <option value="hour">按小时</option>
                        <option value="day" selected>按天</option>
                        <option value="month">按月</option>
                    </select>
                </div>
            </div>

            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">任务统计趋势</div>
                    <div id="taskTrendChart" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">平均响应时间趋势</div>
                    <div id="responseTimeChart" class="chart"></div>
                </div>
            </div>

            <div class="data-table-container">
                <div class="table-controls">
                    <div class="control-group">
                        <label>统计类型:</label>
                        <select id="statisticsTypeSelect" onchange="switchTableType()">
                            <option value="model">按模型统计</option>
                            <option value="system">按业务系统统计</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>每页显示:</label>
                        <select id="pageSizeSelect" onchange="changePageSize()">
                            <option value="10" selected>10条</option>
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                        </select>
                    </div>
                </div>

                <table class="data-table" id="dataTable">
                    <thead id="tableHeader">
                        <!-- 动态生成表头 -->
                    </thead>
                    <tbody id="tableBody">
                        <!-- 动态生成表格内容 -->
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- 动态生成分页 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据生成
        const businessSystems = ['SASS中心', '趋势中心', '门户中心', '用户中心', '订单系统', '推荐系统', '内容管理'];
        const modelNames = ['LLaMA2-7B', 'ChatGLM-6B', 'Stable-Diffusion', 'BERT-Base', 'GPT-3.5', 'Claude-2', 'PaLM-2'];
        const taskTypes = ['文本生成', '图像生成', '文本分类', '情感分析', '机器翻译', '问答系统'];

        // 生成时间序列数据
        function generateTimeSeriesData(days = 30, baseValue = 1000, variance = 500) {
            const data = [];
            const now = new Date();
            for (let i = days; i >= 0; i--) {
                const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
                const value = Math.max(0, baseValue + (Math.random() - 0.5) * variance + Math.sin(i / 7) * 200);
                data.push([date, Math.round(value)]);
            }
            return data;
        }

        // 生成模拟统计数据
        function generateMockData() {
            return {
                sourceDistribution: businessSystems.map(system => ({
                    name: system,
                    value: Math.floor(Math.random() * 5000) + 1000
                })),
                modelRanking: modelNames.slice(0, 5).map((name, index) => ({
                    name: name,
                    calls: Math.floor(Math.random() * 10000) + 5000,
                    percentage: Math.floor(Math.random() * 30) + 10
                })).sort((a, b) => b.calls - a.calls),
                taskTrend: {
                    total: generateTimeSeriesData(30, 5000, 1000),
                    success: generateTimeSeriesData(30, 4500, 800),
                    failed: generateTimeSeriesData(30, 300, 100),
                    processing: generateTimeSeriesData(30, 150, 50),
                    waiting: generateTimeSeriesData(30, 50, 20),
                    cancelled: generateTimeSeriesData(30, 20, 10)
                },
                responseTime: generateTimeSeriesData(30, 150, 50)
            };
        }

        let mockData = generateMockData();
        let currentPage = 1;
        let pageSize = 10;
        let currentTableType = 'model';
        let tableData = [];

        // 初始化调用来源分布饼图
        function initSourceDistributionChart() {
            const chart = echarts.init(document.getElementById('sourceDistributionChart'));
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: mockData.sourceDistribution.map(item => item.name)
                },
                series: [{
                    name: '调用来源',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: mockData.sourceDistribution
                }]
            };
            chart.setOption(option);
        }

        // 初始化热门模型排行榜
        function initModelRanking() {
            const container = document.getElementById('modelRanking');
            container.innerHTML = '';

            mockData.modelRanking.forEach((model, index) => {
                const item = document.createElement('div');
                item.className = 'ranking-item';
                item.innerHTML = `
                    <div class="ranking-info">
                        <div class="ranking-number">${index + 1}</div>
                        <div class="ranking-details">
                            <div class="model-name">${model.name}</div>
                            <div class="model-stats">${model.calls.toLocaleString()} 次调用</div>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${model.percentage}%"></div>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 初始化任务统计趋势图
        function initTaskTrendChart() {
            const chart = echarts.init(document.getElementById('taskTrendChart'));
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['调用总量', '成功数量', '失败数量', '处理中', '等待中', '取消数量'],
                    bottom: 0
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{MM}-{dd}'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                series: [
                    {
                        name: '调用总量',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.total,
                        lineStyle: { color: '#4facfe', width: 2 },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    {offset: 0, color: 'rgba(79, 172, 254, 0.6)'},
                                    {offset: 1, color: 'rgba(79, 172, 254, 0.1)'}
                                ]
                            }
                        }
                    },
                    {
                        name: '成功数量',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.success,
                        lineStyle: { color: '#43e97b', width: 2 }
                    },
                    {
                        name: '失败数量',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.failed,
                        lineStyle: { color: '#ff6b6b', width: 2 }
                    },
                    {
                        name: '处理中',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.processing,
                        lineStyle: { color: '#feca57', width: 2 }
                    },
                    {
                        name: '等待中',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.waiting,
                        lineStyle: { color: '#a55eea', width: 2 }
                    },
                    {
                        name: '取消数量',
                        type: 'line',
                        smooth: true,
                        data: mockData.taskTrend.cancelled,
                        lineStyle: { color: '#95a5a6', width: 2 }
                    }
                ]
            };
            chart.setOption(option);
        }

        // 初始化响应时间趋势图
        function initResponseTimeChart() {
            const chart = echarts.init(document.getElementById('responseTimeChart'));
            const option = {
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].axisValueLabel}<br/>平均响应时间: ${params[0].value[1]}ms`;
                    }
                },
                xAxis: {
                    type: 'time',
                    axisLabel: {
                        formatter: '{MM}-{dd}'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}ms'
                    }
                },
                series: [{
                    name: '平均响应时间',
                    type: 'line',
                    smooth: true,
                    data: mockData.responseTime,
                    lineStyle: { color: '#f093fb', width: 2 },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(240, 147, 251, 0.6)'},
                                {offset: 1, color: 'rgba(240, 147, 251, 0.1)'}
                            ]
                        }
                    }
                }]
            };
            chart.setOption(option);
        }

        // 生成表格数据
        function generateTableData() {
            if (currentTableType === 'model') {
                return modelNames.map((name, index) => ({
                    modelId: `model_${index + 1}`,
                    modelName: name,
                    taskType: taskTypes[Math.floor(Math.random() * taskTypes.length)],
                    totalCalls: Math.floor(Math.random() * 10000) + 1000,
                    successCount: Math.floor(Math.random() * 8000) + 800,
                    failedCount: Math.floor(Math.random() * 500) + 50,
                    processingCount: Math.floor(Math.random() * 100) + 10,
                    waitingCount: Math.floor(Math.random() * 50) + 5,
                    cancelledCount: Math.floor(Math.random() * 30) + 3,
                    avgResponseTime: Math.floor(Math.random() * 200) + 50
                }));
            } else {
                return businessSystems.map(name => ({
                    systemName: name,
                    totalCalls: Math.floor(Math.random() * 15000) + 2000,
                    successCount: Math.floor(Math.random() * 12000) + 1500,
                    failedCount: Math.floor(Math.random() * 800) + 100,
                    processingCount: Math.floor(Math.random() * 200) + 20,
                    waitingCount: Math.floor(Math.random() * 100) + 10,
                    cancelledCount: Math.floor(Math.random() * 50) + 5,
                    avgResponseTime: Math.floor(Math.random() * 300) + 80
                }));
            }
        }

        // 初始化表格
        function initTable() {
            tableData = generateTableData();
            renderTable();
        }

        // 渲染表格
        function renderTable() {
            const headerContainer = document.getElementById('tableHeader');
            const bodyContainer = document.getElementById('tableBody');

            // 生成表头
            let headerHTML = '';
            if (currentTableType === 'model') {
                headerHTML = `
                    <tr>
                        <th onclick="sortTable('modelId')">模型ID</th>
                        <th onclick="sortTable('modelName')">模型中文名称</th>
                        <th onclick="sortTable('taskType')">任务类型</th>
                        <th onclick="sortTable('totalCalls')">调用总量</th>
                        <th onclick="sortTable('successCount')">成功数量</th>
                        <th onclick="sortTable('failedCount')">失败数量</th>
                        <th onclick="sortTable('processingCount')">处理中数量</th>
                        <th onclick="sortTable('waitingCount')">等待中数量</th>
                        <th onclick="sortTable('cancelledCount')">取消数量</th>
                        <th onclick="sortTable('avgResponseTime')">平均响应时间(ms)</th>
                    </tr>
                `;
            } else {
                headerHTML = `
                    <tr>
                        <th onclick="sortTable('systemName')">业务系统名称</th>
                        <th onclick="sortTable('totalCalls')">调用总量</th>
                        <th onclick="sortTable('successCount')">成功数量</th>
                        <th onclick="sortTable('failedCount')">失败数量</th>
                        <th onclick="sortTable('processingCount')">处理中数量</th>
                        <th onclick="sortTable('waitingCount')">等待中数量</th>
                        <th onclick="sortTable('cancelledCount')">取消数量</th>
                        <th onclick="sortTable('avgResponseTime')">平均响应时间(ms)</th>
                    </tr>
                `;
            }
            headerContainer.innerHTML = headerHTML;

            // 生成表格内容
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = tableData.slice(startIndex, endIndex);

            let bodyHTML = '';
            pageData.forEach(row => {
                if (currentTableType === 'model') {
                    bodyHTML += `
                        <tr>
                            <td>${row.modelId}</td>
                            <td>${row.modelName}</td>
                            <td>${row.taskType}</td>
                            <td>${row.totalCalls.toLocaleString()}</td>
                            <td>${row.successCount.toLocaleString()}</td>
                            <td>${row.failedCount.toLocaleString()}</td>
                            <td>${row.processingCount.toLocaleString()}</td>
                            <td>${row.waitingCount.toLocaleString()}</td>
                            <td>${row.cancelledCount.toLocaleString()}</td>
                            <td>${row.avgResponseTime}ms</td>
                        </tr>
                    `;
                } else {
                    bodyHTML += `
                        <tr>
                            <td>${row.systemName}</td>
                            <td>${row.totalCalls.toLocaleString()}</td>
                            <td>${row.successCount.toLocaleString()}</td>
                            <td>${row.failedCount.toLocaleString()}</td>
                            <td>${row.processingCount.toLocaleString()}</td>
                            <td>${row.waitingCount.toLocaleString()}</td>
                            <td>${row.cancelledCount.toLocaleString()}</td>
                            <td>${row.avgResponseTime}ms</td>
                        </tr>
                    `;
                }
            });
            bodyContainer.innerHTML = bodyHTML;

            // 渲染分页
            renderPagination();
        }

        // 渲染分页
        function renderPagination() {
            const container = document.getElementById('pagination');
            const totalPages = Math.ceil(tableData.length / pageSize);

            let paginationHTML = '';

            // 上一页按钮
            paginationHTML += `<button onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;

            // 页码按钮
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    paginationHTML += `<button class="active">${i}</button>`;
                } else {
                    paginationHTML += `<button onclick="changePage(${i})">${i}</button>`;
                }
            }

            // 下一页按钮
            paginationHTML += `<button onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;

            container.innerHTML = paginationHTML;
        }

        // 切换页面
        function changePage(page) {
            const totalPages = Math.ceil(tableData.length / pageSize);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                renderTable();
            }
        }

        // 改变每页显示数量
        function changePageSize() {
            pageSize = parseInt(document.getElementById('pageSizeSelect').value);
            currentPage = 1;
            renderTable();
        }

        // 切换表格类型
        function switchTableType() {
            currentTableType = document.getElementById('statisticsTypeSelect').value;
            currentPage = 1;
            initTable();
        }

        // 表格排序
        function sortTable(column) {
            tableData.sort((a, b) => {
                if (typeof a[column] === 'string') {
                    return a[column].localeCompare(b[column]);
                } else {
                    return b[column] - a[column];
                }
            });
            renderTable();
        }

        // 刷新数据
        function refreshData() {
            console.log('刷新数据...');
            const refreshBtn = document.querySelector('.refresh-btn');
            refreshBtn.innerHTML = '🔄 刷新中...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.innerHTML = '🔄 刷新数据';
                refreshBtn.disabled = false;

                // 重新生成模拟数据
                mockData = generateMockData();

                // 重新初始化所有图表
                initSourceDistributionChart();
                initModelRanking();
                initTaskTrendChart();
                initResponseTimeChart();
                initTable();
            }, 2000);
        }

        // 事件监听器
        document.getElementById('periodSelect').addEventListener('change', function(e) {
            console.log('切换统计周期:', e.target.value);
            refreshData();
        });

        document.getElementById('granularitySelect').addEventListener('change', function(e) {
            console.log('切换统计粒度:', e.target.value);
            refreshData();
        });

        document.getElementById('timeRangeSelect').addEventListener('change', function(e) {
            console.log('切换时间范围:', e.target.value);
            initTaskTrendChart();
            initResponseTimeChart();
        });

        document.getElementById('timeGranularitySelect').addEventListener('change', function(e) {
            console.log('切换时间粒度:', e.target.value);
            initTaskTrendChart();
            initResponseTimeChart();
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initSourceDistributionChart();
            initModelRanking();
            initTaskTrendChart();
            initResponseTimeChart();
            initTable();

            // 设置自动刷新（每5分钟）
            setInterval(function() {
                mockData = generateMockData();
                initSourceDistributionChart();
                initModelRanking();
                initTaskTrendChart();
                initResponseTimeChart();
                initTable();
            }, 300000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            setTimeout(function() {
                echarts.getInstanceByDom(document.getElementById('sourceDistributionChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('taskTrendChart'))?.resize();
                echarts.getInstanceByDom(document.getElementById('responseTimeChart'))?.resize();
            }, 100);
        });
    </script>
</body>
</html>
