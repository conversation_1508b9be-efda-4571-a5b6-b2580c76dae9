package com.zjkj.aigc.service;

import com.zjkj.aigc.job.scheduler.AigcAlarmGenerateScheduler;
import com.zjkj.aigc.job.scheduler.AigcTaskStatDayScheduler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/22 16:47
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@TestPropertySource(properties = {"spring.profiles.active=test-aigc-csrs"})
public class JobTest {

    @Autowired
    private AigcTaskStatDayScheduler statDayScheduler;

    @Autowired
    private AigcAlarmGenerateScheduler aigcAlarmGenerateScheduler;

    @Test
    public void test() {
        statDayScheduler.aigcTaskStatDayJob();
    }

    @Test
    public void testAlert() {
        aigcAlarmGenerateScheduler.aigcAlarmGenerateJob();
    }
}


