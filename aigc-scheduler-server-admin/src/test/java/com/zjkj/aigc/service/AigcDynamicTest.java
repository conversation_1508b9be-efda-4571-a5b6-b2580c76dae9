package com.zjkj.aigc.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.zjkj.aigc.domain.task.service.impl.dynamic.AigcDynamicPredict;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/9
 */
public class AigcDynamicTest {

    List<AigcDynamicPredict.TaskStat> getGroupModelHourStat(LocalDateTime dateTime, long offsetHours, long minusDays) {
        LocalDateTime startTime = dateTime.minusDays(minusDays).minusHours(offsetHours).withMinute(0).withSecond(0);
        LocalDateTime endTime = (minusDays == 0) ? dateTime : startTime.plusHours(offsetHours * 2);
        System.out.println("-" + minusDays + ", " + LocalDateTimeUtil.formatNormal(startTime) + " ~ " + LocalDateTimeUtil.formatNormal(endTime));

        List<AigcDynamicPredict.TaskStat> stats = new ArrayList<>();
        LocalDateTime current = startTime;
        while (!current.isAfter(endTime)) {
            AigcDynamicPredict.TaskStat stat = new AigcDynamicPredict.TaskStat();
            // 随机生成Quantity
            double random = Math.random() * (1000 - 100) + 100;
            stat.setQuantity(Convert.convert(Long.class, random));
            stat.setTimeHour(current.getHour());
            stats.add(stat);
            current = current.plusHours(1);
        }
        
        return stats;
    }

    @Test
    public void test() {
        LocalDateTime dateTime = LocalDateTime.now();
        dateTime = LocalDateTime.parse("2025-04-09T12:02:03");
        List<AigcDynamicPredict.TaskStat> modelStat = getGroupModelHourStat(dateTime, 5, 0L);
        List<AigcDynamicPredict.TaskStat> yesterdayModelStat = getGroupModelHourStat(dateTime, 5, 1L);
        List<AigcDynamicPredict.TaskStat> lastWeekModelStat = getGroupModelHourStat(dateTime, 5, 7L);

        modelStat = JSON.parseArray("[{\"quantity\":594,\"timeHour\":7},{\"quantity\":276,\"timeHour\":8},{\"quantity\":169,\"timeHour\":9},{\"quantity\":585,\"timeHour\":10},{\"quantity\":462,\"timeHour\":11},{\"quantity\":871,\"timeHour\":12}]", AigcDynamicPredict.TaskStat.class);
        yesterdayModelStat = JSON.parseArray("[{\"quantity\":174,\"timeHour\":7},{\"quantity\":153,\"timeHour\":8},{\"quantity\":584,\"timeHour\":9},{\"quantity\":645,\"timeHour\":10},{\"quantity\":185,\"timeHour\":11},{\"quantity\":116,\"timeHour\":12},{\"quantity\":353,\"timeHour\":13},{\"quantity\":487,\"timeHour\":14},{\"quantity\":491,\"timeHour\":15},{\"quantity\":938,\"timeHour\":16},{\"quantity\":755,\"timeHour\":17}]", AigcDynamicPredict.TaskStat.class);
        lastWeekModelStat = JSON.parseArray("[{\"quantity\":724,\"timeHour\":7},{\"quantity\":535,\"timeHour\":8},{\"quantity\":518,\"timeHour\":9},{\"quantity\":281,\"timeHour\":10},{\"quantity\":357,\"timeHour\":11},{\"quantity\":258,\"timeHour\":12},{\"quantity\":583,\"timeHour\":13},{\"quantity\":985,\"timeHour\":14},{\"quantity\":365,\"timeHour\":15},{\"quantity\":235,\"timeHour\":16},{\"quantity\":471,\"timeHour\":17}]", AigcDynamicPredict.TaskStat.class);

        // 测试所有七种数据组合场景
        testAllDataCombinations(dateTime, modelStat, yesterdayModelStat, lastWeekModelStat);
    }
    
    /**
     * 测试所有数据组合场景
     */
    private void testAllDataCombinations(LocalDateTime dateTime, 
                                        List<AigcDynamicPredict.TaskStat> modelStat,
                                        List<AigcDynamicPredict.TaskStat> yesterdayModelStat,
                                        List<AigcDynamicPredict.TaskStat> lastWeekModelStat) {
        // 场景1: 所有数据都有
        System.out.println("\n测试场景1: 所有数据都有");
        runPredictionTest(modelStat, yesterdayModelStat, lastWeekModelStat, dateTime);
        
        // 场景2: 只有今天数据为空
        System.out.println("\n测试场景2: 只有今天数据为空");
        runPredictionTest(List.of(), yesterdayModelStat, lastWeekModelStat, dateTime);
        
        // 场景3: 只有昨天数据为空
        System.out.println("\n测试场景3: 只有昨天数据为空");
        runPredictionTest(modelStat, List.of(), lastWeekModelStat, dateTime);
        
        // 场景4: 只有上周数据为空
        System.out.println("\n测试场景4: 只有上周数据为空");
        runPredictionTest(modelStat, yesterdayModelStat, List.of(), dateTime);
        
        // 场景5: 只有今天数据存在
        System.out.println("\n测试场景5: 只有今天数据存在");
        runPredictionTest(modelStat, List.of(), List.of(), dateTime);
        
        // 场景6: 只有昨天数据存在
        System.out.println("\n测试场景6: 只有昨天数据存在");
        runPredictionTest(List.of(), yesterdayModelStat, List.of(), dateTime);
        
        // 场景7: 只有上周数据存在
        System.out.println("\n测试场景7: 只有上周数据存在");
        runPredictionTest(List.of(), List.of(), lastWeekModelStat, dateTime);
        
        // 场景8: 所有数据都为空 - 极端情况
        System.out.println("\n测试场景8: 所有数据都为空");
        runPredictionTest(List.of(), List.of(), List.of(), dateTime);
    }
    
    /**
     * 运行预测测试并输出结果
     */
    private void runPredictionTest(List<AigcDynamicPredict.TaskStat> modelStat,
                                  List<AigcDynamicPredict.TaskStat> yesterdayModelStat,
                                  List<AigcDynamicPredict.TaskStat> lastWeekModelStat,
                                  LocalDateTime dateTime) {
        System.out.println("modelStat: " + JSON.toJSONString(modelStat));
        System.out.println("yesterdayModelStat: " + JSON.toJSONString(yesterdayModelStat));
        System.out.println("lastWeekModelStat: " + JSON.toJSONString(lastWeekModelStat));

        List<AigcDynamicPredict.TaskPrediction> taskPredictions1h = AigcDynamicPredict.predictWithTimePatterns(
            modelStat, yesterdayModelStat, lastWeekModelStat, dateTime, 1);
        System.out.println("未来1小时预测: " + JSON.toJSONString(taskPredictions1h));
    }
}
