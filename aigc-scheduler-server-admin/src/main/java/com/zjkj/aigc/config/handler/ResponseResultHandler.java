package com.zjkj.aigc.config.handler;

import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.util.Jackson;
import com.zjkj.aigc.config.annotation.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@Slf4j
@SuppressWarnings("all")
@ControllerAdvice(annotations = BaseResponse.class)
public class ResponseResultHandler implements ResponseBodyAdvice {

    /**
     * 特殊响应类型集合
     */
    private static final Set<Class<?>> SPECIAL_RESPONSE_TYPES = Set.of(
            Resource.class,
            byte[].class,
            InputStream.class,
            OutputStream.class,
            MultipartFile.class
    );

    /**
     * 判断是否特殊响应类型
     */
    private boolean isSpecialResponseType(Class<?> parameterType) {
        return SPECIAL_RESPONSE_TYPES.stream()
                .anyMatch(type -> type.isAssignableFrom(parameterType));
    }

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        // 如果返回类型已经是DataResponse则不需要处理
        return !returnType.getParameterType().equals(DataResponse.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {

        Class<?> parameterType = returnType.getParameterType();
        if (Objects.equals(parameterType, String.class)) {
            // 如果是字符串，则将其转换为json字符串
            return Jackson.toJSONString(DataResponse.ok(body));
        }

        if (isSpecialResponseType(parameterType)) {
            return body;
        }

        return DataResponse.ok(body);
    }


}
