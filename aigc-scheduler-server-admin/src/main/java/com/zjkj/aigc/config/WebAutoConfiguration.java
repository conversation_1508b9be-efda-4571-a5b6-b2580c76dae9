package com.zjkj.aigc.config;

import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.zjkj.aigc.config.filter.AuthorizedFilter;
import com.zjkj.aigc.config.filter.TraceIdFilter;
import com.zjkj.saas.admin.sdk.filter.SsoTokenFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
@Configuration
@RequiredArgsConstructor
public class WebAutoConfiguration {

    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Value("${saas.sso.sdk.sso.public-key:}")
    protected String publicKey;

    @Value("${saas.sso.sdk.sso.exclude-patterns:}")
    protected List<String> excludePatterns;

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> {
            builder.simpleDateFormat(DATE_TIME_FORMAT);
            builder.serializers(new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));
        };
    }

    /**
     * traceId过滤器
     *
     * @return 过滤器注册
     */
    @Bean
    public FilterRegistrationBean<TraceIdFilter> traceIdFilter() {
        FilterRegistrationBean<TraceIdFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new TraceIdFilter());
        return filterRegistrationBean;
    }

    /**
     * 权限过滤器
     * @return 过滤器注册
     */
    @Bean
    @ConditionalOnProperty(name = "saas.sso.sdk.admin.enabled", havingValue = "true")
    public FilterRegistrationBean<AuthorizedFilter> authorizedFilter() {
        FilterRegistrationBean<AuthorizedFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new AuthorizedFilter(excludePatterns));
        filterRegistrationBean.setOrder(Ordered.LOWEST_PRECEDENCE);
        return filterRegistrationBean;
    }

    /**
     * sso token过滤器
     * @return 过滤器注册
     */
    @Bean
    @ConditionalOnProperty(name = "saas.sso.sdk.admin.enabled", havingValue = "true")
    public FilterRegistrationBean<SsoTokenFilter> ssoFilter() {
        FilterRegistrationBean<SsoTokenFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new SsoTokenFilter(this.publicKey));
        registrationBean.setOrder(10);
        return registrationBean;
    }

}
