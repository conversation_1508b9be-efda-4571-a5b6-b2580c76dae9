package com.zjkj.aigc.config.filter;

import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.context.TraceId;
import com.zjkj.aigc.common.util.Jackson;
import com.zjkj.saas.admin.sdk.dto.SsoUserDTO;
import com.zjkj.saas.admin.sdk.util.SaasSsoContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Slf4j
public class AuthorizedFilter extends OncePerRequestFilter {

    protected List<String> excludePatterns;

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();

    public AuthorizedFilter(List<String> excludePatterns){
        this.excludePatterns = excludePatterns;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            if (Objects.equals(response.getStatus(), HttpStatus.UNAUTHORIZED.value())) {
                return;
            }

            String ssoToken = request.getHeader("ssoToken");
            if (!StringUtils.hasText(ssoToken)) {
                String resp = Jackson.toJSONString(DataResponse.unAuthorized());
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.getOutputStream().write(resp.getBytes(StandardCharsets.UTF_8));
                return;
            }

            SsoUserDTO ssoUserDTO = SaasSsoContext.getCurrentContext().getSsoUser();
            Optional.ofNullable(ssoUserDTO)
                    .ifPresent(user -> TraceId.setCreator(user.getUserId() + StringPool.DASH + user.getUsername()));

            filterChain.doFilter(request, response);
        } finally {
            TraceId.removeCreator();
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        if (CollectionUtils.isEmpty(excludePatterns)) {
            return false;
        }

        return excludePatterns.stream().anyMatch(pattern -> PATH_MATCHER.match(pattern, requestPath));
    }
}
