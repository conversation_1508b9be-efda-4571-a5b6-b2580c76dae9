package com.zjkj.aigc.config.interceptor;

import com.zjkj.aigc.common.i18n.LocaleUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * 语言环境拦截器
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class LocaleInterceptor implements HandlerInterceptor {

    private static final String LANG_PARAM = "lang";
    private static final String ACCEPT_LANGUAGE_HEADER = "Accept-Language";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 1. 优先从请求参数获取语言
        String lang = request.getParameter(LANG_PARAM);
        
        // 2. 如果参数没有，从请求头获取
        if (!StringUtils.hasText(lang)) {
            lang = request.getHeader(ACCEPT_LANGUAGE_HEADER);
        }
        
        // 3. 解析语言环境
        Locale locale = LocaleUtils.parseLocale(lang);
        
        // 4. 检查是否支持该语言
        if (!LocaleUtils.isSupported(locale)) {
            locale = LocaleUtils.DEFAULT_LOCALE;
        }
        
        // 5. 设置当前线程的语言环境
        LocaleContextHolder.setLocale(locale);
        
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清理线程本地变量
        LocaleContextHolder.resetLocaleContext();
    }
}
