package com.zjkj.aigc.controller.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigCreateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigGlobalUpdateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigGlobalVO;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcDynamicConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * 动态调度配置
 *
 * <AUTHOR>
 * @since 2025/4/1
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/dynamic/config")
@RequiredArgsConstructor
public class AigcDynamicConfigController extends BaseController {

    private final IAigcDynamicConfigService aigcDynamicService;

    /**
     * 获取全局配置
     *
     * @return 全局配置
     */
    @GetMapping("/global")
    public AigcDynamicConfigGlobalVO getGlobalConfig() {
        return aigcDynamicService.getGlobalConfig();
    }

    /**
     * 更新全局配置
     *
     * @param req 全局配置请求
     */
    @PostMapping("/global")
    public void updateGlobalConfig(@RequestBody AigcDynamicConfigGlobalUpdateReq req) {
        aigcDynamicService.updateGlobalConfig(req);
    }

    /**
     * 配置列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcDynamicConfigVO> page(@RequestBody AigcDynamicConfigQuery query) {
        Page<AigcDynamicConfigVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcDynamicService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 配置详情
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("/detail/{id}")
    public AigcDynamicConfigVO detail(@PathVariable Long id) {
        return aigcDynamicService.detail(id);
    }

    /**
     * 配置新增
     *
     * @param createReq 新增请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcDynamicConfigCreateReq createReq) {
        aigcDynamicService.create(createReq);
    }

    /**
     * 配置更新
     *
     * @param createReq 更新请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                       @RequestBody AigcDynamicConfigCreateReq createReq) {
        aigcDynamicService.update(createReq);
    }

    /**
     * 配置批量删除
     */
    @PostMapping("/delete")
    public void delete(@RequestBody BatchOperateReq req) {
        aigcDynamicService.delete(req);
    }

    /**
     * 配置批量启用
     */
    @PostMapping("/enable")
    public void enable(@RequestBody BatchOperateReq req) {
        aigcDynamicService.enable(req);
    }

    /**
     * 配置批量禁用
     */
    @PostMapping("/disable")
    public void disable(@RequestBody BatchOperateReq req) {
        aigcDynamicService.disable(req);
    }

}
