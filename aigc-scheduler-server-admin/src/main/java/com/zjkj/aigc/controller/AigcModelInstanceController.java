package com.zjkj.aigc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordQuery;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordReq;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceAdjustRecordVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.service.IAigcModelInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;

/**
 * 模型实例
 *
 * <AUTHOR>
 * @since 2025/01/17
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/model/instance")
@RequiredArgsConstructor
public class AigcModelInstanceController extends BaseController {

    private final IAigcModelInstanceService iAigcModelInstanceService;

    /**
     * 模型实例调整记录 列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/adjust/record/page")
    public PageVO<AigcModelInstanceAdjustRecordVO> adjustRecordPage(@RequestBody AigcModelInstanceAdjustRecordQuery query) {
        Page<AigcModelInstanceAdjustRecordVO> reqPage = pageDefaultOrder(query);
        reqPage = iAigcModelInstanceService.adjustRecordPage(reqPage, query);
        return convertPageVO(reqPage);
    }


    /**
     * 创建 模型实例调整记录
     *
     * @param req 请求参数
     */
    @PostMapping("/adjust/record/create")
    public void createAdjustRecord(@Validated @RequestBody AigcModelInstanceAdjustRecordReq req) {
        iAigcModelInstanceService.createAdjustRecord(req);
    }

    /**
     * 删除 模型实例调整记录
     *
     * @param id id 数据ID
     */
    @GetMapping("/adjust/record/detail/{id}")
    public AigcModelInstanceAdjustRecordVO adjustRecordDetail(@PathVariable Long id) {
        return iAigcModelInstanceService.adjustRecordDetail(id);
    }

    /**
     * 更新 模型实例调整记录
     *
     * @param req 请求参数
     */
    @PutMapping("/adjust/record/update")
    public void updateAdjustRecord(@Validated({Default.class, ValidationGroup.Update.class}) @RequestBody AigcModelInstanceAdjustRecordReq req) {
        iAigcModelInstanceService.updateAdjustRecord(req);
    }

    /**
     * 删除 模型实例调整记录
     *
     * @param id id 数据ID
     */
    @DeleteMapping("/adjust/record/delete/{id}")
    public void deleteAdjustRecord(@PathVariable Long id) {
        iAigcModelInstanceService.deleteAdjustRecord(id);
    }


}
