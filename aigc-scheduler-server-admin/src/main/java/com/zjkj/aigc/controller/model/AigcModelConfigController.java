package com.zjkj.aigc.controller.model;

import com.zjkj.aigc.common.req.model.AigcModelConfigCreateReq;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.model.AigcModelConfigVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.model.IAigcModelConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * 模型配置管理
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/model/config")
@RequiredArgsConstructor
public class AigcModelConfigController extends BaseController {

    private final IAigcModelConfigService aigcModelConfigService;

    /**
     * 根据模型id获取
     *
     * @param modelId 模型id
     * @return 模型配置
     */
    @GetMapping("/model/{modelId}")
    public AigcModelConfigVO getByModel(@PathVariable Long modelId) {
        return aigcModelConfigService.getByModelId(modelId);
    }

    /**
     * 创建模型配置
     *
     * @param req 模型配置
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcModelConfigCreateReq req) {
        aigcModelConfigService.create(req);
    }

    /**
     * 更新模型配置
     *
     * @param req 模型配置
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Create.class})
                       @RequestBody AigcModelConfigCreateReq req) {
        aigcModelConfigService.update(req);
    }

}
