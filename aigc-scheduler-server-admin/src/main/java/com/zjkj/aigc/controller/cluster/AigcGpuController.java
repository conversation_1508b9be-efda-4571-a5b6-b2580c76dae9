package com.zjkj.aigc.controller.cluster;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.gpu.AigcGpuCreateReq;
import com.zjkj.aigc.common.req.gpu.AigcGpuQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcGpuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.groups.Default;
import java.util.List;

/**
 * GPU管理
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/gpu")
@RequiredArgsConstructor
public class AigcGpuController extends BaseController {

    private final IAigcGpuService aigcGpuService;

    /**
     * GPU列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcGpuVO> page(@RequestBody AigcGpuQuery query) {
        Page<AigcGpuVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcGpuService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * GPU列表-all
     *
     * @return 列表数据
     */
    @PostMapping("/list/all")
    public List<AigcGpuVO> listAll(@RequestBody AigcGpuQuery query) {
        return aigcGpuService.list(query);
    }

    /**
     * GPU详情
     *
     * @param id GPU id
     * @return GPU详情
     */
    @GetMapping("/detail/{id}")
    public AigcGpuVO detail(@PathVariable Long id) {
        return aigcGpuService.detail(id);
    }

    /**
     * 新增GPU
     *
     * @param createReq 新增GPU请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcGpuCreateReq createReq) {
        aigcGpuService.create(createReq);
    }

    /**
     * 更新GPU
     *
     * @param createReq 更新GPU请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                      @RequestBody AigcGpuCreateReq createReq) {
        aigcGpuService.update(createReq);
    }

    /**
     * 删除GPU
     *
     * @param id GPU id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        aigcGpuService.delete(id);
    }
}
