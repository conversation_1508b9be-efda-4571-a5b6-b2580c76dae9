package com.zjkj.aigc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.resource.*;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.resource.*;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.domain.task.service.resource.ResourceBillGroupConfigService;
import com.zjkj.aigc.service.resource.IResourceBillService;
import com.zjkj.aigc.service.resource.IResourceExpendService;
import com.zjkj.aigc.service.resource.ResourcePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.util.List;

/**
 * 资源规划
 *
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/resource")
@RequiredArgsConstructor
public class ResourceController extends BaseController {

    private final ResourcePlanService resourcePlanService;
    private final IResourceExpendService iResourceExpendService;
    private final IResourceBillService iResourceBillService;
    private final ResourceBillGroupConfigService resourceBillGroupConfigService;

    /**
     * 资源规划数据
     *
     * @param query 查询条件
     * @return 资源规划数据
     */
    @PostMapping("/plan/query")
    public List<ResourcePlanVO> queryPlan(@RequestBody ResourcePlanQuery query) {
        return resourcePlanService.queryPlan(query);
    }


    /**
     * 创建规划数据
     *
     * @param req 请求参数
     */
    @PostMapping("/plan/create")
    public void createPlan(@Validated({Default.class, ValidationGroup.Create.class})
                           @RequestBody ResourcePlanCreateReq req) {
        resourcePlanService.createResourcePlan(req);
    }

    /**
     * 更新规划数据
     *
     * @param req 请求参数
     */
    @PutMapping("/plan/update")
    public void updatePlan(@RequestBody ResourcePlanCreateReq req) {
        resourcePlanService.updateResourcePlan(req);
    }

    /**
     * 删除规划数据
     *
     * @param query 请求参数
     */
    @DeleteMapping("/plan/delete")
    public void deletePlan(@RequestBody ResourcePlanQuery query) {
        resourcePlanService.deleteResourcePlan(query);
    }

    /**
     * 资源规划预算数据
     *
     * @param query 查询条件
     * @return 资源规划数据
     */
    @PostMapping("/plan/query/budget")
    public List<ResourcePlanPriceVO> queryPlanbudget(@RequestBody ResourcePlanQuery query) {
        return resourcePlanService.queryBudget(query);
    }

    /**
     * 资源支出数据
     *
     * @param query 查询条件
     * @return 资源支出数据
     */
    @PostMapping("/expend/query")
    public ResourceExpendSummaryVO queryExpend(@RequestBody ResourceExpendQuery query) {
        return iResourceExpendService.query(query);
    }

    /**
     * 创建支出数据
     *
     * @param req 请求参数
     */
    @PostMapping("/expend/create")
    public void createExpend(@Validated({Default.class, ValidationGroup.Create.class})
                             @RequestBody ResourceExpendCreateReq req) {
        iResourceExpendService.createResourceExpend(req);
    }

    /**
     * 更新支出数据
     *
     * @param req 请求参数
     */
    @PutMapping("/expend/update")
    public void updateExpend(@Validated({Default.class, ValidationGroup.Update.class})
                             @RequestBody @Valid ResourceExpendCreateReq req) {
        iResourceExpendService.updateResourceExpend(req);
    }

    /**
     * 费用账单平台
     *
     * @return 账单平台
     */
    @GetMapping("/expend/bill/platform/{type}")
    public List<ResourceBillPlatform> billPlatform(@PathVariable String type) {
        return iResourceBillService.billPlatform(type);
    }

    /**
     * 费用账单导入
     *
     * @param type     账单类型
     * @param platform 云平台
     * @param file     文件
     */
    @PostMapping("/expend/bill/import/{type}/{platform}")
    public Integer billImport(@PathVariable String type, @PathVariable String platform,
                              MultipartFile file) throws Exception {
        return iResourceBillService.importBill(type, platform, file);
    }

    /**
     * 费用账单列表
     *
     * @param query 查询条件
     * @return 账单分页
     */
    @PostMapping("/expend/bill/page")
    public PageVO<ResourceBillVO> billPage(@RequestBody ResourceBillQuery query) {
        Page<ResourceBillVO> reqPage = getPageInfo(query);
        reqPage = iResourceBillService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 费用账单合计
     *
     * @param query 查询条件
     * @return 账单分页
     */
    @PostMapping("/expend/bill/total")
    public BigDecimal billTotal(@RequestBody ResourceBillQuery query) {
        return iResourceBillService.billTotal(query);
    }

    /**
     * 费用分摊信息
     *
     * @param query 查询条件
     * @return 费用分摊信息
     */
    @PostMapping("/expend/bill/type/group")
    public ResourceBillTypeGroupVO billTypeGroup(@Validated @RequestBody ResourceBillTypeQuery query) {
        return iResourceBillService.billTypeGroup(query);
    }

    /**
     * 费用分摊信息配置
     *
     * @param query 查询条件
     * @return 费用分摊信息配置
     */
    @PostMapping("/expend/bill/group/config/query")
    public List<ResourceBillGroupConfigVO> queryBillGroupConfig(@Validated @RequestBody ResourceBillGroupConfigQuery query) {
        return iResourceBillService.queryBillGroupConfig(query);
    }

    /**
     * 费用分摊信息配置保存
     *
     * @param createReq 费用分摊信息配置
     */
    @PostMapping("/expend/bill/group/config/save")
    public void saveBillGroupConfig(@Validated @RequestBody ResourceBillGroupConfigCreateReq createReq) {
        iResourceBillService.saveBillGroupConfig(createReq);
    }

    @GetMapping("/expend/bill/approve")
    public String starExpendBillApprove(){
        return iResourceBillService.starExpendBillApprove();
    }
}
