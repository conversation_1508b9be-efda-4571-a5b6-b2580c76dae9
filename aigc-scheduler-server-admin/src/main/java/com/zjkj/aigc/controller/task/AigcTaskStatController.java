package com.zjkj.aigc.controller.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.stat.AigcTaskStatDayVO;
import com.zjkj.aigc.common.vo.stat.AigcTaskTotalStatVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.AigcTaskStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AI任务统计
 *
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:15
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/stat")
@RequiredArgsConstructor
public class AigcTaskStatController extends BaseController {

    private final AigcTaskStatService taskStatService;

    /**
     * 任务统计列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcTaskStatDayVO> page(@Validated @RequestBody TaskStatSummaryReq query) {
        query.checkTimeRange();
        Page<AigcTaskStatDayVO> reqPage = getPageInfo(query);
        reqPage = taskStatService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 任务数趋势
     * @param query 查询条件
     * @return 趋势图数据
     */
    @PostMapping("/trend")
    public List<AigcTaskStatDayVO> trend(@Validated @RequestBody TaskStatSummaryReq query) {
        return taskStatService.trend(query);
    }

    /**
     * 任务数环比
     * @param query 查询条件
     * @return 趋势图数据
     */
    @PostMapping("/compare/chain")
    public List<AigcTaskStatDayVO> compareChain(@RequestBody TaskStatSummaryReq query) {
        if (!StringUtils.hasText(query.getCompareType())) {
           throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.compare.type.empty"));
        }

        if (!StringUtils.hasText(query.getTaskType()) || !StringUtils.hasText(query.getModelName())) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.task.type.model.name.empty"));
        }

        return taskStatService.compareChain(query);
    }

    /**
     * 合计环比
     * @param query 查询条件
     * @return 数据
     */
    @PostMapping("/total/compare/chain")
    public AigcTaskTotalStatVO totalCompareChain(@Validated @RequestBody TaskStatSummaryReq query) {
        return taskStatService.totalCompareChain(query);
    }


}
