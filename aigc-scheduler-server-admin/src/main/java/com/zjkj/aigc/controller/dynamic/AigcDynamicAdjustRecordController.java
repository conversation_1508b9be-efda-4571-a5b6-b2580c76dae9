package com.zjkj.aigc.controller.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicAdjustRecordQuery;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicAdjustRecordVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.dynamic.IAigcDynamicAdjustRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  动态调度调整记录
 * <AUTHOR>
 * @since 2025/4/27
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/dynamic/record")
@RequiredArgsConstructor
public class AigcDynamicAdjustRecordController extends BaseController {

    private final IAigcDynamicAdjustRecordService aigcDynamicAdjustRecordService;
    /**
     * 调整记录列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcDynamicAdjustRecordVO> page(@RequestBody AigcDynamicAdjustRecordQuery query) {
        Page<AigcDynamicAdjustRecordVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcDynamicAdjustRecordService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 调整记录详情
     * @param id 记录id
     * @return 详情数据
     */
    @GetMapping("/detail/{id}")
    public AigcDynamicAdjustRecordVO detail(@PathVariable("id") Long id) {
        return aigcDynamicAdjustRecordService.detail(id);
    }

    /**
     * 调整取消
     * @param id 记录id
     */
    @PostMapping("/cancel/{id}")
    public void cancel(@PathVariable("id") Long id) {
        aigcDynamicAdjustRecordService.cancel(id);
    }

    /**
     * 调整回退
     * @param id 记录id
     */
    @PostMapping("/rollback/{id}")
    public void rollback(@PathVariable("id") Long id) {
        aigcDynamicAdjustRecordService.rollback(id);
    }
}
