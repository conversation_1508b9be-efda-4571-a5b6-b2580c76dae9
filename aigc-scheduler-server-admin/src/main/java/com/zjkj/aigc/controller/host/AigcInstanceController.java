package com.zjkj.aigc.controller.host;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceQuery;
import com.zjkj.aigc.common.req.node.AigcNodeInstanceQuery;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceBrieflyVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.AigcInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 节点&模型实例
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/instance")
@RequiredArgsConstructor
public class AigcInstanceController extends BaseController {

    private final AigcInstanceService aigcInstanceService;

    /**
     * 节点实例列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/node/page")
    public PageVO<AigcNodeInstanceVO> nodeInstancePage(@RequestBody AigcNodeInstanceQuery query) {
        Page<AigcNodeInstanceVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcInstanceService.nodeInstancePage(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 节点实例简要列表
     *
     * @return 列表信息
     */
    @GetMapping("/node/list/briefly")
    public List<AigcNodeInstanceBrieflyVO> nodebrieflyList() {
        List<AigcNodeInstanceBrieflyVO> brieflyList = aigcInstanceService.nodebrieflyList();
        Collections.reverse(brieflyList);
        return brieflyList;
    }

    /**
     * 模型实例列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/model/page")
    public PageVO<AigcModelInstanceVO> modelInstancePage(@RequestBody AigcModelInstanceQuery query) {
        Page<AigcModelInstanceVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcInstanceService.modelInstancePage(reqPage, query);
        return convertPageVO(reqPage);
    }
}
