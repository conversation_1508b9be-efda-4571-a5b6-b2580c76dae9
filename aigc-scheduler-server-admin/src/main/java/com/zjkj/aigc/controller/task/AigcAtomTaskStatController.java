package com.zjkj.aigc.controller.task;

import com.zjkj.aigc.common.vo.stat.AigcAtomTaskTotalStatVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.AigcAtomTaskStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 原子模型任务统计
 *
 * @Author:zhongyuji
 * @Description:
 * @DATE: 2024/12/30
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/atom/task/stat")
@RequiredArgsConstructor
public class AigcAtomTaskStatController extends BaseController {

    private final AigcAtomTaskStatService taskStatService;


    @GetMapping("/list")
    public List<AigcAtomTaskTotalStatVO> aigcAtomTaskStat() {
        return taskStatService.aigcAtomTaskStat();
    }


}
