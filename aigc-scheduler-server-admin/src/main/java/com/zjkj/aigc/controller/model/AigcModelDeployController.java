package com.zjkj.aigc.controller.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelDeployQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployFullVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcModelDeployService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;


/**
 * 模型部署发布
 *
 * <AUTHOR>
 * @since 2024/11/11
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/model/deploy")
@RequiredArgsConstructor
public class AigcModelDeployController extends BaseController {

    private final IAigcModelDeployService iAigcModelDeployService;

    /**
     * 模型发布列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcModelDeployVO> page(@RequestBody AigcModelDeployQuery query) {
        Page<AigcModelDeployVO> reqPage = pageDefaultOrder(query);
        reqPage = iAigcModelDeployService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 模型发布详情
     *
     * @param id 发布id
     * @return 模型发布详情
     */
    @GetMapping("/detail/{id}")
    public AigcModelDeployVO detail(@PathVariable Long id) {
        return iAigcModelDeployService.detail(id);
    }

    /**
     * 新建模型发布
     *
     * @param req 模型发布信息
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcModelDeployCreateReq req) {
        iAigcModelDeployService.create(req);
    }

    /**
     * 更新模型发布
     *
     * @param req 模型发布信息
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                       @RequestBody AigcModelDeployCreateReq req) {
        iAigcModelDeployService.update(req);
    }

    /**
     * 删除模型发布
     *
     * @param id 模型发布id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        iAigcModelDeployService.delete(id);
    }

    /**
     * 申请模型发布
     *
     * @param id 模型发布id
     */
    @PostMapping("/request/{id}")
    public void request(@PathVariable Long id) {
        iAigcModelDeployService.requestDeploy(id);
    }

    /**
     * 完成模型发布
     *
     * @param id 模型发布id
     */
    @PostMapping("/deployDone/{id}")
    public void deployDone(@PathVariable Long id) {
        iAigcModelDeployService.deployDone(id);
    }

    /**
     * 模型发布通知
     *
     * @param id 模型发布id
     */
    @PostMapping("/notify/{id}")
    public void deployNotify(@PathVariable Long id) {
        iAigcModelDeployService.deployNotify(id);
    }

    /**
     * 模型部署明细
     *
     * @param id 模型发布id
     * @return 模型发布明细
     */
    @GetMapping("/detailFull/{id}")
    public AigcModelDeployFullVO detailFull(@PathVariable Long id) {
        return iAigcModelDeployService.detailFull(id);
    }

}
