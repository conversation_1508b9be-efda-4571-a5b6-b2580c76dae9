package com.zjkj.aigc.controller.cluster;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageStatQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelTypeUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelUsageQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterModelUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageStatVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcModelTypeTrendVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcClusterUsageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.List;

/**
 * 集群资源使用
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/cluster/usage")
@RequiredArgsConstructor
public class AigcClusterUsageController extends BaseController {

    private final IAigcClusterUsageService aigcClusterUsageService;

    /**
     * 资源使用列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcClusterUsageVO> page(@RequestBody AigcClusterUsageQuery query) {
        Page<AigcClusterUsageVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcClusterUsageService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 资源使用详情
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("/detail/{id}")
    public AigcClusterUsageVO detail(@PathVariable Long id) {
        return aigcClusterUsageService.detail(id);
    }

    /**
     * 新增资源使用
     *
     * @param createReq 新增请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcClusterUsageCreateReq createReq) {
        aigcClusterUsageService.create(createReq);
    }

    /**
     * 更新资源使用
     *
     * @param createReq 更新请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                      @RequestBody AigcClusterUsageCreateReq createReq) {
        aigcClusterUsageService.update(createReq);
    }

    /**
     * 删除资源使用
     *
     * @param id 主键
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        aigcClusterUsageService.delete(id);
    }

    /**
     * 资源分布
     *
     * @return 统计结果
     */
    @PostMapping("/located")
    public AigcClusterUsageStatVO located(@RequestBody AigcClusterUsageStatQuery query) {
        return aigcClusterUsageService.located(query);
    }


    /**
     * 模型统计
     *
     * @return 统计结果
     */
    @PostMapping("/model/stat")
    public List<AigcClusterUsageStatVO.ModelProportion> modelStat(@RequestBody AigcClusterUsageStatQuery query) {
        return aigcClusterUsageService.modelStat(query);
    }

    /**
     * 模型资源使用
     *
     * @param query 查询条件
     * @return 资源使用数据
     */
    @PostMapping("/model")
    public AigcClusterModelUsageVO clusterModelUsage(@Validated @RequestBody AigcModelUsageQuery query) {
        return aigcClusterUsageService.modelUsage(query);
    }

    /**
     * 模型类型资源使用趋势
     *
     * @return 统计结果
     */
    @PostMapping("/modelType/trend")
    public AigcModelTypeTrendVO modelTypeTrend(@RequestBody AigcModelTypeUsageQuery query) {
        query.checkParams();
        return aigcClusterUsageService.modelTypeTrend(query);
    }

}
