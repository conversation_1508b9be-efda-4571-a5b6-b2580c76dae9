package com.zjkj.aigc.controller.host;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.host.AigcHostCreateReq;
import com.zjkj.aigc.common.req.host.AigcHostQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.host.AigcHostVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcHostService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * 主机名单管理
 *
 * <AUTHOR>
 * @since 2024/11/25
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/host")
@RequiredArgsConstructor
public class AigcHostController extends BaseController {

    private final IAigcHostService aigcHostService;
    /**
     * 主机列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcHostVO> page(@RequestBody AigcHostQuery query) {
        Page<AigcHostVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcHostService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 主机详情
     *
     * @param id 主机id
     * @return 主机详情
     */
    @GetMapping("/detail/{id}")
    public AigcHostVO detail(@PathVariable Long id) {
      return aigcHostService.detail(id);
    }

    /**
     * 新增主机
     *
     * @param createReq 新增主机请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcHostCreateReq createReq) {
        aigcHostService.create(createReq);
    }

    /**
     * 更新主机
     *
     * @param createReq 更新主机请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                       @RequestBody AigcHostCreateReq createReq) {
        aigcHostService.update(createReq);
    }

    /**
     * 删除主机
     *
     * @param id 主机id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        aigcHostService.delete(id);
    }

    /**
     * 批量启用
     *
     * @param req 请求信息
     */
    @PostMapping("/enable")
    public void enable(@Validated @RequestBody BatchOperateReq req) {
        aigcHostService.enable(req);
    }

    /**
     * 批量禁用
     *
     * @param req 请求信息
     */
    @PostMapping("/disable")
    public void disable(@Validated @RequestBody BatchOperateReq req) {
        aigcHostService.disable(req);
    }

}
