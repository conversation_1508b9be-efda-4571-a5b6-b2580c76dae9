package com.zjkj.aigc.controller.model;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.req.model.test.ModelParamReq;
import com.zjkj.aigc.common.req.model.test.ModelTestCreateReq;
import com.zjkj.aigc.common.req.model.test.ModelTestQuery;
import com.zjkj.aigc.common.vo.ModelTestReportVO;
import com.zjkj.aigc.common.vo.ModelTestVO;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.service.ModelTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 模型测试
 *
 * <AUTHOR>
 * @since 2024/10/17
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/model-test")
@RequiredArgsConstructor
public class ModelTestController extends BaseController {

    private final ModelTestService modelTestService;
    private final AigcTaskService aigcTaskService;

    /**
     * 模型测试列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<ModelTestVO> page(@RequestBody ModelTestQuery query) {
        Page<ModelTestVO> reqPage = getPageInfo(query);
        reqPage.setOrders(Lists.newArrayList(OrderItem.desc("id")));
        reqPage = modelTestService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 获取模型参数
     *
     * @param req 请求参数
     * @return 模型参数
     */
    @PostMapping("/modelParam")
    public String getModelParam(@Validated @RequestBody ModelParamReq req) {
        return aigcTaskService.queryModelParam(req.getTaskType(), req.getModelName());
    }

    /**
     * 新建模型测试
     *
     * @param req 任务信息
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody ModelTestCreateReq req) {
        modelTestService.createModelTest(req);
    }


    /**
     * 模型测试详情
     *
     * @param testNo 测试编号
     * @return 任务详情
     */
    @GetMapping("/detail/{testNo}")
    public ModelTestVO detail(@PathVariable String testNo) {
        return modelTestService.queryByTestNo(testNo);
    }

    /**
     * 更新模型测试
     *
     * @param testNo 测试编号
     * @param req    任务信息
     */
    @PostMapping("/update/{testNo}")
    public void update(@PathVariable String testNo, @Validated @RequestBody ModelTestCreateReq req) {
        req.setTestNo(testNo);
        modelTestService.updateModelTest(req);
    }

    /**
     * 开始模型测试
     *
     * @param testNo 测试编号
     */
    @PostMapping("/start/{testNo}")
    public void start(@PathVariable String testNo) {
        modelTestService.startModelTest(testNo);
    }

    /**
     * 取消模型测试
     *
     * @param testNo 测试编号
     */
    @PostMapping("/cancel/{testNo}")
    public void cancel(@PathVariable String testNo) {
        modelTestService.cancelModelTest(testNo);
    }

    /**
     * 删除模型测试
     *
     * @param testNo 测试编号
     */
    @PostMapping("/del/{testNo}")
    public void delete(@PathVariable String testNo) {
        modelTestService.delModelTest(testNo);
    }

    /**
     * 查看测试报告
     *
     * @param testNo 测试编号
     * @return 测试报告
     */
    @GetMapping("/report/{testNo}")
    public ModelTestReportVO report(@PathVariable String testNo) {
        return modelTestService.queryReportByTestNo(testNo);
    }


}
