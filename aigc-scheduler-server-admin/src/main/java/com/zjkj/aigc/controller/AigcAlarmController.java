package com.zjkj.aigc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmUpdateReq;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.TaskModelSummaryVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmChannelVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmConfigVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.service.alarm.IAigcAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.List;

/**
 * 模型告警
 *
 * <AUTHOR>
 * @since 2024/12/17
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/alarm")
@RequiredArgsConstructor
public class AigcAlarmController extends BaseController {

    private final IAigcAlarmService aigcAlarmService;

    /**
     * 模型告警分页查询
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcAlarmVO> alarmPage(@RequestBody AigcAlarmQuery query) {
        Page<AigcAlarmVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcAlarmService.alarmPage(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 模型告警详情
     *
     * @param id 模型告警 id
     * @return 模型告警详情
     */
    @GetMapping("/detail/{id}")
    public AigcAlarmVO alarmDetail(@PathVariable Long id) {
        return aigcAlarmService.alarmDetail(id);
    }

    /**
     * 更新模型告警
     *
     * @param updateReq 更新模型告警
     */
    @PutMapping("/update")
    public void updateAlarm(@RequestBody AigcAlarmUpdateReq updateReq) {
        aigcAlarmService.updateAlarm(updateReq);
    }

    /**
     * 模型告警配置分页查询
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/config/page")
    public PageVO<AigcAlarmConfigVO> alarmPage(@RequestBody AigcAlarmConfigQuery query) {
        Page<AigcAlarmConfigVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcAlarmService.alarmConfigPage(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 模型告警配置详情
     *
     * @param id 模型告警配置 id
     * @return 模型告警配置详情
     */
    @GetMapping("/config/detail/{id}")
    public AigcAlarmConfigVO alarmConfigDetail(@PathVariable Long id) {
        return aigcAlarmService.alarmConfigDetail(id);
    }

    /**
     * 创建模型告警配置
     *
     * @param createReq 创建模型告警配置
     */
    @PostMapping("/config/save")
    public void createAlarmConfig(@Validated({Default.class, ValidationGroup.Create.class})
                                  @RequestBody AigcAlarmConfigCreateReq createReq) {
        aigcAlarmService.createAlarmConfig(createReq);
    }

    /**
     * 更新模型告警配置
     *
     * @param createReq 模型告警配置
     */
    @PutMapping("/config/update")
    public void updateAlarmConfig(@Validated({Default.class, ValidationGroup.Update.class})
                                  @RequestBody AigcAlarmConfigCreateReq updateReq) {
        aigcAlarmService.updateAlarmConfig(updateReq);
    }

    /**
     * 启用模型告警配置
     *
     * @param id 告警配置ID
     */
    @PutMapping("/config/enable/{id}")
    public void enableAlarmConfig(@PathVariable Long id) {
        aigcAlarmService.updateAlarmConfigStatus(id, AlarmConfigStatusEnum.ENABLE.getStatus());
    }

    /**
     * 禁用模型告警配置
     *
     * @param id 告警配置ID
     */
    @PutMapping("/config/disable/{id}")
    public void disableAlarmConfig(@PathVariable Long id) {
        aigcAlarmService.updateAlarmConfigStatus(id, AlarmConfigStatusEnum.DISABLE.getStatus());
    }

    /**
     * 删除模型告警配置
     *
     * @param id 模型告警配置 id
     */
    @DeleteMapping("/config/delete/{id}")
    public void deleteAlarmConfig(@PathVariable Long id) {
        aigcAlarmService.deleteAlarmConfig(id);
    }

    /**
     * 告警渠道配置集合
     *
     * @return 告警渠道配置集合
     */
    @PostMapping("/channel/list")
    public List<AigcAlarmChannelVO> alarmChannelList(@RequestBody AigcAlarmChannelQuery query) {
        return aigcAlarmService.alarmChannelList(query);
    }

    /**
     * 告警渠道配置详情
     *
     * @param id 数据ID
     * @return 告警渠道配置
     */
    @GetMapping("/channel/detail/{id}")
    public AigcAlarmChannelVO alarmChannelDetail(@PathVariable Long id) {
        return aigcAlarmService.alarmChannelDetail(id);
    }

    /**
     * 新增告警渠道配置
     *
     * @param createReq 告警渠道配置
     */
    @PostMapping("/channel/save")
    public void saveAlarmChannel(@Validated({Default.class, ValidationGroup.Create.class})
                                 @RequestBody AigcAlarmChannelCreateReq createReq) {
        aigcAlarmService.saveAlarmChannel(createReq);
    }

    /**
     * 更新告警渠道配置
     *
     * @param updateReq 更新告警渠道配置
     */
    @PutMapping("/channel/update")
    public void updateAlarmChannel(@Validated({Default.class, ValidationGroup.Update.class})
                                   @RequestBody AigcAlarmChannelCreateReq updateReq) {
        aigcAlarmService.updateAlarmChannel(updateReq);
    }

    /**
     * 删除告警渠道配置
     *
     * @param id 数据ID
     */
    @DeleteMapping("/channel/delete/{id}")
    public void deleteAlarmConfigChannel(@PathVariable Long id) {
        aigcAlarmService.deleteAlarmChannel(id);
    }

    /**
     * 模型任务详情
     *
     * @param id 告警数据ID
     * @return 模型任务详情
     */
    @GetMapping("/task/status/{alarmId}")
    public TaskModelSummaryVO getAlarmTaskStatus(@PathVariable Long alarmId) {
        return aigcAlarmService.getAlarmTaskStatus(alarmId);
    }

}
