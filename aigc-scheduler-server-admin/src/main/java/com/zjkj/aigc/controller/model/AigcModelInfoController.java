package com.zjkj.aigc.controller.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoBrieflyVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcModelInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.Collections;
import java.util.List;

/**
 * 模型基础信息
 *
 * <AUTHOR>
 * @since 2024/11/11
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/model/info")
@RequiredArgsConstructor
public class AigcModelInfoController extends BaseController {

    private final IAigcModelInfoService iAigcModelInfoService;

    /**
     * 模型信息列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcModelInfoVO> page(@RequestBody AigcModelInfoQuery query) {
        Page<AigcModelInfoVO> reqPage = pageDefaultOrder(query);
        reqPage = iAigcModelInfoService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 模型信息简要列表
     * @return 模型信息简要列表
     */
    @GetMapping("/list/briefly")
    public List<AigcModelInfoBrieflyVO> brieflyList() {
        List<AigcModelInfoBrieflyVO> infoBrieflyList = iAigcModelInfoService.brieflyList();
        Collections.reverse(infoBrieflyList);
        return infoBrieflyList;
    }

    /**
     * 模型信息详情
     *
     * @param id 模型基础信息id
     * @return 模型基础信息详情
     */
    @GetMapping("/detail/{id}")
    public AigcModelInfoVO detail(@PathVariable Long id) {
        return iAigcModelInfoService.detail(id);
    }

    /**
     * 创建模型信息
     *
     * @param req 创建模型基础信息请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcModelInfoCreateReq req) {
        iAigcModelInfoService.create(req);
    }

    /**
     * 更新模型信息
     *
     * @param req 更新模型基础信息请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                       @RequestBody AigcModelInfoCreateReq req) {
        iAigcModelInfoService.update(req);
    }

    /**
     * 删除模型信息
     *
     * @param id 模型基础信息id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        iAigcModelInfoService.delete(id);
    }

    /**
     * 模型批量启用
     *
     * @param req 请求信息
     */
    @PostMapping("/enable")
    public void enable(@Validated @RequestBody BatchOperateReq req) {
        iAigcModelInfoService.enable(req);
    }

    /**
     * 模型批量禁用
     *
     * @param req 请求信息
     */
    @PostMapping("/disable")
    public void disable(@Validated @RequestBody BatchOperateReq req) {
        iAigcModelInfoService.disable(req);
    }

    /**
     * 查询同集群的模型
     * @param id 模型ID
     * @return 同集群的模型列表
     */
    @GetMapping("/sameCluster/{id}")
    public List<AigcModelInfoVO> sameCluster(@PathVariable Long id) {
        return iAigcModelInfoService.sameClusterModel(id);
    }
}
