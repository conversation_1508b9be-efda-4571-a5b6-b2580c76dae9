package com.zjkj.aigc.controller;

import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.dto.file.FileDownloadDTO;
import com.zjkj.aigc.common.dto.file.FileUploadDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.domain.task.service.impl.model.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件上传下载控制器
 * 
 * <AUTHOR>
 * @since 2025/7/15
 */
@Slf4j
@RequestLog
@RestController
@RequiredArgsConstructor
@RequestMapping("/aigc/file")
public class FileController {

    private final FileService fileService;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    public DataResponse<FileUploadDTO> upload(@RequestParam("file") MultipartFile file) {
        log.info("文件上传请求，文件名: {}, 文件大小: {} bytes", file.getOriginalFilename(), file.getSize());
        FileUploadDTO result = fileService.uploadFile(file);
        return DataResponse.ok(result);
    }

    /**
     * 文件下载
     *
     * @param filePath 文件路径
     * @return 下载信息
     */
    @GetMapping("/info")
    public DataResponse<FileDownloadDTO> getFileInfo(@RequestParam("filePath") String filePath) {
        log.info("获取文件信息请求，文件路径: {}", filePath);
        
        // 检查文件是否存在
        if (!fileService.exists(filePath)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS, 
                MessageUtils.getMessage("business.file.not.found"));
        }
        
        FileDownloadDTO fileInfo = fileService.downloadFile(filePath);
        // 不返回输入流，避免序列化问题
        fileInfo.setInputStream(null);
        return DataResponse.ok(fileInfo);
    }

    /**
     * 文件下载（直接返回文件流）
     *
     * @param fileDownloadDTO 文件路径
     * @return 文件流
     */
    @PostMapping("/download")
    public ResponseEntity<InputStreamResource> download(@RequestBody FileDownloadDTO fileDownloadDTO) {
        String filePath = fileDownloadDTO.getFilePath();
        log.info("文件下载请求，文件路径: {}", filePath);

        try {
            // 检查文件是否存在
            if (!fileService.exists(filePath)) {
                throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS,
                    MessageUtils.getMessage("business.file.not.found"));
            }

            // 获取文件信息
            FileDownloadDTO fileInfo = fileService.downloadFile(filePath);
            InputStream downloadStream = fileInfo.getInputStream();

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();

            // 设置内容类型
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 设置文件名（支持中文）
            String fileName = fileInfo.getFileName();
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20");
            headers.setContentDispositionFormData("attachment", encodedFileName);

            // 返回文件流
            return new ResponseEntity<>(new InputStreamResource(downloadStream), headers, HttpStatus.OK);

        } catch (BaseBizException e) {
            // 业务异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("文件下载失败，文件路径: {}", filePath, e);
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                MessageUtils.getMessage("business.file.download.failed"));
        }
    }

    /**
     * 文件下载（直接使用OssUtils，不经过FileService）
     *
     * @param filePath 文件路径
     * @param fileName 文件名（可选，如果不提供则从路径中提取）
     * @return 文件流
     *//*
    @GetMapping("/direct-download")
    public ResponseEntity<InputStreamResource> directDownload(
            @RequestParam("filePath") String filePath,
            @RequestParam(value = "fileName", required = false) String fileName) {

        log.info("直接文件下载请求，文件路径: {}, 文件名: {}", filePath, fileName);

        try {
            // 直接从OSS获取文件流
            InputStream downloadStream = OssUtils.getDownloadStream(filePath);

            if (downloadStream == null) {
                throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS,
                    MessageUtils.getMessage("business.file.not.found"));
            }

            // 如果没有提供文件名，从路径中提取
            if (fileName == null || fileName.isEmpty()) {
                fileName = extractFileName(filePath);
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 编码文件名（支持中文）
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20");
            headers.setContentDispositionFormData("attachment", encodedFileName);

            return new ResponseEntity<>(new InputStreamResource(downloadStream), headers, HttpStatus.OK);

        } catch (BaseBizException e) {
            throw e;
        } catch (Exception e) {
            log.error("文件下载失败，文件路径: {}", filePath, e);
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                MessageUtils.getMessage("business.file.download.failed"));
        }
    }*/

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    @GetMapping("/exists")
    public DataResponse<Boolean> exists(@RequestParam("filePath") String filePath) {
        log.info("检查文件是否存在请求，文件路径: {}", filePath);
        boolean exists = fileService.exists(filePath);
        return DataResponse.ok(exists);
    }

    /**
     * 从文件路径中提取文件名
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "unknown";
        }

        // 处理路径分隔符
        String normalizedPath = filePath.replace("\\", "/");
        int lastSlashIndex = normalizedPath.lastIndexOf("/");

        if (lastSlashIndex >= 0 && lastSlashIndex < normalizedPath.length() - 1) {
            return normalizedPath.substring(lastSlashIndex + 1);
        }

        return normalizedPath;
    }
}
