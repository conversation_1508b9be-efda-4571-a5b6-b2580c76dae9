package com.zjkj.aigc.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.PageBaseReq;
import com.zjkj.aigc.common.vo.PageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
public class BaseController {

    @Autowired
    private ServletRequest request;

    private static final long MAX_PAGE_SIZE = 200;

    /**
     * 获取分页对象
     *
     * @param <T> T
     * @return Page<T>
     */
    protected <T> Page<T> getPage() {
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        long pageNum = Long.parseLong(paramMap.getOrDefault("pageNum", "1"));
        long pageSize = Long.parseLong(paramMap.getOrDefault("pageSize", "10"));
        if (pageSize > MAX_PAGE_SIZE) {
            pageSize = MAX_PAGE_SIZE;
        }

        return new Page<>(pageNum, pageSize);
    }

    /**
     * 获取分页对象
     *
     * @param <T> T
     * @return Page<T>
     */
    protected <T> Page<T> getPageInfo(PageBaseReq basePage) {
        long pageNum = Objects.nonNull(basePage.getPageNum()) ? Long.valueOf(basePage.getPageNum()) : 1;
        long pageSize = Objects.nonNull(basePage.getPageSize()) ? Long.valueOf(basePage.getPageSize()) : 10;
        if (pageSize > MAX_PAGE_SIZE) {
            pageSize = MAX_PAGE_SIZE;
        }

        return new Page<>(pageNum, pageSize);
    }

    /**
     * 获取分页对象
     *
     * @param <T> T
     * @return Page<T>
     */
    protected <T> Page<T> pageDefaultOrder(PageBaseReq basePage) {
        Page<T> pageInfo = getPageInfo(basePage);
        OrderItem orderItem = StringUtils.hasText(basePage.getSortField())
                ? new OrderItem(basePage.getSortField(), basePage.isSortAsc()) : OrderItem.desc("id");
        pageInfo.addOrder(orderItem);
        return pageInfo;
    }

    /**
     * 转换分页对象
     *
     * @param page page
     * @param <T>  T
     * @return PageVO<T>
     */
    protected <T> PageVO<T> convertPageVO(Page<T> page) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setPageNum(page.getCurrent());
        pageVO.setPageSize(page.getSize());
        pageVO.setTotal(page.getTotal());
        pageVO.setPages(page.getPages());
        pageVO.setRecords(CollectionUtils.isEmpty(page.getRecords()) ? Collections.emptyList() : page.getRecords());
        return pageVO;
    }
}
