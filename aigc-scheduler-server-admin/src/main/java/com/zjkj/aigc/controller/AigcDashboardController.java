package com.zjkj.aigc.controller;

import com.zjkj.aigc.common.vo.dashboard.AigcClusterResourceDashboardVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.service.AigcDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 首页看板
 *
 * <AUTHOR>
 * @since 2025/01/15
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/dashboard")
@RequiredArgsConstructor
public class AigcDashboardController extends BaseController {

    private final AigcDashboardService aigcDashboardService;

    /**
     * 获取集群资源使用情况
     *
     * @param envType 环境类型
     * @return 获取集群资源使用情况
     */
    @GetMapping("/cluster/resource/{envType}")
    public AigcClusterResourceDashboardVO getAigcClusterResource(@PathVariable String envType) {
        return aigcDashboardService.getAigcClusterResource(envType);
    }

}
