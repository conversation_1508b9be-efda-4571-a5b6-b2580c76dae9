package com.zjkj.aigc.controller.cluster;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterBrieflyVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.IAigcClusterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.List;

/**
 * 集群管理
 *
 * <AUTHOR>
 * @since 2024/12/05
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/cluster")
@RequiredArgsConstructor
public class AigcClusterController extends BaseController {

    private final IAigcClusterService aigcClusterService;

    /**
     * 集群列表-分页
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcClusterVO> page(@RequestBody AigcClusterQuery query) {
        Page<AigcClusterVO> reqPage = pageDefaultOrder(query);
        reqPage = aigcClusterService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 集群简要列表
     *
     * @return 列表数据
     */
    @PostMapping("/list/briefly")
    public List<AigcClusterBrieflyVO> brieflyList(@RequestBody AigcClusterQuery query) {
        return aigcClusterService.brieflyList(query);
    }

    /**
     * 集群详情
     *
     * @param id 主键
     * @return 详情
     */
    @GetMapping("/detail/{id}")
    public AigcClusterVO detail(@PathVariable Long id) {
        return aigcClusterService.detail(id);
    }

    /**
     * 新增集群
     *
     * @param createReq 新增请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcClusterCreateReq createReq) {
        aigcClusterService.create(createReq);
    }

    /**
     * 更新集群
     *
     * @param updateReq 更新请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                      @RequestBody AigcClusterCreateReq updateReq) {
        aigcClusterService.update(updateReq);
    }

    /**
     * 删除集群
     *
     * @param id 主键
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        aigcClusterService.delete(id);
    }

    /**
     * 启用集群
     *
     * @param id 主键
     */
    @PostMapping("/enable/{id}")
    public void enable(@PathVariable Long id) {
        aigcClusterService.enable(id);
    }

    /**
     * 禁用集群
     *
     * @param id 主键
     */
    @PostMapping("/disable/{id}")
    public void disable(@PathVariable Long id) {
        aigcClusterService.disable(id);
    }
}
