package com.zjkj.aigc.controller.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.enums.DataAffiliationTypeEnum;
import com.zjkj.aigc.common.req.task.AigcTaskBatchOperateReq;
import com.zjkj.aigc.common.req.task.AigcTaskCreateReq;
import com.zjkj.aigc.common.req.task.AigcTaskInitEsReq;
import com.zjkj.aigc.common.req.task.AigcTaskQuery;
import com.zjkj.aigc.common.vo.AigcTaskVO;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.AigcTaskESDao;
import com.zjkj.aigc.service.IAigcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 模型任务管理
 *
 * <AUTHOR>
 * @since 2024/11/5
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/task")
@RequiredArgsConstructor
public class AigcTaskController extends BaseController {

    private final IAigcTaskService iAigcTaskService;

    private final AigcTaskService service;

    private final AigcTaskDao dao;

    private final AigcTaskESDao esDao;

    /**
     * 模型连通测试列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/connectTest/page")
    public PageVO<AigcTaskVO> modelConnectTestPage(@RequestBody AigcTaskQuery query) {
        query.setAppId(DataAffiliationTypeEnum.MODEL_CONNECT_TEST.getType());
        return page(query);
    }

    /**
     * 模型任务列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcTaskVO> page(@RequestBody AigcTaskQuery query) {
        Page<AigcTaskVO> reqPage = getPageInfo(query);
        reqPage.setOrders(Lists.newArrayList(OrderItem.desc("id")));
        reqPage = iAigcTaskService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 创建模型任务
     *
     * @param req 请求参数
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody AigcTaskCreateReq req) {
        req.setAppId(DataAffiliationTypeEnum.MODEL_CONNECT_TEST.getType());
        req.setTaskPriority(-1);
        iAigcTaskService.create(req);
    }

    /**
     * 模型任务详情
     *
     * @param aigcTaskId 平台任务ID
     * @return 任务详情
     */
    @GetMapping("/detail/{aigcTaskId}")
    public AigcTaskVO detail(@PathVariable String aigcTaskId) {
        return iAigcTaskService.detail(aigcTaskId);
    }

    /**
     * 更新模型任务
     *
     * @param aigcTaskId 平台任务ID
     * @param req        请求参数
     */
    @PostMapping("/update/{aigcTaskId}")
    public void update(@PathVariable String aigcTaskId, @Validated @RequestBody AigcTaskCreateReq req) {
        req.setAigcTaskId(aigcTaskId);
        iAigcTaskService.update(req);
    }

    /**
     * 取消模型任务
     *
     * @param aigcTaskId 平台任务ID
     */
    @PostMapping("/cancel/{aigcTaskId}")
    public void cancel(@PathVariable String aigcTaskId) {
        iAigcTaskService.cancel(aigcTaskId);
    }

    /**
     * 重试模型任务
     *
     * @param req 请求参数
     */
    @PostMapping("/retry")
    public String batchRetry(@Validated @RequestBody AigcTaskBatchOperateReq req) {
        return iAigcTaskService.batchRetry(req);
    }

    /**
     * 取消模型任务
     *
     * @param req 请求参数
     */
    @PostMapping("/cancel")
    public String batchCancel(@Validated @RequestBody AigcTaskBatchOperateReq req) {
        return iAigcTaskService.batchCancel(req);
    }

    /**
     * 取消模型任务
     *
     * @param req 请求参数
     */
    @PostMapping("/initEs")
    public Object initEs(@Validated @RequestBody AigcTaskInitEsReq req) {
        if (Objects.nonNull(req.getQuery())) {
            AigcTaskQuery query = req.getQuery();
            Page<AigcTask> resultPage = Page.of(query.getPageNum(), query.getPageSize());
            resultPage.setOrders(Lists.newArrayList(OrderItem.desc("id")));

            AigcTaskCondition condition = AigcTaskCondition.builder()
                    .businessId(query.getAppId())
                    .taskStates(query.getTaskStates())
                    .taskType(query.getTaskType())
                    .modelName(query.getModelName())
                    .startTime(query.getStartTime())
                    .endTime(query.getEndTime())
                    .build();
            if (StringUtils.hasText(query.getTaskId())) {
                condition.setTaskIds(List.of(query.getTaskId()));
            }

            if (StringUtils.hasText(query.getAigcTaskId())) {
                condition.setAigcTaskIds(List.of(query.getAigcTaskId()));
            } else if (!CollectionUtils.isEmpty(query.getAigcTaskIds())) {
                condition.setAigcTaskIds(query.getAigcTaskIds());
            }
            condition.setPage(resultPage);

            if (Boolean.TRUE.equals(req.getInitQuery())) {
                resultPage = service.queryPage(condition);
                esDao.saveAll(resultPage.getRecords());
            } else {
                return esDao.queryPage(condition);
            }
        } else if (Boolean.TRUE.equals(req.getInitAll())) {
            Thread r = new Thread(() -> {
                long st = System.currentTimeMillis();
                int pageSize = 1000;
                int currentPageSize = pageSize;
                long lastId = 999999999;
                long total = 0L;
                while (currentPageSize >= pageSize) {
                    try {
                        LambdaQueryWrapper<AigcTask> q = Wrappers.lambdaQuery();
                        q.select(AigcTask.class, tfi -> !tfi.getColumn().equalsIgnoreCase("model_params")
                                && !tfi.getColumn().equalsIgnoreCase("model_output"));
                        q.lt(AigcTask::getId, lastId);
                        q.gt(Objects.nonNull(req.getMinId()), AigcTask::getId, req.getMinId());
                        q.orderByDesc(AigcTask::getId);
                        q.last("limit " + pageSize);
                        List<AigcTask> list = dao.list(q);
                        esDao.saveAll(list);
                        if (!CollectionUtils.isEmpty(list)) {
                            total += list.size();
                            lastId = list.get(list.size() - 1).getId();
                        }
                        log.info("init task es succ, lastId[{}], ids[{}]", lastId,
                                org.apache.commons.lang.StringUtils.join(list.stream().map(AigcTask::getId).collect(Collectors.toList()), ","));
                        currentPageSize = list.size();

                    } catch (Exception e) {
                        log.error("init task es fail", e);
                        break;
                    }
                }
                long et = System.currentTimeMillis();
                log.info("init task es over, total[{}], used[{}]", total, et - st);
            });
            r.start();
        }
        return "ok";
    }
}
