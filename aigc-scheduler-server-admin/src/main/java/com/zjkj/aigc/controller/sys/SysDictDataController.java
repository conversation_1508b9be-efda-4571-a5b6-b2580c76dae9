package com.zjkj.aigc.controller.sys;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.sys.SysDictDataVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.sys.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;
import java.util.List;

/**
 * 字典数据管理
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/sys/dict/data")
@RequiredArgsConstructor
public class SysDictDataController extends BaseController {

    private final ISysDictDataService sysDictDataService;

    /**
     * 字典数据列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<SysDictDataVO> page(@RequestBody SysDictDataQuery query) {
        Page<SysDictDataVO> reqPage = getPageInfo(query);
        reqPage.addOrder(OrderItem.asc("dict_sort"));
        reqPage = sysDictDataService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 根据字典类型查询
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    @GetMapping("/byDictType")
    public List<SysDictDataVO> getByDictType(@NotBlank String dictType) {
        return sysDictDataService.getByDictType(dictType);
    }

    /**
     * 字典数据详情
     *
     * @param id 字典数据id
     * @return 字典数据详情
     */
    @GetMapping("/detail/{id}")
    public SysDictDataVO detail(@PathVariable Long id) {
        return sysDictDataService.detail(id);
    }

    /**
     * 新增字典数据
     *
     * @param createReq 新增字典数据请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody SysDictDataCreateReq createReq) {
        sysDictDataService.create(createReq);
    }

    /**
     * 更新字典数据
     *
     * @param createReq 更新字典数据请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                      @RequestBody SysDictDataCreateReq createReq) {
        sysDictDataService.update(createReq);
    }

    /**
     * 删除字典数据
     *
     * @param id 字典数据id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        sysDictDataService.delete(id);
    }

    /**
     * 批量启用
     *
     * @param req 请求信息
     */
    @PostMapping("/enable")
    public void enable(@Validated @RequestBody BatchOperateReq req) {
        sysDictDataService.enable(req);
    }

    /**
     * 批量禁用
     *
     * @param req 请求信息
     */
    @PostMapping("/disable")
    public void disable(@Validated @RequestBody BatchOperateReq req) {
        sysDictDataService.disable(req);
    }
}
