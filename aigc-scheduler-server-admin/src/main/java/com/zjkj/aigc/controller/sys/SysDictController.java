package com.zjkj.aigc.controller.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictAbleQuery;
import com.zjkj.aigc.common.req.dict.SysDictCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictQuery;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.sys.SysDictVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.service.sys.ISysDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.List;

/**
 * 字典管理
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/sys/dict")
@RequiredArgsConstructor
public class SysDictController extends BaseController {

    private final ISysDictService sysDictService;

    /**
     * 字典列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<SysDictVO> page(@RequestBody SysDictQuery query) {
        Page<SysDictVO> reqPage = pageDefaultOrder(query);
        reqPage = sysDictService.page(reqPage, query);
        return convertPageVO(reqPage);
    }

    /**
     * 字典可用列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/list/able")
    public List<SysDictVO> ableList(@RequestBody SysDictAbleQuery query) {
        return sysDictService.ableList(query);
    }

    /**
     * 字典详情
     *
     * @param id 字典id
     * @return 字典详情
     */
    @GetMapping("/detail/{id}")
    public SysDictVO detail(@PathVariable Long id) {
        return sysDictService.detail(id);
    }

    /**
     * 新增字典
     *
     * @param createReq 新增字典请求
     */
    @PostMapping("/create")
    public void create(@Validated @RequestBody SysDictCreateReq createReq) {
        sysDictService.create(createReq);
    }

    /**
     * 更新字典
     *
     * @param createReq 更新字典请求
     */
    @PostMapping("/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class})
                      @RequestBody SysDictCreateReq createReq) {
        sysDictService.update(createReq);
    }

    /**
     * 删除字典
     *
     * @param id 字典id
     */
    @PostMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        sysDictService.delete(id);
    }

    /**
     * 批量启用
     *
     * @param req 请求信息
     */
    @PostMapping("/enable")
    public void enable(@Validated @RequestBody BatchOperateReq req) {
        sysDictService.enable(req);
    }

    /**
     * 批量禁用
     *
     * @param req 请求信息
     */
    @PostMapping("/disable")
    public void disable(@Validated @RequestBody BatchOperateReq req) {
        sysDictService.disable(req);
    }
}
