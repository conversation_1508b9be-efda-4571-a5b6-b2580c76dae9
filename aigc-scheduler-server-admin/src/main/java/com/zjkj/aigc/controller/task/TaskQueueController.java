package com.zjkj.aigc.controller.task;

import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.domain.task.service.AigcTaskQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 任务队列监控控制器
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/aigc/task-queue")
@RequiredArgsConstructor
public class TaskQueueController {

    private final AigcTaskQueueService aigcTaskQueueService;

    /**
     * 获取队列统计信息
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getQueueStats(@RequestParam String taskType, 
                                            @RequestParam String modelName) {
        return aigcTaskQueueService.getQueueStats(taskType, modelName);
    }

    /**
     * 清理模型缓存
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     */
    @PostMapping("/clear-cache")
    public void clearModelCache(@RequestParam String taskType, 
                               @RequestParam String modelName) {
        aigcTaskQueueService.clearModelCache(taskType, modelName);
    }

    /**
     * 刷新活跃业务线列表
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     */
    @PostMapping("/refresh")
    public void refreshActiveBusinessIds(@RequestParam String taskType, 
                                       @RequestParam String modelName) {
        aigcTaskQueueService.refreshActiveBusinessIds(taskType, modelName);
    }
}
