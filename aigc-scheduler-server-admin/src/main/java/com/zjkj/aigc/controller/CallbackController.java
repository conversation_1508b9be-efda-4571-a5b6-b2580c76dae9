package com.zjkj.aigc.controller;

import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptCallbackDTO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.service.IAigcModelDeployService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 回调控制器
 *
 * <AUTHOR>
 * @since 2024/11/15
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/callback")
@RequiredArgsConstructor
public class CallbackController {

    private final IAigcModelDeployService iAigcModelDeployService;

    /**
     * 模型加密回调
     *
     * @param callback 回调信息
     */
    @PostMapping("/model/encrypt")
    public void encryptCallback(@Validated @RequestBody ModelEncryptCallbackDTO callback) {
        iAigcModelDeployService.encryptCallback(callback);
    }
}
