package com.zjkj.aigc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum;
import com.zjkj.aigc.common.req.approval.GpuApplyCreateReq;
import com.zjkj.aigc.common.req.approval.GpuApplyQuery;
import com.zjkj.aigc.common.req.approval.GpuApplyStatusReq;
import com.zjkj.aigc.common.validate.ValidationGroup;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.common.vo.approval.GpuApplyVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.service.approval.IApprovalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.groups.Default;

/**
 * 审批中心
 *
 * <AUTHOR>
 * @since 2024/12/03
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/approval")
@RequiredArgsConstructor
public class ApprovalController extends BaseController {

    private final IApprovalService iApprovalService;

    /**
     * GPU申请列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/gpu/apply/page")
    public PageVO<GpuApplyVO> gpuApplyPage(@RequestBody GpuApplyQuery query) {
        Page<GpuApplyVO> reqPage = pageDefaultOrder(query);
        reqPage = iApprovalService.page(reqPage, query);
        return convertPageVO(reqPage);
    }


    /**
     * 创建GPU申请任务
     *
     * @param req 请求参数
     */
    @PostMapping("/gpu/apply/create")
    public void create(@Validated @RequestBody GpuApplyCreateReq req) {
        iApprovalService.create(req);
    }

    /**
     * 更新GPU申请任务
     *
     * @param req 请求参数
     */
    @PutMapping("/gpu/apply/update")
    public void update(@Validated({Default.class, ValidationGroup.Update.class}) @RequestBody GpuApplyCreateReq req) {
        iApprovalService.update(req);
    }

    /**
     * GPU申请详情
     *
     * @param id id
     * @return GPU申请详情
     */
    @GetMapping("/gpu/apply/detail/{id}")
    public GpuApplyVO detail(@PathVariable Long id) {
        return iApprovalService.detail(id);
    }

    /**
     * 通过GPU申请
     *
     * @param id GPU申请ID
     */
    @PutMapping("/gpu/apply/approved")
    public void passGpuApply(@Valid @RequestBody GpuApplyStatusReq statusReq) {
        statusReq.setStatus(ApprovalStatusEnum.APPROVED.getStatus());
        iApprovalService.updateStatus(statusReq);
    }

    /**
     * 拒绝GPU申请
     *
     * @param id GPU申请ID
     */
    @PutMapping("/gpu/apply/denied")
    public void deniedGpuApply(@Valid @RequestBody GpuApplyStatusReq statusReq) {
        statusReq.setStatus(ApprovalStatusEnum.DENIED.getStatus());
        iApprovalService.updateStatus(statusReq);
    }

}
