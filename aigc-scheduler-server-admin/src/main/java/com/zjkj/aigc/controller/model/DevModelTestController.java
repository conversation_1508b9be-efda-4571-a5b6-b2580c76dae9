package com.zjkj.aigc.controller.model;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.req.model.test.DevModelTestQuery;
import com.zjkj.aigc.common.req.model.test.EvaluateReq;
import com.zjkj.aigc.common.vo.AigcModelTestTaskVO;
import com.zjkj.aigc.common.vo.PageVO;
import com.zjkj.aigc.config.annotation.BaseResponse;
import com.zjkj.aigc.config.annotation.RequestLog;
import com.zjkj.aigc.controller.BaseController;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelTestTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelTestTask;
import com.zjkj.saas.admin.sdk.dto.SsoUserDTO;
import com.zjkj.saas.admin.sdk.util.SaasSsoContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 模型加速结果评价
 */
@Slf4j
@RequestLog
@BaseResponse
@RestController
@RequestMapping("/dev-model-test")
@RequiredArgsConstructor
public class DevModelTestController extends BaseController {

    private final AigcModelTestTaskDao modelTestTaskDao;

    /**
     * 模型测试列表
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/page")
    public PageVO<AigcModelTestTaskVO> page(@RequestBody DevModelTestQuery query) {
        Page<AigcModelTestTask> reqPage = getPageInfo(query);
        reqPage.setOrders(Lists.newArrayList(OrderItem.desc("id")));

        LambdaQueryWrapper<AigcModelTestTask> q = Wrappers.lambdaQuery();
        q.eq(StringUtils.isNotBlank(query.getTestCode()), AigcModelTestTask::getTestCode, query.getTestCode());
        q.eq(StringUtils.isNotBlank(query.getTaskType()), AigcModelTestTask::getTaskType, query.getTaskType());
        q.eq(StringUtils.isNotBlank(query.getModelName()), AigcModelTestTask::getModelName, query.getModelName());
        q.apply(StringUtils.isNotBlank(query.getStyleModel()), "model_params ->> '$.styleModel' = {0}", query.getStyleModel());
        q.eq(Objects.nonNull(query.getIsAcceptable()), AigcModelTestTask::getIsAcceptable, query.getIsAcceptable());
        q.eq(AigcModelTestTask::getIsDeleted, 0);

        reqPage = modelTestTaskDao.page(reqPage, q);

        Page<AigcModelTestTaskVO> voPage = new Page<>();
        BeanUtils.copyProperties(reqPage, voPage, "records");
        voPage.setRecords(reqPage.getRecords().stream().map(t -> {
            AigcModelTestTaskVO v = new AigcModelTestTaskVO();
            BeanUtils.copyProperties(t, v);
            v.setModelParams(null);
            v.setModelOutput(null);
            v.setStyleModel(JSONUtil.parseObj(t.getModelParams()).getStr("styleModel"));
            v.setRefImgUrl(JSONUtil.parseObj(t.getModelParams()).getStr("refImgUrl"));
            return v;
        }).collect(Collectors.toList()));
        return convertPageVO(voPage);
    }

    /**
     * 模型测试评价
     *
     * @param req 查询条件
     * @return 分页数据
     */
    @PostMapping("/evaluate")
    public void evaluate(@RequestBody EvaluateReq req) {
        AigcModelTestTask e = new AigcModelTestTask();
        BeanUtils.copyProperties(req, e);
        SsoUserDTO ssoUserDTO = SaasSsoContext.getCurrentContext().getSsoUser();
        e.setEvaluatorId(ssoUserDTO.getUserId());
        e.setEvaluator(ssoUserDTO.getUsername());
        modelTestTaskDao.updateById(e);
    }

    /**
     * init
     *
     * @return 分页数据
     */
    @PostMapping("/init")
    public void init(@RequestParam(required = false) String testCode) {
        Path directory = Paths.get("D:\\data\\fg\\fg20_v0.15后处理");

        try (Stream<Path> paths = Files.list(directory)) {
//            paths.forEach(p->log.info(p.toString()));
            paths.filter(f -> !Files.isDirectory(f))
                    .filter(f -> f.getFileName().toString().endsWith("csv"))
                    .forEach(file -> {
                        try (FileInputStream inputStream = new FileInputStream(file.toFile())) {
                            List<Map<String, String>> result = new ArrayList<>();
                            EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                                        private final List<String> headers = new ArrayList<>();

                                        @Override
                                        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                                            headMap.values().forEach(v -> headers.add(StrUtil.cleanBlank(v)));
                                        }

                                        @Override
                                        public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                                            Map<String, String> dataMap = new LinkedHashMap<>();
                                            for (int i = 0; i < headers.size(); i++) {
                                                String value = Optional.ofNullable(rowData.get(i))
                                                        .map(String::trim)
                                                        .orElse(rowData.get(i));
                                                dataMap.put(headers.get(i), value);
                                            }
                                            result.add(dataMap);
                                        }

                                        @Override
                                        public void doAfterAllAnalysed(AnalysisContext context) {
                                        }
                                    })
                                    .sheet()
                                    .doRead();
                            List<AigcModelTestTask> list = new ArrayList<>();
                            result.stream().forEach(t -> {
                                String modelParams = String.valueOf(t.get("input_params"));
                                String modelOutput = String.valueOf(t.get("output_params"));

                                for (String key : t.keySet()) {
                                    if (key.startsWith("new") || key.startsWith("old")) {
                                        AigcModelTestTask e = new AigcModelTestTask();
                                        e.setTaskId(UUID.fastUUID().toString());
                                        e.setTestCode(testCode);
                                        e.setModelParams(modelParams);
                                        e.setModelOutput(modelOutput);
                                        e.setModelName(key);
                                        Map<String, Object> newOutput = new HashMap<>();

                                        String val = t.get(key);
                                        if (JSONUtil.isTypeJSONArray(val)) {
                                            newOutput.put(key, JSONUtil.parseArray(val));
                                        } else if (JSONUtil.isTypeJSONObject(val)) {
                                            newOutput.put(key, JSONUtil.parseObj(val));
                                        } else {
                                            newOutput.put(key, t.get(key));
                                        }
                                        e.setModelNewOutput(JSONUtil.toJsonStr(newOutput));
                                        list.add(e);
                                    }
                                }
                            });
                            modelTestTaskDao.saveBatch(list);
                            log.info("save succ, size[{}]", list.size());
                        } catch (Exception e) {
                            log.error("", e);
                        }
                    });
        } catch (Exception e) {
            log.error("", e);
        }
    }


}
