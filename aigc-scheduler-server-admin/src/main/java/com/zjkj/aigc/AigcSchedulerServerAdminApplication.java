package com.zjkj.aigc;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.retry.annotation.EnableRetry;

/**
 * 启动类
 *
 * <AUTHOR>
 * @since 2024/11/05
 */
@EnableRetry
@EnableDiscoveryClient
@MapperScan({"com.zjkj.aigc.infrastructure.**.mapper"})
@SpringBootApplication(scanBasePackages = {"com.zjkj.aigc.**"})
public class AigcSchedulerServerAdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(AigcSchedulerServerAdminApplication.class);
    }
}
