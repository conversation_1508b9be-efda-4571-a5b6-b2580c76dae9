package com.zjkj.aigc.util;

import com.alibaba.fastjson2.JSON;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import javax.annotation.Resource;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/11/14 16:51
 */
@Component
public class WebClientUtil {

    @Resource
    private WebClient webClient;

    /**
     * get方法
     * @param url
     * @param responseType
     * @return
     * @param
     */
    public <T> T get(String url, Class<T> responseType) {
        return webClient.get().
                uri(url).
                accept(MediaType.APPLICATION_JSON).
                retrieve().
                bodyToMono(responseType).
                block();
    }


    /**
     * 同步post方法
     * @param url
     * @param requestBody
     * @param responseType
     * @return
     * @param
     */
    public <T> T post(String url, Object requestBody, Class<T> responseType) {
        return webClient.post().
                uri(url).
                contentType(MediaType.APPLICATION_JSON).
                bodyValue(requestBody).
                accept(MediaType.APPLICATION_JSON).
                retrieve().
                bodyToMono(responseType).
                block();
    }

    /**
     * 异步post方法
     * @param url
     * @param requestBody
     * @param responseType
     * @return
     * @param
     */
    public <T> void asyncPost(String url, Object requestBody, Class<T> responseType) {
         webClient.post().
                uri(url).
                contentType(MediaType.APPLICATION_JSON).
                bodyValue(JSON.toJSONString(requestBody)).
                accept(MediaType.APPLICATION_JSON).
                retrieve().
                bodyToMono(responseType).
                subscribe();
    }
}
