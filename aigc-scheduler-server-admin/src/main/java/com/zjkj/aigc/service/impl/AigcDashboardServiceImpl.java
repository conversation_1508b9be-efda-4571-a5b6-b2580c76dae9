package com.zjkj.aigc.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.zjkj.aigc.common.enums.ClusterStatusEnum;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.enums.model.AigcModelInfoEnum;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.vo.dashboard.AigcClusterResourceDashboardVO;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.service.AigcDashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AigcDashboardServiceImpl implements AigcDashboardService {

    private final AigcClusterService clusterService;

    private final AigcClusterNodeService clusterNodeService;

    private final AigcGpuService gpuService;

    private final AigcTaskStatDayService taskStatDayService;

    private final AigcModelInstanceService modelInstanceService;

    private final AigcModelConfigService modelConfigService;

    private final AigcModelInfoService modelInfoService;

    @Override
    public AigcClusterResourceDashboardVO getAigcClusterResource(String envType) {
        AigcClusterResourceDashboardVO dashboardVO = AigcClusterResourceDashboardVO.init();
        //获取对应环境集群信息
        AigcClusterCondition clusterCondition = new AigcClusterCondition();
        clusterCondition.setStatus(ClusterStatusEnum.ENABLE.getCode());
        clusterCondition.setEnvType(envType);
        List<AigcCluster> clusterList = clusterService.queryList(clusterCondition);
        if (CollUtil.isEmpty(clusterList)) {
            return dashboardVO;
        }
        // 获取集群在线状态的实例列表
        List<String> nodeIps = clusterList.stream()
                .filter(cluster -> CollUtil.isNotEmpty(cluster.getNodes()))
                .flatMap(cluster -> cluster.getNodes().stream())
                .map(AigcClusterNode::getNodeIp)
                .collect(Collectors.toList());
        AigcModelInstanceCondition condition = new AigcModelInstanceCondition()
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode())
                .setEnvType(envType)
                .setNodeIps(nodeIps);
        List<AigcModelInstance> instanceList = modelInstanceService.queryList(condition);
        Set<String> taskTypes = instanceList.stream()
                .map(AigcModelInstance::getTaskType)
                .collect(Collectors.toSet());
        Set<String> modelNames = instanceList.stream()
                .map(AigcModelInstance::getModelName)
                .collect(Collectors.toSet());
        //封装集群对应模型实例列表
        Map<AigcCluster, List<AigcModelInstance>> clusterInstanceMap = groupClusterInstanceMap(clusterList, instanceList);
        // 资源使用情况
        Map<String, AigcClusterResourceDashboardVO.ModelInfo> modelInfoMap = aigcClusterResourceUsage(dashboardVO, clusterInstanceMap,
                modelNames, taskTypes);
        // 任务数据填充, 只有当前环境类型才会填充任务数据
        String currentEnvType = EnvUtil.getCurrentEnvType();
        if (Objects.equals(currentEnvType, envType)) {
            aigcClusterResourceTask(dashboardVO, clusterInstanceMap, modelNames, taskTypes, modelInfoMap);
        }

        // 集群按GPU使用率排序
        if (CollUtil.isNotEmpty(dashboardVO.getClusterList())) {
            dashboardVO.getClusterList().sort(Comparator.comparingDouble((AigcClusterResourceDashboardVO.Cluster cluster) ->
                    cluster.getGpuUsage().getUsedPercent().doubleValue()).reversed());
        }
        return dashboardVO;
    }

    /**
     * 集群实例分组
     *
     * @param clusterList  集群集合
     * @param instanceList 实例集合
     * @return 集群实例分组
     */
    private Map<AigcCluster, List<AigcModelInstance>> groupClusterInstanceMap(List<AigcCluster> clusterList, List<AigcModelInstance> instanceList) {
        Map<String, List<AigcModelInstance>> nodeIpToInstanceMap = instanceList.stream()
                .collect(Collectors.groupingBy(AigcModelInstance::getNodeIp));
        return clusterList.stream()
                .filter(cluster -> CollUtil.isNotEmpty(cluster.getNodes()))
                .collect(Collectors.toMap(
                        cluster -> cluster,
                        cluster -> cluster.getNodes().stream()
                                .map(AigcClusterNode::getNodeIp)
                                .map(ip -> nodeIpToInstanceMap.getOrDefault(ip, List.of()))
                                .flatMap(List::stream)
                                .collect(Collectors.toList())
                ));
    }

    /**
     * 计算集群资源使用情况
     *
     * @param dashboardVO        资源信息信息
     * @param clusterInstanceMap 集群信息
     * @param modelNames         模型名称集合
     * @param taskTypes          任务类型集合
     * @return 模型配置信息
     */
    private Map<String, AigcClusterResourceDashboardVO.ModelInfo> aigcClusterResourceUsage(AigcClusterResourceDashboardVO dashboardVO,
                                                                                           Map<AigcCluster, List<AigcModelInstance>> clusterInstanceMap,
                                                                                           Set<String> modelNames,
                                                                                           Set<String> taskTypes) {
        Map<String, AigcClusterResourceDashboardVO.ModelInfo> map = new HashMap<>();
        Set<Long> gpuIds = new HashSet<>();
        clusterInstanceMap.keySet().forEach(cluster -> {
            if (CollUtil.isNotEmpty(cluster.getNodes())) {
                cluster.getNodes().forEach(node -> gpuIds.add(node.getGpuId()));
            }
        });
        // GPU 信息
        List<AigcGpu> gpuList = gpuService.queryByIds(new ArrayList<>(gpuIds));
        Map<Long, AigcGpu> gpuMap = gpuList.stream().collect(Collectors.toMap(AigcGpu::getId, Function.identity()));
        // 获取模型信息
        List<AigcModelInfo> modelInfoList = modelInfoService.queryList(AigcModelInfoCondition.builder()
                .modelNames(new ArrayList<>(modelNames))
                .taskTypes(new ArrayList<>(taskTypes))
                .status(AigcModelInfoEnum.ENABLE.getStatus())
                .build());
        Map<String, AigcModelInfo> modelInfoMap = modelInfoList.stream()
                .collect(Collectors.toMap(
                        model -> model.getModelName() + model.getTaskType(),
                        model -> model, (k1, k2) -> k2));
        // 获取模型配置信息(GPU、CPU、内存)
        List<AigcModelConfig> modelConfigList = modelConfigService.getByModelIds(modelInfoList.stream()
                .map(AigcModelInfo::getId)
                .collect(Collectors.toList()));
        Map<Long, AigcModelConfig> modelConfigMap = modelConfigList.stream().collect(Collectors.toMap(AigcModelConfig::getModelId,
                Function.identity(),
                (key1, key2) -> key1));
        List<AigcClusterResourceDashboardVO.Cluster> clusterList = new ArrayList<>();
        for (Map.Entry<AigcCluster, List<AigcModelInstance>> entry : clusterInstanceMap.entrySet()) {
            // 按模型名称分组模型实例
            Map<String, List<AigcModelInstance>> instanceMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(instance -> instance.getModelName() + instance.getTaskType()));
            int cpu = 0, memory = 0, gpu = 0, totalGpu = 0, totalCpu = 0, totalMemory = 0;
            //集群资源合计
            for (AigcClusterNode clusterNode : entry.getKey().getNodes()) {
                if (gpuMap.containsKey(clusterNode.getGpuId())) {
                    AigcGpu aigcGpu = gpuMap.get(clusterNode.getGpuId());
                    totalGpu += aigcGpu.getCardCount() * aigcGpu.getSingleCardMemory();
                }
                totalCpu += clusterNode.getCpuCore();
                totalMemory += clusterNode.getMemorySize();
            }
            //模型实例使用资源合计
            for (Map.Entry<String, List<AigcModelInstance>> instanceEntry : instanceMap.entrySet()) {
                AigcModelInfo modelInfo = modelInfoMap.get(instanceEntry.getKey());
                if (Objects.nonNull(modelInfo)) {
                    AigcModelConfig modelConfig = modelConfigMap.get(modelInfo.getId());
                    if (Objects.nonNull(modelConfig)) {
                        int podCount = instanceEntry.getValue().size();
                        cpu += modelConfig.getCpuCore() * podCount;
                        memory += modelConfig.getMemorySize() * podCount;
                        gpu += modelConfig.getGpuInfo().getMemorySize() * podCount;
                        AigcClusterResourceDashboardVO.ModelInfo modelInfoVO = new AigcClusterResourceDashboardVO.ModelInfo();
                        modelInfoVO.setModelId(modelInfo.getId());
                        modelInfoVO.setModelName(modelInfo.getModelName());
                        modelInfoVO.setTaskType(modelInfo.getTaskType());
                        modelInfoVO.setPodCount(instanceEntry.getValue().size());
                        modelInfoVO.setGpuUsage(modelConfig.getGpuInfo().getMemorySize());
                        modelInfoVO.setCpuUsage(modelConfig.getCpuCore());
                        modelInfoVO.setMemoryUsage(modelConfig.getMemorySize());
                        modelInfoVO.setTotalAmount(0L);
                        modelInfoVO.setWaitingAmount(0L);
                        map.put(modelInfo.getModelName() + modelInfoVO.getTaskType(), modelInfoVO);
                    }
                }
            }
            AigcClusterResourceDashboardVO.Cluster cluster = AigcClusterResourceDashboardVO.initCluster();
            cluster.setClusterId(entry.getKey().getId());
            cluster.setClusterName(entry.getKey().getName());
            cluster.setEnvType(entry.getKey().getEnvType());
            // gpu使用情况
            AigcClusterResourceDashboardVO.Usage gpuUsage = cluster.getGpuUsage();
            gpuUsage.setTotal(totalGpu);
            gpuUsage.setUsed(gpu);
            // cpu使用情况
            AigcClusterResourceDashboardVO.Usage cpuUsage = cluster.getCpuUsage();
            cpuUsage.setUsed(cpu);
            cpuUsage.setTotal(totalCpu);
            // 内存使用情况
            AigcClusterResourceDashboardVO.Usage memoryUsage = cluster.getMemoryUsage();
            memoryUsage.setUsed(memory);
            memoryUsage.setTotal(totalMemory);
            clusterList.add(cluster);
        }
        dashboardVO.setClusterList(clusterList);
        //合计资源使用率
        dashboardVO.calculate();
        return map;
    }

    /**
     * 计算集群资源任务情况
     *
     * @param dashboardVO  集群资源信息
     * @param clusterInstanceMap 集群实例信息
     * @param modelNames   模型名称集合
     * @param taskTypes    任务类型集合
     * @param modelInfoMap 模型配置信息
     */
    private void aigcClusterResourceTask(AigcClusterResourceDashboardVO dashboardVO,
                                         Map<AigcCluster, List<AigcModelInstance>> clusterInstanceMap,
                                         Set<String> modelNames,
                                         Set<String> taskTypes,
                                         Map<String, AigcClusterResourceDashboardVO.ModelInfo> modelInfoMap) {
        Map<Long, AigcClusterResourceDashboardVO.Cluster> clusterMap = dashboardVO.getClusterList().stream()
                .collect(Collectors.toMap(AigcClusterResourceDashboardVO.Cluster::getClusterId, Function.identity()));
        //获取最近三天数据
        List<AigcTaskStatDay> statDayList = taskStatDayService.listByModelStateDate(
                AigcTaskSummaryCondition.builder()
                        .taskTypes(taskTypes)
                        .modelNames(modelNames)
                        .startDate(LocalDate.now().minusDays(2).toString())
                        .endDate(LocalDate.now().toString())
                        .build());
        Map<String, AigcTaskStatDay> statDayMap = statDayList.stream()
                .collect(Collectors.toMap(
                        stat -> stat.getModelName() + stat.getTaskType(),
                        stat -> stat, (k1, k2) -> k2)
                );
        for (Map.Entry<AigcCluster, List<AigcModelInstance>> entry : clusterInstanceMap.entrySet()) {
            Map<String, List<AigcModelInstance>> instanceMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(instance -> instance.getModelName() + instance.getTaskType()));
            List<AigcClusterResourceDashboardVO.ModelInfo> activeModel = new ArrayList<>();
            List<AigcClusterResourceDashboardVO.ModelInfo> freeModel = new ArrayList<>();
            instanceMap.forEach((k, v) -> {
                AigcClusterResourceDashboardVO.ModelInfo modelInfo = modelInfoMap.get(k);
                if (Objects.isNull(modelInfo)) {
                    return;
                }

                //活跃模型
                AigcTaskStatDay statDay = statDayMap.get(k);
                if (Objects.isNull(statDay)) {
                    freeModel.add(modelInfo);
                    return;
                }

                if (statDay.getWaitingAmount() > 0) {
                    modelInfo.setWaitingAmount(statDay.getWaitingAmount());
                    modelInfo.setTotalAmount(statDay.getTotalAmount());
                    Long avgElapsedTime = statDay.getAvgElapsedTime();
                    if (Objects.nonNull(avgElapsedTime) && avgElapsedTime > 0) {
                        BigDecimal speed = BigDecimal.valueOf(modelInfo.getPodCount() * 60 * 60000L)
                                .divide(BigDecimal.valueOf(avgElapsedTime), 2, RoundingMode.HALF_UP);
                        modelInfo.setTaskSpeed(speed);

                        double difference = statDay.getWaitingAmount() * 60 / speed.doubleValue();
                        modelInfo.setExpectFinishedTime(LocalDateTime.now().plusMinutes(Math.round(difference)));
                    }
                    activeModel.add(modelInfo);
                } else {
                    modelInfo.setTotalAmount(statDay.getTotalAmount());
                    freeModel.add(modelInfo);
                }
            });
            activeModel.sort(Comparator.comparingLong(AigcClusterResourceDashboardVO.ModelInfo::getWaitingAmount).reversed());
            freeModel.sort(Comparator.comparingLong(AigcClusterResourceDashboardVO.ModelInfo::getTotalAmount));
            AigcClusterResourceDashboardVO.Cluster cluster = clusterMap.get(entry.getKey().getId());
            cluster.setActiveModels(activeModel);
            cluster.setFreeModels(freeModel);
        }
    }

}
