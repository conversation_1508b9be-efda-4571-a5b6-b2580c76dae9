package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordQuery;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordReq;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceAdjustRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/01/17
 */
public interface IAigcModelInstanceService {

    /**
     * 模型实例调整记录分页查询
     * @param reqPage 分页数据
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcModelInstanceAdjustRecordVO> adjustRecordPage(Page<AigcModelInstanceAdjustRecordVO> reqPage, AigcModelInstanceAdjustRecordQuery query);

    /**
     * 模型实例调整记录详情
     * @param id 模型基础信息id
     * @return 模型基础信息详情
     */
    AigcModelInstanceAdjustRecordVO adjustRecordDetail(Long id);

    /**
     * 模型实例调整记录创建
     * @param req 创建信息
     */
    void createAdjustRecord(AigcModelInstanceAdjustRecordReq req);

    /**
     * 模型实例调整记录更新
     * @param req 更新信息
     */
    void updateAdjustRecord(AigcModelInstanceAdjustRecordReq req);

    /**
     * 模型实例调整记录删除
     * @param id 模型基础信息id
     */
    void deleteAdjustRecord(Long id);


}
