package com.zjkj.aigc.service.resource;

import com.zjkj.aigc.common.req.resource.ResourceExpendCreateReq;
import com.zjkj.aigc.common.req.resource.ResourceExpendQuery;
import com.zjkj.aigc.common.vo.resource.ResourceExpendSummaryVO;

import java.util.List;

public interface IResourceExpendService {

    /**
     * 查询支出
     *
     * @param query 查询条件
     * @return 资源支出数据
     */
    ResourceExpendSummaryVO query(ResourceExpendQuery query);

    /**
     * 批量创建支出
     *
     * @param req 创建参数
     */
    void createResourceExpend(ResourceExpendCreateReq req);

    /**
     * 批量更新支出
     *
     * @param createReq 更新参数
     */
    void updateResourceExpend(ResourceExpendCreateReq req);
}
