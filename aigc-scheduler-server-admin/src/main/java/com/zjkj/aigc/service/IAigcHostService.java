package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.host.AigcHostCreateReq;
import com.zjkj.aigc.common.req.host.AigcHostQuery;
import com.zjkj.aigc.common.vo.host.AigcHostVO;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcHostService {

    /**
     * 分页查询
     *
     * @param reqPage 分页参数
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcHostVO> page(Page<AigcHostVO> reqPage, AigcHostQuery query);

    /**
     * 主机详情
     *
     * @param id 主机id
     * @return 主机详情
     */
    AigcHostVO detail(Long id);

    /**
     * 创建主机
     *
     * @param req 创建主机请求
     */
    void create(AigcHostCreateReq req);

    /**
     * 更新主机
     *
     * @param req 更新主机请求
     */
    void update(AigcHostCreateReq req);

    /**
     * 删除主机
     *
     * @param id 主机id
     */
    void delete(Long id);

    /**
     * 启用主机
     *
     * @param req 操作信息
     */
    void enable(BatchOperateReq req);

    /**
     * 禁用主机
     *
     * @param req 操作信息
     */
    void disable(BatchOperateReq req);
}
