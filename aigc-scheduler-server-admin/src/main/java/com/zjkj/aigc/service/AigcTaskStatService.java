package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.vo.stat.AigcTaskStatDayVO;
import com.zjkj.aigc.common.vo.stat.AigcTaskTotalStatVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
public interface AigcTaskStatService {

    /**
     * 分页查询
     * @param reqPage 分页对象
     * @param query 查询条件
     * @return 分页数据
     */
    Page<AigcTaskStatDayVO> page(Page<AigcTaskStatDayVO> reqPage, TaskStatSummaryReq query);

    /**
     * 任务数趋势
     * @param query 查询条件
     * @return 趋势数据
     */
    List<AigcTaskStatDayVO> trend(TaskStatSummaryReq query);

    /**
     * 任务数环比
     * @param query 查询条件
     * @return 环比数据
     */
    List<AigcTaskStatDayVO> compareChain(TaskStatSummaryReq query);

    /**
     * 合计环比
     * @param query 查询条件
     * @return 同比数据
     */
    AigcTaskTotalStatVO totalCompareChain(TaskStatSummaryReq query);
}
