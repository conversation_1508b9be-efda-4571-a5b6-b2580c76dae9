package com.zjkj.aigc.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictAbleQuery;
import com.zjkj.aigc.common.req.dict.SysDictCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictQuery;
import com.zjkj.aigc.common.vo.sys.SysDictVO;

import java.util.List;

/**
 * 字典服务接口
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
public interface ISysDictService {

    /**
     * 分页查询
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页数据
     */
    Page<SysDictVO> page(Page<SysDictVO> page, SysDictQuery query);

    /**
     * 字典详情
     *
     * @param id 字典id
     * @return 字典详情
     */
    SysDictVO detail(Long id);

    /**
     * 新增字典
     *
     * @param req 新增字典请求
     */
    void create(SysDictCreateReq req);

    /**
     * 更新字典
     *
     * @param req 更新字典请求
     */
    void update(SysDictCreateReq req);

    /**
     * 删除字典
     *
     * @param id 字典id
     */
    void delete(Long id);

    /**
     * 启用字典
     *
     * @param req 操作信息
     */
    void enable(BatchOperateReq req);

    /**
     * 禁用字典
     *
     * @param req 操作信息
     */
    void disable(BatchOperateReq req);

    /**
     * 查询启用的字典
     *
     * @param query 查询条件
     * @return 启用的字典
     */
    List<SysDictVO> ableList(SysDictAbleQuery query);
}
