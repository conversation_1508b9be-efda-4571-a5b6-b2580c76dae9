package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.gpu.AigcGpuCreateReq;
import com.zjkj.aigc.common.req.gpu.AigcGpuQuery;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcGpuCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.service.IAigcGpuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * GPU服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class IAigcGpuServiceImpl extends AbstractBaseService implements IAigcGpuService {

    private final AigcGpuService aigcGpuService;

    @Override
    public Page<AigcGpuVO> page(Page<AigcGpuVO> reqPage, AigcGpuQuery query) {
        AigcGpuCondition condition = new AigcGpuCondition()
                .setPlatform(query.getPlatform())
                .setModel(query.getModel())
                .setCardCount(query.getCardCount())
                .setSingleCardMemory(query.getSingleCardMemory())
                .setCreatorName(query.getCreatorName());

        return genericPageQuery(
                reqPage,
                condition,
                aigcGpuService::queryPage,
                AigcGpuVO.class
        );
    }

    @Override
    public AigcGpuVO detail(Long id) {
        AigcGpu aigcGpu = aigcGpuService.queryById(id);
        return BeanUtil.copyProperties(aigcGpu, AigcGpuVO.class);
    }

    @Override
    public void create(AigcGpuCreateReq req) {
        aigcGpuService.createAigcGpu(req);
    }

    @Override
    public void update(AigcGpuCreateReq req) {
        aigcGpuService.updateAigcGpu(req);
    }

    @Override
    public void delete(Long id) {
        aigcGpuService.deleteAigcGpu(id);
    }

    @Override
    public List<AigcGpuVO> list(AigcGpuQuery query) {
        AigcGpuCondition condition = new AigcGpuCondition()
                .setPlatform(query.getPlatform())
                .setModel(query.getModel())
                .setCardCount(query.getCardCount())
                .setSingleCardMemory(query.getSingleCardMemory());

        List<AigcGpu> gpuList = aigcGpuService.queryList(condition);
        return BeanUtil.copyToList(gpuList, AigcGpuVO.class);
    }
}
