package com.zjkj.aigc.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataQuery;
import com.zjkj.aigc.common.vo.sys.SysDictDataVO;

import java.util.List;

/**
 * 字典数据服务接口
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
public interface ISysDictDataService {

    /**
     * 字典数据列表
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<SysDictDataVO> page(Page<SysDictDataVO> reqPage, SysDictDataQuery query);

    /**
     * 字典数据详情
     *
     * @param id 字典数据id
     * @return 字典数据详情
     */
    SysDictDataVO detail(Long id);

    /**
     * 新增字典数据
     *
     * @param req 新增字典数据请求
     */
    void create(SysDictDataCreateReq req);

    /**
     * 更新字典数据
     *
     * @param req 更新字典数据请求
     */
    void update(SysDictDataCreateReq req);

    /**
     * 删除字典数据
     *
     * @param id 字典数据id
     */
    void delete(Long id);

    /**
     * 启用字典数据
     *
     * @param req 操作信息
     */
    void enable(BatchOperateReq req);

    /**
     * 禁用字典数据
     *
     * @param req 操作信息
     */
    void disable(BatchOperateReq req);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SysDictDataVO> getByDictType(String dictType);
}
