package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoQuery;
import com.zjkj.aigc.common.vo.model.AigcModelInfoBrieflyVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
public interface IAigcModelInfoService {

    /**
     * 分页查询
     * @param reqPage 分页数据
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcModelInfoVO> page(Page<AigcModelInfoVO> reqPage, AigcModelInfoQuery query);

    /**
     * 简要列表
     * @return 简要列表
     */
    List<AigcModelInfoBrieflyVO> brieflyList();
    /**
     * 详情
     * @param id 模型基础信息id
     * @return 模型基础信息详情
     */
    AigcModelInfoVO detail(Long id);

    /**
     * 创建
     * @param req 创建信息
     */
    void create(AigcModelInfoCreateReq req);

    /**
     * 更新
     * @param req 更新信息
     */
    void update(AigcModelInfoCreateReq req);

    /**
     * 删除
     * @param id 模型基础信息id
     */
    void delete(Long id);

    /**
     * 批量启用
     * @param req 批量操作请求
     */
    void enable(BatchOperateReq req);

    /**
     * 批量禁用
     * @param req 批量操作请求
     */
    void disable(BatchOperateReq req);

    /**
     * 查询同集群的模型
     * @param id 模型基础信息id
     * @return 同集群的模型列表
     */
    List<AigcModelInfoVO> sameClusterModel(Long id);
}
