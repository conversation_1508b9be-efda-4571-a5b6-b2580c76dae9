package com.zjkj.aigc.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.CharsetDetector;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.dto.resource.BillImportDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.PlatformEnum;
import com.zjkj.aigc.common.enums.resource.ResourceBillTypeEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigCreateReq;
import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigQuery;
import com.zjkj.aigc.common.req.resource.ResourceBillQuery;
import com.zjkj.aigc.common.req.resource.ResourceBillTypeQuery;
import com.zjkj.aigc.common.util.DateUtils;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.common.vo.resource.ResourceBillGroupConfigVO;
import com.zjkj.aigc.common.vo.resource.ResourceBillPlatform;
import com.zjkj.aigc.common.vo.resource.ResourceBillTypeGroupVO;
import com.zjkj.aigc.common.vo.resource.ResourceBillVO;
import com.zjkj.aigc.domain.task.service.AigcClusterUsageService;
import com.zjkj.aigc.domain.task.service.model.AigcModelUsageService;
import com.zjkj.aigc.domain.task.service.resource.ResourceBillGroupConfigService;
import com.zjkj.aigc.domain.task.service.resource.ResourceBillService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillGroupConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBillGroupConfig;
import com.zjkj.aigc.service.impl.AbstractBaseService;
import com.zjkj.aigc.service.resource.IResourceBillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RefreshScope
public class IResourceBillServiceImpl extends AbstractBaseService implements IResourceBillService {

    /**
     * 文件扩展名
     */
    private static final List<String> ALLOWED_EXTENSIONS = List.of("csv", "xls", "xlsx");
    private static final Map<ResourceBillTypeEnum, Map<String, Map<String, String>>> HEADER_MAPPING = new HashMap<>();

    static {
        // jst、aidc 一致
        HEADER_MAPPING.put(ResourceBillTypeEnum.GPU, Map.of(
                "jst", Map.of("instanceId", "实例ID", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "aidc", Map.of("instanceId", "实例ID", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "klmy", Map.of("instanceId", "实例ID", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "baidu", Map.of("instanceId", "实例ID", "billingType", "付费方式", "paymentAmount", "现金已付", "periodMonth", "账单时间")
        ));

        // jst、aidc 一致
        HEADER_MAPPING.put(ResourceBillTypeEnum.MID, Map.of(
                "jst", Map.of("instanceId", "实例ID", "productName", "产品", "productCode", "产品Code", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "aidc", Map.of("instanceId", "实例ID", "productName", "产品", "productCode", "产品Code", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "klmy", Map.of("instanceId", "实例ID", "productName", "产品", "productCode", "产品Code", "billingType", "消费类型", "paymentTime", "消费时间", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "app", Map.of("productName", "产品", "productCode", "产品Code", "billingType", "消费类型", "paymentAmount", "现金支付", "periodMonth", "账期"),
                "baidu", Map.of("instanceId", "实例ID", "productName", "产品名称", "billingType", "付费方式", "paymentAmount", "现金已付", "periodMonth", "账单时间"),
                "302ai", Map.of("productName", "产品", "productCode", "产品Code", "billingType", "消费类型", "paymentAmount", "现金支付", "periodMonth", "账期")
        ));
    }

    private final ResourceBillService resourcebillservice;
    private final AigcModelUsageService aigcModelUsageService;
    private final AigcClusterUsageService aigcClusterUsageService;
    private final ResourceBillGroupConfigService resourceBillGroupConfigService;

    @Value("${dingtalk.approve-code.expend-bill}")
    private String dingTalkBillApproveCode;

    @Override
    public List<ResourceBillPlatform> billPlatform(String type) {
        ResourceBillTypeEnum typeEnum = ResourceBillTypeEnum.getByType(type);
        Map<String, Map<String, String>> headerMap = HEADER_MAPPING.get(typeEnum);
        return headerMap.entrySet()
                .stream()
                .map(entry -> new ResourceBillPlatform()
                        .setPlatform(entry.getKey())
                        .setExtensions(ALLOWED_EXTENSIONS)
                        .setHeaders(new ArrayList<>(entry.getValue().values())
                        ))
                .collect(Collectors.toList());
    }

    @Override
    public Integer importBill(String type, String platform, MultipartFile file) throws IOException {
        ResourceBillTypeEnum typeEnum = ResourceBillTypeEnum.getByType(type);
        Map<String, String> headerMap = HEADER_MAPPING.get(typeEnum).get(platform);

        // 获取账单导入数据
        List<BillImportDTO> billImportList = getBillImport(platform, headerMap, file);
        Integer importedBill = resourcebillservice.importBill(typeEnum, platform, billImportList);
        log.info("importBill() 导入gpu账单数, platform:{}, fileName:{}, fileCount:{}, importCount:{}", platform, file.getOriginalFilename(), billImportList.size(), importedBill);
        return importedBill;
    }

    /**
     * 获取账单导入数据
     *
     * @param platform  平台
     * @param headerMap 表头映射
     * @param file      文件
     * @return 数据
     * @throws IOException 异常
     */
    private List<BillImportDTO> getBillImport(String platform, Map<String, String> headerMap, MultipartFile file) throws IOException {
        BaseBizException.isTrue(Objects.nonNull(headerMap), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.platform.unsupported", new Object[]{platform}));
        boolean isBaiDu = Objects.equals(platform, PlatformEnum.BAIDU.getName());

        List<BillImportDTO> billImportList = Lists.newArrayList();
        List<Map<String, Object>> dataList = getFileData(file);
        AtomicInteger rowIdx = new AtomicInteger(1);
        dataList.forEach(v -> {
            rowIdx.incrementAndGet();
            BillImportDTO billImport = new BillImportDTO();
            headerMap.forEach((field, title) -> {
                Object value = v.get(title);
                BaseBizException.isTrue(Objects.nonNull(value), CustomErrorCode.PARAM_ERROR,
                        MessageUtils.getMessage("business.file.content.column.empty", new Object[]{rowIdx.get(), title}));
                if (StringUtils.equals(LambdaUtil.getFieldName(BillImportDTO::getPeriodMonth), field)) {
                    BaseBizException.isTrue(DateUtils.isNormMonth(value.toString()), CustomErrorCode.PARAM_ERROR,
                            MessageUtils.getMessage("business.file.date.format.error", new Object[]{rowIdx.get(), title}));
                }
                billImport.setFieldValue(field, value);
            });
            if (isBaiDu) {
                billImport.setBaidu();
            }
            if (Objects.isNull(billImport.getPaymentTime())) {
                billImport.setPaymentTime(LocalDateTime.parse(billImport.getPeriodMonth() + "-01T00:00:00"));
            }
            billImportList.add(billImport);
        });
        return billImportList;
    }

    @Override
    public Page<ResourceBillVO> page(Page<ResourceBillVO> reqPage, ResourceBillQuery query) {
        ResourceBillCondition condition = BeanUtil.copyProperties(query, ResourceBillCondition.class);
        return genericPageQuery(
                reqPage,
                condition,
                resourcebillservice::queryPage,
                ResourceBillVO.class
        );
    }

    @Override
    public BigDecimal billTotal(ResourceBillQuery query) {
        ResourceBillCondition condition = BeanUtil.copyProperties(query, ResourceBillCondition.class);
        return resourcebillservice.billTotal(condition);
    }

    /**
     * 获取文件数据
     *
     * @param file 文件
     * @return 数据
     * @throws IOException 异常
     */
    private List<Map<String, Object>> getFileData(MultipartFile file) throws IOException {
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.file.upload.empty"));
        }

        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.file.name.empty"));
        }

        FastByteArrayOutputStream fastBaos = new FastByteArrayOutputStream();
        StreamUtils.copy(file.getInputStream(), fastBaos);

        // 先获取文件类型
        String extName = FileUtil.extName(file.getOriginalFilename()).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extName)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.file.format.unsupported", new Object[]{ALLOWED_EXTENSIONS.toString()}));
        }

        Charset detect = null;
        if (Objects.equals(extName, "csv")) {
            detect = CharsetDetector.detect(new ByteArrayInputStream(fastBaos.toByteArray()));
            log.info("importBill() 上传的文件: {}, 编码: {}", file.getOriginalFilename(), detect);
            if (Objects.nonNull(detect) && detect.name().contains("UTF-")) {
                detect = Charset.defaultCharset();
            }
        }

        List<Map<String, Object>> readAll = readFile(fastBaos.getInputStream(), detect);
        if (CollectionUtils.isEmpty(readAll)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.upload.file.content.empty"));
        }

        return readAll;
    }

    /**
     * 读取文件
     *
     * @param inputStream 输入流
     * @param detect      编码
     * @return 数据
     */
    private List<Map<String, Object>> readFile(InputStream inputStream, Charset detect) {
        List<Map<String, Object>> result = new ArrayList<>();
        EasyExcel.read(inputStream, new AnalysisEventListener<Map<Integer, String>>() {
                    private final List<String> headers = new ArrayList<>();

                    @Override
                    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                        headMap.values().forEach(v -> headers.add(StrUtil.cleanBlank(v)));
                    }

                    @Override
                    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                        Map<String, Object> dataMap = new LinkedHashMap<>();
                        for (int i = 0; i < headers.size(); i++) {
                            String value = Optional.ofNullable(rowData.get(i))
                                    .map(String::trim)
                                    .orElse(rowData.get(i));
                            dataMap.put(headers.get(i), value);
                        }
                        result.add(dataMap);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                })
                .charset(detect)
                .sheet()
                .doRead();
        return result;
    }

    @Override
    public ResourceBillTypeGroupVO billTypeGroup(ResourceBillTypeQuery query) {
        // 获取对应月份和对应月份最后一天
        Integer month = query.getMonth();
        if (0 == query.getType()) {
            month = DateUtils.getPreviousYearMonth(query.getMonth());
        }
        LocalDate monthEndDay = DateUtils.getLastDay(month);
        // 查询对应月的费用账单
        ResourceBillCondition billCondition = new ResourceBillCondition();
        billCondition.setMonth(month);
        List<ResourceBill> billList = resourcebillservice.queryList(billCondition);
        // 如果账单数据为空，直接返回 null
        if (CollectionUtils.isEmpty(billList)) {
            return null;
        }
        // 查询对应月份模型 GPU 使用信息
        AigcModelUsageCondition modelUsageCondition = new AigcModelUsageCondition().setDataDate(monthEndDay);
        List<AigcModelUsage> modelUsageList = aigcModelUsageService.queryList(modelUsageCondition);
        // 查询对应月份最后一天集群信息
        AigcClusterUsageCondition clusterCondition = new AigcClusterUsageCondition();
        clusterCondition.setDataDate(monthEndDay);
        clusterCondition.setPlatforms(StreamUtil.mapToList(billList, ResourceBill::getPlatform));
        List<AigcClusterUsage> clusterUsageList = aigcClusterUsageService.queryList(clusterCondition);
        // 按平台分组账单
        Map<String, List<ResourceBill>> billPlatformGroup = StreamUtil.groupBy(billList, ResourceBill::getPlatform);
        // 模型使用数据按集群 ID 分组
        Map<Long, List<AigcModelUsage>> modelUsageGroup = StreamUtil.groupBy(modelUsageList, AigcModelUsage::getClusterId);
        // 集群信息按 平台 分组
        Map<String, List<AigcClusterUsage>> clusterUsageGroup = StreamUtil.groupBy(clusterUsageList, AigcClusterUsage::getPlatform);
        //校验信息
        vaildBillTypeGroupInfo(clusterUsageGroup, modelUsageGroup);
        //总费用
        BigDecimal totalMidPrice = BigDecimal.ZERO;
        BigDecimal totalGpuPrice = BigDecimal.ZERO;
        List<ResourceBillTypeGroupVO.PlatformGroup> platformGroupList = new ArrayList<>();
        // 遍历每个平台的账单进行费用汇总及模型数据关联
        for (Map.Entry<String, List<ResourceBill>> entry : billPlatformGroup.entrySet()) {
            String platform = entry.getKey();
            List<ResourceBill> platformBills = entry.getValue();
            // 按费用类型分组
            Map<String, List<ResourceBill>> billTypeMap = StreamUtil.groupBy(platformBills, ResourceBill::getType);
            // GPU费用总和
            BigDecimal gpuTotal = billTypeMap.getOrDefault(ResourceBillTypeEnum.GPU.getType(), Collections.emptyList())
                    .stream()
                    .map(ResourceBill::getPaymentAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 中间件费用总和
            BigDecimal midTotal = billTypeMap.getOrDefault(ResourceBillTypeEnum.MID.getType(), Collections.emptyList())
                    .stream()
                    .map(ResourceBill::getPaymentAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            ResourceBillTypeGroupVO.PlatformGroup platformGroup = new ResourceBillTypeGroupVO.PlatformGroup();
            platformGroup.setPlatform(platform);
            BigDecimal midPriceRedundancy = BigDecimal.ONE;
            BigDecimal gpuPriceRedundancy = BigDecimal.ONE;
            platformGroup.setGpuPrice(query.gpuPriceRedundancy(platform, gpuTotal));
            platformGroup.setMidPrice(query.midPriceRedundancy(platform, midTotal));
            // 填充模型信息、进行费用分摊的计算
            fillBillTypeGroupModelList(platformGroup, clusterUsageGroup.get(platform), modelUsageGroup);
            platformGroupList.add(platformGroup);

            totalGpuPrice = totalGpuPrice.add(platformGroup.getGpuPrice());
            totalMidPrice = totalMidPrice.add(platformGroup.getMidPrice());
        }
        ResourceBillTypeGroupVO groupVO = new ResourceBillTypeGroupVO();
        groupVO.setMonth(query.getMonth());
        groupVO.setPlatformGroupList(platformGroupList);
        groupVO.setMidPrice(totalMidPrice);
        groupVO.setGpuPrice(totalGpuPrice);
        groupVO.setTotalPrice(totalMidPrice.add(totalGpuPrice));
        groupVO.fillModelGroupList();
        return groupVO;
    }

    private void vaildBillTypeGroupInfo(Map<String, List<AigcClusterUsage>> clusterUsageGroup,
                                        Map<Long, List<AigcModelUsage>> modelUsageGroup) {
        //校验模型信息注册信息与集群信息一致
        clusterUsageGroup.forEach((k, v) -> {
            for (AigcClusterUsage clusterUsage : v) {
                List<AigcModelUsage> usages = modelUsageGroup.get(clusterUsage.getClusterId());
                if (!CollectionUtils.isEmpty(usages)) {
                    for (AigcModelUsage usage : usages) {
                        if (!clusterUsage.getServiceType().contains(usage.getModelType())) {
                            throw new BaseBizException(CustomErrorCode.DATA_ERROR, MessageUtils.getMessage("business.service.type.not.match",new Object[]{clusterUsage.getClusterName(), clusterUsage.getServiceType(),
                                    usage.getModelName(), usage.getModelType()}));
                        }
                    }
                }
            }
        });

    }

    private void fillBillTypeGroupModelList(ResourceBillTypeGroupVO.PlatformGroup platformGroup,
                                            List<AigcClusterUsage> clusterUsageList,
                                            Map<Long, List<AigcModelUsage>> modelUsageGroup) {
        List<ResourceBillTypeGroupVO.Model> modelList = new ArrayList<>();
        // 剩余资源
        BigDecimal remainingProportion = null;
        BigDecimal remainingGpuPrice = null;
        BigDecimal remainingMidPrice = BigDecimal.ZERO.add(platformGroup.getMidPrice());
        // GPU信息
        Map<String, ResourceBillTypeGroupVO.Gpu> gpuTotalMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(clusterUsageList)) {
            remainingProportion = BigDecimal.ONE;
            remainingGpuPrice = BigDecimal.ZERO.add(platformGroup.getGpuPrice());
            long totalGpuCount = 0L;
            Map<String, ResourceBillTypeGroupVO.Model> modelMap = new HashMap<>();
            for (AigcClusterUsage clusterUsage : clusterUsageList) {
                //统计GPU信息
                totalGpuCount += clusterUsage.getGpuCount();
                gpuTotalMap.putIfAbsent(clusterUsage.getGpuModel(), ResourceBillTypeGroupVO.Gpu.
                        builder().num(BigDecimal.ZERO).type(clusterUsage.getGpuModel()).build());
                ResourceBillTypeGroupVO.Gpu totalGpu = gpuTotalMap.get(clusterUsage.getGpuModel());
                totalGpu.setNum(totalGpu.getNum().add(new BigDecimal(clusterUsage.getGpuCount())));
                //GPU拆分计算
                if (clusterUsage.getServiceType().size() > 1) {
                    //按模型比例拆分
                    List<AigcModelUsage> usageList = modelUsageGroup.get(clusterUsage.getClusterId());
                    for (AigcModelUsage modelUsage : usageList) {
                        modelMap.putIfAbsent(modelUsage.getModelType(), ResourceBillTypeGroupVO.Model.builder()
                                .type(modelUsage.getModelType())
                                .gpuList(new ArrayList<>())
                                .gpuCount(BigDecimal.ZERO).build());
                        // 计算集群单卡显存（假设显存均分）
                        long gpuMemoryPerCard = clusterUsage.getTotalGpuMemorySize() / clusterUsage.getGpuCount();
                        BigDecimal usedGpuCount = new BigDecimal(modelUsage.getGpuMemorySize())
                                .divide(new BigDecimal(gpuMemoryPerCard), 2, RoundingMode.HALF_UP);

                        ResourceBillTypeGroupVO.Gpu gpu = ResourceBillTypeGroupVO.Gpu.builder()
                                .type(clusterUsage.getGpuModel())
                                .num(usedGpuCount).build();
                        ResourceBillTypeGroupVO.Model model = modelMap.get(modelUsage.getModelType());
                        model.setGpuCount(model.getGpuCount().add(BigDecimal.valueOf(clusterUsage.getGpuCount())));
                        model.getGpuList().add(gpu);
                    }
                } else {
                    //当前模型类型独占整个集群资源(含未注册的模型)
                    String modelType = clusterUsage.getServiceType().get(0);
                    modelMap.putIfAbsent(modelType, ResourceBillTypeGroupVO.Model.builder()
                            .type(modelType)
                            .gpuList(new ArrayList<>())
                            .gpuCount(BigDecimal.ZERO).build());
                    ResourceBillTypeGroupVO.Gpu gpu = ResourceBillTypeGroupVO.Gpu.builder()
                            .type(clusterUsage.getGpuModel())
                            .num(BigDecimal.valueOf(clusterUsage.getGpuCount())).build();
                    ResourceBillTypeGroupVO.Model model = modelMap.get(modelType);
                    model.setGpuCount(model.getGpuCount().add(BigDecimal.valueOf(clusterUsage.getGpuCount())));
                    model.getGpuList().add(gpu);
                }
            }

            for (ResourceBillTypeGroupVO.Model model : modelMap.values()) {
                BigDecimal modelGpuCount = BigDecimal.ZERO;
                Map<String, ResourceBillTypeGroupVO.Gpu> gpuMap = new HashMap<>();
                for (ResourceBillTypeGroupVO.Gpu g : model.getGpuList()) {
                    gpuMap.putIfAbsent(g.getType(), ResourceBillTypeGroupVO.Gpu.builder()
                            .type(g.getType()).num(BigDecimal.ZERO).build());
                    ResourceBillTypeGroupVO.Gpu modelGpu = gpuMap.get(g.getType());
                    modelGpu.setNum(modelGpu.getNum().add(g.getNum()));
                    // 减去已被模型使用的 GPU
                    ResourceBillTypeGroupVO.Gpu gpu = gpuTotalMap.get(g.getType());
                    gpu.setNum(gpu.getNum().subtract(g.getNum()));
                    modelGpuCount = modelGpuCount.add(g.getNum());
                }
                model.setGpuList(new ArrayList<>(gpuMap.values()));
                model.setGpuCount(modelGpuCount);

                // 计算该模型所占的 GPU 比例
                BigDecimal modelGpuProportion = modelGpuCount.divide(new BigDecimal(totalGpuCount), 2, RoundingMode.HALF_UP);
                model.setGpuProportionRate(modelGpuProportion);

                // 按比例分摊费用
                model.setMidPrice(platformGroup.getMidPrice().multiply(modelGpuProportion).setScale(2, RoundingMode.HALF_UP));
                model.setGpuPrice(platformGroup.getGpuPrice().multiply(modelGpuProportion).setScale(2, RoundingMode.HALF_UP));
                model.setTotalPrice(model.getMidPrice().add(model.getGpuPrice()));

                modelList.add(model);
                remainingProportion = remainingProportion.subtract(modelGpuProportion);
                remainingMidPrice = remainingMidPrice.subtract(model.getMidPrice());
                remainingGpuPrice = remainingGpuPrice.subtract(model.getGpuPrice());
            }
        }
        modelList.sort(Comparator.comparing(ResourceBillTypeGroupVO.Model::getType));
        // 处理剩余资源归为 "其他"
        List<ResourceBillTypeGroupVO.Gpu> remainingGpuList = new ArrayList<>(gpuTotalMap.values());
        remainingGpuList.sort(Comparator.comparing(ResourceBillTypeGroupVO.Gpu::getType));

        ResourceBillTypeGroupVO.Model otherModel = ResourceBillTypeGroupVO.Model.builder()
                .build();
        otherModel.setType(StringPool.OTHER);
        otherModel.setGpuList(remainingGpuList);
        BigDecimal otherGpuCount = remainingGpuList.stream()
                .map(ResourceBillTypeGroupVO.Gpu::getNum)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        otherModel.setGpuCount(otherGpuCount);
        otherModel.setGpuProportionRate(remainingProportion);
        BigDecimal totalPrice = BigDecimal.ZERO;
        if (Objects.nonNull(remainingMidPrice)) {
            otherModel.setMidPrice(remainingMidPrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : remainingMidPrice);
            totalPrice = totalPrice.add(otherModel.getMidPrice());
        }
        if (Objects.nonNull(remainingGpuPrice)) {
            otherModel.setGpuPrice(remainingGpuPrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : remainingGpuPrice);
            totalPrice = totalPrice.add(otherModel.getGpuPrice());
        }
        otherModel.setTotalPrice(totalPrice);

        modelList.add(otherModel);
        platformGroup.setModelList(modelList);
    }

    @Override
    public List<ResourceBillGroupConfigVO> queryBillGroupConfig(ResourceBillGroupConfigQuery query) {
        List<ResourceBillGroupConfig> configList = resourceBillGroupConfigService.queryList(ResourceBillGroupConfigCondition.builder()
                .month(query.getMonth()).build());
        return BeanUtil.copyToList(configList, ResourceBillGroupConfigVO.class);
    }

    @Override
    public void saveBillGroupConfig(ResourceBillGroupConfigCreateReq createReq) {
        resourceBillGroupConfigService.saveOrUpdateBatch(createReq);
    }

    @Override
    public String starExpendBillApprove() {
        return dingTalkBillApproveCode;
    }
}
