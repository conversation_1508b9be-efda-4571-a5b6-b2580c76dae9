package com.zjkj.aigc.service.impl.dynamic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigCreateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigGlobalUpdateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigQuery;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigGlobalVO;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoBrieflyVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import com.zjkj.aigc.service.IAigcDynamicConfigService;
import com.zjkj.aigc.service.impl.AbstractBaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Service
@RequiredArgsConstructor
public class IAigcDynamicConfigServiceImpl extends AbstractBaseService implements IAigcDynamicConfigService {

    private final AigcDynamicConfigService aigcDynamicConfigService;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public AigcDynamicConfigGlobalVO getGlobalConfig() {
        AigcDynamicConfigGlobal dynamicConfigGlobal = aigcDynamicConfigService.getGlobalConfig();
       return BeanUtil.copyProperties(dynamicConfigGlobal, AigcDynamicConfigGlobalVO.class);

    }

    @Override
    public void updateGlobalConfig(AigcDynamicConfigGlobalUpdateReq req) {
        AigcDynamicConfigGlobal dynamicConfigGlobal = Optional.ofNullable(aigcDynamicConfigService.getGlobalConfig())
                .orElse(new AigcDynamicConfigGlobal());
        BeanUtil.copyProperties(req, dynamicConfigGlobal);
        aigcDynamicConfigService.updateGlobalConfig(dynamicConfigGlobal);
    }

    /**
     * 填充模型信息
     * @param records 动态配置列表
     */
    private void fillModelInfo(List<AigcDynamicConfigVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<Long> modelIds = CollStreamUtil.toSet(records, AigcDynamicConfigVO::getModelId);
        AigcModelInfoCondition aigcModelInfoCondition = AigcModelInfoCondition.builder()
                .idList(modelIds)
                .build();
        List<AigcModelInfoBrieflyVO> aigcModelInfoBrieflyList = BeanUtil.copyToList(aigcModelInfoService.queryList(aigcModelInfoCondition), AigcModelInfoBrieflyVO.class);
        Map<Long, AigcModelInfoBrieflyVO> modelMap = CollStreamUtil.toIdentityMap(aigcModelInfoBrieflyList, AigcModelInfoBrieflyVO::getId);
        records.forEach(v -> {
            AigcModelInfoBrieflyVO modelInfoBriefly = modelMap.get(v.getModelId());
            v.setModelInfo(modelInfoBriefly);
        });
    }

    @Override
    public Page<AigcDynamicConfigVO> page(Page<AigcDynamicConfigVO> reqPage, AigcDynamicConfigQuery query) {
        AigcDynamicConfigCondition condition = BeanUtil.copyProperties(query, AigcDynamicConfigCondition.class);
        Page<AigcDynamicConfigVO> reultPage = genericPageQuery(
                reqPage,
                condition,
                aigcDynamicConfigService::page,
                AigcDynamicConfigVO.class
        );

        // fill model info
        fillModelInfo(reultPage.getRecords());
        return reultPage;
    }

    @Override
    public AigcDynamicConfigVO detail(Long id) {
        AigcDynamicConfig config = aigcDynamicConfigService.queryById(id);
        if (Objects.isNull(config)) {
           return null;
        }

        AigcDynamicConfigVO aigcDynamicConfigVO = BeanUtil.copyProperties(config, AigcDynamicConfigVO.class);
        AigcModelInfo modelInfo = aigcModelInfoService.getById(aigcDynamicConfigVO.getModelId());
        if (Objects.nonNull(modelInfo)) {
            AigcModelInfoBrieflyVO modelInfoBriefly = BeanUtil.copyProperties(modelInfo, AigcModelInfoBrieflyVO.class);
            aigcDynamicConfigVO.setModelInfo(modelInfoBriefly);
        }
        return aigcDynamicConfigVO;
    }

    @Override
    public void create(AigcDynamicConfigCreateReq createReq) {
        aigcDynamicConfigService.createConfig(createReq);
    }

    @Override
    public void update(AigcDynamicConfigCreateReq createReq) {
        aigcDynamicConfigService.updateConfig(createReq);
    }

    @Override
    public void delete(BatchOperateReq req) {
        aigcDynamicConfigService.batchDel(req.getIds());
    }

    @Override
    public void enable(BatchOperateReq req) {
        aigcDynamicConfigService.changeStatus(req.getIds(), GeneralEnum.SWITCH.DISABLE, GeneralEnum.SWITCH.ENABLE);
    }

    @Override
    public void disable(BatchOperateReq req) {
        aigcDynamicConfigService.changeStatus(req.getIds(), GeneralEnum.SWITCH.ENABLE, GeneralEnum.SWITCH.DISABLE);
    }
}
