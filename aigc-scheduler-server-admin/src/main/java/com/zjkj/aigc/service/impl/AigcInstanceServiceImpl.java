package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.req.model.AigcModelInstanceQuery;
import com.zjkj.aigc.common.req.node.AigcNodeInstanceQuery;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceBrieflyVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceVO;
import com.zjkj.aigc.domain.config.propertie.ModelInstanceProperties;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import com.zjkj.aigc.service.AigcInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcInstanceServiceImpl extends AbstractBaseService implements AigcInstanceService {

    private final AigcNodeInstanceService aigcNodeInstanceService;
    private final AigcModelInstanceService aigcModelInstanceService;
    private final ModelInstanceProperties modelInstanceProperties;

    @Override
    public Page<AigcNodeInstanceVO> nodeInstancePage(Page<AigcNodeInstanceVO> reqPage, AigcNodeInstanceQuery query) {
        AigcNodeInstanceCondition condition = BeanUtil.copyProperties(query, AigcNodeInstanceCondition.class);
        return genericPageQuery(
                reqPage,
                condition,
                aigcNodeInstanceService::queryPage,
                AigcNodeInstanceVO.class
        );
    }

    @Override
    public Page<AigcModelInstanceVO> modelInstancePage(Page<AigcModelInstanceVO> reqPage, AigcModelInstanceQuery query) {
        AigcModelInstanceCondition condition = BeanUtil.copyProperties(query, AigcModelInstanceCondition.class);
        if (StringUtils.hasText(query.getNodeIp())) {
            condition.setNodeIps(List.of(query.getNodeIp()));
        }
        Page<AigcModelInstanceVO> resultPage = genericPageQuery(
                reqPage,
                condition,
                aigcModelInstanceService::queryPage,
                AigcModelInstanceVO.class
        );

        if (!CollectionUtils.isEmpty(resultPage.getRecords())
                && StringUtils.hasText(modelInstanceProperties.getEndpoint())
                && StringUtils.hasText(modelInstanceProperties.getSpilt())) {
            resultPage.getRecords().forEach(record -> {
                try {
                    setInstanceEndpoint(record);
                } catch (Exception ex) {
                    log.error("setInstanceEndpoint error, podName:{}, podNamespace:{}", record.getPodName(), record.getPodNamespace(), ex);
                }
            });
        }

        return resultPage;
    }

    private void setInstanceEndpoint(AigcModelInstanceVO record) {
        String[] split = record.getPodNamespace().split(modelInstanceProperties.getSpilt());
        if (split.length < 2) {
            return;
        }

        String podName = record.getPodName();
        int secondLastHyphenIndex = podName.lastIndexOf(StringPool.DASH, podName.lastIndexOf(StringPool.DASH) - 1);
        String extractedName = podName.substring(0, secondLastHyphenIndex);
        String endpoint = String.format(modelInstanceProperties.getEndpoint(), split[0], extractedName, split[1]);
        record.setEndpoint(endpoint);
    }

    @Override
    public List<AigcNodeInstanceBrieflyVO> nodebrieflyList() {
        AigcNodeInstanceCondition condition = new AigcNodeInstanceCondition()
                .setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode())
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        List<AigcNodeInstance> nodeInstances = aigcNodeInstanceService.queryList(condition);
        return BeanUtil.copyToList(nodeInstances, AigcNodeInstanceBrieflyVO.class);
    }
}
