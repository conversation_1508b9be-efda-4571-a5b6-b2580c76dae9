package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.TaskStatCompareTypeEnum;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.vo.stat.AigcTaskStatDayVO;
import com.zjkj.aigc.common.vo.stat.AigcTaskTotalStatVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.service.AigcTaskStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskStatServiceImpl implements AigcTaskStatService {

    private final AigcTaskStatDayService aigcTaskStatDayService;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public Page<AigcTaskStatDayVO> page(Page<AigcTaskStatDayVO> page, TaskStatSummaryReq query) {
        Page<AigcTaskStatDay> reqPage = Page.of(page.getCurrent(), page.getSize());
        Page<AigcTaskStatDay> resultPage = aigcTaskStatDayService.pageByGroupModel(reqPage, query);
        page.setPages(resultPage.getPages());
        page.setTotal(resultPage.getTotal());

        List<AigcTaskStatDayVO> statDayVOList = BeanUtil.copyToList(resultPage.getRecords(), AigcTaskStatDayVO.class);
        fillModelInfo(statDayVOList);
        page.setRecords(statDayVOList);
        return page;
    }


    /**
     * 填充模型信息
     * @param statDayVOList 统计数据列表
     */
    private void fillModelInfo(List<AigcTaskStatDayVO> statDayVOList) {
        if (CollectionUtils.isEmpty(statDayVOList)) {
            return;
        }

        Set<String> taskTypes = CollStreamUtil.toSet(statDayVOList, AigcTaskStatDayVO::getTaskType);
        Set<String> modelNames = CollStreamUtil.toSet(statDayVOList, AigcTaskStatDayVO::getModelName);
        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        Map<Map.Entry<String, String>, AigcModelInfo> modelMap = CollStreamUtil.toIdentityMap(aigcModelInfoService.queryList(condition), v -> Map.entry(v.getTaskType(), v.getModelName()));
        if (CollectionUtils.isEmpty(modelMap)) {
            return;
        }

        statDayVOList.forEach(statDayVO -> {
            AigcModelInfo modelInfo = modelMap.get(Map.entry(statDayVO.getTaskType(), statDayVO.getModelName()));
            if (Objects.isNull(modelInfo)) {
                return;
            }

            statDayVO.setModelId(modelInfo.getId());
            statDayVO.setModelNameZh(modelInfo.getName());
            if (Objects.nonNull(statDayVO.getWaitingAmount()) && statDayVO.getWaitingAmount() > 0) {
                long instance = modelInfo.getInstanceByEnv(EnvUtil.getCurrentEnvType());
                if (instance <= 0) {
                    return;
                }

                Long avgElapsedTime = Objects.nonNull(statDayVO.getAvgElapsedTime()) ? statDayVO.getAvgElapsedTime() : modelInfo.getAvgElapsedTime();
                if (Objects.isNull(avgElapsedTime) || avgElapsedTime <= 0) {
                    return;
                }

                // 预计完成时间
                BigDecimal speed = BigDecimal.valueOf(instance * 60 * 60000L)
                        .divide(BigDecimal.valueOf(avgElapsedTime), 2, RoundingMode.HALF_UP);
                double difference = statDayVO.getWaitingAmount() * 60 / speed.doubleValue();
                statDayVO.setExpectFinishedTime(LocalDateTime.now().plusMinutes(Math.round(difference)));
            }

        });
    }

    @Override
    public List<AigcTaskStatDayVO> trend(TaskStatSummaryReq query) {
        AigcTaskStatCondition condition = AigcTaskStatCondition.builder()
                .modelName(query.getModelName())
                .taskType(query.getTaskType())
                .modelType(query.getModelType())
                .startDate(query.getStartTime())
                .endDate(query.getEndTime())
                .build();
        List<AigcTaskStatDay> taskStatDayList = aigcTaskStatDayService.sumByGroupStateDate(condition);
        return BeanUtil.copyToList(taskStatDayList, AigcTaskStatDayVO.class);
    }

    /**
     * 按模型合计
     * @param query 查询
     * @return 合计数据
     */
    private AigcTaskStatDayVO sumByModel(TaskStatSummaryReq query) {
        List<AigcTaskStatDay> aigcTaskStatDayList = aigcTaskStatDayService.sumByModel(query);
        AigcTaskStatDayVO taskStatDayVO;
        if (!CollectionUtils.isEmpty(aigcTaskStatDayList)) {
            taskStatDayVO = BeanUtil.copyProperties(aigcTaskStatDayList.get(0), AigcTaskStatDayVO.class);
        } else {
            // 默认
            taskStatDayVO = AigcTaskStatDayVO.initZero();
        }

        if (StringUtils.hasText(query.getStartTime())) {
            taskStatDayVO.setStatDate(LocalDate.parse(query.getStartTime()));
        }
        return taskStatDayVO;
    }
    
    @Override
    public List<AigcTaskStatDayVO> compareChain(TaskStatSummaryReq query) {
        List<AigcTaskStatDayVO> resultList = new ArrayList<>(2);
        LocalDate now = LocalDate.now();
        // 本
        query.setDateByCompareType(false, now);
        AigcTaskStatDayVO statDayVO = sumByModel(query);
        resultList.add(statDayVO);

        // 上
        query.setDateByCompareType(true, now);
        AigcTaskStatDayVO lastStatDayVO = sumByModel(query);
        resultList.add(lastStatDayVO);
        return resultList;
    }

    /**
     * 计算增长率
     * @param current 当前
     * @param last 上一
     * @return 增长率
     */
    public BigDecimal calculateGrowthRate(long current, long last) {
        BigDecimal hundred = BigDecimal.valueOf(100);
        if (last == 0) {
            return current > 0 ? BigDecimal.valueOf(current).multiply(hundred) : BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(current - last)
                .divide(BigDecimal.valueOf(last), 4, RoundingMode.HALF_UP)
                .multiply(hundred)
                .setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public AigcTaskTotalStatVO totalCompareChain(TaskStatSummaryReq query) {
        // 当前合计
        AigcTaskStatDay currentStat = aigcTaskStatDayService.sumTotal(query);

        // 上一合计
        LocalDate startDate = LocalDateTimeUtil.parseDate(query.getStartTime());
        query.setCompareType(TaskStatCompareTypeEnum.DAY.getType());
        query.setDateByCompareType(true, startDate);
        AigcTaskStatDay lastStat = aigcTaskStatDayService.sumTotal(query);

        // 本周合计
        query.setCompareType(TaskStatCompareTypeEnum.WEEK.getType());
        query.setDateByCompareType(false, startDate);
        AigcTaskStatDay weekStat = aigcTaskStatDayService.sumTotal(query);

        // 上周合计
        query.setDateByCompareType(true, startDate);
        AigcTaskStatDay lastWeekStat = aigcTaskStatDayService.sumTotal(query);

        return new AigcTaskTotalStatVO()
                .setTotalAmount(currentStat.getTotalAmount())
                .setDayChainGrowth(calculateGrowthRate(currentStat.getTotalAmount(), lastStat.getTotalAmount()))
                .setWeekChainGrowth(calculateGrowthRate(weekStat.getTotalAmount(), lastWeekStat.getTotalAmount()));
    }
}
