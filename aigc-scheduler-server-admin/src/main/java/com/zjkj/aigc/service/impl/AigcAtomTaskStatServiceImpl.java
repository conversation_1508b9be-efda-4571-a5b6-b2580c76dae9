package com.zjkj.aigc.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.zjkj.aigc.common.enums.model.AigcModelTypeEnum;
import com.zjkj.aigc.common.vo.stat.AigcAtomTaskTotalStatVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.service.AigcAtomTaskStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcAtomTaskStatServiceImpl implements AigcAtomTaskStatService {

    private final RabbitTemplate rabbitTemplate;

    private final MongoTemplate mongoTemplate;

    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public List<AigcAtomTaskTotalStatVO> aigcAtomTaskStat() {
        List<AigcAtomTaskTotalStatVO> stats = new CopyOnWriteArrayList<>();
        List<AigcModelInfo> modelInfoList = aigcModelInfoService.queryList(AigcModelInfoCondition.builder()
                .type(AigcModelTypeEnum.DATA_MQ.getCode())
                .build());
        if (CollUtil.isNotEmpty(modelInfoList)) {
            LocalDateTime nowMinusOneMinute = LocalDateTime.now().minusMinutes(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
            String formattedDate = nowMinusOneMinute.format(formatter);
            Query query = new Query(Criteria.where("create_time").gte(formattedDate));
            RabbitAdmin rabbitAdmin = new RabbitAdmin(rabbitTemplate);
            modelInfoList.parallelStream().forEach(modelInfo -> {
                QueueInformation information = rabbitAdmin.getQueueInfo(modelInfo.getModelName());
                if (Objects.nonNull(information) && information.getMessageCount() > 0L) {
                    AigcAtomTaskTotalStatVO statVO = AigcAtomTaskTotalStatVO.initZero();
                    statVO.setModelName(modelInfo.getModelName());
                    statVO.setTaskType(modelInfo.getTaskType());
                    statVO.setWaitingAmount(Long.valueOf(information.getMessageCount()));
                    long speed = mongoTemplate.count(query, modelInfo.getModelName()) * 60L;
                    statVO.setTaskSpeed(BigDecimal.valueOf(speed));
                    //预计完成时间
                    if (speed > 0) {
                        statVO.setExpectFinishedTime(LocalDateTime.now().plusMinutes(statVO.getWaitingAmount() * 60 / speed));
                    }
                    stats.add(statVO);
                }
            });
        }
        if (CollUtil.isNotEmpty(stats)) {
            stats.sort(Comparator.comparingLong(AigcAtomTaskTotalStatVO::getWaitingAmount).reversed());
        }
        return stats;
    }

    @Override
    public AigcAtomTaskTotalStatVO aigcAtomTaskStat(String modelName) {
        List<AigcModelInfo> modelInfoList = aigcModelInfoService.queryList(AigcModelInfoCondition.builder()
                .modelName(modelName)
                .type(AigcModelTypeEnum.DATA_MQ.getCode())
                .build());
        AigcAtomTaskTotalStatVO statVO = AigcAtomTaskTotalStatVO.initZero();
        statVO.setModelName(modelName);
        if (CollUtil.isNotEmpty(modelInfoList)) {
            statVO.setTaskType(modelInfoList.get(0).getTaskType());
        }
        RabbitAdmin rabbitAdmin = new RabbitAdmin(rabbitTemplate);
        QueueInformation information = rabbitAdmin.getQueueInfo(modelName);
        if (Objects.nonNull(information)) {
            statVO.setWaitingAmount(Long.valueOf(information.getMessageCount()));
            //处理任务速率
            LocalDateTime nowMinusOneMinute = LocalDateTime.now().minusMinutes(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
            String formattedDate = nowMinusOneMinute.format(formatter);
            Query query = new Query(Criteria.where("create_time").gte(formattedDate));
            long speed = mongoTemplate.count(query, modelName) * 60L;
            statVO.setTaskSpeed(BigDecimal.valueOf(speed));
            //预计完成时间
            if (speed > 0 && statVO.getWaitingAmount() > 0) {
                statVO.setExpectFinishedTime(LocalDateTime.now().plusMinutes(statVO.getWaitingAmount() * 60 / speed));
            }
        }
        return statVO;
    }
}
