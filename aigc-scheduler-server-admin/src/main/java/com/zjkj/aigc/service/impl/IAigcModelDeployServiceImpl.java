package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptCallbackDTO;
import com.zjkj.aigc.common.dto.webhook.DingMessageDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.model.ModelDeployEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelDeployQuery;
import com.zjkj.aigc.common.vo.model.AigcModelDeployFullVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployVO;
import com.zjkj.aigc.domain.config.propertie.ModelDeployProperties;
import com.zjkj.aigc.domain.task.service.AigcModelDeployService;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.service.IAigcModelDeployService;
import com.zjkj.aigc.util.WebClientUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcModelDeployServiceImpl extends AbstractBaseService implements IAigcModelDeployService {

    private final AigcModelDeployService aigcModelDeployService;
    private final AigcModelInfoService aigcModelInfoService;
    private final ModelDeployProperties deployProperties;
    private final WebClientUtil webClientUtil;

    @Override
    public Page<AigcModelDeployVO> page(Page<AigcModelDeployVO> reqPage, AigcModelDeployQuery query) {
        AigcModelDeployCondition condition = AigcModelDeployCondition.builder()
                .title(query.getKeyword())
                .modelId(query.getModelId())
                .statusList(query.getStatusList())
                .encryptStatusList(query.getEncryptStatusList())
                .isEncrypt(query.getIsEncrypt())
                .build();

        Page<AigcModelDeployVO> resultPage = genericPageQuery(
                reqPage,
                condition,
                aigcModelDeployService::queryPage,
                AigcModelDeployVO.class
        );

        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            fillModelNameZh(resultPage.getRecords());
        }

        return reqPage;
    }

    /**
     * 填充模型名称中文
     *
     * @param aigcModelDeployList 模型发布列表
     */
    public void fillModelNameZh(List<AigcModelDeployVO> aigcModelDeployList) {
        if (CollectionUtils.isEmpty(aigcModelDeployList)) {
            return;
        }

        List<Long> modelIds = aigcModelDeployList.stream()
                .map(AigcModelDeployVO::getModelId)
                .distinct()
                .collect(Collectors.toList());

        AigcModelInfoCondition condition = AigcModelInfoCondition
                .builder()
                .idList(modelIds)
                .build();

        Map<Long, String> nameMap = aigcModelInfoService.queryList(condition)
                .stream()
                .collect(Collectors.toMap(AigcModelInfo::getId, AigcModelInfo::getName));
        if (CollectionUtils.isEmpty(nameMap)) {
            return;
        }

        aigcModelDeployList.forEach(aigcModelDeploy -> aigcModelDeploy.setModelNameZh(nameMap.get(aigcModelDeploy.getModelId())));
    }


    @Override
    public AigcModelDeployVO detail(Long id) {
        AigcModelDeploy aigcModelDeploy = aigcModelDeployService.queryById(id);
        if (Objects.isNull(aigcModelDeploy)) {
            return null;
        }

        AigcModelDeployVO aigcModelDeployVO = BeanUtil.copyProperties(aigcModelDeploy, AigcModelDeployVO.class);
        AigcModelInfo modelInfo = aigcModelInfoService.getById(aigcModelDeployVO.getModelId());
        Optional.ofNullable(modelInfo)
                .ifPresent(info -> aigcModelDeployVO.setModelNameZh(info.getName()));
        return aigcModelDeployVO;
    }

    @Override
    public void create(AigcModelDeployCreateReq req) {
        List<Integer> statusList = List.of(ModelDeployEnum.Status.DRAFT.getStatus(), ModelDeployEnum.Status.DEPLOYING.getStatus());
        boolean existed = aigcModelDeployService.existAppointStatusSameDeploy(req.getModelId(), statusList);
        BaseBizException.isTrue(!existed, CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.deploy.already.exists"));

        AigcModelInfo modelInfo = aigcModelInfoService.getById(req.getModelId());
        BaseBizException.isTrue(Objects.nonNull(modelInfo), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.not.exists"));
        aigcModelDeployService.createAigcModelDeploy(req);
    }

    @Override
    public void update(AigcModelDeployCreateReq req) {
        boolean updated = aigcModelDeployService.updateAigcModelDeploy(req);
        BaseBizException.isTrue(updated, CustomErrorCode.DATA_CHANGED);
    }

    @Override
    public void delete(Long id) {
        aigcModelDeployService.deleteAigcModelDeploy(id);
    }

    @Override
    public void requestDeploy(Long id) {
        AigcModelDeploy modelDeploy = aigcModelDeployService.requestDeploy(id);
        boolean modelFileChanged = modelDeploy.getChanged().isModelFileChanged();
        if (!modelDeploy.getIsEncrypt() || !modelFileChanged) {
            dingNotify(modelDeploy);
        }
    }

    @Override
    public void encryptCallback(ModelEncryptCallbackDTO callback) {
        AigcModelDeploy modelDeploy = aigcModelDeployService.encryptCallback(callback);
        dingNotify(modelDeploy);
    }

    @Override
    public void deployDone(Long id) {
        AigcModelDeploy modelDeploy = aigcModelDeployService.deployDone(id);
        dingNotify(modelDeploy);
    }

    @Override
    public AigcModelDeployFullVO detailFull(Long id) {
        AigcModelDeploy modelDeploy = aigcModelDeployService.queryById(id, true);
        AigcModelInfo modelInfo = aigcModelInfoService.getById(modelDeploy.getModelId());
        AigcModelDeployFullVO fullVO = new AigcModelDeployFullVO();
        if (Objects.nonNull(modelInfo)) {
            BeanUtil.copyProperties(modelInfo, fullVO);
            fullVO.setModelNameZh(modelInfo.getName());
        }

        BeanUtil.copyProperties(modelDeploy, fullVO);
        return fullVO;
    }

    @Override
    public void deployNotify(Long id) {
        AigcModelDeploy modelDeploy = aigcModelDeployService.queryById(id, true);
        dingNotify(modelDeploy);
    }

    /**
     * 钉钉通知
     * @param modelDeploy 模型发布
     */
    public void dingNotify(AigcModelDeploy modelDeploy) {
        if (!StringUtils.hasText(deployProperties.getDingHook())) {
            log.info("dingNotify(). 钉钉通知hook为空，不发送通知");
            return;
        }

        ThreadUtil.execAsync(() -> {
            try {
                ModelDeployEnum.Status deployStatus = ModelDeployEnum.Status.of(modelDeploy.getStatus());
                if (Objects.isNull(deployStatus)) {
                    log.info("dingNotify(). 发布id:{}, 未知状态:{}", modelDeploy.getId(), modelDeploy.getStatus());
                    return;
                }

                AigcModelInfo modelInfo = Optional.ofNullable(aigcModelInfoService.getById(modelDeploy.getModelId()))
                        .orElseGet(AigcModelInfo::new);
                String text = buildNotifyContent(modelDeploy, modelInfo, deployStatus);

                String pageUrl = DingMessageDTO.getPcOutSlideUrl(String.format(deployProperties.getDetailPage(), modelDeploy.getId()));
                DingMessageDTO.ActionCard actionCard = DingMessageDTO.ActionCard.builder()
                        .title("模型部署申请发布")
                        .text(text)
                        .singleTitle("查看部署详情")
                        .singleUrl(pageUrl)
                        .build();
                Map<String, Object> reqMap = DingMessageDTO.buildRequest(actionCard);
                String resp = webClientUtil.post(deployProperties.getDingHook(), reqMap, String.class);
                log.info("dingNotify(). 发布id:{}, 钉钉通知结果：{}", modelDeploy.getId(), resp);
            } catch (Exception ex) {
                log.error("dingNotify(). 发布id:{}, 钉钉通知异常", modelDeploy.getId(), ex);
            }
        });
    }

    /**
     * 构建通知内容
     */
    private String buildNotifyContent(AigcModelDeploy modelDeploy, AigcModelInfo modelInfo, ModelDeployEnum.Status status) {
        StringBuilder content = new StringBuilder()
                .append(String.format("## <font color=%s>【调度平台】模型部署-%s</font> \n\n ",
                        status.getColor(), status.getDesc()))
                .append(formatKeyValue("发布ID", modelDeploy.getId()))
                .append(formatKeyValue("发布标题", modelDeploy.getTitle()))
                .append(formatKeyValue("模型中文名称", modelInfo.getName()))
                .append(formatKeyValue("工程名称", modelDeploy.getProjectName()))
                .append(formatKeyValue("任务类型", modelInfo.getTaskType()))
                .append(formatKeyValue("模型名称", modelInfo.getModelName()))
                .append(formatKeyValue("发布状态", ModelDeployEnum.Status.ofDesc(modelDeploy.getStatus())))
                .append(formatKeyValue("是否加密", modelDeploy.getIsEncrypt()))
                .append(formatKeyValue("加密状态", ModelDeployEnum.EncryptStatus.ofDesc(modelDeploy.getEncryptStatus())))
                .append(formatKeyValue("创建时间", LocalDateTimeUtil.formatNormal(modelDeploy.getCreatedTime())))
                .append(formatKeyValue("申请人", modelDeploy.getCreatorName()));

        // 如果加密失败，添加失败原因
        if (Objects.equals(modelDeploy.getEncryptStatus(),
                ModelDeployEnum.EncryptStatus.ENCRYPT_FAIL.getStatus())) {
            content.append(formatKeyValue("失败原因", modelDeploy.getMessage()));
        }

        return content.toString();
    }

    /**
     * 格式化键值对
     */
    private String formatKeyValue(String key, Object value) {
        return String.format("> %s：%s \n\n ", key, value);
    }
}
