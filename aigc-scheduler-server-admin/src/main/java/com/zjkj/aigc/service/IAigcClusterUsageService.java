package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageStatQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelTypeUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelUsageQuery;
import com.zjkj.aigc.common.vo.cluster.AigcClusterModelUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageStatVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcModelTypeTrendVO;

import java.util.List;

/**
 * 集群资源利用服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcClusterUsageService {

    /**
     * 分页查询
     *
     * @param reqPage 分页参数
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcClusterUsageVO> page(Page<AigcClusterUsageVO> reqPage, AigcClusterUsageQuery query);

    /**
     * 列表查询
     *
     * @param query 查询条件
     * @return 列表数据
     */
    List<AigcClusterUsageVO> list(AigcClusterUsageQuery query);

    /**
     * 详情
     *
     * @param id 主键
     * @return 详情
     */
    AigcClusterUsageVO detail(Long id);

    /**
     * 创建
     *
     * @param createReq 创建请求
     */
    void create(AigcClusterUsageCreateReq createReq);

    /**
     * 更新
     *
     * @param createReq 更新请求
     */
    void update(AigcClusterUsageCreateReq createReq);

    /**
     * 删除
     *
     * @param id 主键
     */
    void delete(Long id);

    /**
     * 资源分布
     *
     * @param query 查询条件
     * @return 统计数据
     */
    AigcClusterUsageStatVO located(AigcClusterUsageStatQuery query);

    /**
     * 模型统计
     *
     * @param query 查询条件
     * @return 统计数据
     */
    List<AigcClusterUsageStatVO.ModelProportion> modelStat(AigcClusterUsageStatQuery query);

    /**
     * 模型资源使用
     *
     * @param query 查询条件
     * @return 模型资源使用
     */
    AigcClusterModelUsageVO modelUsage(AigcModelUsageQuery query);

    /**
     * 模型类型使用趋势
     *
     * @param query 查询条件
     * @return 模型类型使用趋势
     */
    AigcModelTypeTrendVO modelTypeTrend(AigcModelTypeUsageQuery query);
}
