package com.zjkj.aigc.service;


import com.zjkj.aigc.common.vo.stat.AigcAtomTaskTotalStatVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
public interface AigcAtomTaskStatService {

    /**
     * 原子模型任务统计
     *
     * @return 原子模型任务统计
     */
    List<AigcAtomTaskTotalStatVO> aigcAtomTaskStat();

    /**
     * 原子模型任务统计
     *
     * @return 原子模型任务统计
     */
    AigcAtomTaskTotalStatVO aigcAtomTaskStat(String modelName);
}
