package com.zjkj.aigc.service.alarm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.vo.TaskModelSummaryVO;
import com.zjkj.aigc.common.req.alarm.*;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmChannelVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmConfigVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
public interface IAigcAlarmService {
    /**
     * 模型告警分页查询
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcAlarmVO> alarmPage(Page<AigcAlarmVO> reqPage, AigcAlarmQuery query);

    /**
     * 模型告警详情
     *
     * @param id 模型告警详情id
     * @return 详情信息
     */
    AigcAlarmVO alarmDetail(Long id);

    /**
     * 更新模型告警
     *
     * @param req 模型告警更新数据
     */
    void updateAlarm(AigcAlarmUpdateReq req);

    /**
     * 模型告警配置分页查询
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcAlarmConfigVO> alarmConfigPage(Page<AigcAlarmConfigVO> reqPage, AigcAlarmConfigQuery query);

    /**
     * 模型告警配置详情
     *
     * @param id 模型告警配置详情id
     * @return 详情信息
     */
    AigcAlarmConfigVO alarmConfigDetail(Long id);

    /**
     * 新增模型告警配置
     *
     * @param req 模型告警配置新增数据
     */
    void createAlarmConfig(AigcAlarmConfigCreateReq req);

    /**
     * 更新模型告警配置
     *
     * @param req 模型告警配置更新数据
     */
    void updateAlarmConfig(AigcAlarmConfigCreateReq req);

    /**
     * 更新模型告警配置状态
     *
     * @param id     数据ID
     * @param status 状态
     */
    void updateAlarmConfigStatus(Long id, Integer status);

    /**
     * 删除模型告警配置
     *
     * @param id 模型告警配置详情id
     */
    void deleteAlarmConfig(Long id);

    /**
     * 告警渠道配置集合
     *
     * @param query 查询条件
     * @return 告警渠道配置集合
     */
    List<AigcAlarmChannelVO> alarmChannelList(AigcAlarmChannelQuery query);

    /**
     * 告警渠道配置详情
     *
     * @param id 数据ID
     * @return 告警渠道配置详情
     */
    AigcAlarmChannelVO alarmChannelDetail(Long id);

    /**
     * 新增告警渠道配置
     *
     * @param req 告警渠道配置数据
     */
    void saveAlarmChannel(AigcAlarmChannelCreateReq req);

    /**
     * 更新告警渠道配置
     *
     * @param req 告警渠道配置数据
     */
    void updateAlarmChannel(AigcAlarmChannelCreateReq req);

    /**
     * 删除告警渠道配置
     *
     * @param id 数据ID
     */
    void deleteAlarmChannel(Long id);

    /**
     * 获取告警任务信息
     *
     * @param alarmId 告警ID
     * @return 告警任务信息
     */
    TaskModelSummaryVO getAlarmTaskStatus(Long alarmId);
}
