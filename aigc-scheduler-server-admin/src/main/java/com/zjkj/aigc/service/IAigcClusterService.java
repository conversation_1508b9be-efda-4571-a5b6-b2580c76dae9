package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterQuery;
import com.zjkj.aigc.common.vo.cluster.AigcClusterBrieflyVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterVO;

import java.util.List;

/**
 * 集群应用服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcClusterService {

    /**
     * 分页查询
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页数据
     */
    Page<AigcClusterVO> page(Page<AigcClusterVO> page, AigcClusterQuery query);

    /**
     * 查询列表
     *
     * @param query 查询条件
     * @return 列表数据
     */
    List<AigcClusterVO> list(AigcClusterQuery query);

    /**
     * 查询详情
     *
     * @param id 主键
     * @return 详情
     */
    AigcClusterVO detail(Long id);

    /**
     * 创建集群
     *
     * @param createReq 创建请求
     */
    void create(AigcClusterCreateReq createReq);

    /**
     * 更新集群
     *
     * @param updateReq 更新请求
     */
    void update(AigcClusterCreateReq updateReq);

    /**
     * 删除集群
     *
     * @param id 主键
     */
    void delete(Long id);

    /**
     * 启用集群
     *
     * @param id 主键
     */
    void enable(Long id);

    /**
     * 禁用集群
     *
     * @param id 主键
     */
    void disable(Long id);

    /**
     * 集群简要列表
     *
     * @return 列表数据
     */
    List<AigcClusterBrieflyVO> brieflyList(AigcClusterQuery query);

}
