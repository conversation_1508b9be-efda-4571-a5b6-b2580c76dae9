package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.ReUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.DataAffiliationTypeEnum;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.task.AigcTaskBatchOperateReq;
import com.zjkj.aigc.common.req.task.AigcTaskCreateReq;
import com.zjkj.aigc.common.req.task.AigcTaskQuery;
import com.zjkj.aigc.common.vo.AigcTaskVO;
import com.zjkj.aigc.domain.config.propertie.BatchModelProperties;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.domain.task.service.DataAffiliationService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.DataAffiliation;
import com.zjkj.aigc.service.IAigcTaskService;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
@Service
@RequiredArgsConstructor
public class IAigcTaskServiceImpl implements IAigcTaskService {

    private final AigcTaskService aigcTaskService;
    private final BatchModelProperties batchModelProperties;
    private final DataAffiliationService dataAffiliationService;

    private String REG_IMG_URL = "https?://[^\\s]+?\\.(jpg|jpeg|png|gif|bmp|webp)(\\?[^\"]*)?";

    @Override
    public Page<AigcTaskVO> page(Page<AigcTaskVO> reqPage, AigcTaskQuery query) {
        Page<AigcTask> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());

        AigcTaskCondition condition = AigcTaskCondition.builder()
                .businessId(query.getAppId())
                .taskStates(query.getTaskStates())
                .taskType(query.getTaskType())
                .modelName(query.getModelName())
                .startTime(query.getStartTime())
                .endTime(query.getEndTime())
                .build();
        if (StringUtils.hasText(query.getTaskId())) {
            condition.setTaskIds(List.of(query.getTaskId()));
        }

        if (StringUtils.hasText(query.getAigcTaskId())) {
            condition.setAigcTaskIds(List.of(query.getAigcTaskId()));
        }

        condition.setPage(resultPage);
        resultPage = aigcTaskService.queryPage(condition);
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());
        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            boolean batchOpen = batchModelProperties.batchOpen(query.getTaskType(), query.getModelName());
            aigcTaskService.fillAigcTaskRank(query.getTaskType(), query.getModelName(), batchOpen, resultPage.getRecords());
        }

        String pfn = LambdaUtil.getFieldName(AigcTask::getModelParams);
        String ofn = LambdaUtil.getFieldName(AigcTask::getModelOutput);
        String tpfn = LambdaUtil.getFieldName(AigcTaskVO::getModelParamsImgs);
        String tofn = LambdaUtil.getFieldName(AigcTaskVO::getModelOutputImgs);
        List<AigcTaskVO> aigcTasks = BeanUtil.copyToList(resultPage.getRecords(), AigcTaskVO.class,
                CopyOptions.create().setConverter(null).setFieldMapping(Map.of(pfn, tpfn, ofn, tofn))
                        .setFieldValueEditor((f, v) -> {
                            if (v != null && v instanceof String && (Objects.equals(f, tpfn)
                                    || Objects.equals(f, tofn))) {
                                List<String> imgs = ReUtil.findAll(REG_IMG_URL, (String) v, 0);
                                return imgs;
                            }
                            return v;
                        }));
        // 模型连通测试，填充数据所属
        if (Objects.equals(query.getAppId(), DataAffiliationTypeEnum.MODEL_CONNECT_TEST.getType())) {
            fillDataAffiliation(query.getAppId(), aigcTasks);
        }

        reqPage.setRecords(aigcTasks);
        return reqPage;
    }

    /**
     * 填充数据所属
     *
     * @param appId     appId
     * @param aigcTasks 模型任务
     */
    public void fillDataAffiliation(String appId, List<AigcTaskVO> aigcTasks) {
        if (CollectionUtils.isEmpty(aigcTasks) || !StringUtils.hasText(appId)) {
            return;
        }

        List<String> aigcTaskIds = aigcTasks.stream()
                .map(AigcTaskVO::getAigcTaskId)
                .collect(Collectors.toList());

        List<DataAffiliation> dataAffiliations = dataAffiliationService.queryByDataTypeId(appId, aigcTaskIds);
        if (CollectionUtils.isEmpty(dataAffiliations)) {
            return;
        }

        Map<String, DataAffiliation> dataAffiliationMap = dataAffiliations.stream()
                .collect(Collectors.toMap(DataAffiliation::getDataId, Function.identity(), (v1, v2) -> v2));
        aigcTasks.forEach(aigcTask -> {
            DataAffiliation dataAffiliation = dataAffiliationMap.get(aigcTask.getAigcTaskId());
            if (Objects.nonNull(dataAffiliation)) {
                aigcTask.setCreatorId(dataAffiliation.getCreatorId());
                aigcTask.setCreatorName(dataAffiliation.getCreatorName());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(AigcTaskCreateReq req) {
        CreateAiTaskReq<Map<String, Object>> createAiTaskReq = new CreateAiTaskReq<>();
        BeanUtil.copyProperties(req, createAiTaskReq);
        createAiTaskReq.setBusinessId(req.getAppId());
        AigcTask aigcTask = aigcTaskService.saveAigcTask(createAiTaskReq);

        DataAffiliation dataAffiliation = new DataAffiliation();
        dataAffiliation.setDataId(aigcTask.getAigcTaskId());
        dataAffiliation.setDataType(aigcTask.getBusinessId());
        dataAffiliationService.create(dataAffiliation);
    }

    @Override
    public AigcTaskVO detail(String aigcTaskId) {
        AigcTask aigcTask = aigcTaskService.getByAigcTaskId(aigcTaskId);
        return BeanUtil.copyProperties(aigcTask, AigcTaskVO.class);
    }

    @Override
    public void cancel(String aigcTaskId) {
        AigcTask aigcTask = aigcTaskService.getByAigcTaskId(aigcTaskId);

        boolean cancelled = aigcTaskService.cancelAiTask(aigcTask);
        BaseBizException.isTrue(cancelled, CustomErrorCode.DATA_CHANGED);
    }

    @Override
    public void update(AigcTaskCreateReq req) {
        boolean updated = aigcTaskService.updateAigcTask(req);
        BaseBizException.isTrue(updated, CustomErrorCode.DATA_CHANGED);
    }

    @Override
    public String batchRetry(AigcTaskBatchOperateReq req) {
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .aigcTaskIds(req.getAigcTaskIds())
                .build();
        List<AigcTask> aigcTasks = aigcTaskService.queryList(condition)
                .stream()
                .filter(v -> TaskStateEnum.isFailed(v.getTaskState())
                        || TaskStateEnum.isTimeoutFailed(v.getTaskState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return MessageUtils.getMessage("business.not.allow.retry");
        }

        String str = "重试成功：%d，重试失败：%d";
        int fail = 0;
        for (AigcTask aigcTask : aigcTasks) {
            boolean retryTask = aigcTaskService.retryTask(aigcTask);
            if (!retryTask) {
                fail++;
            }
        }
        return MessageUtils.getMessage("business.retry.result",new Object[]{aigcTasks.size() - fail, fail});
    }

    @Override
    public String batchCancel(AigcTaskBatchOperateReq req) {
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .aigcTaskIds(req.getAigcTaskIds())
                .build();
        List<AigcTask> aigcTasks = aigcTaskService.queryList(condition)
                .stream()
                .filter(v -> TaskStateEnum.isWait(v.getTaskState())
                        || TaskStateEnum.isProcess(v.getTaskState()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return MessageUtils.getMessage("business.not.allow.cancel");
        }

        String str = "取消成功：%d，取消失败：%d";
        int fail = 0;
        for (AigcTask aigcTask : aigcTasks) {
            try {
                boolean cancelled = aigcTaskService.cancelAiTask(aigcTask);
                if (!cancelled) {
                    fail++;
                }
            } catch (Exception e) {
                fail++;
            }
        }
        return MessageUtils.getMessage("business.retry.result",new Object[]{aigcTasks.size() - fail, fail});
    }
}
