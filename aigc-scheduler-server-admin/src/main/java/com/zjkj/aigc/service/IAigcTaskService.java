package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.task.AigcTaskBatchOperateReq;
import com.zjkj.aigc.common.req.task.AigcTaskCreateReq;
import com.zjkj.aigc.common.req.task.AigcTaskQuery;
import com.zjkj.aigc.common.vo.AigcTaskVO;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
public interface IAigcTaskService {

    /**
     *  aigc任务分页列表
     *
     * @param reqPage 分页数据
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcTaskVO> page(Page<AigcTaskVO> reqPage, AigcTaskQuery query);

    /**
     * 创建aigc任务
     *
     * @param req 请求参数
     */
    void create(AigcTaskCreateReq req);

    /**
     * aigc任务详情
     * @param aigcTaskId 任务ID
     * @return 任务详情
     */
    AigcTaskVO detail(String aigcTaskId);

    /**
     * 取消任务
     * @param aigcTaskId 任务ID
     */
    void cancel(String aigcTaskId);

    /**
     * 更新任务
     * @param req 请求参数
     */
    void update(AigcTaskCreateReq req);

    /**
     * 重试任务
     * @param req 请求参数
     */
    String batchRetry(AigcTaskBatchOperateReq req);

    /**
     * 取消任务
     * @param req 请求参数
     */
    String batchCancel(AigcTaskBatchOperateReq req);
}
