package com.zjkj.aigc.service.resource;

import com.zjkj.aigc.common.req.resource.ResourcePlanCreateReq;
import com.zjkj.aigc.common.req.resource.ResourcePlanQuery;
import com.zjkj.aigc.common.vo.resource.ResourcePlanPriceVO;
import com.zjkj.aigc.common.vo.resource.ResourcePlanVO;

import java.util.List;

public interface ResourcePlanService {

    /**
     * 查询资源规划
     *
     * @param query 查询条件
     * @return 资源规划数据
     */
    List<ResourcePlanVO> queryPlan(ResourcePlanQuery query);

    /**
     * 创建资源规划
     *
     * @param createReq 创建参数
     */
    void createResourcePlan(ResourcePlanCreateReq createReq);

    /**
     * 更新资源规划
     *
     * @param createReq 更新参数
     */
    void updateResourcePlan(ResourcePlanCreateReq createReq);

    /**
     * 删除资源规划
     *
     * @param query 删除条件
     */
    void deleteResourcePlan(ResourcePlanQuery query);

    /**
     * 资源规划预算
     *
     * @param query
     * @return 资源规划预算
     */
    List<ResourcePlanPriceVO> queryBudget(ResourcePlanQuery query);
}
