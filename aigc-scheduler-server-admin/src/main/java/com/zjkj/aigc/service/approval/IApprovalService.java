package com.zjkj.aigc.service.approval;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.approval.GpuApplyCreateReq;
import com.zjkj.aigc.common.req.approval.GpuApplyQuery;
import com.zjkj.aigc.common.req.approval.GpuApplyStatusReq;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelDeployQuery;
import com.zjkj.aigc.common.vo.approval.GpuApplyVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployVO;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
public interface IApprovalService {
    /**
     * 分页查询
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<GpuApplyVO> page(Page<GpuApplyVO> reqPage, GpuApplyQuery query);

    /**
     * GPU申请资源详情
     *
     * @param id 模型发布id
     * @return 详情信息
     */
    GpuApplyVO detail(Long id);

    /**
     * 创建GPU申请
     *
     * @param req 创建参数
     */
    void create(GpuApplyCreateReq req);

    /**
     * 创建GPU申请
     *
     * @param req 创建参数
     */
    void update(GpuApplyCreateReq req);

    /**
     * 更新GPU申请状态
     *
     * @param statusReq 更新参数
     */
    void updateStatus(GpuApplyStatusReq statusReq);
}
