package com.zjkj.aigc.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.resource.ResourceExpendCreateReq;
import com.zjkj.aigc.common.req.resource.ResourceExpendQuery;
import com.zjkj.aigc.common.req.resource.ResourcePlanQuery;
import com.zjkj.aigc.common.vo.resource.ResourceExpendSummaryVO;
import com.zjkj.aigc.common.vo.resource.ResourcePlanPriceVO;
import com.zjkj.aigc.domain.task.service.resource.ResourceExpendService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceExpendCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceExpend;
import com.zjkj.aigc.service.resource.IResourceExpendService;
import com.zjkj.aigc.service.resource.ResourcePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IResourceExpendServiceImpl implements IResourceExpendService {

    private final ResourceExpendService resourceExpendService;

    private final ResourcePlanService resourcePlanService;

    @Override
    public ResourceExpendSummaryVO query(ResourceExpendQuery query) {
        List<ResourceExpend> expendList = resourceExpendService.queryList(ResourceExpendCondition.builder()
                .month(query.getMonth()).build());
        if (CollUtil.isNotEmpty(expendList)) {
            List<String> platforms = expendList.stream()
                    .map(ResourceExpend::getPlatform)
                    .collect(Collectors.toSet())
                    .stream()
                    .collect(Collectors.toList());
            //查出对应月份预算
            List<ResourcePlanPriceVO> priceList = resourcePlanService.queryBudget(
                    ResourcePlanQuery.builder()
                            .month(query.getMonth())
                            .platforms(platforms)
                            .build()
            );
            //填充预算值及待充值值
            Map<String, BigDecimal> platformPriceMap = priceList.stream()
                    .collect(Collectors.toMap(ResourcePlanPriceVO::getPlatform, ResourcePlanPriceVO::getPrice));
            List<ResourceExpendSummaryVO.Expend> expends = BeanUtil.copyToList(expendList, ResourceExpendSummaryVO.Expend.class);
            expends.forEach(expend -> {
                BigDecimal price = platformPriceMap.get(expend.getPlatform());
                if (Objects.nonNull(price)) {
                    expend.setThisMonthBudget(price);
                } else {
                    expend.setThisMonthBudget(BigDecimal.ZERO);
                }
                expend.setThisMonthTodoRecharge(expend.getThisMonthBudget()
                        .subtract(expend.getLastMonthRemain())
                        .subtract(expend.getThisMonthRecharge()));
                if (expend.getThisMonthTodoRecharge().compareTo(BigDecimal.ZERO) < 0) {
                    expend.setThisMonthTodoRecharge(BigDecimal.ZERO);
                }
            });
            ResourceExpendSummaryVO summaryVO = new ResourceExpendSummaryVO();
            summaryVO.setMonth(query.getMonth());
            summaryVO.setExpendList(expends);
            summaryVO.calculate();
            return summaryVO;
        }
        return null;
    }

    @Override
    public void createResourceExpend(ResourceExpendCreateReq req) {
        List<ResourceExpend> expendList = resourceExpendService.queryList(ResourceExpendCondition.builder()
                .month(req.getMonth()).build());
        if (CollUtil.isNotEmpty(expendList)) {
            throw new BaseBizException(CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.resource.expend.month.data.exists"));
        }
        req.getExpendList().forEach(expend -> {
            expend.setMonth(req.getMonth());
        });
        resourceExpendService.createExpendBath(req.getExpendList());
    }

    @Override
    public void updateResourceExpend(ResourceExpendCreateReq req) {
        //不能修改月份
        req.getExpendList().forEach(expend -> {
            expend.setMonth(null);
        });
        resourceExpendService.updateExpendBath(req.getExpendList());
    }
}
