package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.test.ModelTestCreateReq;
import com.zjkj.aigc.common.req.model.test.ModelTestQuery;
import com.zjkj.aigc.common.vo.ModelTestReportVO;
import com.zjkj.aigc.common.vo.ModelTestVO;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
public interface ModelTestService {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return 分页数据
     */
    Page<ModelTestVO> page(Page<ModelTestVO> page, ModelTestQuery query);

    /**
     * 创建模型测试
     *
     * @param req 请求参数
     */
    void createModelTest(ModelTestCreateReq req);

    /**
     * 根据测试编号查询
     *
     * @param testNo 测试编号
     * @return 测试数据
     */
    ModelTestVO queryByTestNo(String testNo);

    /**
     * 更新模型测试
     *
     * @param req    请求参数
     */
    void updateModelTest(ModelTestCreateReq req);

    /**
     * 开始模型测试
     *
     * @param testNo 测试编号
     */
    void startModelTest(String testNo);

    /**
     * 取消模型测试
     *
     * @param testNo 测试编号
     */
    void cancelModelTest(String testNo);

    /**
     * 删除模型测试
     *
     * @param testNo 测试编号
     */
    void delModelTest(String testNo);

    /**
     * 查看测试报告
     * @param testNo 测试编号
     * @return 测试报告
     */
    ModelTestReportVO queryReportByTestNo(String testNo);
}
