package com.zjkj.aigc.service.impl.sys;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictAbleQuery;
import com.zjkj.aigc.common.req.dict.SysDictCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictQuery;
import com.zjkj.aigc.common.vo.sys.SysDictDataVO;
import com.zjkj.aigc.common.vo.sys.SysDictVO;
import com.zjkj.aigc.domain.task.service.sys.SysDictDataService;
import com.zjkj.aigc.domain.task.service.sys.SysDictService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictDataCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;
import com.zjkj.aigc.service.impl.AbstractBaseService;
import com.zjkj.aigc.service.sys.ISysDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典服务实现类
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ISysDictServiceImpl extends AbstractBaseService implements ISysDictService {

    private final SysDictService sysDictService;
    private final SysDictDataService sysDictDataService;

    @Override
    public Page<SysDictVO> page(Page<SysDictVO> reqPage, SysDictQuery query) {
        SysDictCondition condition = new SysDictCondition()
                .setType(query.getType())
                .setName(query.getKeyword())
                .setStatus(query.getStatus());
        return genericPageQuery(
                reqPage,
                condition,
                sysDictService::queryPage,
                SysDictVO.class
        );
    }

    @Override
    public SysDictVO detail(Long id) {
        SysDict sysDict = sysDictService.queryById(id);
        return BeanUtil.copyProperties(sysDict, SysDictVO.class);
    }

    @Override
    public void create(SysDictCreateReq req) {
        sysDictService.createSysDict(req);
    }

    @Override
    public void update(SysDictCreateReq req) {
        sysDictService.updateSysDict(req);
    }

    @Override
    public void delete(Long id) {
        sysDictService.deleteSysDict(id);
    }

    @Override
    public void enable(BatchOperateReq req) {
        sysDictService.changeStatus(req.getIds(), GeneralEnum.SWITCH.DISABLE, GeneralEnum.SWITCH.ENABLE);
    }

    @Override
    public void disable(BatchOperateReq req) {
        sysDictService.changeStatus(req.getIds(), GeneralEnum.SWITCH.ENABLE, GeneralEnum.SWITCH.DISABLE);
    }

    @Override
    public List<SysDictVO> ableList(SysDictAbleQuery query) {
        SysDictCondition condition = new SysDictCondition()
                .setStatus(GeneralEnum.SWITCH.ENABLE.getCode())
                .setTypes(query.getTypes());
        List<SysDict> sysDicts = sysDictService.queryList(condition);
        if (CollectionUtils.isEmpty(sysDicts)) {
            return List.of();
        }

        List<Long> ids = sysDicts.stream()
                .map(SysDict::getId)
                .collect(Collectors.toList());

        // 查询字典数据
        SysDictDataCondition dictDataCondition = new SysDictDataCondition()
                .setDictIds(ids)
                .setStatus(GeneralEnum.SWITCH.ENABLE.getCode());
        Map<Long, List<SysDictData>> dataMap = sysDictDataService.queryList(dictDataCondition)
                .stream()
                .collect(Collectors.groupingBy(SysDictData::getDictId));
        List<SysDictVO> result = BeanUtil.copyToList(sysDicts, SysDictVO.class);
        result.forEach(sysDictVO -> {
            List<SysDictData> dictData = dataMap.getOrDefault(sysDictVO.getId(), List.of());
            if (!CollectionUtils.isEmpty(dictData)) {
                dictData.sort(Comparator.comparing(SysDictData::getDictSort));
            }
            sysDictVO.setDictDataList(BeanUtil.copyToList(dictData, SysDictDataVO.class));
        });
        return result;
    }
}
