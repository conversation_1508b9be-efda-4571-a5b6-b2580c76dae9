package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptCallbackDTO;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelDeployQuery;
import com.zjkj.aigc.common.vo.model.AigcModelDeployFullVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployVO;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
public interface IAigcModelDeployService {

    /**
     * 分页查询
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcModelDeployVO> page(Page<AigcModelDeployVO> reqPage, AigcModelDeployQuery query);

    /**
     * 模型发布详情
     * @param id 模型发布id
     * @return 详情信息
     */
    AigcModelDeployVO detail(Long id);

    /**
     * 创建模型发布
     * @param req 创建参数
     */
    void create(AigcModelDeployCreateReq req);

    /**
     * 更新模型发布
     * @param req 更新参数
     */
    void update(AigcModelDeployCreateReq req);

    /**
     * 删除模型发布
     * @param id 模型发布id
     */
    void delete(Long id);

    /**
     * 请求部署
     * @param id 模型发布id
     */
    void requestDeploy(Long id);

    /**
     * 模型加密回调
     * @param callback 加密回调
     */
    void encryptCallback(ModelEncryptCallbackDTO callback);

    /**
     * 完成模型发布
     * @param id 模型发布id
     */
    void deployDone(Long id);

    /**
     * 部署明细
     * @param id 模型发布id
     * @return 明细信息
     */
    AigcModelDeployFullVO detailFull(Long id);

    /**
     * 模型发布通知
     * @param id 模型发布id
     */
    void deployNotify(Long id);
}
