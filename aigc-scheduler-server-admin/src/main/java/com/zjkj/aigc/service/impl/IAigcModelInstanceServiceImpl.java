package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.ModelInstanceAdjustDTO;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordQuery;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordReq;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceAdjustRecordVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceAdjustRecordService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstanceAdjustRecord;
import com.zjkj.aigc.service.IAigcModelInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/01/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcModelInstanceServiceImpl implements IAigcModelInstanceService {

    private final AigcModelInstanceAdjustRecordService modelInstanceAdjustRecordService;

    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public Page<AigcModelInstanceAdjustRecordVO> adjustRecordPage(Page<AigcModelInstanceAdjustRecordVO> reqPage, AigcModelInstanceAdjustRecordQuery query) {
        Page<AigcModelInstanceAdjustRecord> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());
        AigcModelInstanceAdjustRecordCondition condition = AigcModelInstanceAdjustRecordCondition.builder()
                .page(resultPage)
                .build();
        resultPage = modelInstanceAdjustRecordService.queryPage(condition);
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());
        List<AigcModelInstanceAdjustRecordVO> recordVOList = BeanUtil.copyToList(resultPage.getRecords(), AigcModelInstanceAdjustRecordVO.class);
        reqPage.setRecords(recordVOList);
        return reqPage;
    }

    @Override
    public AigcModelInstanceAdjustRecordVO adjustRecordDetail(Long id) {
        AigcModelInstanceAdjustRecord record = modelInstanceAdjustRecordService.queryById(id);
        if (Objects.nonNull(record)) {
            return BeanUtil.copyProperties(record, AigcModelInstanceAdjustRecordVO.class);
        }
        return null;
    }

    @Override
    public void createAdjustRecord(AigcModelInstanceAdjustRecordReq req) {
        modelInstanceAdjustRecordService.create(req);
    }

    @Override
    public void updateAdjustRecord(AigcModelInstanceAdjustRecordReq req) {
        modelInstanceAdjustRecordService.update(req);
    }

    @Override
    public void deleteAdjustRecord(Long id) {
        modelInstanceAdjustRecordService.delete(id);
    }
}
