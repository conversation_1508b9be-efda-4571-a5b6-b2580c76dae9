package com.zjkj.aigc.service.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicAdjustRecordQuery;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicAdjustRecordVO;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
public interface IAigcDynamicAdjustRecordService {
    /**
     * 调整记录列表-分页
     *
     * @param reqPage 分页请求
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcDynamicAdjustRecordVO> page(Page<AigcDynamicAdjustRecordVO> reqPage, AigcDynamicAdjustRecordQuery query);
    /**
     * 查询调整记录详情
     * @param id 调整记录ID
     * @return 调整记录详情
     */
    AigcDynamicAdjustRecordVO detail(Long id);
    /**
     * 调整取消
     * @param id 记录id
     */
    void cancel(Long id);
    /**
     * 调整回退
     * @param id 记录id
     */
    void rollback(Long id);
}
