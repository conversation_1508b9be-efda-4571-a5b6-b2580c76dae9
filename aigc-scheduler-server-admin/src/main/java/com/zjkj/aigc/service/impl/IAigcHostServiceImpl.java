package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.enums.host.HostStatusEnum;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.host.AigcHostCreateReq;
import com.zjkj.aigc.common.req.host.AigcHostQuery;
import com.zjkj.aigc.common.vo.host.AigcHostVO;
import com.zjkj.aigc.domain.task.service.AigcHostService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcHostCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import com.zjkj.aigc.service.IAigcHostService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class IAigcHostServiceImpl extends AbstractBaseService implements IAigcHostService {

    private final AigcHostService aigcHostService;
    private final AigcNodeInstanceService aigcNodeInstanceService;

    @Override
    public Page<AigcHostVO> page(Page<AigcHostVO> reqPage, AigcHostQuery query) {
        AigcHostCondition condition = new AigcHostCondition()
                .setStatus(query.getStatus())
                .setHostIp(query.getHostIp());
        if (StringUtils.hasText(query.getPlatform())) {
            condition.setPlatforms(List.of(query.getPlatform()));
        }
        if (StringUtils.hasText(query.getRegion())) {
            condition.setRegions(List.of(query.getRegion()));
        }

        Page<AigcHostVO> resultPage = genericPageQuery(
                reqPage,
                condition,
                aigcHostService::queryPage,
                AigcHostVO.class
        );
        fillOnlineNodeCrt(resultPage.getRecords());
        return resultPage;
    }

    /**
     * 填充在线节点数
     * @param records 主机列表
     */
    public void fillOnlineNodeCrt(List<AigcHostVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<String> hostIps = records.stream()
                .map(AigcHostVO::getHostIp)
                .collect(Collectors.toList());

        AigcNodeInstanceCondition condition = new AigcNodeInstanceCondition()
                .setNodeIps(hostIps)
                .setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode())
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        Map<String, Long> ipCountMap = aigcNodeInstanceService.queryList(condition)
                .stream()
                .map(AigcNodeInstance::getNodeIp)
                .collect(Collectors.groupingBy(
                        Function.identity(),
                        Collectors.counting()
                ));
        if (CollectionUtils.isEmpty(ipCountMap)) {
            return;
        }

        records.forEach(record -> {
            Long crt = ipCountMap.getOrDefault(record.getHostIp(), 0L);
            record.setOnlineNodeCrt(crt.intValue());
        });
    }

    @Override
    public AigcHostVO detail(Long id) {
        AigcHost aigcHost = aigcHostService.queryById(id);
        return BeanUtil.copyProperties(aigcHost, AigcHostVO.class);
    }

    @Override
    public void create(AigcHostCreateReq req) {
        aigcHostService.createAigcHost(req);
    }

    @Override
    public void update(AigcHostCreateReq req) {
        aigcHostService.updateAigcHost(req);
    }

    @Override
    public void delete(Long id) {
        aigcHostService.deleteAigcHost(id);
    }

    @Override
    public void enable(BatchOperateReq req) {
        aigcHostService.changeStatus(req.getIds(), HostStatusEnum.DISABLE, HostStatusEnum.ENABLE);
    }

    @Override
    public void disable(BatchOperateReq req) {
        aigcHostService.changeStatus(req.getIds(), HostStatusEnum.ENABLE, HostStatusEnum.DISABLE);
    }
}
