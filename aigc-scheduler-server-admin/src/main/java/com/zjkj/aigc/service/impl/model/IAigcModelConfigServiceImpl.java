package com.zjkj.aigc.service.impl.model;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.req.model.AigcModelConfigCreateReq;
import com.zjkj.aigc.common.vo.model.AigcModelConfigVO;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.service.model.IAigcModelConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
@Service
@RequiredArgsConstructor
public class IAigcModelConfigServiceImpl implements IAigcModelConfigService {

    private final AigcModelConfigService aigcModelConfigService;

    @Override
    public AigcModelConfigVO getByModelId(Long modelId) {
        AigcModelConfig aigcModelConfig = aigcModelConfigService.getByModelId(modelId);
        return BeanUtil.copyProperties(aigcModelConfig, AigcModelConfigVO.class);
    }

    @Override
    public void create(AigcModelConfigCreateReq req) {
        aigcModelConfigService.createModelConfig(req);
    }

    @Override
    public void update(AigcModelConfigCreateReq req) {
        aigcModelConfigService.updateModelConfig(req);
    }
}
