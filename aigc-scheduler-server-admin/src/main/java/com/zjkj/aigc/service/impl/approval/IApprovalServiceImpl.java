package com.zjkj.aigc.service.impl.approval;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.approval.GpuApplyCreateReq;
import com.zjkj.aigc.common.req.approval.GpuApplyQuery;
import com.zjkj.aigc.common.req.approval.GpuApplyStatusReq;
import com.zjkj.aigc.common.vo.approval.GpuApplyVO;
import com.zjkj.aigc.common.vo.model.AigcModelDeployVO;
import com.zjkj.aigc.domain.task.service.approval.GpuApplyService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval.GpuApplyCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval.GpuApply;
import com.zjkj.aigc.service.approval.IApprovalService;
import com.zjkj.saas.admin.sdk.dto.SsoUserDTO;
import com.zjkj.saas.admin.sdk.util.SaasSsoContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IApprovalServiceImpl implements IApprovalService {

    private final GpuApplyService gpuApplyService;

    @Override
    public Page<GpuApplyVO> page(Page<GpuApplyVO> reqPage, GpuApplyQuery query) {
        Page<GpuApply> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());
        GpuApplyCondition condition = GpuApplyCondition.builder()
                .code(query.getCode())
                .type(query.getType())
                .status(query.getStatus())
                .platform(query.getPlatform())
                .creatorName(query.getCreatorName())
                .page(resultPage)
                .build();
        if (Objects.nonNull(query.getCurrentUser()) && Boolean.TRUE.equals(query.getCurrentUser())) {
            SsoUserDTO ssoUserDTO = SaasSsoContext.getCurrentContext().getSsoUser();
            condition.setCreatorId(ssoUserDTO.getUserId());
        }
        resultPage = gpuApplyService.queryPage(condition);
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());
        List<GpuApplyVO> gpuApplyList = BeanUtil.copyToList(resultPage.getRecords(), GpuApplyVO.class);
        reqPage.setRecords(gpuApplyList);
        return reqPage;
    }

    @Override
    public GpuApplyVO detail(Long id) {
        GpuApply gpuApply = gpuApplyService.queryById(id, Boolean.TRUE);
        GpuApplyVO applyVO = BeanUtil.copyProperties(gpuApply, GpuApplyVO.class);
        return applyVO;
    }

    @Override
    public void create(GpuApplyCreateReq req) {
        gpuApplyService.createGpuApply(req);
    }

    @Override
    public void update(GpuApplyCreateReq req) {
        gpuApplyService.updateGpuApply(req);
    }

    @Override
    public void updateStatus(GpuApplyStatusReq statusReq) {
        GpuApply gpuApply = gpuApplyService.queryById(statusReq.getId(), Boolean.TRUE);
        if (ApprovalStatusEnum.PENDING.getStatus() != gpuApply.getStatus()) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.approval.completed"));
        }
        gpuApplyService.updateGpuApplyStatus(statusReq);
    }
}
