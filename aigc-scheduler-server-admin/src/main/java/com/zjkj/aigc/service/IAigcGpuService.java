package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.gpu.AigcGpuCreateReq;
import com.zjkj.aigc.common.req.gpu.AigcGpuQuery;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import java.util.List;

/**
 * GPU服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface IAigcGpuService {

    /**
     * 分页查询
     *
     * @param reqPage 分页参数
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcGpuVO> page(Page<AigcGpuVO> reqPage, AigcGpuQuery query);

    /**
     * GPU详情
     *
     * @param id GPU id
     * @return GPU详情
     */
    AigcGpuVO detail(Long id);

    /**
     * 创建GPU
     *
     * @param req 创建GPU请求
     */
    void create(AigcGpuCreateReq req);

    /**
     * 更新GPU
     *
     * @param req 更新GPU请求
     */
    void update(AigcGpuCreateReq req);

    /**
     * 删除GPU
     *
     * @param id GPU id
     */
    void delete(Long id);

    /**
     * GPU列表
     *
     * @param query 查询条件
     * @return 列表数据
     */
    List<AigcGpuVO> list(AigcGpuQuery query);
}
