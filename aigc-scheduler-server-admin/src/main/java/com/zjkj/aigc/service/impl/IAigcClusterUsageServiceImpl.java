package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageStatQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelTypeUsageQuery;
import com.zjkj.aigc.common.req.cluster.AigcModelUsageQuery;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.util.MathUtil;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.common.vo.cluster.AigcClusterModelUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageStatVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcModelTypeTrendVO;
import com.zjkj.aigc.common.vo.cluster.AigcModelTypeUsageVO;
import com.zjkj.aigc.common.vo.cluster.AigcModelUsageVO;
import com.zjkj.aigc.domain.task.service.AigcClusterUsageService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.domain.task.service.model.AigcModelUsageService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.service.IAigcClusterUsageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 集群资源利用服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class IAigcClusterUsageServiceImpl extends AbstractBaseService implements IAigcClusterUsageService {

    private final AigcClusterUsageService aigcClusterUsageService;
    private final AigcTaskStatDayService aigcTaskStatDayService;
    private final AigcModelUsageService aigcModelUsageService;

    @Override
    public Page<AigcClusterUsageVO> page(Page<AigcClusterUsageVO> reqPage, AigcClusterUsageQuery query) {
        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setClusterId(query.getClusterId())
                .setPlatform(query.getPlatform())
                .setDataDate(query.getDataDate());

        return genericPageQuery(
                reqPage,
                condition,
                aigcClusterUsageService::page,
                AigcClusterUsageVO.class
        );
    }

    @Override
    public List<AigcClusterUsageVO> list(AigcClusterUsageQuery query) {
        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setClusterId(query.getClusterId())
                .setDataDate(query.getDataDate());

        List<AigcClusterUsage> usageList = aigcClusterUsageService.queryList(condition);
        return BeanUtil.copyToList(usageList, AigcClusterUsageVO.class);
    }

    @Override
    public AigcClusterUsageVO detail(Long id) {
        AigcClusterUsage usage = aigcClusterUsageService.queryById(id);
        return BeanUtil.copyProperties(usage, AigcClusterUsageVO.class);
    }

    @Override
    public void create(AigcClusterUsageCreateReq createReq) {
        aigcClusterUsageService.createAigcClusterUsage(createReq);
    }

    @Override
    public void update(AigcClusterUsageCreateReq createReq) {
        aigcClusterUsageService.updateAigcClusterUsage(createReq);
    }

    @Override
    public void delete(Long id) {
        aigcClusterUsageService.deleteAigcClusterUsage(id);
    }

    @Override
    public AigcClusterUsageStatVO located(AigcClusterUsageStatQuery query) {
        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setPlatform(query.getPlatform())
                .setClusterId(query.getClusterId())
                .setDataDate(query.getDataDate());
        List<AigcClusterUsage> usageList = aigcClusterUsageService.queryList(condition);
        AigcClusterUsageStatVO usageStatVO;
        if (CollectionUtils.isEmpty(usageList)) {
            usageStatVO = AigcClusterUsageStatVO.buildDefault();
        } else {
            // gpu使用率
            long totalGpuMemorySize = 0, usedGpuMemorySize = 0;
            for (AigcClusterUsage clusterUsage : usageList) {
                totalGpuMemorySize += clusterUsage.getTotalGpuMemorySize();
                usedGpuMemorySize += clusterUsage.getUsedGpuMemorySize();
            }
            BigDecimal gpuUsageRate = MathUtil.percentage(totalGpuMemorySize, usedGpuMemorySize);

            // 模型类型占比
            List<AigcClusterUsageStatVO.ModelProportion> modelProportions = statModelProportion(query.getDataDate(), usageList);
            usageStatVO = new AigcClusterUsageStatVO()
                    .setTotalGpuMemorySize(totalGpuMemorySize)
                    .setUsedGpuMemorySize(usedGpuMemorySize)
                    .setGpuUsageRate(gpuUsageRate)
                    .setModelProportions(modelProportions);
        }

        // 近x天gpu使用量
        LocalDate minusDays = query.getDataDate().minusDays(14);
        condition.setStartDate(minusDays);
        condition.setEndDate(query.getDataDate());
        condition.setDataDate(null);
        List<AigcClusterUsageStatVO.GpuUsageRate> gpuUsageRates = statRangGpuUsageRate(condition);
        usageStatVO.setGpuUsageRates(gpuUsageRates);
        return usageStatVO;
    }

    @Override
    public List<AigcClusterUsageStatVO.ModelProportion> modelStat(AigcClusterUsageStatQuery query) {
        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setPlatform(query.getPlatform())
                .setClusterId(query.getClusterId())
                .setDataDate(query.getDataDate());
        // 模型占比
        List<AigcClusterUsage> usageList = aigcClusterUsageService.queryList(condition);
        List<AigcClusterUsageStatVO.ModelProportion> modelProportions = statModelProportion(query.getDataDate(), usageList);

        // 模型统计
        TaskStatSummaryReq summaryReq = new TaskStatSummaryReq();
        summaryReq.setStartTime(query.getDataDate().toString());
        summaryReq.setEndTime(query.getDataDate().toString());
        Map<String, AigcTaskStatDay> statDayMap = aigcTaskStatDayService.sumByModelType(summaryReq)
                .stream()
                .collect(Collectors.toMap(AigcTaskStatDay::getModelType, Function.identity(), (v1, v2) -> v2));

        Long zeroLong = 0L;
        modelProportions.forEach(modelProportion -> {
            String key = modelProportion.getType();
            AigcTaskStatDay statDay = statDayMap.get(key);
            if (Objects.nonNull(statDay)) {
                modelProportion.setTotalTaskCount(statDay.getTotalAmount());
                modelProportion.setModelCount(statDay.getModelCount());
                statDayMap.remove(key);
            } else {
                modelProportion.setTotalTaskCount(zeroLong);
                modelProportion.setModelCount(zeroLong);
            }
        });

        if (!CollectionUtils.isEmpty(statDayMap)) {
            // 补全模型
            statDayMap.forEach((type, statDay) -> {
                if (StringUtils.hasText(type)) {
                    AigcClusterUsageStatVO.ModelProportion modelProportion = new AigcClusterUsageStatVO.ModelProportion()
                            .setType(type)
                            .setTotalTaskCount(statDay.getTotalAmount())
                            .setModelCount(statDay.getModelCount())
                            .setGpuCount(zeroLong)
                            .setGpuProportionRate(BigDecimal.ZERO)
                            .setGpuUsageRate(BigDecimal.ZERO);
                    modelProportions.add(modelProportion);
                }
            });
        }

        return modelProportions;
    }

    @Override
    public AigcClusterModelUsageVO modelUsage(AigcModelUsageQuery query) {
        AigcModelUsageCondition condition = new AigcModelUsageCondition()
                .setClusterId(query.getClusterId())
                .setDataDate(query.getDataDate());

        List<AigcModelUsage> modelUsages = aigcModelUsageService.queryList(condition);
        if (modelUsages.isEmpty()) {
            return AigcClusterModelUsageVO.buildDefault();
        }

        AigcClusterUsageCondition clusterUsageCondition = new AigcClusterUsageCondition()
                .setClusterId(query.getClusterId())
                .setDataDate(query.getDataDate());
        List<AigcClusterUsage> aigcClusterUsages = aigcClusterUsageService.queryList(clusterUsageCondition);
        long totalGpuMemorySize = aigcClusterUsages.stream()
                .findFirst()
                .map(AigcClusterUsage::getTotalGpuMemorySize)
                .orElse(0L);
        // 按模型类型合计gpu使用率
        Map<String, List<AigcModelUsage>> mTypeUsageMap = StreamUtil.groupBy(modelUsages, AigcModelUsage::getModelType);
        List<AigcModelTypeUsageVO> modelTypeUsages = mTypeUsageMap.entrySet()
                .stream()
                .map(entry -> {
                    long gpuMemorySize = entry.getValue().stream()
                            .mapToLong(AigcModelUsage::getGpuMemorySize)
                            .sum();

                    return new AigcModelTypeUsageVO()
                            .setType(entry.getKey())
                            .setModelCount((long) entry.getValue().size())
                            .setGpuMemorySize(gpuMemorySize)
                            .setGpuMemorySizeRatio(MathUtil.percentage(totalGpuMemorySize, gpuMemorySize));
                }).collect(Collectors.toList());

        // 模型资源使用列表
        List<AigcModelUsageVO> aigcModelUsageList = BeanUtil.copyToList(modelUsages, AigcModelUsageVO.class);
        return new AigcClusterModelUsageVO()
                .setModelUsages(aigcModelUsageList)
                .setModelTypeUsages(modelTypeUsages);
    }

    /**
     * 补全数据
     *
     * @param trendList 趋势数据
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 补全后的数据
     */
    private AigcModelTypeTrendVO replenish(List<AigcModelTypeTrendVO.Trend> trendList, LocalDate startDate, LocalDate endDate) {
        // 有数据的日期
        List<LocalDate> dateList = trendList.stream()
                .map(AigcModelTypeTrendVO.Trend::getDataDate)
                .collect(Collectors.toList());

        trendList = CollectionUtils.isEmpty(trendList) ? new ArrayList<>() : trendList;
        List<AigcModelTypeTrendVO.AvgUsage> avgUsages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trendList)) {
            Map<String, List<AigcModelTypeUsageVO>> modelTypeMap = trendList.stream()
                    .map(AigcModelTypeTrendVO.Trend::getModelTypeUsages)
                    .flatMap(List::stream)
                    .collect(Collectors.groupingBy(AigcModelTypeUsageVO::getType));

            // 按模型类型计算显存大小、占比平均值
            modelTypeMap.forEach((modelType, modelTypeUsages) -> {
                long gpuMemorySize = (long) Math.ceil(modelTypeUsages.stream()
                        .mapToLong(AigcModelTypeUsageVO::getGpuMemorySize)
                        .filter(size -> size > 0)
                        .average()
                        .orElse(0));

                BigDecimal gpuMemorySizeRatio = modelTypeUsages.stream()
                        .map(AigcModelTypeUsageVO::getGpuMemorySizeRatio)
                        .filter(ratio -> ratio.compareTo(BigDecimal.ZERO) > 0)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(modelTypeUsages.size()), RoundingMode.HALF_UP);

                AigcModelTypeTrendVO.AvgUsage avgUsage = new AigcModelTypeTrendVO.AvgUsage()
                        .setType(modelType)
                        .setGpuMemorySize(gpuMemorySize)
                        .setGpuMemorySizeRatio(gpuMemorySizeRatio);
                avgUsages.add(avgUsage);
            });
        }

        // 生成缺失的日期
        Stream.iterate(startDate, date -> !date.isAfter(endDate), date -> date.plusDays(1))
                .filter(date -> !dateList.contains(date))
                .map(AigcModelTypeTrendVO.Trend::buildDefault)
                .forEach(trendList::add);

        trendList.sort(Comparator.comparing(AigcModelTypeTrendVO.Trend::getDataDate));
        return new AigcModelTypeTrendVO()
                .setTrends(trendList)
                .setAvgUsages(avgUsages);
    }


    @Override
    public AigcModelTypeTrendVO modelTypeTrend(AigcModelTypeUsageQuery query) {
        AigcClusterUsageCondition clusterUsageCondition = new AigcClusterUsageCondition()
                .setPlatform(query.getPlatform())
                .setClusterId(query.getClusterId())
                .setStartDate(query.getStartDate())
                .setEndDate(query.getEndDate());
        List<AigcClusterUsage> clusterUsages = aigcClusterUsageService.queryList(clusterUsageCondition);
        if (CollectionUtils.isEmpty(clusterUsages)) {
            return replenish(List.of(), query.getStartDate(), query.getEndDate());
        }

        Set<Long> clusterIds = StreamUtil.mapToSet(clusterUsages, AigcClusterUsage::getClusterId);
        AigcModelUsageCondition modelUsageCondition = new AigcModelUsageCondition()
                .setClusterIds(clusterIds)
                .setStartDate(query.getStartDate())
                .setEndDate(query.getEndDate());
        List<AigcModelUsage> modelUsages = aigcModelUsageService.queryList(modelUsageCondition);
        if (CollectionUtils.isEmpty(modelUsages)) {
            return replenish(List.of(), query.getStartDate(), query.getEndDate());
        }

        // 模型类型
        Set<String> modelTypes = StreamUtil.mapToSet(modelUsages, AigcModelUsage::getModelType);

        // gpu显存合计
        Map<LocalDate, Long> clusterTotalGpuMap = clusterUsages.stream()
                .collect(Collectors.groupingBy(AigcClusterUsage::getDataDate,
                        Collectors.summingLong(AigcClusterUsage::getTotalGpuMemorySize)));
        // 模型任务统计
        Map<LocalDate, Map<Map.Entry<String, String>, AigcTaskStatDay>> taskStatMap = queryTaskStatMap(modelUsages, query.getStartDate(), query.getEndDate());

        List<AigcModelTypeTrendVO.Trend> trendList = new ArrayList<>();
        // 模型使用情况
        Map<LocalDate, List<AigcModelUsage>> modelUsageMap = StreamUtil.groupBy(modelUsages, AigcModelUsage::getDataDate);
        modelUsageMap.forEach((dataDate, usages) -> {
            // 按模型类型合计
            Map<String, List<AigcModelUsage>> mTypeUsageMap = StreamUtil.groupBy(usages, AigcModelUsage::getModelType);
            List<AigcModelTypeUsageVO> modelTypeUsages = new ArrayList<>(mTypeUsageMap.size());
            mTypeUsageMap.forEach((modelType, modelUsageList) -> {
                long gpuMemorySize = modelUsageList.stream()
                        .mapToLong(AigcModelUsage::getGpuMemorySize)
                        .sum();

                BigDecimal gpuUsageRatio = MathUtil.percentage(clusterTotalGpuMap.getOrDefault(dataDate, 0L), gpuMemorySize);

                // 模型任务统计
                Map<Map.Entry<String, String>, AigcTaskStatDay> taskStatDayMap = taskStatMap.getOrDefault(dataDate, Map.of());
                long totalAmount = 0;
                Set<Map.Entry<String, String>> modelSet = new HashSet<>();
                for (AigcModelUsage modelUsage : modelUsageList) {
                    Map.Entry<String, String> modelKey = Map.entry(modelUsage.getTaskType(), modelUsage.getModelName());
                    if (modelSet.add(modelKey)) {
                        AigcTaskStatDay taskStatDay = taskStatDayMap.get(Map.entry(modelUsage.getTaskType(), modelUsage.getModelName()));
                        if (Objects.nonNull(taskStatDay)) {
                            totalAmount += taskStatDay.getTotalAmount();
                        }
                    }
                }

                AigcModelTypeUsageVO modelTypeUsage = new AigcModelTypeUsageVO()
                        .setType(modelType)
                        .setGpuMemorySize(gpuMemorySize)
                        .setGpuMemorySizeRatio(gpuUsageRatio)
                        .setModelCount((long) modelSet.size())
                        .setTotalTaskCount(totalAmount);
                modelTypeUsages.add(modelTypeUsage);
            });

            // 补全数据
            modelTypes.stream()
                    .filter(modelType -> !mTypeUsageMap.containsKey(modelType))
                    .forEach(modelType -> modelTypeUsages.add(AigcModelTypeUsageVO.buildDefault(modelType)));
            AigcModelTypeTrendVO.Trend trend = new AigcModelTypeTrendVO.Trend()
                    .setDataDate(dataDate)
                    .setModelTypeUsages(modelTypeUsages);
            trendList.add(trend);
        });

        return replenish(trendList, query.getStartDate(), query.getEndDate());
    }

    /**
     * 查询任务统计
     *
     * @param modelUsages 模型使用情况
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 任务统计
     */
    public Map<LocalDate, Map<Map.Entry<String, String>, AigcTaskStatDay>> queryTaskStatMap(List<AigcModelUsage> modelUsages, LocalDate startDate, LocalDate endDate) {
        if (CollectionUtils.isEmpty(modelUsages)) {
            return Map.of();
        }

        Set<String> taskTypes = new HashSet<>();
        Set<String> modelNames = new HashSet<>();
        modelUsages.forEach(usage -> {
            taskTypes.add(usage.getTaskType());
            modelNames.add(usage.getModelName());
        });

        AigcTaskStatCondition statCondition = AigcTaskStatCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .startDate(startDate.toString())
                .endDate(endDate.toString())
                .build();
        // 按模型、数据日期分组合计
        List<AigcTaskStatDay> taskStatDays = aigcTaskStatDayService.sumByGroupModelStatDate(statCondition);
        return taskStatDays.stream()
                .collect(Collectors.groupingBy(
                        AigcTaskStatDay::getStatDate,
                        Collectors.toMap(stat -> Map.entry(stat.getTaskType(), stat.getModelName()), Function.identity())));
    }


    /**
     * 统计gpu使用
     *
     * @param condition 查询条件
     * @return gpu使用率
     */
    public List<AigcClusterUsageStatVO.GpuUsageRate> statRangGpuUsageRate(AigcClusterUsageCondition condition) {
        List<AigcClusterUsage> usageList = aigcClusterUsageService.statByDate(condition);
        List<AigcClusterUsageStatVO.GpuUsageRate> gpuUsageRates = BeanUtil.copyToList(usageList, AigcClusterUsageStatVO.GpuUsageRate.class);

        // 有数据的日期
        List<LocalDate> dateList = gpuUsageRates.stream()
                .map(AigcClusterUsageStatVO.GpuUsageRate::getDataDate)
                .collect(Collectors.toList());

        // 生成缺失的日期并填充默认值
        Stream.iterate(condition.getStartDate(), date -> !date.isAfter(condition.getEndDate()), date -> date.plusDays(1))
                .filter(date -> !dateList.contains(date))
                .map(AigcClusterUsageStatVO.GpuUsageRate::buildDefault)
                .forEach(gpuUsageRates::add);

        // 按日期排序
        gpuUsageRates.sort(Comparator.comparing(AigcClusterUsageStatVO.GpuUsageRate::getDataDate));
        return gpuUsageRates;
    }

    /**
     * 统计模型占比
     *
     * @param dataDate      数据日期
     * @param clusterUsages 集群使用情况
     * @return 模型占比
     */
    public List<AigcClusterUsageStatVO.ModelProportion> statModelProportion(LocalDate dataDate, List<AigcClusterUsage> clusterUsages) {
        List<Long> clusterIds = StreamUtil.mapToList(clusterUsages, AigcClusterUsage::getClusterId);
        AigcModelUsageCondition condition = new AigcModelUsageCondition()
                .setDataDate(dataDate)
                .setClusterIds(clusterIds);
        List<AigcModelUsage> modelUsages = aigcModelUsageService.queryList(condition);
        if (CollectionUtils.isEmpty(modelUsages)) {
            return List.of();
        }

        // 集群总显存
        long gpuMemorySizeTotal = clusterUsages.stream()
                .mapToLong(AigcClusterUsage::getTotalGpuMemorySize)
                .sum();

        // 模型占用总显存
        long modelGpuMemorySizeTotal = modelUsages.stream()
                .mapToLong(AigcModelUsage::getGpuMemorySize)
                .sum();

        // 计算模型类型占比
        Map<String, List<AigcModelUsage>> modelTypeMap = StreamUtil.groupBy(modelUsages, AigcModelUsage::getModelType);
        return modelTypeMap.entrySet().stream()
                .map(entry -> {
                    Set<Map.Entry<String, String>> modelSet = entry.getValue().stream()
                            .map(usage -> Map.entry(usage.getTaskType(), usage.getModelName()))
                            .collect(Collectors.toSet());

                    long gpuMemorySize = entry.getValue().stream()
                            .mapToLong(AigcModelUsage::getGpuMemorySize)
                            .sum();

                    return new AigcClusterUsageStatVO.ModelProportion()
                            .setType(entry.getKey())
                            .setModelCount((long) modelSet.size())
                            .setGpuMemorySize(gpuMemorySize)
                            .setGpuProportionRate(MathUtil.percentage(modelGpuMemorySizeTotal, gpuMemorySize))
                            .setGpuUsageRate(MathUtil.percentage(modelGpuMemorySizeTotal, gpuMemorySizeTotal));
                })
                .sorted(Comparator.comparing(AigcClusterUsageStatVO.ModelProportion::getGpuMemorySize).reversed())
                .collect(Collectors.toList());
    }


}
