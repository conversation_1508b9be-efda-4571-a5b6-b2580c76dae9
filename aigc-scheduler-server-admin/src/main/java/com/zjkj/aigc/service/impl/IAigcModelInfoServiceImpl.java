package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.enums.model.AigcModelInfoEnum;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoCreateReq;
import com.zjkj.aigc.common.req.model.AigcModelInfoQuery;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.vo.model.AigcModelInfoBrieflyVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.service.IAigcModelInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcModelInfoServiceImpl extends AbstractBaseService implements IAigcModelInfoService {

    private final AigcModelInfoService aigcModelInfoService;
    private final AigcModelInstanceService aigcModelInstanceService;

    @Override
    public Page<AigcModelInfoVO> page(Page<AigcModelInfoVO> reqPage, AigcModelInfoQuery query) {
        AigcModelInfoCondition condition = BeanUtil.copyProperties(query, AigcModelInfoCondition.class);
        condition.setName(query.getKeyword());
        Page<AigcModelInfoVO> resultPage = genericPageQuery(
                reqPage,
                condition,
                aigcModelInfoService::queryPage,
                AigcModelInfoVO.class
        );

        // 填充在线实例
        resultPage.getRecords().forEach(record -> {
            List<AigcModelInfoVO.OnlineInstance> onlineInstanceList = Optional.ofNullable(record.getEnvInstance()).orElse(Map.of())
                    .entrySet().stream()
                    .map(entry -> new AigcModelInfoVO.OnlineInstance()
                            .setEnvType(entry.getKey())
                            .setOnlineInstance(entry.getValue()))
                    .collect(Collectors.toList());
            record.setOnlineInstanceList(onlineInstanceList);
        });
        return resultPage;
    }

    @Override
    public List<AigcModelInfoBrieflyVO> brieflyList() {
        AigcModelInfoCondition condition = AigcModelInfoCondition.builder().build();
        List<AigcModelInfo> aigcModelInfos = aigcModelInfoService.queryList(condition);
        return BeanUtil.copyToList(aigcModelInfos, AigcModelInfoBrieflyVO.class);
    }

    @Override
    public AigcModelInfoVO detail(Long id) {
        AigcModelInfo aigcModelInfo = aigcModelInfoService.getById(id);
        return BeanUtil.copyProperties(aigcModelInfo, AigcModelInfoVO.class);
    }

    @Override
    public void create(AigcModelInfoCreateReq req) {
        aigcModelInfoService.saveModelInfo(req);
    }

    @Override
    public void update(AigcModelInfoCreateReq req) {
        aigcModelInfoService.updateModelInfo(req);
    }

    @Override
    public void delete(Long id) {
        aigcModelInfoService.deleteModelInfo(id);
    }

    @Override
    public void enable(BatchOperateReq req) {
        List<Long> ids = req.getIds();
        aigcModelInfoService.changeStatus(ids, AigcModelInfoEnum.ENABLE.getStatus());
    }

    @Override
    public void disable(BatchOperateReq req) {
        List<Long> ids = req.getIds();
        aigcModelInfoService.changeStatus(ids, AigcModelInfoEnum.DISABLE.getStatus());
    }

    @Override
    public List<AigcModelInfoVO> sameClusterModel(Long id) {
        AigcModelInstanceCondition condition = new AigcModelInstanceCondition()
                .setModelId(id)
                .setEnvType(EnvUtil.getCurrentEnvType())
                .setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode())
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        Set<Long> clusterIds = aigcModelInstanceService.queryList(condition).stream()
                .map(AigcModelInstance::getClusterId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(clusterIds)) {
            return List.of();
        }

        condition.setModelId(null);
        condition.setClusterIds(clusterIds);
        Set<Long> modelIds = aigcModelInstanceService.queryList(condition).stream()
                .map(AigcModelInstance::getModelId)
                .filter(v -> Objects.nonNull(v) && !Objects.equals(v, id))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(modelIds)) {
            return List.of();
        }

        AigcModelInfoCondition infoCondition = AigcModelInfoCondition
                .builder()
                .build();
        infoCondition.setIdList(modelIds);
        List<AigcModelInfo> modelInfos = aigcModelInfoService.queryList(infoCondition);
        return BeanUtil.copyToList(modelInfos, AigcModelInfoVO.class);
    }
}
