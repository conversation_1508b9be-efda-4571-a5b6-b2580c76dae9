package com.zjkj.aigc.service.impl.sys;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataCreateReq;
import com.zjkj.aigc.common.req.dict.SysDictDataQuery;
import com.zjkj.aigc.common.vo.sys.SysDictDataVO;
import com.zjkj.aigc.domain.task.service.sys.SysDictDataService;
import com.zjkj.aigc.domain.task.service.sys.SysDictService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictDataCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;
import com.zjkj.aigc.service.impl.AbstractBaseService;
import com.zjkj.aigc.service.sys.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 字典数据服务实现类
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ISysDictDataServiceImpl extends AbstractBaseService implements ISysDictDataService {

    private final SysDictDataService sysDictDataService;
    private final SysDictService sysDictService;

    @Override
    public Page<SysDictDataVO> page(Page<SysDictDataVO> reqPage, SysDictDataQuery query) {
        SysDictDataCondition condition = new SysDictDataCondition()
                .setDictId(query.getDictId())
                .setStatus(query.getStatus());

        return genericPageQuery(
                reqPage,
                condition,
                sysDictDataService::queryPage,
                SysDictDataVO.class
        );
    }

    @Override
    public SysDictDataVO detail(Long id) {
        SysDictData sysDictData = sysDictDataService.queryById(id);
        return BeanUtil.copyProperties(sysDictData, SysDictDataVO.class);
    }

    @Override
    public void create(SysDictDataCreateReq req) {
        sysDictDataService.createSysDictData(req);
    }

    @Override
    public void update(SysDictDataCreateReq req) {
        sysDictDataService.updateSysDictData(req);
    }

    @Override
    public void delete(Long id) {
        sysDictDataService.deleteSysDictData(id);
    }

    @Override
    public void enable(BatchOperateReq req) {
        sysDictDataService.changeStatus(req.getIds(), GeneralEnum.SWITCH.DISABLE, GeneralEnum.SWITCH.ENABLE);
    }

    @Override
    public void disable(BatchOperateReq req) {
        sysDictDataService.changeStatus(req.getIds(), GeneralEnum.SWITCH.ENABLE, GeneralEnum.SWITCH.DISABLE);
    }

    @Override
    public List<SysDictDataVO> getByDictType(String dictType) {
        SysDict dict = sysDictService.queryByType(dictType);
        if (Objects.isNull(dict) || Objects.equals(dict.getStatus(), GeneralEnum.SWITCH.DISABLE.getCode())) {
            return List.of();
        }

        SysDictDataCondition condition = new SysDictDataCondition()
                .setStatus(GeneralEnum.SWITCH.ENABLE.getCode())
                .setDictId(dict.getId());
        List<SysDictData> sysDictData = sysDictDataService.queryList(condition);
        return BeanUtil.copyToList(sysDictData, SysDictDataVO.class);
    }
}
