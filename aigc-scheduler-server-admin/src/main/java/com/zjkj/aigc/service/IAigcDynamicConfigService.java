package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.BatchOperateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigCreateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigGlobalUpdateReq;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigQuery;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigGlobalVO;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicConfigVO;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
public interface IAigcDynamicConfigService {

    /**
     * 动态调度全局配置
     *
     * @return 全局配置
     */
    AigcDynamicConfigGlobalVO getGlobalConfig();

    /**
     * 更新全局配置
     *
     * @param req 全局配置请求
     */
    void updateGlobalConfig(AigcDynamicConfigGlobalUpdateReq req);

    /**
     * 动态调度配置列表-分页
     *
     * @param reqPage 分页请求
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcDynamicConfigVO> page(Page<AigcDynamicConfigVO> reqPage, AigcDynamicConfigQuery query);

    /**
     * 动态调度配置详情
     * @param id 配置id
     * @return 配置详情
     */
    AigcDynamicConfigVO detail(Long id);

    /**
     *  动态调度配置创建
     * @param createReq 创建请求
     */
    void create(AigcDynamicConfigCreateReq createReq);

    /**
     * 动态调度配置更新
     * @param createReq 更新请求
     */
    void update(AigcDynamicConfigCreateReq createReq);

    /**
     * 动态调度配置删除
     * @param req 批量删除请求
     */
    void delete(BatchOperateReq req);

    /**
     * 动态调度配置启用
     * @param req 批量启用请求
     */
    void enable(BatchOperateReq req);

    /**
     * 动态调度配置禁用
     * @param req 批量禁用请求
     */
    void disable(BatchOperateReq req);
}
