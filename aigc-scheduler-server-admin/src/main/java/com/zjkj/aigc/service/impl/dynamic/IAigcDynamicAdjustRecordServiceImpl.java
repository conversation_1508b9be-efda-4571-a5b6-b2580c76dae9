package com.zjkj.aigc.service.impl.dynamic;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicAdjustRecordQuery;
import com.zjkj.aigc.common.vo.dynamic.AigcDynamicAdjustRecordVO;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.service.dynamic.IAigcDynamicAdjustRecordService;
import com.zjkj.aigc.service.impl.AbstractBaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@Service
@RequiredArgsConstructor
public class IAigcDynamicAdjustRecordServiceImpl extends AbstractBaseService implements IAigcDynamicAdjustRecordService {

    private final AigcDynamicAdjustRecordService aigcDynamicAdjustRecordService;

    @Override
    public Page<AigcDynamicAdjustRecordVO> page(Page<AigcDynamicAdjustRecordVO> reqPage, AigcDynamicAdjustRecordQuery query) {
        AigcDynamicAdjustRecordCondition condition = BeanUtil.copyProperties(query, AigcDynamicAdjustRecordCondition.class);
        return genericPageQuery(
                reqPage,
                condition,
                aigcDynamicAdjustRecordService::queryPage,
                AigcDynamicAdjustRecordVO.class
        );
    }

    @Override
    public AigcDynamicAdjustRecordVO detail(Long id) {
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setIncludeDetail(true);
        condition.setIds(List.of(id));
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryList(condition);
        return BeanUtil.copyProperties(CollectionUtils.firstElement(records), AigcDynamicAdjustRecordVO.class);
    }

    @Override
    public void cancel(Long id) {
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setIncludeDetail(true);
        condition.setIds(List.of(id));
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryList(condition);
        AigcDynamicAdjustRecord record = CollectionUtils.firstElement(records);
        BaseBizException.isTrue(Objects.nonNull(record), CustomErrorCode.DATA_NOT_EXISTS);
        aigcDynamicAdjustRecordService.cancelAdjust(record);
    }

    @Override
    public void rollback(Long id) {
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setIncludeDetail(true);
        condition.setIds(List.of(id));
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryList(condition);
        AigcDynamicAdjustRecord record = CollectionUtils.firstElement(records);
        BaseBizException.isTrue(Objects.nonNull(record), CustomErrorCode.DATA_NOT_EXISTS);
        boolean rollback = aigcDynamicAdjustRecordService.rollbackInstanceRecord(record, "手动回退");
        BaseBizException.isTrue(rollback, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.current.status.cannot.rollback"));
    }
}
