package com.zjkj.aigc.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.resource.ResourceGpuPlanCreateReq;
import com.zjkj.aigc.common.req.resource.ResourcePlanCreateReq;
import com.zjkj.aigc.common.req.resource.ResourcePlanQuery;
import com.zjkj.aigc.common.vo.resource.ResourcePlanPriceVO;
import com.zjkj.aigc.common.vo.resource.ResourcePlanVO;
import com.zjkj.aigc.domain.task.service.resource.ResourceGpuPlanService;
import com.zjkj.aigc.domain.task.service.resource.ResourceMidPlanService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceGpuPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceMidPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;
import com.zjkj.aigc.service.resource.ResourcePlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourcePlanServiceImpl implements ResourcePlanService {

    private final ResourceGpuPlanService resourceGpuPlanService;

    private final ResourceMidPlanService resourceMidPlanService;

    @Override
    public void createResourcePlan(ResourcePlanCreateReq createReq) {
        checkCreateResourcePlan(createReq.getMonth(), createReq.getPlatform());
        if (CollUtil.isNotEmpty(createReq.getGpuList())) {
            createReq.getGpuList().forEach(gpu -> {
                gpu.setMonth(createReq.getMonth());
                gpu.setPlatform(createReq.getPlatform());
            });
            resourceGpuPlanService.createGpuPlanBath(createReq.getGpuList());
        }
        if (Objects.nonNull(createReq.getMid())) {
            createReq.getMid().setMonth(createReq.getMonth());
            createReq.getMid().setPlatform(createReq.getPlatform());
            resourceMidPlanService.createMidPlan(createReq.getMid());
        }
    }

    /**
     * 一个月只允许添加一条规划
     *
     * @param month    规划月份
     * @param platform 平台
     */
    private void checkCreateResourcePlan(Integer month, String platform) {
        List<ResourceGpuPlan> gpuPlanList = resourceGpuPlanService.queryList(ResourceGpuPlanCondition.builder()
                .month(month)
                .platform(platform)
                .build());
        List<ResourceMidPlan> midPlanList = resourceMidPlanService.queryList(ResourceMidPlanCondition.builder()
                .month(month)
                .platform(platform)
                .build());
        if (CollUtil.isNotEmpty(gpuPlanList) || CollUtil.isNotEmpty(midPlanList)) {
            throw new BaseBizException(CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.current.month.plan.data.exists"));
        }
    }

    @Override
    public List<ResourcePlanVO> queryPlan(ResourcePlanQuery query) {
        List<ResourceGpuPlan> gpuPlanList = resourceGpuPlanService.queryList(ResourceGpuPlanCondition.builder()
                .month(query.getMonth())
                .platforms(query.getPlatforms())
                .build());
        List<ResourceMidPlan> midPlanList = resourceMidPlanService.queryList(ResourceMidPlanCondition.builder()
                .month(query.getMonth())
                .platforms(query.getPlatforms())
                .build());
        List<ResourcePlanVO> planList = new ArrayList<>();
        // 按platform分组
        Map<String, List<ResourceGpuPlan>> gpuGroup = gpuPlanList.stream()
                .collect(Collectors.groupingBy(ResourceGpuPlan::getPlatform));
        Map<String, List<ResourceMidPlan>> midGroup = midPlanList.stream()
                .collect(Collectors.groupingBy(ResourceMidPlan::getPlatform));
        if (CollUtil.isEmpty(gpuGroup) && CollUtil.isEmpty(midGroup)) {
            return planList;
        }

        Set<String> platforms = Stream.concat(gpuGroup.keySet().stream(), midGroup.keySet().stream())
                .collect(Collectors.toSet());

        for (String platform : platforms) {
            ResourcePlanVO planVO = new ResourcePlanVO();
            List<ResourceGpuPlan> resourceGpuPlanList = gpuGroup.get(platform);
            if (CollUtil.isNotEmpty(resourceGpuPlanList)) {
                List<ResourcePlanVO.Gpu> gpuList = BeanUtil.copyToList(resourceGpuPlanList, ResourcePlanVO.Gpu.class);
                planVO.setGpuList(gpuList);
            }
            List<ResourceMidPlan> midList = midGroup.get(platform);
            if (CollUtil.isNotEmpty(midList)) {
                ResourcePlanVO.Mid mid = BeanUtil.copyProperties(midList.get(0), ResourcePlanVO.Mid.class);
                planVO.setMid(mid);
            }
            if (CollUtil.isNotEmpty(planVO.getGpuList()) || Objects.nonNull(planVO.getMid())) {
                planVO.setMonth(query.getMonth());
                planVO.setPlatform(platform);
                planVO.calculate();
                planList.add(planVO);
            }
        }
        return planList;
    }

    @Override
    public void updateResourcePlan(ResourcePlanCreateReq createReq) {
        List<Long> notDelIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(createReq.getGpuList())) {
            for (ResourceGpuPlanCreateReq gpu : createReq.getGpuList()) {
                gpu.setMonth(createReq.getMonth());
                gpu.setPlatform(createReq.getPlatform());
                if (Objects.nonNull(gpu.getId())) {
                    notDelIds.add(gpu.getId());
                }
            }
        }
        //处理GPU
        resourceGpuPlanService.deleteGpuPlan(ResourceGpuPlanCondition
                .builder().month(createReq.getMonth())
                .platform(createReq.getPlatform())
                .notDelIds(notDelIds).build());
        resourceGpuPlanService.saveOrUpdateBatch(createReq.getGpuList());
        //处理中间件
        if (Objects.isNull(createReq.getMid())) {
            resourceMidPlanService.deleteMidPlan(ResourceMidPlanCondition.builder()
                    .month(createReq.getMonth()).platform(createReq.getPlatform())
                    .build());
        } else {
            resourceMidPlanService.saveOrUpdateMidPlan(createReq.getMid());
        }
    }

    @Override
    public void deleteResourcePlan(ResourcePlanQuery query) {
        resourceGpuPlanService.deleteGpuPlan(ResourceGpuPlanCondition.builder()
                .month(query.getMonth()).platform(query.getPlatform())
                .build());
        resourceMidPlanService.deleteMidPlan(ResourceMidPlanCondition.builder()
                .month(query.getMonth()).platform(query.getPlatform())
                .build());
    }

    @Override
    public List<ResourcePlanPriceVO> queryBudget(ResourcePlanQuery query) {
        List<ResourceGpuPlan> gpuPlanList = resourceGpuPlanService.queryList(ResourceGpuPlanCondition.builder()
                .month(query.getMonth())
                .platforms(query.getPlatforms())
                .build());
        List<ResourceMidPlan> midPlanList = resourceMidPlanService.queryList(ResourceMidPlanCondition.builder()
                .month(query.getMonth())
                .platform(query.getPlatform())
                .build());
        List<ResourcePlanPriceVO> priceList = new ArrayList<>(3);
        for (String platform : query.getPlatforms()) {
            BigDecimal price = new BigDecimal(0);
            if (CollUtil.isNotEmpty(gpuPlanList)) {
                price = gpuPlanList.stream()
                        .filter(gpu -> gpu.getPlatform().equals(platform))
                        .map(gpu -> {
                            if (gpu.getNorms() <= 0) {
                                return BigDecimal.ZERO;
                            }
                            //长期卡
                            int longTerm = gpu.getBusinessDemand() + gpu.getMarkDemand()
                                    + gpu.getTrainDemand() + gpu.getTestDemand();
                            int total = longTerm + gpu.getTmp() + gpu.getDevopsDemand();
                            //向上取整
                            int demandNum = (total + gpu.getNorms() - 1) / gpu.getNorms();
                            return gpu.getPrice().multiply(BigDecimal.valueOf(demandNum))
                                    .setScale(2, RoundingMode.HALF_UP);
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            if (CollUtil.isNotEmpty(midPlanList)) {
                price = price.add(midPlanList.stream()
                        .filter(mid -> mid.getPlatform().equals(platform))
                        .map(mid -> {
                            return mid.getTotalPrice();
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            ResourcePlanPriceVO planPrice = new ResourcePlanPriceVO();
            planPrice.setPlatform(platform);
            planPrice.setPrice(price);
            priceList.add(planPrice);
        }
        return priceList;
    }
}
