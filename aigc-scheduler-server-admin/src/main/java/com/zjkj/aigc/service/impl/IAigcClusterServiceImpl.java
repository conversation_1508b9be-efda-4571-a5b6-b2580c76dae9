package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.ClusterStatusEnum;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.req.cluster.AigcClusterCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterQuery;
import com.zjkj.aigc.common.vo.cluster.AigcClusterBrieflyVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterNodeVO;
import com.zjkj.aigc.common.vo.cluster.AigcClusterVO;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcClusterUsageService;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterNodeCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import com.zjkj.aigc.service.IAigcClusterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集群应用服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class IAigcClusterServiceImpl extends AbstractBaseService implements IAigcClusterService {

    private final AigcClusterService aigcClusterService;
    private final AigcGpuService aigcGpuService;
    private final AigcClusterUsageService aigcClusterUsageService;
    private final AigcClusterNodeService aigcClusterNodeService;
    private final AigcNodeInstanceService aigcNodeInstanceService;

    @Override
    public Page<AigcClusterVO> page(Page<AigcClusterVO> reqPage, AigcClusterQuery query) {
        AigcClusterCondition condition = BeanUtil.copyProperties(query, AigcClusterCondition.class);
        condition.setName(query.getKeyword());

        // 根据节点查询条件构建集群查询条件
        if (StringUtils.hasText(query.getNodeIp()) || StringUtils.hasText(query.getInstanceId())) {
            AigcClusterNodeCondition clusterNodeCondition = new AigcClusterNodeCondition()
                    .setNodeIp(query.getNodeIp())
                    .setInstanceId(query.getInstanceId());
            Set<Long> ids = aigcClusterNodeService.queryList(clusterNodeCondition)
                    .stream()
                    .map(AigcClusterNode::getClusterId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(ids)) {
                return reqPage;
            }
            condition.setIds(ids);
        }
        Page<AigcClusterVO> resultPage = genericPageQuery(
                reqPage,
                condition,
                aigcClusterService::queryPage,
                AigcClusterVO.class
        );

        if (!CollectionUtils.isEmpty(resultPage.getRecords())) {
            // 填充GPU信息
            fillSummaryGpus(resultPage.getRecords());
            // 填充GPU使用率
            fillGpuUsageRate(resultPage.getRecords());
            // 填充节点在线
            fillNodeOnline(resultPage.getRecords());
        }

        return resultPage;
    }

    /**
     * 填充节点在线状态
     * @param records 集群列表
     */
    private void fillNodeOnline(List<AigcClusterVO> records) {
        List<String> hostIps = records.stream()
                .filter(cluster -> Objects.equals(ClusterStatusEnum.ENABLE.getCode(), cluster.getStatus()))
                .flatMap(aigcCluster -> aigcCluster.getNodes().stream())
                .map(AigcClusterNodeVO::getNodeIp)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hostIps)) {
            return;
        }

        AigcNodeInstanceCondition condition = new AigcNodeInstanceCondition()
                .setNodeIps(hostIps)
                .setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode())
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        Set<String> onlineNodeIps = aigcNodeInstanceService.queryList(condition).stream()
                .map(AigcNodeInstance::getNodeIp)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(onlineNodeIps)) {
            return;
        }

        records.stream()
                .filter(cluster -> Objects.equals(ClusterStatusEnum.ENABLE.getCode(), cluster.getStatus()))
                .forEach(cluster -> cluster.getNodes().forEach(node -> {
                    if (onlineNodeIps.contains(node.getNodeIp())) {
                        node.setOnline(true);
                    }
                }));

    }

    /**
     * 填充GPU使用率
     * @param records 集群列表
     */
    private void fillGpuUsageRate(List<AigcClusterVO> records) {
        List<Long> clusterIds = records.stream()
                .map(AigcClusterVO::getId)
                .collect(Collectors.toList());

        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setDataDate(LocalDate.now())
                .setClusterIds(clusterIds);
        Map<Long, BigDecimal> gpuUsageMap = aigcClusterUsageService.queryList(condition).stream()
                .collect(Collectors.toMap(
                        AigcClusterUsage::getClusterId,
                        AigcClusterUsage::getGpuUsageRate, (v1, v2) -> v2));

        if (CollectionUtils.isEmpty(gpuUsageMap)) {
            return;
        }

        records.forEach(cluster -> {
            BigDecimal gpuUsageRate = gpuUsageMap.get(cluster.getId());
            cluster.setGpuUsageRate(gpuUsageRate);
        });
    }

    /**
     * 填充GPU节点信息
     *
     * @param records 集群列表
     */
    private void fillSummaryGpus(List<AigcClusterVO> records) {
        Map<Long, List<Long>> gpuIdMap = records.stream()
                .flatMap(aigcCluster -> aigcCluster.getNodes().stream())
                .collect(Collectors.groupingBy(
                        AigcClusterNodeVO::getClusterId,
                        Collectors.mapping(AigcClusterNodeVO::getGpuId, Collectors.toList())
                        )
                );

        // 统计每个集群的GPU使用情况
        Map<Long, Map<Long, Integer>> clusterGpuSummary = gpuIdMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, e -> e.getValue().stream()
                                .collect(Collectors.groupingBy(
                                        Function.identity(),
                                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue))
                                )
                ));

        List<Long> gpuIds = gpuIdMap.values().stream()
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, AigcGpu> gpuMap = aigcGpuService.queryByIds(gpuIds)
                .stream()
                .collect(Collectors.toMap(AigcGpu::getId, gpu -> gpu));

        records.forEach(cluster -> {
            List<AigcGpuVO> gpus = clusterGpuSummary.getOrDefault(cluster.getId(), Collections.emptyMap())
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        Long gpuId = entry.getKey();
                        Integer count = entry.getValue();
                        AigcGpuVO gpuVO = BeanUtil.copyProperties(gpuMap.get(gpuId), AigcGpuVO.class);
                        gpuVO.setCardCount(gpuVO.getCardCount() * count);
                        return gpuVO;
                    }).collect(Collectors.toList());
            cluster.setGpus(gpus);
            // 填充节点GPU信息
            cluster.getNodes().forEach(node -> {
                AigcGpu aigcGpu = gpuMap.get(node.getGpuId());
                AigcGpuVO aigcGpuVO = BeanUtil.copyProperties(aigcGpu, AigcGpuVO.class);
                node.setGpu(aigcGpuVO);
            });
        });
    }

    @Override
    public List<AigcClusterVO> list(AigcClusterQuery query) {
        AigcClusterCondition condition = BeanUtil.copyProperties(query, AigcClusterCondition.class);
        condition.setName(query.getKeyword());

        List<AigcCluster> clusterList = aigcClusterService.queryList(condition);
        return BeanUtil.copyToList(clusterList, AigcClusterVO.class);
    }

    @Override
    public AigcClusterVO detail(Long id) {
        AigcCluster cluster = aigcClusterService.queryIncludeNodeById(id);
        return BeanUtil.copyProperties(cluster, AigcClusterVO.class);
    }

    @Override
    public void create(AigcClusterCreateReq createReq) {
        aigcClusterService.createAigcCluster(createReq);
    }

    @Override
    public void update(AigcClusterCreateReq updateReq) {
        aigcClusterService.updateAigcCluster(updateReq);
    }

    @Override
    public void delete(Long id) {
        aigcClusterService.deleteAigcCluster(id);
    }

    @Override
    public void enable(Long id) {
        aigcClusterService.enableAigcCluster(id);
    }

    @Override
    public void disable(Long id) {
        aigcClusterService.disableAigcCluster(id);
    }

    @Override
    public List<AigcClusterBrieflyVO> brieflyList(AigcClusterQuery query) {
        AigcClusterCondition condition = BeanUtil.copyProperties(query, AigcClusterCondition.class);
        condition.setNoFillNodes(true);

        List<AigcCluster> clusterList = aigcClusterService.queryList(condition);
        return BeanUtil.copyToList(clusterList, AigcClusterBrieflyVO.class);
    }
}
