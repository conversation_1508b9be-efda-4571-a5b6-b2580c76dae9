package com.zjkj.aigc.service.impl.alarm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.model.AigcModelTypeEnum;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmQuery;
import com.zjkj.aigc.common.req.alarm.AigcAlarmUpdateReq;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.common.vo.TaskModelSummaryVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmChannelVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmConfigVO;
import com.zjkj.aigc.common.vo.alarm.AigcAlarmVO;
import com.zjkj.aigc.common.vo.stat.AigcAtomTaskTotalStatVO;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmChannelService;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmConfigService;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmChannelCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;
import com.zjkj.aigc.service.AigcAtomTaskStatService;
import com.zjkj.aigc.service.alarm.IAigcAlarmService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAigcAlarmServiceImpl implements IAigcAlarmService {

    private final AigcAlarmService alarmService;

    private final AigcAlarmConfigService configService;

    private final AigcAlarmChannelService channelService;

    private final AigcTaskStatDayService taskStatDayService;

    private final AigcAtomTaskStatService aigcAtomTaskStatService;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public Page<AigcAlarmVO> alarmPage(Page<AigcAlarmVO> reqPage, AigcAlarmQuery query) {
        Page<AigcAlarm> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());
        AigcAlarmCondition condition = AigcAlarmCondition.builder()
                .taskType(query.getTaskType())
                .modelName(query.getModelName())
                .alarmType(query.getAlarmType())
                .status(query.getStatus())
                .startTime(query.getStartTime())
                .endTime(query.getEndTime())
                .page(resultPage)
                .build();
        resultPage = alarmService.queryPage(condition);
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());
        List<AigcAlarmVO> voList = BeanUtil.copyToList(resultPage.getRecords(), AigcAlarmVO.class);
        reqPage.setRecords(voList);
        return reqPage;
    }

    @Override
    public AigcAlarmVO alarmDetail(Long id) {
        AigcAlarm alarm = alarmService.queryById(id);
        if (Objects.nonNull(alarm)) {
            return BeanUtil.copyProperties(alarm, AigcAlarmVO.class);
        }
        return null;
    }

    @Override
    public void updateAlarm(AigcAlarmUpdateReq req) {
        alarmService.updateAigcAlarm(req);
    }

    @Override
    public Page<AigcAlarmConfigVO> alarmConfigPage(Page<AigcAlarmConfigVO> reqPage, AigcAlarmConfigQuery query) {
        Page<AigcAlarmConfig> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());
        AigcAlarmConfigCondition condition = AigcAlarmConfigCondition.builder()
                .modelId(query.getModelId())
                .alarmType(query.getAlarmType())
                .status(query.getStatus())
                .tag(query.getTag())
                .page(resultPage)
                .build();
        resultPage = configService.queryPage(condition);
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());
        List<AigcAlarmConfigVO> voList = BeanUtil.copyToList(resultPage.getRecords(), AigcAlarmConfigVO.class);
        reqPage.setRecords(voList);
        return reqPage;
    }

    @Override
    public AigcAlarmConfigVO alarmConfigDetail(Long id) {
        AigcAlarmConfig config = configService.queryById(id);
        if (Objects.nonNull(config)) {
            return BeanUtil.copyProperties(config, AigcAlarmConfigVO.class);
        }
        return null;
    }

    @Override
    public void createAlarmConfig(AigcAlarmConfigCreateReq req) {
        configService.createAigcAlarmConfig(req);
    }

    @Override
    public void updateAlarmConfig(AigcAlarmConfigCreateReq req) {
        configService.updateAigcAlarmConfig(req);
    }

    @Override
    public void updateAlarmConfigStatus(Long id, Integer status) {
        configService.updateAigcAlarmConfigStatus(id, status);
    }

    @Override
    public void deleteAlarmConfig(Long id) {
        configService.deleteById(id);
    }

    @Override
    public List<AigcAlarmChannelVO> alarmChannelList(AigcAlarmChannelQuery query) {
        AigcAlarmChannelCondition condition = AigcAlarmChannelCondition.builder()
                .tag(query.getTag())
                .channelType(query.getChannelType())
                .build();
        List<AigcAlarmChannel> channelList = channelService.query(condition);
        if (CollUtil.isNotEmpty(channelList)) {
            return BeanUtil.copyToList(channelList, AigcAlarmChannelVO.class);
        }
        return null;
    }

    @Override
    public AigcAlarmChannelVO alarmChannelDetail(Long id) {
        AigcAlarmChannel channel = channelService.getById(id);
        if (Objects.nonNull(channel)) {
            return BeanUtil.copyProperties(channel, AigcAlarmChannelVO.class);
        }
        return null;
    }

    @Override
    public void saveAlarmChannel(AigcAlarmChannelCreateReq req) {
        channelService.saveAigcAlarmChannel(req);
    }

    @Override
    public void updateAlarmChannel(AigcAlarmChannelCreateReq req) {
        channelService.updateAigcAlarmChannel(req);
    }

    @Override
    public void deleteAlarmChannel(Long id) {
        channelService.deleteAigcAlarmChannel(id);
    }

    @Override
    public TaskModelSummaryVO getAlarmTaskStatus(Long alarmId) {
        AigcAlarm alarm = alarmService.queryById(alarmId);
        if (Objects.nonNull(alarm)) {
            TaskModelSummaryVO summary = new TaskModelSummaryVO();
            summary.setTaskType(alarm.getTaskType());
            summary.setModelName(alarm.getModelName());
            summary.setModelType(alarm.getModelType());
            if (AigcModelTypeEnum.DATA_MQ.getCode().equals(alarm.getModelType())) {
                AigcAtomTaskTotalStatVO aigcAtomTaskTotalStatVO = aigcAtomTaskStatService.aigcAtomTaskStat(alarm.getModelName());
                if (Objects.nonNull(aigcAtomTaskTotalStatVO)) {
                    summary.setWaitTaskCount(aigcAtomTaskTotalStatVO.getWaitingAmount());
                    summary.setTaskSpeed(aigcAtomTaskTotalStatVO.getTaskSpeed());
                }
            } else {
                //近3天任务数据
                LocalDate today = LocalDate.now();
                LocalDate formerDay = today.minusDays(2);
                List<AigcTaskStatDay> statDayList = taskStatDayService.listByModelStateDate(
                        AigcTaskSummaryCondition.builder()
                                .taskTypes(Set.of(alarm.getTaskType()))
                                .modelNames(Set.of(alarm.getModelName()))
                                .startDate(formerDay.toString())
                                .endDate(today.toString())
                                .build());
                if (CollUtil.isNotEmpty(statDayList)) {
                    summary.setWaitTaskCount(statDayList.get(0).getWaitingAmount());
                    Long avgElapsedTime = statDayList.get(0).getAvgElapsedTime();
                    if (Objects.nonNull(avgElapsedTime) && avgElapsedTime > 0L) {
                        BigDecimal divide = BigDecimal.valueOf(60 * 60000L)
                                .divide(BigDecimal.valueOf(avgElapsedTime), 2, RoundingMode.HALF_UP);
                        summary.setTaskSpeed(divide);
                    } else {
                        summary.setTaskSpeed(BigDecimal.ZERO);
                    }
                } else {
                    summary.setWaitTaskCount(0L);
                    summary.setTaskSpeed(BigDecimal.ZERO);
                }
            }

            // 查询模型
            long onlineInstance = 0;
            AigcModelInfo modelInfo = aigcModelInfoService.queryByTaskTypeAndModelName(summary.getTaskType(), summary.getModelName());
            if (Objects.nonNull(modelInfo)) {
                onlineInstance = modelInfo.getInstanceByEnv(EnvUtil.getCurrentEnvType());
            }

            summary.setOnlineInstance(onlineInstance);
            // 如果是数据模型，计算任务速率
            if (AigcModelTypeEnum.DATA_MQ.getCode().equals(alarm.getModelType()) && onlineInstance > 0) {
                BigDecimal taskSpeed = summary.getTaskSpeed();
                if (Objects.nonNull(taskSpeed)) {
                    summary.setTaskSpeed(taskSpeed.divide(BigDecimal.valueOf(onlineInstance), 2, RoundingMode.HALF_UP));
                }
            }

            return summary;
        }
        return null;
    }
}
