package com.zjkj.aigc.service.resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigCreateReq;
import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigQuery;
import com.zjkj.aigc.common.req.resource.ResourceBillQuery;
import com.zjkj.aigc.common.req.resource.ResourceBillTypeQuery;
import com.zjkj.aigc.common.vo.resource.ResourceBillGroupConfigVO;
import com.zjkj.aigc.common.vo.resource.ResourceBillPlatform;
import com.zjkj.aigc.common.vo.resource.ResourceBillTypeGroupVO;
import com.zjkj.aigc.common.vo.resource.ResourceBillVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
public interface IResourceBillService {
    /**
     * 费用账单平台
     *
     * @param billType 账单类型
     * @return 平台
     */
    List<ResourceBillPlatform> billPlatform(String billType);

    /**
     * 导入账单
     *
     * @param type     账单类型
     * @param platform 平台
     * @param file     文件
     * @return 数量
     */
    Integer importBill(String type, String platform, MultipartFile file) throws IOException;

    /**
     * 账单分页
     *
     * @param reqPage 分页
     * @param query   查询条件
     * @return 分页数据
     */
    Page<ResourceBillVO> page(Page<ResourceBillVO> reqPage, ResourceBillQuery query);

    /**
     * 账单总额
     *
     * @param query 查询条件
     * @return 总额
     */
    BigDecimal billTotal(ResourceBillQuery query);

    /**
     * 费用分摊信息
     *
     * @param query 查询条件
     * @return 分摊信息
     */
    ResourceBillTypeGroupVO billTypeGroup(ResourceBillTypeQuery query);

    /**
     * 费用分摊信息配置
     *
     * @param query 查询条件
     * @return 费用分摊信息配置
     */
    List<ResourceBillGroupConfigVO> queryBillGroupConfig(ResourceBillGroupConfigQuery query);

    /**
     * 费用分摊信息配置保存
     *
     * @param req 费用分摊信息配置
     */
    void saveBillGroupConfig(ResourceBillGroupConfigCreateReq createReq);

    /**
     * 预算申请发起-获取对应的钉钉审批模版编码
     * @return
     */
    String starExpendBillApprove();
}
