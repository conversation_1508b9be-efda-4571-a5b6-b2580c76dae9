package com.zjkj.aigc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.TypeUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.BaseCondition;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public abstract class AbstractBaseService {

    /**
     * 检查排序字段是否存在
     *
     * @param orders 排序字段
     * @param tClass 实体类类型
     * @param <T>    实体类类型
     */
    private <T> void checkOrder(List<OrderItem> orders, Class<T> tClass) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        // 获取实体类类型
        Type typeArgument = TypeUtil.getTypeArgument(tClass);
        orders.forEach(orderItem -> {
            // 字段是否存在
            String column = orderItem.getColumn();
            BaseBizException.isTrue(ReflectUtil.hasField((Class<?>) typeArgument, StrUtil.toCamelCase(column)), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.sort.field.not.exists", new Object[]{column}));
            orderItem.setColumn(StrUtil.toUnderlineCase(column));
        });
    }

    /**
     * 通用的分页查询方法，支持不同类型的实例查询
     *
     * @param reqPage            请求的分页参数
     * @param condition          查询条件
     * @param serviceQueryMethod 具体的服务查询方法
     * @param voClass            要转换的VO类型
     * @param <T>                原始实体类型
     * @param <V>                视图对象类型
     * @param <C>                条件对象类型
     * @return 处理后的分页结果
     */
    public <T, V, C extends BaseCondition<T>> Page<V> genericPageQuery(Page<V> reqPage, C condition, Function<C, Page<T>> serviceQueryMethod, Class<V> voClass) {
        checkOrder(reqPage.orders(), condition.getClass());
        // 创建结果分页对象
        Page<T> resultPage = Page.of(reqPage.getCurrent(), reqPage.getSize());
        resultPage.setOrders(reqPage.orders());

        // 设置分页信息
        condition.setPage(resultPage);

        // 执行查询
        resultPage = serviceQueryMethod.apply(condition);

        // 设置分页信息
        reqPage.setPages(resultPage.getPages());
        reqPage.setTotal(resultPage.getTotal());

        // 转换结果
        List<V> instanceList = BeanUtil.copyToList(resultPage.getRecords(), voClass);
        reqPage.setRecords(instanceList);
        return reqPage;
    }
}
