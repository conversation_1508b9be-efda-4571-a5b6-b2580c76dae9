package com.zjkj.aigc.service.model;

import com.zjkj.aigc.common.req.model.AigcModelConfigCreateReq;
import com.zjkj.aigc.common.vo.model.AigcModelConfigVO;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
public interface IAigcModelConfigService {

    /**
     * 根据模型id获取模型配置
     *
     * @param modelId 模型id
     * @return 模型配置
     */
    AigcModelConfigVO getByModelId(Long modelId);

    /**
     * 创建模型配置
     *
     * @param req 模型配置
     */
    void create(AigcModelConfigCreateReq req);

    /**
     * 更新模型配置
     *
     * @param req 模型配置
     */
    void update(AigcModelConfigCreateReq req);
}
