package com.zjkj.aigc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceQuery;
import com.zjkj.aigc.common.req.node.AigcNodeInstanceQuery;
import com.zjkj.aigc.common.vo.model.AigcModelInstanceVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceBrieflyVO;
import com.zjkj.aigc.common.vo.node.AigcNodeInstanceVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcInstanceService {

    /**
     * 节点实例列表
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcNodeInstanceVO> nodeInstancePage(Page<AigcNodeInstanceVO> reqPage, AigcNodeInstanceQuery query);

    /**
     * 模型实例列表
     *
     * @param reqPage 分页信息
     * @param query   查询条件
     * @return 分页数据
     */
    Page<AigcModelInstanceVO> modelInstancePage(Page<AigcModelInstanceVO> reqPage, AigcModelInstanceQuery query);

    /**
     * 节点实例简要列表
     * @return 列表信息
     */
    List<AigcNodeInstanceBrieflyVO> nodebrieflyList();

}
