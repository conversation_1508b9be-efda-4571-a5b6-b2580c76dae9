#server:
#  undertow:
#    threads:
#      io: 16
#      worker: 256
#    max-http-post-size: 104857600
#  tomcat:
#    uri-encoding: utf-8
#
#spring:
#  servlet:
#    multipart:
#      max-file-size: 100MB
#      max-request-size: 100MB
#  redis:
#    redisson:
#      config: |
#        singleServerConfig:
#          address: redis://redis.xggrydetmtdt.publicscs.bj.baidubce.com:6379
#          password: 1Qaz2Wsx3Edc
#          database: 12
#        codec: !<org.redisson.codec.JsonJacksonCodec> {}
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: **************************************************************************************************************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: ODBNy7aJscIX
#    hikari:
#      connection-test-query: SELECT 1
#      maximum-pool-size: 100
#      minimum-idle: 10
#    dynamic:
#      enabled: false
#  data:
#    mongodb:
#      uri: mongodb://aigc-dev:vMiQLl!<EMAIL>:27017/aigc-dev
#  rabbitmq:
#    host: rabbitmq-iiDMs4eR6LnxdOdw9dye.rabbitmq.bj.baidubce.com
#    port: 5672
#    username: admin
#    password: x30p%5MMqYTJoRbEegND9lZs
#    virtual-host: agic-dataset
#    publisher-confirm-type: correlated
#    publisher-confirms: true
#    publisher-returns: true
#    template:
#      mandatory: true
#
#  elasticsearch:
#    uris:  https://************:8200
#    username: superuser
#    password: 1tMP41Fux0lo%0Kc0y!ED%Vb
#    connection-timeout: 5000
#    socket-timeout: 30000
#
#mybatis-plus:
#  mapper-locations: classpath*:mapper/*.xml
#  type-aliases-package: com.zjkj.aigc.infrastructure.mybatis.aigc.entity*,;
#  type-handlers-package: com.zjkj.aigc.infrastructure.mybatis.aigc.mapper*,;
#  configuration:
#    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
#
#logging:
#  level:
#    "com.zjkj.aigc.infrastructure.mybatis.aigc.mapper.**": DEBUG
#    org.apache.ibatis: DEBUG
#    java.sql.Statement: DEBUG
#    java.sql.PreparedStatement: DEBUG
#
#ai:
#  task:
#    schedule:
#      global-config:
#        - maxRetry: 3
#          timeout: 10m
#      config-list:
#      # - taskType: aigc
#      #   maxRetry: 3
#      #   timeout: 10m
#  model:
#    deploy:
#      detail-page: https://dev-aip-admin.tiangong.tech/#/model/deploy/detailFull?id=%d
#      #ding-hook: https://oapi.dingtalk.com/robot/send?access_token=0a14a190828320048760c2c058edaef1e73deb875b890fb7403f4f8dad33f404
#    encrypt:
#      endpoint: http://**************:5000
#      callback-url: https://dev-aip.tiangong.tech/aigc-admin/callback/model/encrypt
#    alarm:
#      detail-page: https://dev-aip-admin.tiangong.tech/#/model/alarm/detail/%d
#    instance:
#      endpoint: http://zadig.tiangong.site/v1/projects/detail/%s/envs/detail/%s?envName=%s&envSource=spock&workLoadType=
#      spilt: -env-
#xxl:
#  job:
#    enabled: true
#    admin-addresses: https://base-xxljob.robotees.tech/xxl-job-admin/
#    access-token: default-token
#    appname: dev-base-aigc-scheduler-server-admin-executor
#    port: 9998
#    log-path: /data/logs/xxl-job/jobhandler
#    log-retention-days: 7
#
#dingtalk:
#  approve-code:
#    expend-bill: PROC-F2E943E5-3E45-463D-AABA-D90CAC5440EA
#
#saas:
#  sso:
#    sdk:
#      gateway:
#        enabled: true
#      admin:
#        # enabled = true，在工程中引用saas-admin-sdk-for-microservice 依赖的情况下，会注入该依赖中的 SsoTokenFilter ，将请求头 ssoToken 的值解析成 SsoUserDTO 对象，并且放入 SaasSsoContext 上下文中，该过滤器必须搭配 saas.sso.sdk.sso.public-key 的值使用，缺省为true，因为网关并不是spring mvc服务，所以sso提供的 SsoTokenFilter 不生效，需自主解析
#        enabled: true
#        url: https://dev-sso-api.robot-sew.com
#
#      sso:
#        public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtGScrVA4EXGklclyiJLsT9qwjCf0nSAjl6zi/lcJ0AXwSXPhpyd7qZrpPqvFvnTAOIdpqAyqgjGdgg0y24keWxgmxkpULHqmKsiN7yLzV7evt3UUhyoplMi3mNd4ck85LCCNHfY+gZ2IMOVQlwd1mkBcNNIAL0wp3Vw3AMtPq4MTyXpRLHd5LKwL4/P3rFdZw4EysZFFlAHA5f0jgVVgeZlLmmOSVGvubekFpRC1sY4dMP9cl57EPegtjxxHcrLwN4/8PtliLUP+PbChO0k8Memz3luL76Ya+tIDGvoGIQAN1jGswBkiweXuYzrxYDDnjbE4zi+R+jkfI3/x2AEKEwIDAQAB
#        exclude-patterns:  /callback/**
#      systems:
#        - id: 1
#          secretKey: a8c70b5edd084a4b82f305f09a359fa
#          code: AI_CSRS