package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.cluster.AigcClusterNodeCreateReq;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterNodeCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcClusterNodeDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 集群节点服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcClusterNodeServiceImpl implements AigcClusterNodeService {

    private final AigcClusterNodeDao aigcClusterNodeDao;

    @Override
    public List<AigcClusterNode> listByClusterId(Long clusterId) {
        return aigcClusterNodeDao.listByClusterId(List.of(clusterId));
    }

    @Override
    public List<AigcClusterNode> listByClusterId(List<Long> clusterIds) {
        return CollectionUtils.isEmpty(clusterIds) ? List.of() : aigcClusterNodeDao.listByClusterId(clusterIds);
    }

    /**
     * 转换请求对象为节点对象
     */
    private AigcClusterNode convertToNode(AigcClusterNodeCreateReq req, Long clusterId, String... ignoreProperties) {
        AigcClusterNode node = BeanUtil.copyProperties(req, AigcClusterNode.class, ignoreProperties);
        node.setClusterId(clusterId);
        return node;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(Long clusterId, List<AigcClusterNodeCreateReq> nodeReqs) {
        List<AigcClusterNode> nodes = nodeReqs.stream()
                .map(nodeReq -> convertToNode(nodeReq, clusterId, "id"))
                .collect(Collectors.toList());
        aigcClusterNodeDao.saveBatch(nodes);
    }

    /**
     * 检查节点IP是否重复
     * @param nodeReqs 节点请求列表
     */
    public void checkDuplicate(List<AigcClusterNodeCreateReq> nodeReqs) {
        String duplicateIps = nodeReqs.stream()
                .collect(Collectors.groupingBy(AigcClusterNodeCreateReq::getNodeIp, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.joining(StringPool.COMMA));
        BaseBizException.isTrue(!StringUtils.hasText(duplicateIps), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.node.ip.duplicate", new Object[]{duplicateIps}));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdate(AigcCluster aigcCluster, List<AigcClusterNodeCreateReq> nodeReqs) {
        // 检查节点IP是否重复
        checkDuplicate(nodeReqs);

        Long clusterId = aigcCluster.getId();
        // 获取已存在的节点
        Map<String, Long> nodeMap = StreamUtil.toMap(aigcCluster.getNodes(), AigcClusterNode::getNodeIp, AigcClusterNode::getId);
        List<AigcClusterNode> insertNodes = new ArrayList<>();
        List<AigcClusterNode> updateNodes = new ArrayList<>(nodeReqs.size());
        nodeReqs.forEach(req -> {
            // 如果请求中有ID且不在已存在的节点中，则清空ID
            if (Objects.nonNull(req.getId()) && !nodeMap.containsValue(req.getId())) {
                req.setId(null);
            }

            if (Objects.isNull(req.getId())) {
                req.setId(nodeMap.get(req.getNodeIp()));
            }

            AigcClusterNode aigcClusterNode = convertToNode(req, clusterId);
            if (Objects.nonNull(aigcClusterNode.getId())) {
                updateNodes.add(aigcClusterNode);
            } else {
                insertNodes.add(aigcClusterNode);
            }
        });

        // 执行批量操作
        if (!CollectionUtils.isEmpty(insertNodes)) {
            aigcClusterNodeDao.saveBatch(insertNodes);
            log.info("新增集群节点, clusterId:{}, size:{}", clusterId, insertNodes.size());
        }
        if (!CollectionUtils.isEmpty(updateNodes)) {
            aigcClusterNodeDao.updateBatchById(updateNodes);
            log.info("更新集群节点, clusterId:{}, size:{}", clusterId, updateNodes.size());
        }

        Set<Long> updateNodeIds = StreamUtil.mapToSet(updateNodes, AigcClusterNode::getId);
        Collection<Long> deleteIds = new ArrayList<>(nodeMap.values());
        deleteIds.removeAll(updateNodeIds);
        if (!CollectionUtils.isEmpty(deleteIds)) {
            aigcClusterNodeDao.removeByIds(deleteIds);
            log.info("删除集群节点, clusterId:{}, size:{}", clusterId, deleteIds.size());
        }
    }

    @Override
    public void removeByClusterId(Long clusterId) {
        aigcClusterNodeDao.removeByClusterId(clusterId);
    }

    @Override
    public boolean gpuIsInUsed(Long gpuId) {
        return aigcClusterNodeDao.gpuIsInUsed(gpuId);
    }

    @Override
    public List<AigcClusterNode> queryList(AigcClusterNodeCondition condition) {
        return aigcClusterNodeDao.list(condition.buildQuery());
    }

}
