package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInfoCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 模型基础信息表(AigcModelInfo)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-11 18:30:44
 */
public interface AigcModelInfoService {

    /**
     * 查询分页
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcModelInfo> queryPage(AigcModelInfoCondition condition);

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcModelInfo> queryList(AigcModelInfoCondition condition);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 模型基础信息
     */
    AigcModelInfo getById(Long id);

    /**
     * 根据任务类型和模型名称查询
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 是否存在
     */
    AigcModelInfo queryByTaskTypeAndModelName(String taskType, String modelName);

    /**
     * 保存模型基础信息
     *
     * @param req 请求参数
     */
    void saveModelInfo(AigcModelInfoCreateReq req);

    /**
     * 更新模型基础信息
     *
     * @param req 请求参数
     */
    void updateModelInfo(AigcModelInfoCreateReq req);

    /**
     * 删除模型基础信息
     *
     * @param id id
     */
    void deleteModelInfo(Long id);

    /**
     * 修改状态
     *
     * @param ids    id列表
     * @param status 状态
     */
    void changeStatus(List<Long> ids, Integer status);

    /**
     * 刷新活跃时间
     *
     * @param statMap 活跃时间
     */
    void refreshActiveTime(Map<Map.Entry<String, String>, LocalDateTime> statMap);

    /**
     * 刷新实例
     *
     * @param modelInstanceMap 实例
     */
    void refreshInstance(Map<Map.Entry<String, String>, List<AigcModelInstance>> modelInstanceMap);

    /**
     * 刷新模型平均耗时
     * @param aigcTaskStatDays 统计数据
     */
    void refreshAvgElapsedTime(List<AigcTaskStatDay> aigcTaskStatDays);

    /**
     * 批量更新模型信息
     *
     * @param updateList 更新列表
     */
    void updateBatchById(List<AigcModelInfo> updateList);
}
