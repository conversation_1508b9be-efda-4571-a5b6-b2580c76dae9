package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.gpu.AigcGpuCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcGpuCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;

import java.util.List;

/**
 * GPU服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcGpuService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcGpu> queryPage(AigcGpuCondition condition);

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcGpu> queryList(AigcGpuCondition condition);

    /**
     * 根据ID查询
     *
     * @param id GPU id
     * @return GPU信息
     */
    AigcGpu queryById(Long id);

    /**
     * 根据ID查询
     *
     * @param ids GPU id
     * @return GPU信息
     */
    List<AigcGpu> queryByIds(List<Long> ids);
    /**
     * 创建GPU
     *
     * @param createReq 创建请求
     */
    void createAigcGpu(AigcGpuCreateReq createReq);

    /**
     * 更新GPU
     *
     * @param req 更新请求
     */
    void updateAigcGpu(AigcGpuCreateReq req);

    /**
     * 删除GPU
     *
     * @param id GPU id
     */
    void deleteAigcGpu(Long id);
}
