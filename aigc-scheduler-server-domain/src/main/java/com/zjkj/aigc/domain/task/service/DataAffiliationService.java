package com.zjkj.aigc.domain.task.service;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.DataAffiliation;

import java.util.List;

/**
 * 数据所属表(DataAffiliation)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-11 15:32:42
 */
public interface DataAffiliationService {

    /**
     * 获取数据
     *
     * @param dataType 数量类型
     * @param dataIds  数据id
     * @return 列表
     */
    List<DataAffiliation> queryByDataTypeId(String dataType, List<String> dataIds);

    /**
     * 创建数据所属
     * @param dataAffiliation 数据
     */
    void create(DataAffiliation dataAffiliation);
}
