package com.zjkj.aigc.domain.task.service.alarm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;

import java.util.List;

/**
 * 模型告警配置(AigcAlarmConfig)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface AigcAlarmConfigService {
    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcAlarmConfig> queryPage(AigcAlarmConfigCondition condition);

    /**
     * 创建模型告警配置
     *
     * @param req 模型告警配置数据
     */
    void createAigcAlarmConfig(AigcAlarmConfigCreateReq req);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 详情
     */
    AigcAlarmConfig queryById(Long id);

    /**
     * 更新模型告警配置
     *
     * @param req 更新参数
     */
    void updateAigcAlarmConfig(AigcAlarmConfigCreateReq req);

    /**
     * 更新模型告警配置状态
     *
     * @param id     数据ID
     * @param status 状态
     */
    void updateAigcAlarmConfigStatus(Long id, Integer status);

    /**
     * 删除数据
     *
     * @param id 数据Id
     */
    void deleteById(Long id);
}
