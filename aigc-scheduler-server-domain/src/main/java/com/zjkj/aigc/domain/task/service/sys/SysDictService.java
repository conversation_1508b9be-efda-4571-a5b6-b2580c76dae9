package com.zjkj.aigc.domain.task.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.dict.SysDictCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;

import java.util.List;

/**
 * 字典领域服务接口
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
public interface SysDictService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<SysDict> queryPage(SysDictCondition condition);
    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<SysDict> queryList(SysDictCondition condition);
    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 字典信息
     */
    SysDict queryById(Long id);

    /**
     * 根据type查询
     *
     * @param type 类型
     * @return 字典信息
     */
    SysDict queryByType(String type);
    /**
     * 创建字典
     *
     * @param req 创建请求
     */
    void createSysDict(SysDictCreateReq req);

    /**
     * 更新字典
     *
     * @param req 更新请求
     */
    void updateSysDict(SysDictCreateReq req);

    /**
     * 删除字典
     *
     * @param id 主键
     */
    void deleteSysDict(Long id);

    /**
     * 更改状态
     *
     * @param ids    主键集合
     * @param source 源状态
     * @param target 目标状态
     */
    void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target);
}
