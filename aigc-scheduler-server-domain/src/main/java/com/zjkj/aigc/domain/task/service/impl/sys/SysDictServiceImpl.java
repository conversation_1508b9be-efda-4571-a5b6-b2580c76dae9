package com.zjkj.aigc.domain.task.service.impl.sys;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.dict.SysDictCreateReq;
import com.zjkj.aigc.domain.task.service.sys.SysDictDataService;
import com.zjkj.aigc.domain.task.service.sys.SysDictService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.sys.SysDictDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDict;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 字典领域服务实现类
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDictServiceImpl implements SysDictService {

    private final SysDictDao sysDictDao;
    private final SysDictDataService sysDictDataService;

    @Override
    public Page<SysDict> queryPage(SysDictCondition condition) {
        return sysDictDao.queryPage(condition);
    }

    @Override
    public List<SysDict> queryList(SysDictCondition condition) {
        return sysDictDao.queryList(condition);
    }

    @Override
    public SysDict queryById(Long id) {
        return sysDictDao.getById(id);
    }

    @Override
    public SysDict queryByType(String type) {
        return sysDictDao.queryByType(type);
    }

    @Override
    public void createSysDict(SysDictCreateReq req) {
        SysDict dict = queryByType(req.getType());
        BaseBizException.isTrue(Objects.isNull(dict), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.dict.type.already.exists"));

        SysDict sysDict = BeanUtil.copyProperties(req, SysDict.class, "id");
        sysDictDao.save(sysDict);
    }

    @Override
    public void updateSysDict(SysDictCreateReq req) {
        SysDict sysDict = queryById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(sysDict), CustomErrorCode.DATA_NOT_EXISTS);

        if (!Objects.equals(sysDict.getType(), req.getType())) {
            SysDict dict = queryByType(req.getType());
            BaseBizException.isTrue(Objects.isNull(dict), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.dict.type.exists"));
        }

        SysDict update = BeanUtil.copyProperties(req, SysDict.class);
        sysDictDao.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSysDict(Long id) {
        SysDict sysDict = queryById(id);
        BaseBizException.isTrue(Objects.nonNull(sysDict), CustomErrorCode.DATA_NOT_EXISTS);

        sysDictDao.delById(id);
        sysDictDataService.removeByDictId(id);
    }

    @Override
    public void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        SysDictCondition condition = new SysDictCondition();
        condition.setIds(ids);
        condition.setStatus(source.getCode());
        ids = sysDictDao.queryList(condition).stream()
                .map(SysDict::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        sysDictDao.changeStatus(SysDict::getId, ids, SysDict::getStatus, target.getCode());
    }


}
