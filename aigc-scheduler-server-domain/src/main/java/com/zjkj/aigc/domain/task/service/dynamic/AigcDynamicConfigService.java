package com.zjkj.aigc.domain.task.service.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;

import java.util.Collection;
import java.util.List;

/**
 * 动态配置表(AigcDynamicConfig)服务接口
 *
 * <AUTHOR>
 * @since 2025-04-01 15:31:17
 */
public interface AigcDynamicConfigService {

    /**
     * 获取全局配置
     *
     * @return 全局配置
     */
    AigcDynamicConfigGlobal getGlobalConfig();

    /**
     * 更新全局配置
     *
     * @param dynamicConfigGlobal 全局配置
     */
    void updateGlobalConfig(AigcDynamicConfigGlobal dynamicConfigGlobal);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcDynamicConfig> page(AigcDynamicConfigCondition condition);

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcDynamicConfig> queryList(AigcDynamicConfigCondition condition);

    /**
     * 根据ID查询
     *
     * @param id 配置ID
     * @return 配置详情
     */
    AigcDynamicConfig queryById(Long id);

    /**
     * 根据模型ID查询
     *
     * @param modelId 模型ID
     * @return 动态配置
     */
    AigcDynamicConfig queryByModelId(Long modelId);

    /**
     * 根据模型ID查询
     *
     * @param modelIds 模型ID
     * @return 动态配置
     */
    List<AigcDynamicConfig> queryByModelIds(Collection<Long> modelIds);

    /**
     * 创建动态配置
     *
     * @param createReq 创建请求
     */
    void createConfig(AigcDynamicConfigCreateReq createReq);

    /**
     * 更新动态配置
     *
     * @param createReq 更新请求
     */
    void updateConfig(AigcDynamicConfigCreateReq createReq);

    /**
     * 批量删除
     *
     * @param ids 主键列表
     */
    void batchDel(List<Long> ids);

    /**
     * 更改状态
     *
     * @param ids    主键集合
     * @param source 源状态
     * @param target 目标状态
     */
    void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target);

    /**
     * 刷新动态配置伸缩时间
     *
     * @param recordDetails 动态调整记录详情列表
     */
    void refreshScaleTime(List<AigcDynamicAdjustRecordDetail> recordDetails);
}
