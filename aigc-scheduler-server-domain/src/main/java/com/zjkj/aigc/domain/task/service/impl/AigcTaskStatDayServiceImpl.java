package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zjkj.aigc.common.dto.TaskSummaryStatDTO;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.vo.TaskSummaryStatResp;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcTaskStatDayService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskStatDayDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.TaskStatDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskStatDayServiceImpl implements AigcTaskStatDayService {

    private static final BigDecimal ONE_THOUSAND = new BigDecimal(1000);

    private final TaskStatDao taskStatDao;
    private final AigcTaskStatDayDao aigcTaskStatDayDao;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public List<TaskSummaryStatResp> summary(TaskSummaryStatCondition condition) {
        List<TaskSummaryStatDTO> list = taskStatDao.summaryStat(condition);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        Map<Map.Entry<String, String>, List<TaskSummaryStatDTO>> groupInfo = list.stream().collect(Collectors.groupingBy(stat ->
            new AbstractMap.SimpleEntry<>(stat.getModelName(), stat.getTaskType())
        ));

        List<TaskSummaryStatResp> listResp = Lists.newArrayList();
        groupInfo.forEach((key, stats) -> {
            TaskSummaryStatResp resp = new TaskSummaryStatResp();
            resp.setModelName(key.getKey());
            resp.setTaskType(key.getValue());

            stats.forEach(stat -> {
                if(TaskStateEnum.isSucc(stat.getTaskState())){
                    resp.setSuccessCount(stat.getAmount());
                    BigDecimal avgTimeSec = stat.getAvgTimeSec();
                    if (Objects.nonNull(avgTimeSec)) {
                        avgTimeSec = avgTimeSec.multiply(ONE_THOUSAND);
                        resp.setAvgTimeMs(avgTimeSec.longValue());
                    }
                }else if (TaskStateEnum.isStatFailed(stat.getTaskState())){
                    resp.setFailCount(resp.getFailCount()+stat.getAmount());
                }else if (TaskStateEnum.isCancel(stat.getTaskState())){
                    resp.setCancelCount(stat.getAmount());
                }else if (TaskStateEnum.isProcess(stat.getTaskState())){
                    resp.setRunningCount(stat.getAmount());
                }else if (TaskStateEnum.isWait(stat.getTaskState())){
                    resp.setWaitingCount(stat.getAmount());
                }
            });

            resp.setTotalCount(resp.getSuccessCount() + resp.getFailCount() + resp.getCancelCount() + resp.getRunningCount() + resp.getWaitingCount());
            listResp.add(resp);
        });

        return listResp;
    }

    @Override
    public Page<AigcTaskStatDay> pageByGroupModel(Page<AigcTaskStatDay> page, TaskStatSummaryReq query) {
        AigcTaskStatCondition condition = AigcTaskStatCondition.builder()
                .modelName(query.getModelName())
                .taskType(query.getTaskType())
                .modelType(query.getModelType())
                .startDate(query.getStartTime())
                .endDate(query.getEndTime())
                .build();
        return aigcTaskStatDayDao.pageByGroupModel(page, condition);
    }

    /**
     * 查询模型信息
     * @param summaryStatRespList 统计数据
     * @return 模型信息
     */
    public Map<Map.Entry<String, String>, AigcModelInfo> queryModelInfoMap(List<TaskSummaryStatResp> summaryStatRespList) {
        if (CollectionUtils.isEmpty(summaryStatRespList)) {
            return Maps.newHashMap();
        }

        List<String> taskTypes = Lists.newArrayList();
        List<String> modelNames = Lists.newArrayList();
        summaryStatRespList.forEach(stat -> {
            taskTypes.add(stat.getTaskType());
            modelNames.add(stat.getModelName());
        });

        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        List<AigcModelInfo> aigcModelInfos = aigcModelInfoService.queryList(condition);
        return aigcModelInfos.stream()
                .collect(Collectors.toMap(
                model -> new AbstractMap.SimpleEntry<>(model.getTaskType(), model.getModelName()),
                model -> model,
                (existingValue, newValue) -> newValue
        ));
    }

    /**
     * 获取模型类型
     *
     * @param taskType         任务类型
     * @param modelName        模型名称
     * @param aigcModelInfoMap 模型信息
     * @return 模型类型
     */
    public String getModelType(String taskType, String modelName, Map<Map.Entry<String, String>, AigcModelInfo> aigcModelInfoMap) {
        AigcModelInfo modelInfo = aigcModelInfoMap.get(new AbstractMap.SimpleEntry<>(taskType, modelName));
        return Objects.nonNull(modelInfo) ? modelInfo.getType() : null;
    }

    @Override
    public void statDay(String appId, LocalDate statDate) {
        TaskSummaryStatCondition query = new TaskSummaryStatCondition();
        query.setAppId(appId);
        query.setStartTime(LocalDateTimeUtil.formatNormal(statDate));

        LocalDateTime endOfDay = LocalDateTimeUtil.endOfDay(statDate);
        query.setEndTime(LocalDateTimeUtil.formatNormal(endOfDay));
        List<TaskSummaryStatResp> summaryStatRespList = summary(query);
        if (CollectionUtils.isEmpty(summaryStatRespList)) {
            return;
        }

        // 查询模型信息
        Map<Map.Entry<String, String>, AigcModelInfo> aigcModelInfoMap = queryModelInfoMap(summaryStatRespList);
        List<AigcTaskStatDay> existedStatDayList = aigcTaskStatDayDao.listByStateDate(statDate, appId);
        Map<Map.Entry<String, String>, TaskSummaryStatResp> summaryStatMap = summaryStatRespList.stream()
                .collect(Collectors.toMap(
                        stat -> new AbstractMap.SimpleEntry<>(stat.getTaskType(), stat.getModelName()),
                        stat -> stat,
                        (existingValue, newValue) -> newValue
                ));

        // 更新已存在的统计数据
        LocalDateTime now = LocalDateTime.now();
        existedStatDayList.forEach(stat -> {
            AbstractMap.SimpleEntry<String, String> key = new AbstractMap.SimpleEntry<>(stat.getTaskType(), stat.getModelName());
            TaskSummaryStatResp summaryStat = summaryStatMap.get(key);
            if (Objects.isNull(summaryStat)) {
                return;
            }
            fillSummary(summaryStat, stat, aigcModelInfoMap);
            stat.setRevisedTime(now);
            summaryStatMap.remove(key);
        });

        List<AigcTaskStatDay> newStatDayList = covertAigcTaskStatDay(appId,  statDate, summaryStatMap.values(), aigcModelInfoMap);
        if (!CollectionUtils.isEmpty(newStatDayList)) {
            aigcTaskStatDayDao.saveBatch(newStatDayList);
            log.info("statDay. appId:{}, statDate:{}, save size:{}", appId, statDate, newStatDayList.size());
        }

        if (!CollectionUtils.isEmpty(existedStatDayList)) {
            aigcTaskStatDayDao.updateBatchById(existedStatDayList);
            log.info("statDay. appId:{}, statDate:{}, update size:{}", appId, statDate, existedStatDayList.size());
        }
    }

    @Override
    public List<AigcTaskStatDay> sumByGroupStateDate(AigcTaskStatCondition condition) {
        return aigcTaskStatDayDao.sumByGroupStateDate(condition);
    }

    @Override
    public List<AigcTaskStatDay> sumByModel(TaskStatSummaryReq query) {
        AigcTaskStatCondition condition = AigcTaskStatCondition.builder()
                .modelName(query.getModelName())
                .taskType(query.getTaskType())
                .modelType(query.getModelType())
                .startDate(query.getStartTime())
                .endDate(query.getEndTime())
                .noGroupByModel(Boolean.TRUE)
                .build();
        return aigcTaskStatDayDao.sumByGroupModel(condition);
    }

    @Override
    public List<AigcTaskStatDay> queryIncompleteStat(String startDate, String endDate) {
        return aigcTaskStatDayDao.queryIncompleteStat(startDate, endDate);
    }

    @Override
    public void recalculateStat(AigcTaskStatDay taskStatDay) {
        String statDate = taskStatDay.getStatDate().toString();
        TaskSummaryStatCondition condition = new TaskSummaryStatCondition()
                .setAppId(taskStatDay.getAppId())
                .setTaskType(taskStatDay.getTaskType())
                .setModelName(taskStatDay.getModelName())
                .setStartTime(statDate + " 00:00:00")
                .setEndTime(statDate + " 23:59:59");
        List<TaskSummaryStatResp> summaryStatList = summary(condition);
        if (CollectionUtils.isEmpty(summaryStatList)) {
            return;
        }

        TaskSummaryStatResp summaryStatResp = summaryStatList.get(0);
        AigcTaskStatDay updateStat = new AigcTaskStatDay();
        updateStat.setId(taskStatDay.getId());

        fillSummary(summaryStatResp, updateStat, Maps.newHashMap());
        aigcTaskStatDayDao.updateById(updateStat);
    }

    @Override
    public List<AigcTaskStatDay> sumByModelType(TaskStatSummaryReq query) {
        AigcTaskStatCondition condition = AigcTaskStatCondition.builder()
                .startDate(query.getStartTime())
                .endDate(query.getEndTime())
                .build();
        return aigcTaskStatDayDao.sumByModelType(condition);
    }

    @Override
    public AigcTaskStatDay sumTotal(TaskStatSummaryReq query) {
        AigcTaskStatCondition condition = AigcTaskStatCondition.builder()
                .startDate(query.getStartTime())
                .endDate(query.getEndTime())
                .build();
        return aigcTaskStatDayDao.sumTotal(condition);
    }

    @Override
    public List<AigcTaskStatDay> listByModelStateDate(AigcTaskSummaryCondition condition) {
        return aigcTaskStatDayDao.listByModelStateDate(condition);
    }

    @Override
    public List<AigcTaskStatDay> sumByGroupModelStatDate(AigcTaskStatCondition condition) {
        return aigcTaskStatDayDao.sumByGroupModelStatDate(condition);
    }

    /**
     * 转换统计数据
     *
     * @param appId            应用id
     * @param summaryStatList  统计数据
     * @param aigcModelInfoMap 模型信息
     * @return 统计数据
     */
    public List<AigcTaskStatDay> covertAigcTaskStatDay(String appId, LocalDate statDate, Collection<TaskSummaryStatResp> summaryStatList, Map<Map.Entry<String, String>, AigcModelInfo> aigcModelInfoMap) {
        if (CollectionUtils.isEmpty(summaryStatList)) {
            return Lists.newArrayList();
        }

        return summaryStatList.stream().map(v -> {
            AigcTaskStatDay stat = new AigcTaskStatDay();
            stat.setModelName(v.getModelName());
            stat.setTaskType(v.getTaskType());
            fillSummary(v, stat, aigcModelInfoMap);
            stat.setStatDate(statDate);
            stat.setAppId(appId);
            return stat;
        }).collect(Collectors.toList());

    }

    /**
     * 填充统计数据
     *
     * @param source           原
     * @param target           目标
     * @param aigcModelInfoMap 模型信息
     */
    private void fillSummary(TaskSummaryStatResp source, AigcTaskStatDay target, Map<Map.Entry<String, String>, AigcModelInfo> aigcModelInfoMap) {
        target.setSuccessAmount(source.getSuccessCount());
        target.setFailAmount(source.getFailCount());
        target.setWaitingAmount(source.getWaitingCount());
        target.setRunningAmount(source.getRunningCount());
        target.setCancelAmount(source.getCancelCount());
        target.setTotalAmount(source.getTotalCount());
        target.setAvgElapsedTime(source.getAvgTimeMs());

        if (!StringUtils.hasText(target.getModelType()) && !CollectionUtils.isEmpty(aigcModelInfoMap)) {
            String serviceType = getModelType(target.getTaskType(), target.getModelName(), aigcModelInfoMap);
            target.setModelType(serviceType);
        }

    }
}
