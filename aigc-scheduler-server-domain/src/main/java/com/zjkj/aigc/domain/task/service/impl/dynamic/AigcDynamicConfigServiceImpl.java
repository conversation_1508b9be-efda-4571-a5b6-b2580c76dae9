package com.zjkj.aigc.domain.task.service.impl.dynamic;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.dynamic.AigcDynamicConfigCreateReq;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic.AigcDynamicConfigDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic.AigcDynamicConfigGlobalDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 动态配置表(AigcDynamicConfig)服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-01 15:31:17
 */
@Service
@RequiredArgsConstructor
public class AigcDynamicConfigServiceImpl implements AigcDynamicConfigService {

    private final AigcDynamicConfigGlobalDao dynamicConfigGlobalDao;
    private final AigcDynamicConfigDao aigcDynamicConfigDao;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public AigcDynamicConfigGlobal getGlobalConfig() {
        return dynamicConfigGlobalDao.getGlobalConfig();
    }

    @Override
    public void updateGlobalConfig(AigcDynamicConfigGlobal dynamicConfigGlobal) {
        if (Objects.nonNull(dynamicConfigGlobal.getId())) {
            dynamicConfigGlobalDao.updateById(dynamicConfigGlobal);
            return;
        }
        dynamicConfigGlobalDao.save(dynamicConfigGlobal);
    }

    @Override
    public Page<AigcDynamicConfig> page(AigcDynamicConfigCondition condition) {
        return aigcDynamicConfigDao.queryPage(condition);
    }

    @Override
    public List<AigcDynamicConfig> queryList(AigcDynamicConfigCondition condition) {
        return aigcDynamicConfigDao.queryList(condition);
    }

    @Override
    public AigcDynamicConfig queryById(Long id) {
        return aigcDynamicConfigDao.getById(id);
    }

    @Override
    public AigcDynamicConfig queryByModelId(Long modelId) {
        return aigcDynamicConfigDao.queryByModelId(modelId);
    }

    @Override
    public List<AigcDynamicConfig> queryByModelIds(Collection<Long> modelIds) {
        AigcDynamicConfigCondition condition = new AigcDynamicConfigCondition()
                .setModelIds(modelIds);
        return queryList(condition);
    }

    @Override
    public void createConfig(AigcDynamicConfigCreateReq createReq) {
        AigcModelInfo modelInfo = aigcModelInfoService.getById(createReq.getModelId());
        BaseBizException.isTrue(Objects.nonNull(modelInfo), CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.model.not.exists"));

        AigcDynamicConfig existedConfig = queryByModelId(createReq.getModelId());
        BaseBizException.isTrue(Objects.isNull(existedConfig), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.model.config.already.exists"));

        AigcDynamicConfig config = BeanUtil.copyProperties(createReq, AigcDynamicConfig.class, "id");
        aigcDynamicConfigDao.save(config);
    }

    @Override
    public void updateConfig(AigcDynamicConfigCreateReq createReq) {

        AigcDynamicConfig existedConfig = queryByModelId(createReq.getModelId());
        BaseBizException.isTrue(Objects.nonNull(existedConfig), CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.model.config.not.exists"));
        if (!CollectionUtils.isEmpty(createReq.getDeprivedModelIds())) {
            createReq.getDeprivedModelIds().remove(existedConfig.getModelId());
        }
        AigcDynamicConfig config = BeanUtil.copyProperties(createReq, AigcDynamicConfig.class, "modelId");
        aigcDynamicConfigDao.updateById(config);
    }

    @Override
    public void batchDel(List<Long> ids) {
        aigcDynamicConfigDao.removeBatchByIds(ids);
    }

    @Override
    public void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        AigcDynamicConfigCondition condition = new AigcDynamicConfigCondition()
                .setStatus(source.getCode());
        condition.setIds(ids);

        ids = queryList(condition).stream()
                .map(AigcDynamicConfig::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        aigcDynamicConfigDao.changeStatus(AigcDynamicConfig::getId, ids, AigcDynamicConfig::getStatus, target.getCode());
    }

    @Override
    public void refreshScaleTime(List<AigcDynamicAdjustRecordDetail> recordDetails) {
        if (CollectionUtils.isEmpty(recordDetails)) {
            return;
        }

        Map<Long, AigcDynamicAdjustRecordDetail> detailMap = recordDetails.stream()
                .filter(detail -> Objects.nonNull(detail.getScaleTime()))
                .collect(Collectors.groupingBy(
                        AigcDynamicAdjustRecordDetail::getModelId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(AigcDynamicAdjustRecordDetail::getScaleTime)),
                                optional -> optional.orElse(null)
                        )
                ));

        if (CollectionUtils.isEmpty(detailMap)) {
            return;
        }

        List<AigcDynamicConfig> configs = queryByModelIds(detailMap.keySet());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        List<AigcDynamicConfig> updateConfigs = new ArrayList<>(configs.size());
        configs.forEach(config -> {
            AigcDynamicAdjustRecordDetail detail = detailMap.get(config.getModelId());
            if (Objects.isNull(detail) || Objects.isNull(detail.getScaleTime())) {
                return;
            }

            if (Objects.nonNull(config.getLastScaleTime()) && config.getLastScaleTime().isAfter(detail.getScaleTime())) {
                return;
            }

            AigcDynamicConfig update = new AigcDynamicConfig()
                    .setLastScaleTime(detail.getScaleTime())
                    .setLastScaleType(detail.getScaleType());
            update.setId(config.getId());
            updateConfigs.add(update);
        });

        if (!CollectionUtils.isEmpty(updateConfigs)) {
            aigcDynamicConfigDao.updateBatchById(updateConfigs);
        }


    }
}
