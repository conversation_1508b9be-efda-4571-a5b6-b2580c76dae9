package com.zjkj.aigc.domain.task.service.approval;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.approval.GpuApplyCreateReq;
import com.zjkj.aigc.common.req.approval.GpuApplyStatusReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval.GpuApplyCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval.GpuApply;

/**
 * GPU资源申请表(GpuApply)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface GpuApplyService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<GpuApply> queryPage(GpuApplyCondition condition);

    /**
     * 创建GPU资源申请表
     *
     * @param req GPU资源申请数据
     */
    void createGpuApply(GpuApplyCreateReq req);

    /**
     * 更新GPU资源申请表
     *
     * @param req GPU资源申请数据
     */
    void updateGpuApply(GpuApplyCreateReq req);

    /**
     * 根据id查询
     *
     * @param id id
     * @param nullThrowEx 不存在是否抛出异常
     * @return 详情
     */
    GpuApply queryById(Long id, boolean nullThrowEx);

    /**
     * 更新GPU资源申请状态
     *
     * @param statusReq 更新参数
     */
    void updateGpuApplyStatus(GpuApplyStatusReq statusReq);
}
