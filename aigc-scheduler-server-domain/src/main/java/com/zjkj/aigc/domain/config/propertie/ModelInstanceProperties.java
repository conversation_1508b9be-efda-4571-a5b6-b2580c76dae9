package com.zjkj.aigc.domain.config.propertie;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/3/12
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.model.instance")
public class ModelInstanceProperties {
    /**
     * 页面地址
     */
    private String endpoint;
    /**
     * 分割符号
     */
    private String spilt;
}
