package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.ClusterStatusEnum;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.req.node.AigcNodeHeartbeat;
import com.zjkj.aigc.common.req.node.AigcNodeRegisterReq;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterNodeCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcClusterDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcNodeInstanceDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 节点实例表(AigcNodeInstance)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 18:49:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcNodeInstanceServiceImpl implements AigcNodeInstanceService {

    private final AigcNodeInstanceDao aigcNodeInstanceDao;
    private final AigcClusterDao aigcClusterDao;
    private final AigcClusterNodeService aigcClusterNodeService;

    @Override
    public AigcNodeInstance nodeRegister(AigcNodeRegisterReq register) {
        AigcNodeInstance updateInstance = BeanUtil.copyProperties(register, AigcNodeInstance.class);
        updateInstance.setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        updateInstance.setLastHeartbeatTime(LocalDateTime.now());

        // 节点是否合法
        RegisterStatusEnum.Compliance compliance = nodeCompliance(register.getNodeIp());
        updateInstance.setStatus(compliance.getCode());

        AigcNodeInstance sourceInstance = aigcNodeInstanceDao.getByHostname(register.getHostname());
        if (Objects.isNull(sourceInstance)) {
            aigcNodeInstanceDao.save(updateInstance);
        } else {
            updateInstance.setId(sourceInstance.getId());
            aigcNodeInstanceDao.updateById(updateInstance);
        }

        return updateInstance;
    }

    /**
     * 节点是否合法
     *
     * @param nodeIp 节点IP
     * @return Compliance
     */
    private RegisterStatusEnum.Compliance nodeCompliance(String nodeIp) {
        AigcClusterNodeCondition condition = new AigcClusterNodeCondition()
                .setNodeIp(nodeIp);
        List<AigcClusterNode> aigcClusterNodes = aigcClusterNodeService.queryList(condition);
        if (CollectionUtils.isEmpty(aigcClusterNodes)) {
            return RegisterStatusEnum.Compliance.ILLEGAL;
        }

        Set<Long> clusterIds = StreamUtil.mapToSet(aigcClusterNodes, AigcClusterNode::getClusterId);
        boolean match = aigcClusterDao.listByIds(clusterIds).stream()
                .anyMatch(cluster -> Objects.equals(cluster.getStatus(), ClusterStatusEnum.ENABLE.getCode()));

        return match ? RegisterStatusEnum.Compliance.LEGAL : RegisterStatusEnum.Compliance.ILLEGAL;
    }

    @Override
    public boolean nodeHeartbeat(AigcNodeHeartbeat heartbeat) {
        AigcNodeInstance nodeInstance = aigcNodeInstanceDao.getById(heartbeat.getId());
        if (Objects.isNull(nodeInstance)) {
            return false;
        }

        AigcNodeInstance updateInstance = new AigcNodeInstance()
                .setId(nodeInstance.getId())
                .setLastHeartbeatTime(LocalDateTime.now());
        if (Objects.equals(nodeInstance.getHealthStatus(), RegisterStatusEnum.HealthStatus.OFFLINE.getCode())) {
            updateInstance.setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        }

        aigcNodeInstanceDao.updateById(updateInstance);
        return Objects.equals(nodeInstance.getStatus(), RegisterStatusEnum.Compliance.LEGAL.getCode());
    }

    @Override
    public boolean isLegalNode(String nodeIp, String podNamespace) {
        if (!StringUtils.hasText(nodeIp) || !StringUtils.hasText(podNamespace)) {
            return false;
        }

        List<AigcNodeInstance> nodeInstances =  aigcNodeInstanceDao.queryByNodeIp(nodeIp);
        if (CollectionUtils.isEmpty(nodeInstances)) {
            return false;
        }

        return nodeInstances.stream()
                .filter(node -> Objects.equals(node.getStatus(), RegisterStatusEnum.Compliance.LEGAL.getCode()))
                .anyMatch(node -> node.getNamespace().contains(podNamespace));
    }

    @Override
    public Long getClusterId(String nodeIp, String podNamespace) {
        if (!StringUtils.hasText(nodeIp) || !StringUtils.hasText(podNamespace)) {
            return null;
        }

        List<AigcNodeInstance> nodeInstances =  aigcNodeInstanceDao.queryByNodeIp(nodeIp);
        if (CollectionUtils.isEmpty(nodeInstances)) {
            return null;
        }

        Optional<AigcNodeInstance> nodeInstance = nodeInstances.stream()
                .filter(node -> Objects.equals(node.getStatus(), RegisterStatusEnum.Compliance.LEGAL.getCode())
                        && node.getNamespace().contains(podNamespace))
                .findFirst();
        if (nodeInstance.isEmpty()) {
            return null;
        }

        AigcClusterNodeCondition condition = new AigcClusterNodeCondition()
                .setNodeIp(nodeInstance.get().getNodeIp());
        List<AigcClusterNode> clusterNodes = aigcClusterNodeService.queryList(condition);
        AigcClusterNode aigcClusterNode = CollectionUtils.lastElement(clusterNodes);
        if (Objects.isNull(aigcClusterNode)) {
            return null;
        }

        return aigcClusterNode.getClusterId();
    }

    @Override
    public Page<AigcNodeInstance> queryPage(AigcNodeInstanceCondition condition) {
        return aigcNodeInstanceDao.queryPage(condition);
    }

    @Override
    public List<AigcNodeInstance> queryList(AigcNodeInstanceCondition condition) {
        return aigcNodeInstanceDao.queryList(condition);
    }

    @Override
    public void changeStatusByHostIp(List<String> hostIps, RegisterStatusEnum.Compliance sourceStatus, RegisterStatusEnum.Compliance targetStatus) {
        if (CollectionUtils.isEmpty(hostIps)) {
            return;
        }

        AigcNodeInstanceCondition condition = new AigcNodeInstanceCondition()
                .setNodeIps(hostIps)
                .setStatus(sourceStatus.getCode());
        List<AigcNodeInstance> aigcNodeInstances = queryList(condition);
        if (CollectionUtils.isEmpty(aigcNodeInstances)) {
            return;
        }

        List<Long> ids = aigcNodeInstances.stream()
                .map(AigcNodeInstance::getId)
                .collect(Collectors.toList());
        aigcNodeInstanceDao.changeStatus(AigcNodeInstance::getId, ids, AigcNodeInstance::getStatus, targetStatus.getCode());
        log.info("changeStatusByHostIp() 修改节点状态. hostIps:{}, sourceStatus:{}, targetStatus:{}, ids:{}", hostIps, sourceStatus.getCode(), targetStatus.getCode(), ids);
    }

    @Override
    public void checkHealthStatus(Duration timeout) {
        AigcNodeInstanceCondition condition = new AigcNodeInstanceCondition()
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        List<AigcNodeInstance> nodeInstances = queryList(condition);
        LocalDateTime now = LocalDateTime.now();
        List<Long> ids = nodeInstances.stream()
                .filter(instance -> {
                    Duration duration = Duration.between(instance.getLastHeartbeatTime(), now);
                    return duration.compareTo(timeout) > 0;
                })
                .map(AigcNodeInstance::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        aigcNodeInstanceDao.changeStatus(AigcNodeInstance::getId, ids, AigcNodeInstance::getHealthStatus, RegisterStatusEnum.HealthStatus.OFFLINE.getCode());
        log.info("checkHealthStatus() 节点健康检查完成. 置为离线 ids:{}", ids);
    }
}
