package com.zjkj.aigc.domain.task.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigCreateReq;
import com.zjkj.aigc.domain.task.service.resource.ResourceBillGroupConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillGroupConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource.ResourceBillGroupConfigDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBillGroupConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceBillGroupConfigServiceImpl implements ResourceBillGroupConfigService {

    private final ResourceBillGroupConfigDao resourceBillGroupConfigDao;

    @Override
    public List<ResourceBillGroupConfig> queryList(ResourceBillGroupConfigCondition condition) {
        return resourceBillGroupConfigDao.list(condition.buildQuery());
    }

    @Override
    public void saveOrUpdateBatch(ResourceBillGroupConfigCreateReq req) {
        //覆盖更新，删除旧数据
        ResourceBillGroupConfigCondition condition = ResourceBillGroupConfigCondition.builder()
                .month(req.getMonth()).build();
        resourceBillGroupConfigDao.remove(condition.buildQuery());

        List<ResourceBillGroupConfig> configList = BeanUtil.copyToList(req.getRedundancyList(), ResourceBillGroupConfig.class);
        configList.forEach(config -> {
            config.setMonth(req.getMonth());
        });
        resourceBillGroupConfigDao.saveBatch(configList);
    }
}
