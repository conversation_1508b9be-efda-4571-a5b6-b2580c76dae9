package com.zjkj.aigc.domain.config.propertie;

import com.zjkj.aigc.common.constant.StringPool;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.task.batch-model")
public class BatchModelProperties {

    /**
     * 模型前缀
     */
    private String modelPrefix;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 配置列表taskType, modelName
     */
    private List<String> modelList;

    /**
     * 是否批量模型
     * @param modelName 模型名称
     * @return 是否批量模型
     */
    public boolean isBatchModel(String modelName) {
        if (!StringUtils.hasText(modelPrefix)) {
            return false;
        }
        return modelName.startsWith(modelPrefix);
    }

    /**
     *  去除前缀
     * @param modelName 模型名称
     * @return 模型名称
     */
    public String removePrefix(String modelName) {
        if (isBatchModel(modelName)) {
            return modelName.substring(modelPrefix.length());
        }
        return modelName;
    }

    /**
     * 是否批量模型
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 是否批量模型
     */
    public boolean batchOpen(String taskType, String modelName) {
        if (CollectionUtils.isEmpty(this.modelList)) {
            return false;
        }

        if (StringUtils.hasText(taskType) && StringUtils.hasText(modelName)) {
            String result = String.join(StringPool.COMMA, taskType, modelName);
            return modelList.contains(result);
        }

        return false;
    }
}
