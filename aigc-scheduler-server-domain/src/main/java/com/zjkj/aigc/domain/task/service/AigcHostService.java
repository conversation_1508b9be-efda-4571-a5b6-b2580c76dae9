package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.host.HostStatusEnum;
import com.zjkj.aigc.common.req.host.AigcHostCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcHostCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 主机表(AigcHost)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-25 14:08:02
 */
public interface AigcHostService {

    /**
     * 判断节点是否合法
     *
     * @param nodeIp 节点IP地址
     * @return 是否合法
     */
    boolean isLegalHost(String nodeIp);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcHost> queryPage(AigcHostCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcHost> queryList(AigcHostCondition condition);

    /**
     * 根据主机ip查询
     *
     * @param hostIp 主机ip
     * @return 主机信息
     */
    AigcHost queryByHostIp(String hostIp);

    /**
     * 根据id查询
     *
     * @param id 主机id
     * @return 主机信息
     */
    AigcHost queryById(Long id);

    /**
     * 创建主机
     *
     * @param req 创建主机请求
     */
    void createAigcHost(AigcHostCreateReq req);

    /**
     * 更新主机
     *
     * @param req 更新主机请求
     */
    void updateAigcHost(AigcHostCreateReq req);

    /**
     * 删除主机
     *
     * @param id 主机id
     */
    void deleteAigcHost(Long id);

    /**
     * 更改主机状态
     *
     * @param ids          主机id列表
     * @param sourceStatus 原状态
     * @param targetStatus 目标状态
     */
    void changeStatus(List<Long> ids, HostStatusEnum sourceStatus, HostStatusEnum targetStatus);
}
