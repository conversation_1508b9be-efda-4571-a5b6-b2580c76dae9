package com.zjkj.aigc.domain.config.propertie;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.model.encrypt")
public class ModelEncryptProperties {
    /**
     * 加密服务地址
     */
    private String endpoint;
    /**
     * 结果回调地址
     */
    private String callbackUrl;

}
