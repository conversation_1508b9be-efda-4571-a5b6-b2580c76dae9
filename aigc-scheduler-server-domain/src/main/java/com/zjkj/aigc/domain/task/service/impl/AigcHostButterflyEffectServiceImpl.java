package com.zjkj.aigc.domain.task.service.impl;

import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.domain.task.service.AigcHostButterflyEffectService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class AigcHostButterflyEffectServiceImpl implements AigcHostButterflyEffectService {

    private final AigcNodeInstanceService aigcNodeInstanceService;
    private final AigcModelInstanceService aigcModelInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableHost(List<String> hostIps) {
        aigcNodeInstanceService.changeStatusByHostIp(hostIps, RegisterStatusEnum.Compliance.ILLEGAL, RegisterStatusEnum.Compliance.LEGAL);
        aigcModelInstanceService.changeStatusByHostIp(hostIps, RegisterStatusEnum.Compliance.ILLEGAL, RegisterStatusEnum.Compliance.LEGAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableHost(List<String> hostIps) {
        aigcNodeInstanceService.changeStatusByHostIp(hostIps, RegisterStatusEnum.Compliance.LEGAL, RegisterStatusEnum.Compliance.ILLEGAL);
        aigcModelInstanceService.changeStatusByHostIp(hostIps, RegisterStatusEnum.Compliance.LEGAL, RegisterStatusEnum.Compliance.ILLEGAL);
    }
}
