package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.req.model.AigcModelHeartbeat;
import com.zjkj.aigc.common.req.model.AigcModelRegister;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 模型实例表(AigcModelInstance)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-25 14:07:33
 */
public interface AigcModelInstanceService {

    /**
     * 模型注册
     *
     * @param register 注册信息
     * @return AigcModelInstance
     */
    AigcModelInstance modelRegister(AigcModelRegister register);

    /**
     * 模型心跳
     *
     * @param heartbeat 心跳信息
     * @return boolean
     */
    boolean modelHeartbeat(AigcModelHeartbeat heartbeat);

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcModelInstance> queryPage(AigcModelInstanceCondition condition);

    /**
     * 列表查询
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcModelInstance> queryList(AigcModelInstanceCondition condition);

    /**
     * 根据hostIp改变状态
     *
     * @param hostIps      ip列表
     * @param sourceStatus 原状态
     * @param targetStatus 目标状态
     */
    void changeStatusByHostIp(List<String> hostIps, RegisterStatusEnum.Compliance sourceStatus, RegisterStatusEnum.Compliance targetStatus);

    /**
     * 检查健康状态
     * @param timeout 超时时间
     */
    void checkHealthStatus(Duration timeout);

    /**
     * 查询实例
     * @param condition 查询条件
     * @return 实例
     */
    Map<Map.Entry<String, String>, List<AigcModelInstance>> queryInstance(AigcModelInstanceCondition condition);

    /**
     * 填充模型ID
     * @param missModelIdInstances 缺少模型ID的实例列表
     */
    void fillModelId(List<AigcModelInstance> missModelIdInstances);
}
