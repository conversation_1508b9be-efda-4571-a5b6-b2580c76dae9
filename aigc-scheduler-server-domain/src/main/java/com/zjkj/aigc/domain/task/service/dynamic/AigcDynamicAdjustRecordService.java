package com.zjkj.aigc.domain.task.service.dynamic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;

import java.util.Collection;
import java.util.List;

/**
 * 动态调整记录表(AigcDynamicAdjustRecord)服务接口
 *
 * <AUTHOR>
 * @since 2025-04-16 11:23:06
 */
public interface AigcDynamicAdjustRecordService {
    /**
     * 分页查询动态调整记录
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcDynamicAdjustRecord> queryPage(AigcDynamicAdjustRecordCondition condition);
    /**
     * 查询动态调整记录列表
     * @param condition 查询条件
     * @return 动态调整记录列表
     */
    List<AigcDynamicAdjustRecord> queryList(AigcDynamicAdjustRecordCondition condition);
    /**
     * 查询生效效的动态调整记录
     *
     * @param modelIds 模型ID集合
     * @return 生效的动态调整记录列表
     */
    List<AigcDynamicAdjustRecord> queryEffectiveRecord(Collection<Long> modelIds);
    /**
     * 回退实例
     *
     * @param record 动态调整记录
     * @param remark 回退备注
     */
    boolean rollbackInstanceRecord(AigcDynamicAdjustRecord record, String remark);
    /**
     * 根据ID更新动态调整记录
     *
     * @param record 动态调整记录
     */
    void updateById(AigcDynamicAdjustRecord record);

    /**
     * 保存动态调整记录
     * @param adjustRecords 动态调整记录列表
     */
    void saveScaleOutRecord(List<AigcDynamicAdjustRecord> adjustRecords);

    /**
     * 更新动态调整记录状态
     * @param id 动态调整记录ID
     * @param source 源状态
     * @param target 目标状态
     * @return 是否成功
     */
    boolean changeScaleStatus(Long id, DynamicAdjustScaleStatusEnum source, DynamicAdjustScaleStatusEnum target);

    /**
     * 取消动态调整
     * @param record 动态调整记录
     */
    void cancelAdjust(AigcDynamicAdjustRecord record);
}
