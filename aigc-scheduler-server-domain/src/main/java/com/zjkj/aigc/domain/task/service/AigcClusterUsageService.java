package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;

import java.time.LocalDate;
import java.util.List;

/**
 * 集群资源利用服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcClusterUsageService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcClusterUsage> page(AigcClusterUsageCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcClusterUsage> queryList(AigcClusterUsageCondition condition);

    /**
     * 根据ID查询
     *
     * @param id 主键
     * @return 集群资源利用信息
     */
    AigcClusterUsage queryById(Long id);

    /**
     * 根据集群ID和日期查询
     *
     * @param clusterId 集群id
     * @param dataDate  日期
     * @return 集群资源利用信息
     */
    AigcClusterUsage queryByClusterIdAndDataDate(Long clusterId, LocalDate dataDate);

    /**
     * 创建集群资源利用
     *
     * @param createReq 创建请求
     */
    void createAigcClusterUsage(AigcClusterUsageCreateReq createReq);

    /**
     * 更新集群资源利用
     *
     * @param req 更新请求
     */
    void updateAigcClusterUsage(AigcClusterUsageCreateReq req);

    /**
     * 删除集群资源利用
     *
     * @param id 主键
     */
    void deleteAigcClusterUsage(Long id);

    /**
     * 按日期统计
     *
     * @param condition 查询条件
     * @return 统计数据
     */
    List<AigcClusterUsage> statByDate(AigcClusterUsageCondition condition);

    /**
     * 集群资源利用统计
     *
     * @param cluster 集群
     * @param dataDa 日期
     */
    void clusterUsageStat(AigcCluster cluster, LocalDate dataDa);
}
