package com.zjkj.aigc.domain.task.service.impl.dynamic;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicScaleTypeEnum;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.domain.config.propertie.ModelInstanceProperties;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordDetailService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicConfigService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicService;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordDetailCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.TaskStatDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicDataModel;
import com.zjkj.aigc.zadig.client.ZadigClient;
import com.zjkj.aigc.zadig.model.EnvServiceDetail;
import com.zjkj.aigc.zadig.model.ScaleServiceParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcDynamicServiceImpl implements AigcDynamicService {

    private final TaskStatDao taskStatDao;
    private final AigcModelInfoService aigcModelInfoService;
    private final AigcModelConfigService aigcModelConfigService;
    private final AigcDynamicConfigService aigcDynamicConfigService;
    private final AigcDynamicAdjustRecordService aigcDynamicAdjustRecordService;
    private final AigcDynamicAdjustRecordDetailService aigcDynamicAdjustRecordDetailService;
    private final AigcModelInstanceService aigcModelInstanceService;
    private final ModelInstanceProperties modelInstanceProperties;

    /**
     * 获取动态调度模型数据
     *
     * @param globalConfig 全局配置
     * @param statDayList  统计数据列表
     * @return 动态调度模型数据
     */
    public AigcDynamicDataModel getDynamicDataModel(AigcDynamicConfigGlobal globalConfig, List<AigcTaskStatDay> statDayList) {
        // 查询在线模型
        String currentEnvType = EnvUtil.getCurrentEnvType();
        AigcModelInfoCondition infoCondition = AigcModelInfoCondition.builder()
                .online(true)
                .envType(EnvUtil.getCurrentEnvType())
                .build();
        List<AigcModelInfo> aigcModelInfos = aigcModelInfoService.queryList(infoCondition);
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            log.info("getDynamicDataModel. no online model");
            return null;
        }

        // 转换模型统计数据
        Map<Map.Entry<String, String>, AigcModelInfo> modelInfoMap = CollStreamUtil.toIdentityMap(aigcModelInfos, v -> Map.entry(v.getTaskType(), v.getModelName()));
        List<AigcDynamicDataModel.Model> modelList = new ArrayList<>(statDayList.size());
        statDayList.forEach(statDay -> {
            AigcModelInfo modelInfo = modelInfoMap.get(Map.entry(statDay.getTaskType(), statDay.getModelName()));
            if (Objects.isNull(modelInfo)) {
                log.info("getDynamicDataModel. no modelInfo/offline, taskType: {}, modelName: {}", statDay.getTaskType(), statDay.getModelName());
                return;
            }

            Long avgElapsedTime = Objects.nonNull(statDay.getAvgElapsedTime()) ? statDay.getAvgElapsedTime() : modelInfo.getAvgElapsedTime();
            if (Objects.isNull(avgElapsedTime)) {
                log.info("getDynamicDataModel. no avgElapsedTime, taskType: {}, modelName: {}", statDay.getTaskType(), statDay.getModelName());
                return;
            }
            AigcDynamicDataModel.Model model = new AigcDynamicDataModel.Model()
                    .setModelId(modelInfo.getId())
                    .setName(modelInfo.getName())
                    .setTaskType(modelInfo.getTaskType())
                    .setModelName(modelInfo.getModelName())
                    .setInstance(modelInfo.getInstanceByEnv(currentEnvType))
                    .setUnprocessedTasks(statDay.getWaitingAmount())
                    .setAvgElapsedTime(avgElapsedTime);
            modelList.add(model);
        });

        if (CollectionUtils.isEmpty(modelList)) {
            return null;
        }

        // 模型配置信息
        List<Long> modelIds = CollStreamUtil.toList(aigcModelInfos, AigcModelInfo::getId);
        // 有效的GPU配置
        Map<Long, GpuInfo> configMap = aigcModelConfigService.getByModelIds(modelIds)
                .stream()
                .filter(v -> Objects.nonNull(v.getGpuInfo()) && Objects.nonNull(v.getGpuInfo().getMemorySize()) && v.getGpuInfo().getMemorySize() > 0)
                .collect(Collectors.toMap(AigcModelConfig::getModelId, AigcModelConfig::getGpuInfo));

        if (CollectionUtils.isEmpty(configMap)) {
            log.info("getDynamicDataModel. no gpu config. modelIds:{}", modelIds);
            return null;
        }

        // 过滤模型列表
        List<AigcDynamicDataModel.Model> filterModelList = modelList.stream()
                .filter(v -> configMap.containsKey(v.getModelId()))
                .peek(model -> {
                    GpuInfo gpuInfo = configMap.get(model.getModelId());
                    model.setGpuMemorySize(gpuInfo.getMemorySize());
                }).collect(Collectors.toList());

        Set<Long> filterModelIds = CollStreamUtil.toSet(filterModelList, AigcDynamicDataModel.Model::getModelId);
        List<Long> modelWhitelist = globalConfig.getWhitelistEnabled() ? globalConfig.getModelWhitelist() : List.of();
        // 当前分钟数
        boolean nearEnd = LocalDateTime.now().getMinute() > 45;
        // 可剥夺的模型信息
        List<AigcModelInfo> deprivedModelList = aigcModelInfos.stream()
                .filter(model -> !modelWhitelist.contains(model.getId())
                        && !filterModelIds.contains(model.getId())
                        && configMap.containsKey(model.getId())
                        && (!nearEnd || model.getPreThreshold() < 80))
                .collect(Collectors.toList());

        return new AigcDynamicDataModel()
                .setModelList(filterModelList)
                .setDeprivedModelMap(CollStreamUtil.toIdentityMap(deprivedModelList, AigcModelInfo::getId))
                .setModelConfigMap(configMap);
    }

    /**
     * 获取模型小时统计数据
     *
     * @param taskTypes     任务类型
     * @param modelNames    模型名称
     * @param localDateTime 时间
     * @param offsetHours   偏移小时数
     * @param minusDays     减去的天数
     * @return 模型小时统计数据
     */
    private Map<Map.Entry<String, String>, List<AigcDynamicPredict.TaskStat>> getGroupModelHourStat(Set<String> taskTypes, Set<String> modelNames, LocalDateTime localDateTime, long offsetHours, long minusDays) {
        LocalDateTime startTime = localDateTime.minusDays(minusDays).minusHours(offsetHours).withMinute(0).withSecond(0);
        LocalDateTime endTime = startTime.plusHours(offsetHours * 2);
        TaskSummaryStatCondition condition = new TaskSummaryStatCondition()
                .setTaskTypes(taskTypes)
                .setModelNames(modelNames)
                .setStartTime(LocalDateTimeUtil.formatNormal(startTime))
                .setEndTime(LocalDateTimeUtil.formatNormal(endTime));
        return taskStatDao.groupModelHourStat(condition)
                .stream()
                .collect(Collectors.groupingBy(
                        v -> Map.entry(v.getTaskType(), v.getModelName()),
                        Collectors.mapping(
                                v -> new AigcDynamicPredict.TaskStat(v.getTaskTimeHour(), v.getAmount()),
                                Collectors.toList()
                        )
                ));
    }

    @Override
    public void modelPreThreshold(List<AigcModelInfo> aigcModelInfos) {
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            return;
        }

        Set<String> taskTypes = CollStreamUtil.toSet(aigcModelInfos, AigcModelInfo::getTaskType);
        Set<String> modelNames = CollStreamUtil.toSet(aigcModelInfos, AigcModelInfo::getModelName);
        // 获取模型小时统计数据
        LocalDateTime dateTime = LocalDateTime.now();
        Map<Map.Entry<String, String>, List<AigcDynamicPredict.TaskStat>> modelStatMap = getGroupModelHourStat(taskTypes, modelNames, dateTime, 5L, 0L);
        Map<Map.Entry<String, String>, List<AigcDynamicPredict.TaskStat>> yesterdayModelStatMap = getGroupModelHourStat(taskTypes, modelNames, dateTime, 5L, 1L);
        Map<Map.Entry<String, String>, List<AigcDynamicPredict.TaskStat>> lastWeekModelStatMap = getGroupModelHourStat(taskTypes, modelNames, dateTime, 5L, 7L);

        // 计算模型预测负载
        String currentEnvType = EnvUtil.getCurrentEnvType();
        int predictHours = 1;
        List<AigcModelInfo> updateList = new ArrayList<>(aigcModelInfos.size());
        aigcModelInfos.forEach(model -> {
            Map.Entry<String, String> modelKey = Map.entry(model.getTaskType(), model.getModelName());
            List<AigcDynamicPredict.TaskStat> today = modelStatMap.getOrDefault(modelKey, List.of());
            List<AigcDynamicPredict.TaskStat> yesterday = yesterdayModelStatMap.getOrDefault(modelKey, List.of());
            List<AigcDynamicPredict.TaskStat> lastWeek = lastWeekModelStatMap.getOrDefault(modelKey, List.of());
            // 预测数据
            List<AigcDynamicPredict.TaskPrediction> predictions = AigcDynamicPredict.predictWithTimePatterns(today, yesterday, lastWeek, dateTime, predictHours);
            AigcDynamicPredict.TaskPrediction prediction = predictions.get(0);

            // 计算预估负载
            long calPreThreshold = model.calPreThreshold(prediction.getPreQuantity(), model.getInstanceByEnv(currentEnvType), model.getAvgElapsedTime());
            AigcModelInfo updateModel = new AigcModelInfo();
            updateModel.setId(model.getId());
            updateModel.setPreQuantity(prediction.getPreQuantity());
            updateModel.setPreThreshold(calPreThreshold);
            if (!Objects.equals(model.getPreThreshold(), updateModel.getPreThreshold())) {
                updateList.add(updateModel);
            }
        });

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcModelInfoService.updateBatchById(updateList);
            log.info("modelPreThreshold. update [{}] model preThreshold. :{}", updateList.size(), JSON.toJSONString(updateList));
        }
    }

    @Override
    public void scaleOutByTask(AigcDynamicConfigGlobal globalConfig, List<AigcTaskStatDay> statDayList) {
        AigcDynamicDataModel dynamicDataModel = getDynamicDataModel(globalConfig, statDayList);
        if (Objects.isNull(dynamicDataModel)) {
            return;
        }

        // 在线模型动态配置
        Set<Long> modelIds = CollStreamUtil.toSet(dynamicDataModel.getModelList(), AigcDynamicDataModel.Model::getModelId);
        modelIds.addAll(dynamicDataModel.getDeprivedModelMap().keySet());
        Map<Long, AigcDynamicConfig> dynamicConfigMap = CollStreamUtil.toIdentityMap(aigcDynamicConfigService.queryByModelIds(modelIds), AigcDynamicConfig::getModelId);
        dynamicDataModel.setDynamicConfigMap(dynamicConfigMap);

        // 已被剥夺资源的模型
        AigcDynamicAdjustRecordDetailCondition detailCondition = new AigcDynamicAdjustRecordDetailCondition()
                .setScaleType(DynamicScaleTypeEnum.SCALE_IN.getType())
                .setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setModelIds(modelIds);
        Set<Long> beDeprivedModelIds = CollStreamUtil.toSet(aigcDynamicAdjustRecordDetailService.queryList(detailCondition), AigcDynamicAdjustRecordDetail::getModelId);

        // 无动态配置，或涉及到夺回资
        List<AigcDynamicDataModel.Model> recaptureModelList = new ArrayList<>();
        List<AigcDynamicDataModel.Model> scaleOutModelList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        dynamicDataModel.getModelList().forEach(model -> {
            AigcDynamicConfig config = dynamicConfigMap.get(model.getModelId());
            // 无配置
            if (Objects.isNull(config)) {
                recaptureModelList.add(model);
                return;
            }

            // 检查冷却时间
            if (Objects.equals(config.getLastScaleType(), DynamicScaleTypeEnum.SCALE_OUT.getType()) && Objects.nonNull(config.getLastScaleTime())) {
                Integer cooldownMinute = config.getCooldownMinute() > 0 ? config.getCooldownMinute() : globalConfig.getCooldownMinute();
                if (config.getLastScaleTime().plusMinutes(cooldownMinute).isAfter(now)) {
                    log.info("scaleOutByTask. modelId: {}, cooldown time not reached, lastScaleTime: {}, cooldownMinute: {}",model.getModelId(), config.getLastScaleTime(), cooldownMinute);
                    return;
                }
            }

            // 检查扩容条件
            boolean shouldBeScaleOut = model.shouldBeScaleOut(config.getScaleOutMinute(), config.getScaleOutThreshold(), config.getMaxReplica());
            if (!shouldBeScaleOut) {
                log.info("scaleOutByTask. modelId: {}, should not be scale out, unprocessedTasks: {}, avgElapsedTime: {}, instance: {}, scaleOutMinute: {}, scaleOutThreshold: {}, maxReplica: {}", model.getModelId(), model.getUnprocessedTasks(), model.getAvgElapsedTime(), model.getInstance(), config.getScaleOutMinute(), config.getScaleOutThreshold(), config.getMaxReplica());
                return;
            }

            // 已被剥夺
            if (beDeprivedModelIds.contains(model.getModelId())) {
                model.setConfig(config);
                recaptureModelList.add(model);
                log.info("scaleOutByTask. modelId: {}, already deprived, need to recapture", model.getModelId());
                return;
            }

            if (Objects.equals(GeneralEnum.SWITCH.ENABLE.getCode(), config.getStatus())) {
                List<Long> deprivedModelIds = config.getDeprivedModelIds();
                // 过滤可剥夺模型(目前简化处理，只剥夺指定的模型)
                deprivedModelIds.removeIf(modelId -> !dynamicDataModel.getDeprivedModelMap().containsKey(modelId));
                if (CollectionUtils.isEmpty(deprivedModelIds)) {
                    log.info("scaleOutByTask. modelId: {}, no deprived model, scale out", model.getModelId());
                    return;
                }
                config.setDeprivedModelIds(deprivedModelIds);
                model.setConfig(config);
                scaleOutModelList.add(model);
                log.info("scaleOutByTask. modelId: {}, scale out, deprivedModelIds: {}", model.getModelId(), deprivedModelIds);
            }

        });

        // 无配置/被剥夺模型，进行夺回
        recaptureInstance(recaptureModelList);

        // 夺取扩容
        depriveInstanceResources(dynamicDataModel, scaleOutModelList);
    }

    /**
     * 夺取实例资源
     *
     * @param dataModel    动态数据模型
     * @param modelList    模型列表
     */
    private void depriveInstanceResources(AigcDynamicDataModel dataModel, List<AigcDynamicDataModel.Model> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        // 查询进行的动态记录
        AigcDynamicAdjustRecordCondition condition = new AigcDynamicAdjustRecordCondition()
                .setModelIds(CollStreamUtil.toSet(modelList, AigcDynamicDataModel.Model::getModelId))
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setScaleStatusList(DynamicAdjustScaleStatusEnum.TO_SCALE_STATUS_SET);
        Set<Long> modelIds = CollStreamUtil.toSet(aigcDynamicAdjustRecordService.queryList(condition), AigcDynamicAdjustRecord::getModelId);
        if (!CollectionUtils.isEmpty(modelIds)) {
            log.info("depriveInstanceResources. have ready record modelIds: {}", modelIds);;
            // 过滤掉正在进行的动态调整的模型
            modelList.removeIf(model -> modelIds.contains(model.getModelId()));
            if (CollectionUtils.isEmpty(modelList)) {
                log.info("depriveInstanceResources. modelIds is empty after filter");
                return;
            }
        }

        Set<Long> deprivedModelIds = modelList.stream()
                .flatMap(model -> model.getConfig().getDeprivedModelIds().stream())
                .collect(Collectors.toSet());
        // 待处理的调度调整记录
        Map<Map.Entry<Long, String>, Integer> modelReadyReplicaMap = queryModelReadyReplicas(deprivedModelIds);

        // 调整排序处理
        AigcDynamicDataModel.adjustSort(modelList);
        List<Long> scaleOutModelIds = CollStreamUtil.toList(modelList, AigcDynamicDataModel.Model::getModelId);
        log.info("depriveInstanceResources. scaleOutModelIds: {}", scaleOutModelIds);
        List<AigcDynamicAdjustRecord> adjustRecords = new ArrayList<>(modelList.size());
        modelList.forEach((model) -> {
            AigcDynamicConfig config = model.getConfig();
            List<Long> targetDeprivedModelIds = config.getDeprivedModelIds();
            targetDeprivedModelIds.removeAll(scaleOutModelIds);
            if (CollectionUtils.isEmpty(targetDeprivedModelIds)) {
                return;
            }

            // 计算扩容模型实例
            long calInstanceChange = model.calInstanceChange(model.getUnprocessedTasks(), config.getScaleOutThreshold(), model.getInstance(), model.getAvgElapsedTime());
            if (calInstanceChange <= 0 || model.getInstance() >= config.getMaxReplica()) {
                log.info("depriveInstanceResources. 扩容实例数小于0或已达到最大副本数，modelId: {}, calInstanceChange: {}, instance: {}, maxReplica: {}", model.getModelId(), calInstanceChange, model.getInstance(), config.getMaxReplica());
                return;
            }

            calInstanceChange = Math.min(config.getMaxReplica() - model.getInstance(), calInstanceChange);
            targetDeprivedModelIds.add(model.getModelId());
            AigcModelInstanceCondition instanceCondition = new AigcModelInstanceCondition()
                    .setModelIds(targetDeprivedModelIds)
                    .setEnvType(EnvUtil.getCurrentEnvType())
                    .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode())
                    .setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode());
            Map<Long, List<AigcModelInstance>> clusterModelMap = aigcModelInstanceService.queryList(instanceCondition).stream()
                    .filter(v -> Objects.nonNull(v.getClusterId()))
                    .collect(Collectors.groupingBy(AigcModelInstance::getClusterId));
            long finalCalInstanceChange = calInstanceChange;
            clusterModelMap.forEach((clusterId, instances) -> {
                // 模型实例数
                Map<Long, List<AigcModelInstance>> instanceMap = CollStreamUtil.groupByKey(instances, AigcModelInstance::getModelId);
                // 移除扩容模型本身
                List<AigcModelInstance> removed = instanceMap.remove(model.getModelId());
                targetDeprivedModelIds.remove(model.getModelId());
                if (CollectionUtils.isEmpty(removed) || CollectionUtils.isEmpty(instanceMap)) {
                    log.info("depriveInstanceResources. no removed/instanceMap, modelId: {}", model.getModelId());
                    return;
                }

                // instanceMap 排序
                Map<Long, List<AigcModelInstance>> sortedInstanceMap = new LinkedHashMap<>();
                for (Long modelId : targetDeprivedModelIds) {
                    if (instanceMap.containsKey(modelId)) {
                        sortedInstanceMap.put(modelId, instanceMap.get(modelId));
                    }
                }
                instanceMap = sortedInstanceMap;

                AigcModelInstance aigcModelInstance = removed.get(0);
                String[] podEnv = aigcModelInstance.getPodEnv(modelInstanceProperties.getSpilt());
                String serviceName = aigcModelInstance.getServiceName();
                if (Objects.isNull(podEnv) || Objects.isNull(serviceName)) {
                    log.info("depriveInstanceResources. no podEnv/serviceName, modelId: {}", model.getModelId());
                    return;
                }

                List<AigcDynamicAdjustRecordDetail> scaleInDetails = calculateCumulativeScaleIn(model, (int) finalCalInstanceChange, instanceMap, modelReadyReplicaMap, dataModel);
                if (CollectionUtils.isEmpty(scaleInDetails)) {
                    log.info("depriveInstanceResources. no scaleInDetails, modelId: {}", model.getModelId());
                    return;
                }

                //要加进去modelReadyReplicaMap
                scaleInDetails.forEach(detail -> {
                    Map.Entry<Long, String> key = Map.entry(detail.getModelId(), detail.getProjectName() + modelInstanceProperties.getSpilt() + detail.getEnvName());
                    modelReadyReplicaMap.put(key, modelReadyReplicaMap.getOrDefault(key, 0) + detail.getDiffReplica());
                });

                // 计算总内存
                int accumulatedMemory = scaleInDetails.stream()
                        .mapToInt(AigcDynamicAdjustRecordDetail::getGpuMemorySize)
                        .sum();

                // 计算扩容模型实例
                int diffInstance = (int) Math.floor((double) accumulatedMemory / model.getGpuMemorySize());
                AigcDynamicAdjustRecordDetail scaleOutDetail = new AigcDynamicAdjustRecordDetail()
                        .setModelId(model.getModelId())
                        .setModelNameZh(model.getName())
                        .setScaleType(DynamicScaleTypeEnum.SCALE_OUT.getType())
                        .setEnvName(podEnv[1])
                        .setProjectName(podEnv[0])
                        .setServiceName(serviceName)
                        .setSourceReplica(removed.size())
                        .setTargetReplica(removed.size() + diffInstance)
                        .setDiffReplica(diffInstance)
                        .setGpuMemorySize(diffInstance * model.getGpuMemorySize())
                        .setScaleStatus(DynamicAdjustScaleStatusEnum.PENDING.getStatus())
                        .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus());
                scaleInDetails.add(scaleOutDetail);
                AigcDynamicAdjustRecord adjustRecord = new AigcDynamicAdjustRecord()
                        .setDynamicConfigId(model.getConfig().getId())
                        .setModelId(model.getModelId())
                        .setModelNameZh(model.getName())
                        .setScaleType(DynamicScaleTypeEnum.SCALE_OUT.getType())
                        .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                        .setScaleStatus(DynamicAdjustScaleStatusEnum.PENDING.getStatus())
                        .setDynamicConfigSnapshot(JSON.toJSONString(model.getConfig().buildSnapshot()))
                        .setRecordDetails(scaleInDetails);
                adjustRecords.add(adjustRecord);
            });
        });

        if (!CollectionUtils.isEmpty(adjustRecords)) {
            aigcDynamicAdjustRecordService.saveScaleOutRecord(adjustRecords);
            log.info("depriveInstanceResources. save [{}] scale out record", adjustRecords.size());
        }
    }

    /**
     * 计算扩容模型的剥夺实例
     *
     * @param model                模型
     * @param calInstanceChange    扩容模型实例数
     * @param instanceMap          模型实例列表
     * @param modelReadyReplicaMap 模型待处理副本数
     * @param dataModel            动态数据模型
     * @return 剥夺实例列表
     */
    List<AigcDynamicAdjustRecordDetail> calculateCumulativeScaleIn(AigcDynamicDataModel.Model model, int calInstanceChange,
                                                                   Map<Long, List<AigcModelInstance>> instanceMap, Map<Map.Entry<Long, String>, Integer> modelReadyReplicaMap,
                                                                   AigcDynamicDataModel dataModel) {
        // 扩容模型的单实例GPU大小
        Integer gpuMemorySize = model.getGpuMemorySize();
        // GPU内存总量
        int requiredMemory = gpuMemorySize * calInstanceChange;

        int accumulatedMemory = 0;
        // 计算可剥夺的模型
        List<AigcDynamicAdjustRecordDetail> finalDetails = new ArrayList<>(instanceMap.size());
        List<AigcDynamicAdjustRecordDetail> currentBatch = new ArrayList<>();

        // 临时存储当前批次的内存累计
        int currentBatchMemory = 0;
        for (Map.Entry<Long, List<AigcModelInstance>> entry : instanceMap.entrySet()) {
            Long modelId = entry.getKey();
            List<AigcModelInstance> instanceList = entry.getValue();
            if (accumulatedMemory >= requiredMemory) {
                break;
            }

            AigcModelInstance modelInstance = instanceList.get(0);
            String[] podEnv = modelInstance.getPodEnv(modelInstanceProperties.getSpilt());
            String serviceName = modelInstance.getServiceName();
            if (Objects.isNull(podEnv) || Objects.isNull(serviceName)) {
                continue;
            }

            // 已被剥夺待处理的实例数
            Integer replicas = modelReadyReplicaMap.getOrDefault(Map.entry(modelId, modelInstance.getPodName()), 0);
            AigcModelInfo modelInfo = dataModel.getDeprivedModelMap().get(modelId);
            long currentInstance = modelInfo.getInstanceByEnv(EnvUtil.getCurrentEnvType());
            if (currentInstance <= 1 || instanceList.size() <= 1) {
                continue;
            }

            // 最小副本数
            Integer minReplica = 1;
            AigcDynamicConfig config = dataModel.getDynamicConfigMap().get(modelId);
            if (Objects.nonNull(config)) {
                minReplica = config.getMinReplica();
            }

            // 剩余实例数=现有实例数-待处理的实例数
            long remain = currentInstance - replicas;
            if (remain <= minReplica) {
                continue;
            }

            // 可支配
            long free = remain - minReplica;
            if (free <= 0) {
                continue;
            }

            int diffInstance = Math.min(instanceList.size() - replicas - 1, (int) free);
            if (diffInstance <= 0) {
                continue;
            }

            GpuInfo gpuInfo = dataModel.getModelConfigMap().get(modelId);
            if (Objects.isNull(gpuInfo)) {
                continue;
            }

            // 当前模型可提供的内存大小
            int availableMemory = gpuInfo.getMemorySize() * diffInstance;

            // 创建当前模型的调整记录
            AigcDynamicAdjustRecordDetail potentialDetail = new AigcDynamicAdjustRecordDetail()
                    .setModelId(modelId)
                    .setModelNameZh(modelInfo.getName())
                    .setScaleType(DynamicScaleTypeEnum.SCALE_IN.getType())
                    .setEnvName(podEnv[1])
                    .setProjectName(podEnv[0])
                    .setServiceName(serviceName)
                    .setSourceReplica(instanceList.size() - replicas)
                    .setTargetReplica(instanceList.size() - replicas - diffInstance)
                    .setDiffReplica(diffInstance)
                    .setGpuMemorySize(availableMemory)
                    .setScaleStatus(DynamicAdjustScaleStatusEnum.PENDING.getStatus())
                    .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus());

            // 累计计算内存
            currentBatchMemory += availableMemory;
            currentBatch.add(potentialDetail);

            // 检查当前批次是否满足一个完整实例的内存需求
            if (currentBatchMemory >= gpuMemorySize) {
                // 累计内存足够至少一个实例，添加到最终列表
                finalDetails.addAll(currentBatch);

                // 更新总累计内存
                accumulatedMemory += currentBatchMemory;

                // 重置当前批次
                currentBatch.clear();
                currentBatchMemory = 0;
            }
        }

        if (currentBatchMemory >= gpuMemorySize && accumulatedMemory < requiredMemory) {
            finalDetails.addAll(currentBatch);
        }

        return finalDetails;
    }

    /**
     * 查询模型待处理的副本数
     * @param modeIds 模型id
     * @return Map<Map.Entry<模型id, 命名空间>, 副本数>
     */
    private Map<Map.Entry<Long, String>, Integer> queryModelReadyReplicas(Collection<Long> modeIds) {
        AigcDynamicAdjustRecordDetailCondition detailCondition = new AigcDynamicAdjustRecordDetailCondition()
                .setModelIds(modeIds)
                .setScaleType(DynamicScaleTypeEnum.SCALE_IN.getType())
                .setScaleStatusList(DynamicAdjustScaleStatusEnum.TO_SCALE_STATUS_SET)
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus());
        return aigcDynamicAdjustRecordDetailService.queryList(detailCondition).stream()
                .collect(Collectors.groupingBy(
                        v -> Map.entry(v.getModelId(), v.getProjectName() + modelInstanceProperties.getSpilt() + v.getEnvName()),
                        Collectors.summingInt(AigcDynamicAdjustRecordDetail::getDiffReplica)
                ));
    }

    @Override
    public void scaleInstance(ZadigClient zadigClient, AigcDynamicAdjustRecord record) {
        if (!DynamicAdjustScaleStatusEnum.TO_SCALE_STATUS_SET.contains(record.getScaleStatus()) || CollectionUtils.isEmpty(record.getRecordDetails())) {
            return;
        }

        List<AigcDynamicAdjustRecordDetail> recordDetails = record.getRecordDetails();
        if (Objects.equals(record.getScaleStatus(), DynamicAdjustScaleStatusEnum.PENDING.getStatus())) {
            record.setScaleStatus(DynamicAdjustScaleStatusEnum.SCALING.getStatus());
            boolean changedScaleStatus = aigcDynamicAdjustRecordService.changeScaleStatus(record.getId(), DynamicAdjustScaleStatusEnum.PENDING, DynamicAdjustScaleStatusEnum.SCALING);
            if (!changedScaleStatus) {
                log.info("scaleInstance. change scale status failed. recordId: {}, scaleStatus: {}", record.getId(), record.getScaleStatus());
                return;
            }
        }

        // 先缩容
        recordDetails.stream()
                .filter(v -> Objects.equals(v.getScaleType(), DynamicScaleTypeEnum.SCALE_IN.getType()))
                .forEach(detail -> adjustInstance(zadigClient, detail, false));

        // 检查全部缩容完成
        boolean canScaleOut = recordDetails.stream()
                .filter(v -> Objects.equals(v.getScaleType(), DynamicScaleTypeEnum.SCALE_IN.getType()))
                .allMatch(v -> Objects.equals(v.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus()));
        if (canScaleOut) {
            //进行扩容
            recordDetails.stream()
                    .filter(v -> Objects.equals(v.getScaleType(), DynamicScaleTypeEnum.SCALE_OUT.getType()))
                    .forEach(detail -> adjustInstance(zadigClient, detail, true));
        }

        // 检查是否有失败
        boolean anyFailed = recordDetails.stream()
                .anyMatch(v -> Objects.equals(v.getScaleStatus(), DynamicAdjustScaleStatusEnum.FAILED.getStatus()));
        if (anyFailed) {
            // 伸缩成功的进行回退
            recordDetails.stream()
                    .filter(v -> Objects.equals(v.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus()))
                    .forEach(v -> adjustRollbackInstance(zadigClient, v));
        }

        // 更新记录状态
        if (anyFailed) {
            record.setScaleStatus(DynamicAdjustScaleStatusEnum.FAILED.getStatus());
            record.setStatus(DynamicAdjustStatusEnum.END.getStatus());
            record.setMessage("部分实例伸缩失败");
        } else {
            boolean allSuccess = recordDetails.stream()
                    .allMatch(v -> Objects.equals(v.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus()));
            if (allSuccess) {
                record.setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus());
                if (Objects.equals(record.getScaleType(), DynamicScaleTypeEnum.SCALE_IN.getType())) {
                    record.setStatus(DynamicAdjustStatusEnum.END.getStatus());
                }
            }
        }

        if (Objects.equals(record.getStatus(), DynamicAdjustStatusEnum.END.getStatus())) {
            recordDetails.forEach(recordDetail -> recordDetail.setStatus(DynamicAdjustStatusEnum.END.getStatus()));
        }

        aigcDynamicAdjustRecordDetailService.updateBatchById(recordDetails);
        aigcDynamicAdjustRecordService.updateById(record);

        // 更新动态配置
        aigcDynamicConfigService.refreshScaleTime(recordDetails);
    }

    @Override
    public void scaleInByLowThresholdDeprived(Map<Map.Entry<String, String>, Long> statModeMap, List<AigcDynamicAdjustRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<Long> modelIds = CollStreamUtil.toList(records, AigcDynamicAdjustRecord::getModelId);
        Map<Long, AigcDynamicConfig> configMap = CollStreamUtil.toIdentityMap(aigcDynamicConfigService.queryByModelIds(modelIds), AigcDynamicConfig::getId);
        AigcModelInfoCondition infoCondition = AigcModelInfoCondition.builder()
                .idList(modelIds)
                .build();
        Map<Long, AigcModelInfo> modelInfoMap = CollStreamUtil.toIdentityMap(aigcModelInfoService.queryList(infoCondition), AigcModelInfo::getId);
        List<AigcDynamicAdjustRecord> rollbackList = new ArrayList<>();
        records.forEach(record -> {
            if (!Objects.equals(record.getStatus(), DynamicAdjustStatusEnum.EFFECTIVE.getStatus())) {
                return;
            }

            AigcModelInfo modelInfo = modelInfoMap.get(record.getModelId());
            if (Objects.isNull(modelInfo)) {
                rollbackList.add(record);
                return;
            }

            Optional<AigcDynamicAdjustRecordDetail> recordDetail = record.getRecordDetails().stream()
                    .filter(v -> Objects.equals(v.getModelId(), record.getModelId())
                            && Objects.equals(v.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())
                    ).findFirst();
            if (recordDetail.isEmpty()) {
                rollbackList.add(record);
                return;
            }

            AigcDynamicAdjustRecordDetail detail = recordDetail.get();
            AigcDynamicConfig config = configMap.get(record.getModelId());
            if (Objects.isNull(config)) {
                config = JSON.parseObject(record.getDynamicConfigSnapshot(), AigcDynamicConfig.class);
            }

            Long unprocessedTasks = Convert.convert(Long.class, statModeMap.getOrDefault(Map.entry(modelInfo.getTaskType(), modelInfo.getModelName()), 0L));
            long threshold = modelInfo.calPreThreshold(unprocessedTasks, config.getScaleOutMinute(), modelInfo.getInstanceByEnv(EnvUtil.getCurrentEnvType()), modelInfo.getAvgElapsedTime());
            if (threshold >= config.getScaleOutThreshold()) {
                log.info("scaleInByLowThresholdDeprived. modelId: {}, scaleIn threshold not reached, unprocessedTasks: {}, threshold: {}", modelInfo.getId(), unprocessedTasks, threshold);
                return;
            }

            LocalDateTime startTime = detail.getScaleTime();
            LocalDateTime endTime = startTime.plusMinutes(config.getScaleInMinute());
            LocalDateTime now = LocalDateTime.now();
            if (endTime.isAfter(now)) {
                log.info("scaleInByLowThresholdDeprived. modelId: {}, scaleIn time not reached, endTime: {}, now: {}", modelInfo.getId(), endTime, now);
                return;
            }

            LocalDateTime nowMinusDateTime = now.minusMinutes(config.getScaleInMinute());
            if (nowMinusDateTime.isAfter(endTime)) {
                startTime = nowMinusDateTime;
                endTime = now;
            }

            TaskSummaryStatCondition statCondition = new TaskSummaryStatCondition()
                    .setTaskType(modelInfo.getTaskType())
                    .setModelName(modelInfo.getModelName())
                    .setStartTime(LocalDateTimeUtil.formatNormal(startTime))
                    .setEndTime(LocalDateTimeUtil.formatNormal(endTime));
            unprocessedTasks =  taskStatDao.statRangeModelTasks(statCondition);
            threshold = modelInfo.calPreThreshold(unprocessedTasks, modelInfo.getInstanceByEnv(EnvUtil.getCurrentEnvType()), modelInfo.getAvgElapsedTime());
            if (threshold <= config.getScaleInThreshold()) {
                rollbackList.add(record);
            }
        });

        rollbackList.forEach(record -> {
            boolean rollback = aigcDynamicAdjustRecordService.rollbackInstanceRecord(record, "实例低负载释放");
            log.info("scaleInByLowThresholdDeprived. rollback recordId: {}, result: {}", record.getId(), rollback);
        });

    }

    /**
     * 实例回退
     *
     * @param zadigClient ZadigClient
     * @param detail      伸缩记录明细
     */
    private void adjustRollbackInstance(ZadigClient zadigClient, AigcDynamicAdjustRecordDetail detail) {
        if (!Objects.equals(detail.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())) {
            return;
        }

        EnvServiceDetail envServiceDetail = zadigClient.getEnvServiceDetail(detail.getProjectName(), detail.getEnvName(), detail.getServiceName());
        int replicas = envServiceDetail.getTotalReplicas();
        int targetReplicas = Objects.equals(detail.getScaleType(), DynamicScaleTypeEnum.SCALE_OUT.getType())
                ? replicas - detail.getDiffReplica()
                : replicas + detail.getDiffReplica();
        if (targetReplicas > 0) {
            ScaleServiceParam param = new ScaleServiceParam()
                    .setProjectName(detail.getProjectName())
                    .setEnvName(detail.getEnvName())
                    .setName(detail.getServiceName())
                    .setNumber(targetReplicas);
            zadigClient.scaleNew(param.getEnvName(), param.getName(), param);
        }
        detail.setScaleTime(LocalDateTime.now());
        detail.setScaleStatus(DynamicAdjustScaleStatusEnum.ROLLBACK.getStatus());
    }

    /**
     * 调整实例副本数
     *
     * @param zadigClient ZadigClient
     * @param detail      伸缩记录明细
     * @param isScaleOut  是否为扩容操作
     */
    private void adjustInstance(ZadigClient zadigClient, AigcDynamicAdjustRecordDetail detail, boolean isScaleOut) {
        if (Objects.equals(detail.getScaleStatus(), DynamicAdjustScaleStatusEnum.PENDING.getStatus())) {
            try {
                EnvServiceDetail envServiceDetail = zadigClient.getEnvServiceDetail(detail.getProjectName(), detail.getEnvName(), detail.getServiceName());
                int replicas = envServiceDetail.getTotalReplicas();
                int targetReplicas = isScaleOut
                        ? replicas + detail.getDiffReplica()
                        : replicas - detail.getDiffReplica();

                detail.setRevisedTime(LocalDateTime.now());
                if (!isScaleOut && replicas <= detail.getDiffReplica()) {
                    detail.setScaleStatus(DynamicAdjustScaleStatusEnum.FAILED.getStatus());
                    detail.setRemark(String.format("无法缩容. 当前副本数[%d]，小于等于减少的副本数[%d]", replicas, detail.getDiffReplica()));
                    return;
                }

                // 调整实例数
                ScaleServiceParam param = new ScaleServiceParam()
                        .setProjectName(detail.getProjectName())
                        .setEnvName(detail.getEnvName())
                        .setName(detail.getServiceName())
                        .setNumber(targetReplicas);
                zadigClient.scaleNew(param.getEnvName(), param.getName(), param);
                detail.setScaleStatus(DynamicAdjustScaleStatusEnum.SCALING.getStatus());
                detail.setScaleTime(LocalDateTime.now());
                if (detail.getRetryCount() > 0 && StringUtils.hasText(detail.getRemark())) {
                    detail.setRemark(StringPool.EMPTY);
                }
                if (!Objects.equals(detail.getSourceReplica(), replicas)) {
                    String remark = String.format("原副本数[%d]-实际副本数[%d]. 原目标副本数[%d]-实际目标副本数[%d]", detail.getSourceReplica(), replicas, detail.getTargetReplica(), param.getNumber());
                    detail.setSourceReplica(replicas);
                    detail.setTargetReplica(param.getNumber());
                    detail.setRemark(remark);
                } else if (detail.getRetryCount() > 0 && StringUtils.hasText(detail.getRemark())) {
                    detail.setRemark(StringPool.EMPTY);
                }
            } catch (Exception ex) {
                detail.setRetryCount(detail.getRetryCount() + 1);
                detail.setRemark(StrUtil.sub(ex.getMessage(), 0,200));
                if (detail.getRetryCount() > 3) {
                    detail.setScaleStatus(DynamicAdjustScaleStatusEnum.FAILED.getStatus());
                }
            }
        }

        if (Objects.equals(detail.getScaleStatus(), DynamicAdjustScaleStatusEnum.SCALING.getStatus())) {
            // 核对副本数
            EnvServiceDetail envServiceDetail = zadigClient.getEnvServiceDetail(detail.getProjectName(), detail.getEnvName(), detail.getServiceName());
            if (envServiceDetail.getTotalReplicas() == detail.getTargetReplica()) {
                detail.setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus());
            } else {
                detail.setScaleStatus(DynamicAdjustScaleStatusEnum.FAILED.getStatus());
                detail.setRemark(String.format("%s失败，当前副本数[%d]，目标副本数[%d]", isScaleOut ? "扩容" : "缩容", envServiceDetail.getTotalReplicas(), detail.getTargetReplica()));
            }
            detail.setRevisedTime(LocalDateTime.now());
        }
    }

    /**
     * 实例夺回
     * @param modelList 模型列表
     */
    private void recaptureInstance(List<AigcDynamicDataModel.Model> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }

        List<Long> modelIds = CollStreamUtil.toList(modelList, AigcDynamicDataModel.Model::getModelId);
        List<AigcDynamicAdjustRecord> records = aigcDynamicAdjustRecordService.queryEffectiveRecord(modelIds);
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Map<Long, List<Map.Entry<Long, Integer>>> modelReplicaMap = new HashMap<>();
        records.forEach(record ->
                record.getRecordDetails()
                        .stream()
                        .filter(v -> Objects.equals(v.getScaleType(), DynamicScaleTypeEnum.SCALE_IN.getType()))
                        .forEach(recordDetail ->
                        modelReplicaMap.computeIfAbsent(recordDetail.getModelId(), k -> new ArrayList<>())
                                .add(Map.entry(recordDetail.getAdjustRecordId(), recordDetail.getDiffReplica()))
                )
        );

        Set<Long> rollbackRecordIds = new HashSet<>();
        for (AigcDynamicDataModel.Model model : modelList) {
            List<Map.Entry<Long, Integer>> entries = modelReplicaMap.get(model.getModelId());
            if (CollectionUtils.isEmpty(entries)) {
                continue;
            }

            Integer targetThreshold = Objects.nonNull(model.getConfig()) ? model.getConfig().getScaleOutThreshold() : 80;
            long instanceChange = model.calInstanceChange(model.getUnprocessedTasks(), targetThreshold, model.getInstance(), model.getAvgElapsedTime());
            if (instanceChange > 0) {
                for (Map.Entry<Long, Integer> entry : entries) {
                    if (instanceChange <= 0) {
                        break;
                    }
                    rollbackRecordIds.add(entry.getKey());
                    instanceChange -= entry.getValue();
                }
            } else {
                // 直接回滚
                rollbackRecordIds.addAll(entries.stream()
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet()));
            }
        }

        //实例回退
        records.stream()
                .filter(record -> rollbackRecordIds.contains(record.getId()))
                .forEach(record -> {
                    boolean rollback = aigcDynamicAdjustRecordService.rollbackInstanceRecord(record, "实例夺回释放");
                    log.info("recaptureInstance. rollback recordId: {}, result: {}", record.getId(), rollback);
                });
    }
}
