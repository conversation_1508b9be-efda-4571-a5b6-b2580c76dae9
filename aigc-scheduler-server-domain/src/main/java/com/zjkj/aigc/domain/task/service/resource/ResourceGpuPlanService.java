package com.zjkj.aigc.domain.task.service.resource;

import com.zjkj.aigc.common.req.resource.ResourceGpuPlanCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceGpuPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;

import java.util.List;

/**
 * GPU资源规划表(ResourceGpuPlan)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface ResourceGpuPlanService {

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<ResourceGpuPlan> queryList(ResourceGpuPlanCondition condition);

    /**
     * 批量创建GPU资源规划表
     *
     * @param reqList GPU资源规划数据
     */
    void createGpuPlanBath(List<ResourceGpuPlanCreateReq> reqList);

    /**
     * 批量更新GPU资源规划表
     *
     * @param reqList GPU资源规划数据
     */
    void saveOrUpdateBatch(List<ResourceGpuPlanCreateReq> reqList);

    /**
     * 条件删除GPU资源规划表
     *
     * @param month
     * @param platform
     */
    void deleteGpuPlan(ResourceGpuPlanCondition condition);
}
