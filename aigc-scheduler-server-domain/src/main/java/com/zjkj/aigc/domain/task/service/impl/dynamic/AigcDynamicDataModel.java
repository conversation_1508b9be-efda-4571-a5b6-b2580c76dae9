package com.zjkj.aigc.domain.task.service.impl.dynamic;

import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
@Data
@Accessors(chain = true)
public class AigcDynamicDataModel {

    private List<Model> modelList;

    private List<AigcModelInfo> onlineModelInfoList;

    private Map<Long, GpuInfo> modelConfigMap;

    @Data
    @Accessors(chain = true)
    public static class Model {
        /**
         * 模型ID
         */
        private Long modelId;
        /**
         * 任务类型
         */
        private String taskType;
        /**
         * 模型名称
         */
        private String modelName;
        /**
         * 未处理任务数
         */
        private Long unprocessedTasks;
        /**
         * 平均耗时ms
         */
        private Long avgElapsedTime;
        /**
         * 在线实例
         */
        private long instance;
        /**
         * GPU内存大小
         */
        private Integer gpuMemorySize;
        /**
         * 优先级
         */
        private Integer priority;

        /**
         * 是否扩容
         *
         * @param scaleOutMinute    扩容维度:分钟数
         * @param scaleOutThreshold 扩容负载: 阈值1-100%
         * @param maxReplica        最大副本数
         * @return 是否扩容
         */
        public boolean shouldBeScaleOut(Integer scaleOutMinute, Integer scaleOutThreshold, Integer maxReplica) {
            if (instance >= maxReplica) {
                return false;
            }
            // 单个实例可处理的任务数
            long processCount = (scaleOutMinute * 60 * 1000) / avgElapsedTime;
            // 计算阈值任务数
            long threshold = (processCount * instance) * (scaleOutThreshold / 100);
            return unprocessedTasks > threshold;
        }
    }
}
