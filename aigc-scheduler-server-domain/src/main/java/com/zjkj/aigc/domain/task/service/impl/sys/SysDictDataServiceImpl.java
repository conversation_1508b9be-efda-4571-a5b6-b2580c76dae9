package com.zjkj.aigc.domain.task.service.impl.sys;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.req.dict.SysDictDataCreateReq;
import com.zjkj.aigc.domain.task.service.sys.SysDictDataService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictDataCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.sys.SysDictDataDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 字典数据领域服务实现类
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDictDataServiceImpl implements SysDictDataService {

    private final SysDictDataDao sysDictDataDao;

    @Override
    public Page<SysDictData> queryPage(SysDictDataCondition condition) {
        return sysDictDataDao.queryPage(condition);
    }

    @Override
    public List<SysDictData> queryList(SysDictDataCondition condition) {
        return sysDictDataDao.queryList(condition);
    }

    @Override
    public SysDictData queryById(Long id) {
        return sysDictDataDao.getById(id);
    }

    @Override
    public void createSysDictData(SysDictDataCreateReq req) {
        SysDictData sysDictData = BeanUtil.copyProperties(req, SysDictData.class, "id");
        sysDictDataDao.save(sysDictData);
    }

    @Override
    public void updateSysDictData(SysDictDataCreateReq req) {
        SysDictData sysDictData = queryById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(sysDictData), CustomErrorCode.DATA_NOT_EXISTS);

        SysDictData update = BeanUtil.copyProperties(req, SysDictData.class);
        sysDictDataDao.updateById(update);
    }

    @Override
    public void deleteSysDictData(Long id) {
        sysDictDataDao.removeById(id);
    }

    @Override
    public void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        SysDictDataCondition condition = new SysDictDataCondition();
        condition.setIds(ids);
        condition.setStatus(source.getCode());
        ids = queryList(condition).stream()
                .map(SysDictData::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        sysDictDataDao.changeStatus(SysDictData::getId, ids, SysDictData::getStatus, target.getCode());
    }

    @Override
    public void removeByDictId(Long dictId) {
        SysDictDataCondition condition = new SysDictDataCondition()
                .setDictId(dictId);

        List<Long> ids = queryList(condition).stream()
                .map(SysDictData::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        sysDictDataDao.removeByIds(ids);
    }


}
