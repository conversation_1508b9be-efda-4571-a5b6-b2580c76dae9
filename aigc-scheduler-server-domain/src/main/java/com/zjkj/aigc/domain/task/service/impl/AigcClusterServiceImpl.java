package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.zjkj.aigc.common.enums.ClusterStatusEnum;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.cluster.AigcClusterCreateReq;
import com.zjkj.aigc.common.req.cluster.AigcClusterNodeCreateReq;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcHostButterflyEffectService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcClusterDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 集群服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcClusterServiceImpl implements AigcClusterService {

    private final AigcClusterDao aigcClusterDao;
    private final AigcClusterNodeService aigcClusterNodeService;
    private final AigcHostButterflyEffectService aigcHostButterflyEffectService;

    @Override
    public Page<AigcCluster> queryPage(AigcClusterCondition condition) {
        Page<AigcCluster> aigcClusterPage = aigcClusterDao.queryPage(condition);
        fillClusterNodes(aigcClusterPage.getRecords());
        return aigcClusterPage;
    }

    @Override
    public List<AigcCluster> listById(Collection<Long> ids) {
        return aigcClusterDao.listByIds(ids);
    }

    @Override
    public List<AigcCluster> queryList(AigcClusterCondition condition) {
        List<AigcCluster> aigcClusters = aigcClusterDao.list(condition.buildQuery());
        if (condition.isNoFillNodes()) {
            return aigcClusters;
        }

        fillClusterNodes(aigcClusters);
        return aigcClusters;
    }

    /**
     * 填充集群节点信息
     * @param aigcClusters 集群列表
     */
    private void fillClusterNodes(List<AigcCluster> aigcClusters) {
        if (CollectionUtils.isEmpty(aigcClusters)) {
            return;
        }

        List<Long> clusterIds = StreamUtil.mapToList(aigcClusters, AigcCluster::getId);
        List<AigcClusterNode> aigcClusterNodes = aigcClusterNodeService.listByClusterId(clusterIds);
        Map<Long, List<AigcClusterNode>> clusterNodeMap = StreamUtil.groupBy(aigcClusterNodes, AigcClusterNode::getClusterId);

        // 填充节点信息
        aigcClusters.forEach(aigcCluster -> {
            List<AigcClusterNode> nodes = clusterNodeMap.getOrDefault(aigcCluster.getId(), List.of());
            aigcCluster.setNodes(nodes);
        });
    }

    @Override
    public AigcCluster queryIncludeNodeById(Long id) {
        AigcCluster cluster = aigcClusterDao.getById(id);
        if (Objects.nonNull(cluster)) {
            List<AigcClusterNode> nodes = aigcClusterNodeService.listByClusterId(id);
            cluster.setNodes(nodes);
        }
        return cluster;
    }

    @Override
    public AigcCluster queryById(Long id) {
        return aigcClusterDao.getById(id);
    }

    /**
     * 计算集群总成本
     * @param nodes 集群节点列表
     * @return 集群总成本
     */
    public BigDecimal getCost(List<AigcClusterNodeCreateReq> nodes) {
        return nodes.stream()
                .map(AigcClusterNodeCreateReq::getCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAigcCluster(AigcClusterCreateReq createReq) {
        // 创建集群
        AigcCluster aigcCluster = BeanUtil.copyProperties(createReq, AigcCluster.class, "id", "nodes");
        aigcCluster.setCost(getCost(createReq.getNodes()));
        aigcClusterDao.save(aigcCluster);

        // 创建节点
        aigcClusterNodeService.batchCreate(aigcCluster.getId(), createReq.getNodes());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAigcCluster(AigcClusterCreateReq updateReq) {
        // 检查集群是否存在
        AigcCluster aigcCluster = queryIncludeNodeById(updateReq.getId());
        BaseBizException.isTrue(Objects.nonNull(aigcCluster), CustomErrorCode.DATA_NOT_EXISTS);

        // 更新集群信息
        AigcCluster updateCluster = BeanUtil.copyProperties(updateReq, AigcCluster.class, "nodes");
        updateCluster.setCost(getCost(updateReq.getNodes()));
        aigcClusterDao.updateById(updateCluster);

        // 更新节点
        aigcClusterNodeService.batchUpdate(aigcCluster, updateReq.getNodes());

        // 刷新节点状态
        refreshNodeStatus(aigcCluster, updateReq.getNodes());
    }

    /**
     * 刷新节点状态
     * @param aigcCluster 集群
     * @param resultNodes 节点列表
     */
    private void refreshNodeStatus(AigcCluster aigcCluster, List<AigcClusterNodeCreateReq> resultNodes) {
        if (Objects.equals(aigcCluster.getStatus(), ClusterStatusEnum.DISABLE.getCode()) || CollectionUtils.isEmpty(resultNodes)) {
            return;
        }

        Set<String> sourceNodeIps = StreamUtil.mapToSet(aigcCluster.getNodes(), AigcClusterNode::getNodeIp);
        Set<String> resultNodeIps = StreamUtil.mapToSet(resultNodes, AigcClusterNodeCreateReq::getNodeIp);

        // 新增。节点实例置合法
        Set<String> inResultButNotInSource = Sets.difference(resultNodeIps, sourceNodeIps);
        aigcHostButterflyEffectService.enableHost(List.copyOf(inResultButNotInSource));

        // 删除。节点实例置非法
        Set<String> inSourceButNotInResult = Sets.difference(sourceNodeIps, resultNodeIps);
        disableHost(aigcCluster.getId(), inSourceButNotInResult);
    }

    /**
     * 禁用节点
     * @param clusterId 集群ID
     * @param hostIps 节点IP列表
     */
    private void disableHost(Long clusterId, Set<String> hostIps) {
        if (Objects.isNull(clusterId) || CollectionUtils.isEmpty(hostIps)) {
            return;
        }

        AigcClusterCondition condition = new AigcClusterCondition()
                .setStatus(ClusterStatusEnum.ENABLE.getCode());
        // 提取节点IP列表
        Set<String> nodeIps = queryList(condition).stream()
                .filter(cluster -> !Objects.equals(cluster.getId(), clusterId))
                .flatMap(cluster -> cluster.getNodes().stream())
                .map(AigcClusterNode::getNodeIp)
                .collect(Collectors.toSet());

        // hostIps去除存在于nodeIps的IP
        Set<String> illegalHostIps = Sets.difference(hostIps, nodeIps);
        if (CollectionUtils.isEmpty(illegalHostIps)) {
            return;
        }
        // 禁用节点
        aigcHostButterflyEffectService.disableHost(List.copyOf(illegalHostIps));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAigcCluster(Long id) {
        // 检查集群是否存在
        AigcCluster aigcCluster = queryById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcCluster), CustomErrorCode.DATA_NOT_EXISTS);

        // 检查集群是否禁用
        BaseBizException.isTrue(Objects.equals(aigcCluster.getStatus(), ClusterStatusEnum.DISABLE.getCode()), CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.cluster.disable.before.delete"));

        // 删除集群
        aigcClusterDao.removeById(id);

        // 删除关联的节点
        aigcClusterNodeService.removeByClusterId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableAigcCluster(Long id) {
        AigcCluster aigcCluster = queryIncludeNodeById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcCluster), CustomErrorCode.DATA_NOT_EXISTS);
        if (Objects.equals(aigcCluster.getStatus(), ClusterStatusEnum.ENABLE.getCode())) {
            return;
        }

        // 更新状态为启用
        AigcCluster updateCluster = new AigcCluster();
        updateCluster.setId(id);
        updateCluster.setStatus(ClusterStatusEnum.ENABLE.getCode());
        aigcClusterDao.updateById(updateCluster);

        // 节点实例-合法
        List<String> nodeIps = StreamUtil.mapToList(aigcCluster.getNodes(), AigcClusterNode::getNodeIp);
        aigcHostButterflyEffectService.enableHost(nodeIps);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableAigcCluster(Long id) {
        AigcCluster aigcCluster = queryIncludeNodeById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcCluster), CustomErrorCode.DATA_NOT_EXISTS);
        if (Objects.equals(aigcCluster.getStatus(), ClusterStatusEnum.DISABLE.getCode())) {
            return;
        }

        // 更新状态为禁用
        AigcCluster updateCluster = new AigcCluster();
        updateCluster.setId(id);
        updateCluster.setStatus(ClusterStatusEnum.DISABLE.getCode());
        aigcClusterDao.updateById(updateCluster);

        // 节点实例-非法
        Set<String> nodeIps = StreamUtil.mapToSet(aigcCluster.getNodes(), AigcClusterNode::getNodeIp);
        disableHost(aigcCluster.getId(), nodeIps);
    }
}
