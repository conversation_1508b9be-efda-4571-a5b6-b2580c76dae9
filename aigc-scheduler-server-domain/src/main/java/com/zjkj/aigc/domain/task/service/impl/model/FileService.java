package com.zjkj.aigc.domain.task.service.impl.model;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.common.utils.IOUtils;
import com.zjkj.aigc.common.dto.file.FileDownloadDTO;
import com.zjkj.aigc.common.dto.file.FileUploadDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.exception.UploadOssException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.domain.utils.OssUtils;
import com.zjkj.aigc.domain.utils.S3Utils;
import com.zjkj.aigc.domain.utils.SpringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/17 18:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {
    /**
     * 文件上传
     *
     * @param file 文件
     * @return 文件路径
     */
    public String upload(MultipartFile file) {
        FileUploadDTO uploadResult = uploadFile(file);
        return uploadResult.getFilePath();
    }

    /**
     * 文件上传（返回完整的上传信息）
     *
     * @param file 文件
     * @return 文件上传DTO
     */
    public FileUploadDTO uploadFile(MultipartFile file) {
        log.info("文件上传开始");

        // 校验文件是否为空
        if (Objects.isNull(file) || Objects.equals(file.getSize(), 0)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR,
                MessageUtils.getMessage("business.file.upload.empty"));
        }

        // 校验文件名称
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR,
                MessageUtils.getMessage("business.file.name.empty"));
        }

        // 校验文件大小
        long maxSize = 500 * 1024 * 1024L; // 500MB
        if (file.getSize() > maxSize) {
            String maxSizeStr = "500MB";
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR,
                MessageUtils.getMessage("business.file.upload.size.exceeded", new Object[]{maxSizeStr}));
        }

        try {
            String nameWithoutExt = FilenameUtils.getBaseName(originalFilename);
            String extension = FilenameUtils.getExtension(originalFilename);
            String dateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String timestampedFilename = nameWithoutExt + "_" + dateTime + "." + extension;
            // 根据配置选择存储服务
            String storageType = SpringUtils.getProperties("storage.type");
            String filePath;
            if ("s3".equals(storageType)) {
                filePath = S3Utils.uploadFile(IOUtils.newRepeatableInputStream(file.getInputStream()), timestampedFilename);
            } else {
                // 默认使用OSS
                filePath = OssUtils.uploadFile(IOUtils.newRepeatableInputStream(file.getInputStream()), timestampedFilename);
            }

            // 获取文件内容类型
            String contentType = getContentType(originalFilename);

            // 构建完整的访问URL
            String accessUrl = buildAccessUrl(filePath);

            log.info("文件上传成功，文件路径: {}, 原始文件名: {}, 文件大小: {} bytes, 访问URL: {}",
                filePath, originalFilename, file.getSize(), accessUrl);

            FileUploadDTO result = new FileUploadDTO(filePath, originalFilename, file.getSize(), contentType);
            result.setOssFileName(timestampedFilename);
            result.setAccessUrl(accessUrl);
            return result;

        } catch (Exception e) {
            log.error("文件上传失败，文件名: {}", originalFilename, e);
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                MessageUtils.getMessage("business.file.upload.failed"));
        }
    }

    /**
     * 文件下载
     *
     * @param filePath 文件路径
     * @return 文件输入流
     */
    public InputStream download(String filePath) {
        log.info("文件下载开始，文件路径: {}", filePath);

        // 校验文件路径
        if (StrUtil.isBlank(filePath)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR,
                MessageUtils.getMessage("business.file.path.empty"));
        }

        try {
            // 根据配置选择存储服务
            String storageType = SpringUtils.getProperties("storage.type");
            InputStream inputStream;
            if ("s3".equals(storageType)) {
                inputStream = S3Utils.getDownloadStream(filePath);
            } else {
                // 默认使用OSS
                inputStream = OssUtils.getDownloadStream(filePath);
            }

            if (inputStream == null) {
                throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS,
                    MessageUtils.getMessage("business.file.not.found"));
            }
            log.info("文件下载成功，文件路径: {}", filePath);
            return inputStream;
        } catch (BaseBizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("文件下载失败，文件路径: {}", filePath, e);
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                MessageUtils.getMessage("business.file.download.failed"));
        }
    }

    /**
     * 文件下载（返回完整的下载信息）
     *
     * @param filePath 文件路径
     * @return 文件下载DTO
     */
    public FileDownloadDTO downloadFile(String filePath) {
        log.info("文件下载开始，文件路径: {}", filePath);

        // 校验文件路径
        if (StrUtil.isBlank(filePath)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR,
                MessageUtils.getMessage("business.file.path.empty"));
        }

        try {
            // 根据配置选择存储服务
            String storageType = SpringUtils.getProperties("storage.type");
            InputStream inputStream;
            if ("s3".equals(storageType)) {
                inputStream = S3Utils.getDownloadStream(filePath);
            } else {
                // 默认使用OSS
                inputStream = OssUtils.getDownloadStream(filePath);
            }

            if (inputStream == null) {
                throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS,
                    MessageUtils.getMessage("business.file.not.found"));
            }

            // 从文件路径中提取文件名
            String fileName = extractFileName(filePath);

            // 根据文件扩展名确定内容类型
            String contentType = getContentType(fileName);

            log.info("文件下载成功，文件路径: {}, 文件名: {}", filePath, fileName);
            return new FileDownloadDTO(fileName, inputStream, contentType);

        } catch (BaseBizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("文件下载失败，文件路径: {}", filePath, e);
            throw new BaseBizException(CustomErrorCode.UNKNOWN_ERROR,
                MessageUtils.getMessage("business.file.download.failed"));
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    public boolean exists(String filePath) {
        log.info("检查文件是否存在，文件路径: {}", filePath);

        if (StrUtil.isBlank(filePath)) {
            return false;
        }

        try {
            String storageType = SpringUtils.getProperties("storage.type");
            if ("s3".equals(storageType)) {
                return S3Utils.doesObjectExist(filePath);
            } else {
                // 默认使用OSS
                InputStream inputStream = OssUtils.getDownloadStream(filePath);
                if (inputStream != null) {
                    inputStream.close();
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
            log.warn("检查文件存在性失败，文件路径: {}", filePath, e);
            return false;
        }
    }

    /**
     * 从文件路径中提取文件名
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    private String extractFileName(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return "unknown";
        }

        // 处理路径分隔符
        String normalizedPath = filePath.replace("\\", "/");
        int lastSlashIndex = normalizedPath.lastIndexOf("/");

        if (lastSlashIndex >= 0 && lastSlashIndex < normalizedPath.length() - 1) {
            return normalizedPath.substring(lastSlashIndex + 1);
        }

        return normalizedPath;
    }

    /**
     * 构建文件的完整访问URL
     *
     * @param filePath 文件路径
     * @return 完整的访问URL
     */
    private String buildAccessUrl(String filePath) {
        if (StrUtil.isBlank(filePath)) {
            return null;
        }

        try {
            String storageType = SpringUtils.getProperties("storage.type");

            if ("s3".equals(storageType)) {
                // S3访问URL构建
                String baseUrl = SpringUtils.getProperties("s3.base_url");
                if (StrUtil.isBlank(baseUrl)) {
                    // 如果没有配置base_url，根据bucket和region自动构建
                    String bucketName = SpringUtils.getProperties("s3.bucket_name");
                    String region = SpringUtils.getProperties("s3.region");
                    if (StrUtil.isNotBlank(bucketName) && StrUtil.isNotBlank(region)) {
                        baseUrl = String.format("https://%s.s3.%s.amazonaws.com", bucketName, region);
                    } else {
                        log.warn("S3配置不完整，无法构建访问URL");
                        return null;
                    }
                }
                String accessUrl = baseUrl + "/" + filePath;
                log.debug("构建S3访问URL: {}", accessUrl);
                return accessUrl;
            } else {
                // 原有的OSS访问URL构建逻辑
                String ossUrl = SpringUtils.getProperties("oss.oss_url");
                String bucketName = SpringUtils.getProperties("oss.bucket_name");

                if (StrUtil.isBlank(ossUrl) || StrUtil.isBlank(bucketName)) {
                    log.warn("OSS配置不完整，无法构建访问URL");
                    return null;
                }

                String accessUrl = String.format("https://%s.%s/%s", bucketName, ossUrl, filePath);
                log.debug("构建OSS访问URL: {}", accessUrl);
                return accessUrl;
            }

        } catch (Exception e) {
            log.error("构建访问URL失败，文件路径: {}", filePath, e);
            return null;
        }
    }

    /**
     * 根据文件扩展名获取内容类型
     *
     * @param fileName 文件名
     * @return 内容类型
     */
    private String getContentType(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "application/octet-stream";
        }

        String lowerFileName = fileName.toLowerCase();

        // 图片类型
        if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowerFileName.endsWith(".webp")) {
            return "image/webp";
        }
        // 文档类型
        else if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (lowerFileName.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        }
        // 文本类型
        else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else if (lowerFileName.endsWith(".csv")) {
            return "text/csv";
        } else if (lowerFileName.endsWith(".json")) {
            return "application/json";
        } else if (lowerFileName.endsWith(".xml")) {
            return "application/xml";
        }
        // 压缩文件
        else if (lowerFileName.endsWith(".zip")) {
            return "application/zip";
        } else if (lowerFileName.endsWith(".rar")) {
            return "application/x-rar-compressed";
        } else if (lowerFileName.endsWith(".7z")) {
            return "application/x-7z-compressed";
        }
        // 视频文件
        else if (lowerFileName.endsWith(".mp4")) {
            return "video/mp4";
        } else if (lowerFileName.endsWith(".avi")) {
            return "video/x-msvideo";
        } else if (lowerFileName.endsWith(".mov")) {
            return "video/quicktime";
        }
        // 音频文件
        else if (lowerFileName.endsWith(".mp3")) {
            return "audio/mpeg";
        } else if (lowerFileName.endsWith(".wav")) {
            return "audio/wav";
        }
        // 默认类型
        else {
            return "application/octet-stream";
        }
    }
}
