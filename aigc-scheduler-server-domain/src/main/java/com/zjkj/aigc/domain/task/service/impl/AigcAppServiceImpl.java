package com.zjkj.aigc.domain.task.service.impl;

import com.zjkj.aigc.common.enums.AppStatusEnum;
import com.zjkj.aigc.domain.task.service.AigcAppService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcAppCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcAppDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用表(AigcApp)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-04 11:44:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcAppServiceImpl implements AigcAppService {

    private final AigcAppDao aigcAppDao;

    @Override
    public List<AigcApp> enableApp() {
        AigcAppCondition condition = new AigcAppCondition()
                .setStatusList(List.of(AppStatusEnum.ENABLE.code()));
        return aigcAppDao.queryList(condition);
    }

    @Override
    public List<AigcApp> activeAfterTime(String currentTime) {
        return aigcAppDao.activeAfterTime(currentTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshActiveApp(List<AigcApp> aigcAppList) {
        if (CollectionUtils.isEmpty(aigcAppList)) {
            return;
        }

        Map<String, AigcApp> statMap = aigcAppList.stream()
                .collect(Collectors.toMap(AigcApp::getAppId, Function.identity()));

        // 存在的应用
        Map<String, Long> appMap = aigcAppDao.queryList(new AigcAppCondition().setAppIds(statMap.keySet()))
                .stream()
                .collect(Collectors.toMap(AigcApp::getAppId, AigcApp::getId, (k1, k2) -> k2));

        List<AigcApp> insertList = new ArrayList<>();
        List<AigcApp> updateList = new ArrayList<>();
        aigcAppList.forEach(app -> {
            Long id = appMap.get(app.getAppId());
            if (Objects.isNull(id)) {
                app.setName(app.getAppId());
                app.setStatus(AppStatusEnum.ENABLE.code());
                insertList.add(app);
            } else {
                app.setId(id);
                updateList.add(app);
            }
        });

        if (!CollectionUtils.isEmpty(insertList)) {
            aigcAppDao.saveBatch(insertList);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcAppDao.updateBatchById(updateList);
        }
    }
}
