package com.zjkj.aigc.domain.task.service.alarm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.alarm.AigcAlarmCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmUpdateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;

import java.util.List;

/**
 * 模型告警(AigcAlarm)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface AigcAlarmService {
    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcAlarm> queryPage(AigcAlarmCondition condition);

    /**
     * 创建模型告警
     *
     * @param req 模型告警数据
     */
    void createAigcAlarm(AigcAlarmCreateReq req);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 详情
     */
    AigcAlarm queryById(Long id);

    /**
     * 更新模型告警
     *
     * @param req 更新参数
     */
    void updateAigcAlarm(AigcAlarmUpdateReq req);
}
