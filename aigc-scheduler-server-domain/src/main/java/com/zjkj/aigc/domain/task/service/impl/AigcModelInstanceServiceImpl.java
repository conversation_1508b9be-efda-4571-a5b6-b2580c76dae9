package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.req.model.AigcModelHeartbeat;
import com.zjkj.aigc.common.req.model.AigcModelRegister;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.AigcNodeInstanceService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelInstanceDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 模型实例表(AigcModelInstance)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:07:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcModelInstanceServiceImpl implements AigcModelInstanceService {

    private final AigcNodeInstanceService aigcNodeInstanceService;
    private final AigcModelInstanceDao aigcModelInstanceDao;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public AigcModelInstance modelRegister(AigcModelRegister register) {
        Long clusterId = aigcNodeInstanceService.getClusterId(register.getNodeIp(), register.getPodNamespace());
        BaseBizException.isTrue(Objects.nonNull(clusterId), CustomErrorCode.UNAUTHORIZED);

        AigcModelInstance modelInstance = aigcModelInstanceDao.queryByPodName(register.getPodName());
        if (Objects.isNull(modelInstance)) {
            modelInstance = new AigcModelInstance();
        }

        BeanUtil.copyProperties(register, modelInstance);
        modelInstance.setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode())
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode())
                .setLastHeartbeatTime(LocalDateTime.now())
                .setClusterId(clusterId);

        // 查询模型信息
        AigcModelInfo aigcModelInfo = aigcModelInfoService.queryByTaskTypeAndModelName(register.getTaskType(), register.getModelName());
        if (Objects.nonNull(aigcModelInfo)) {
            modelInstance.setModelId(aigcModelInfo.getId());
        }

        if (Objects.isNull(modelInstance.getId())) {
            aigcModelInstanceDao.save(modelInstance);
        } else {
            modelInstance.setRevisedTime(LocalDateTime.now());
            aigcModelInstanceDao.updateById(modelInstance);
        }

        return modelInstance;
    }

    @Override
    public boolean modelHeartbeat(AigcModelHeartbeat heartbeat) {
        AigcModelInstance modelInstance = aigcModelInstanceDao.getById(heartbeat.getId());
        if (Objects.isNull(modelInstance)) {
            return false;
        }

        AigcModelInstance updateInstance = new AigcModelInstance()
                .setId(modelInstance.getId())
                .setPodStatus(heartbeat.getStatus())
                .setLastHeartbeatTime(LocalDateTime.now());
        if (Objects.equals(modelInstance.getHealthStatus(), RegisterStatusEnum.HealthStatus.OFFLINE.getCode())) {
            updateInstance.setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        }

        aigcModelInstanceDao.updateById(updateInstance);
        return Objects.equals(modelInstance.getStatus(), RegisterStatusEnum.Compliance.LEGAL.getCode());
    }

    @Override
    public Page<AigcModelInstance> queryPage(AigcModelInstanceCondition condition) {
        return aigcModelInstanceDao.queryPage(condition);
    }

    @Override
    public List<AigcModelInstance> queryList(AigcModelInstanceCondition condition) {
        return aigcModelInstanceDao.queryList(condition);
    }

    @Override
    public void changeStatusByHostIp(List<String> hostIps, RegisterStatusEnum.Compliance sourceStatus, RegisterStatusEnum.Compliance targetStatus) {
        if (CollectionUtils.isEmpty(hostIps)) {
            return;
        }

        AigcModelInstanceCondition condition = new AigcModelInstanceCondition()
                .setNodeIps(hostIps)
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode())
                .setStatus(sourceStatus.getCode());
        List<AigcModelInstance> modelInstances = aigcModelInstanceDao.queryList(condition);
        if (CollectionUtils.isEmpty(modelInstances)) {
            return;
        }

        List<Long> ids = modelInstances.stream()
                .map(AigcModelInstance::getId)
                .collect(Collectors.toList());
        aigcModelInstanceDao.changeStatus(AigcModelInstance::getId, ids, AigcModelInstance::getStatus, targetStatus.getCode());
        log.info("changeStatusByHostIp() 修改模型实例状态. hostIps:{}, sourceStatus:{}, targetStatus:{}, ids:{}", hostIps, sourceStatus.getCode(), targetStatus.getCode(), ids);
    }

    @Override
    public void checkHealthStatus(Duration timeout) {
        AigcModelInstanceCondition condition = new AigcModelInstanceCondition()
                .setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());

        List<AigcModelInstance> modelInstances = queryList(condition);
        LocalDateTime now = LocalDateTime.now();
        List<Long> ids = modelInstances.stream()
                .filter(instance -> {
                    Duration duration = Duration.between(instance.getLastHeartbeatTime(), now);
                    return duration.compareTo(timeout) > 0;
                })
                .map(AigcModelInstance::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        aigcModelInstanceDao.changeStatus(AigcModelInstance::getId, ids, AigcModelInstance::getHealthStatus, RegisterStatusEnum.HealthStatus.OFFLINE.getCode());
        log.info("checkHealthStatus() 模型健康检查完成. 置为离线 ids:{}", ids);
    }

    @Override
    public Map<Map.Entry<String, String>, List<AigcModelInstance>> queryInstance(AigcModelInstanceCondition condition) {
        condition.setStatus(RegisterStatusEnum.Compliance.LEGAL.getCode());
        condition.setHealthStatus(RegisterStatusEnum.HealthStatus.ONLINE.getCode());
        return queryList(condition).stream()
                .collect(Collectors.groupingBy(
                        instance -> Map.entry(instance.getTaskType(), instance.getModelName()))
                );
    }

    @Override
    public void fillModelId(List<AigcModelInstance> missModelIdInstances) {
        Set<String> taskTypes = CollStreamUtil.toSet(missModelIdInstances, AigcModelInstance::getTaskType);
        Set<String> modelNames = CollStreamUtil.toSet(missModelIdInstances, AigcModelInstance::getModelName);
        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        Map<Map.Entry<String, String>, Long> modelMap = CollStreamUtil.toMap(aigcModelInfoService.queryList(condition), v -> Map.entry(v.getTaskType(), v.getModelName()), AigcModelInfo::getId);
        if (CollectionUtils.isEmpty(modelMap)) {
            return;
        }

        List<AigcModelInstance> updateInstances = missModelIdInstances.stream()
                .filter(instance -> modelMap.containsKey(Map.entry(instance.getTaskType(), instance.getModelName())))
                .map(instance -> new AigcModelInstance()
                         .setId(instance.getId())
                         .setModelId(modelMap.get(Map.entry(instance.getTaskType(), instance.getModelName()))))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateInstances)) {
            return;
        }

        aigcModelInstanceDao.updateBatchById(updateInstances);
    }
}
