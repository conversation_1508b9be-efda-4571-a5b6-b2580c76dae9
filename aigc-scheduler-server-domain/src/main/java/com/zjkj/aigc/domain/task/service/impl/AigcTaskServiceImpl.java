package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.task.AigcTaskCreateReq;
import com.zjkj.aigc.common.util.IdGenerator;
import com.zjkj.aigc.domain.batch.BatchProcessor;
import com.zjkj.aigc.domain.task.service.AigcTaskQueueService;
import com.zjkj.aigc.domain.task.service.AigcTaskService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcTaskDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.doc.AigcTaskDoc;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.AigcTaskESDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.event.AigcTaskBatchEvent;
import com.zjkj.aigc.infrastructure.mybatis.aigc.es.event.AigcTaskEvent;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AigcTaskServiceImpl implements AigcTaskService {
    @Value("${ai.task.priority.order:true}")
    private boolean orderByPriority;
    private final AigcTaskDao aigcTaskDao;
    private final AigcTaskESDao aigcTaskESDao;
    private final AigcTaskQueueService aigcTaskQueueService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 转换任务
     *
     * @param req 请求参数
     * @param <T> 参数类型
     * @return 任务信息
     */
    @Override
    public <T> AigcTask covertToAigcTask(CreateAiTaskReq<T> req) {
        AigcTask aigcTask = BeanUtil.copyProperties(req, AigcTask.class);
        String idStr = IdGenerator.nexStrId();
        aigcTask.setAigcTaskId(idStr);
        if (!StringUtils.hasText(aigcTask.getTaskId())) {
            aigcTask.setTaskId(idStr);
        }

        aigcTask.setModelParams(JSON.toJSONString(req.getParams()));
        aigcTask.setTaskSource(StringUtils.hasText(req.getTaskSource()) ? req.getTaskSource() : StringPool.EMPTY);
        aigcTask.setTaskState(TaskStateEnum.WAITE.getCode());
        if (Objects.isNull(aigcTask.getTaskPriority())) {
            aigcTask.setTaskPriority(0);
        }
        return aigcTask;
    }

    /**
     * 保存任务
     *
     * @param req 请求任务信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> AigcTask saveAigcTask(CreateAiTaskReq<T> req) {
        AigcTask aigcTask = covertToAigcTask(req);
        aigcTaskDao.save(aigcTask);
        aigcTaskQueueService.add(aigcTask);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return aigcTask;
    }

    /**
     * 批量保存任务
     *
     * @param reqList 请求参数
     * @param <T>     参数类型
     * @return 任务列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T> List<AigcTask> batchSaveAigcTask(List<CreateAiTaskReq<T>> reqList) {
        List<AigcTask> aigcTaskList = reqList.stream()
                .map(this::covertToAigcTask)
                .collect(Collectors.toList());
        aigcTaskDao.saveBatch(aigcTaskList);
        aigcTaskQueueService.batchAdd(aigcTaskList);
        eventPublisher.publishEvent(new AigcTaskBatchEvent(aigcTaskList));
        return aigcTaskList;
    }


    /**
     * 获取任务信息
     *
     * @param req 请求参数
     * @return 任务信息
     */
    @Override
    public AigcTask getAiTask(GetAiTaskReq req) {
        AigcTask aigcTask = aigcTaskDao.getByTaskId(req.getTaskId(), req.getBusinessId());
        if (Objects.isNull(aigcTask)) {
            return null;
        }

        if (Objects.equals(aigcTask.getTaskState(), TaskStateEnum.WAITE.getCode())) {
//            Map<String, Integer> rankMap = getWaitTaskRank(Lists.newArrayList(aigcTask.getAigcTaskId()), req.getTaskType(), req.getModelName());
//            aigcTask.setRank(rankMap.get(aigcTask.getAigcTaskId()));
            Integer rank = aigcTaskQueueService.getRank(aigcTask.getTaskType(), aigcTask.getModelName(), aigcTask.getId());
            aigcTask.setRank(rank);
        }

        return aigcTask;
    }

    /**
     * 获取任务排名列表
     *
     * @param req 请求参数
     * @return 任务排名列表
     */
    @Override
    public List<AigcTask> getAigcTaskRankList(BatchGetAiTaskReq req) {
        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .taskIds(req.getTaskIds())
                .businessId(req.getBusinessId())
                .build();
        List<AigcTask> aigcTaskList = aigcTaskDao.queryByCondition(taskCondition);
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return Lists.newArrayList();
        }

        fillAigcTaskRank(req.getTaskType(), req.getModelName(), req.isBatchOpen(), aigcTaskList);
        return aigcTaskList;
    }

    /**
     * 填充任务排名
     *
     * @param taskType     任务类型
     * @param modelName    模型名称
     * @param batchOpen    是否批量
     * @param aigcTaskList 任务列表
     */
    @Override
    public void fillAigcTaskRank(String taskType, String modelName, boolean batchOpen, List<AigcTask> aigcTaskList) {
        Map<Long, Integer> rankMap = getQueueRankMap(taskType, modelName, aigcTaskList);
        if (CollectionUtils.isEmpty(rankMap)) {
            return;
        }

        aigcTaskList.stream()
                .filter(v -> rankMap.containsKey(v.getId()))
                .forEach(v -> v.setRank(rankMap.get(v.getId())));
    }

    @Override
    public boolean updateTaskPriority(Collection<Long> ids, int targetPriority) {
        return aigcTaskDao.updateTaskPriority(ids, targetPriority);
    }

    @Override
    public void taskRefreshQueue(LocalDateTime endTime) {
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .taskState(TaskStateEnum.WAITE.getCode())
                .build();
        if (Objects.nonNull(endTime)) {
            condition.setEndTime(LocalDateTimeUtil.formatNormal(endTime));
        }

        BatchProcessor<AigcTask> batchProcessor = new BatchProcessor<>(500, aigcTaskQueueService::batchAdd);
        aigcTaskDao.streamByCondition(condition, resultContext -> batchProcessor.add(resultContext.getResultObject()));
        batchProcessor.flush();
        log.info("taskRefreshQueue. totalProcessed:{}", batchProcessor.getTotalProcessed());
    }

    /**
     * 获取任务排名
     *
     * @param taskType     任务类型
     * @param modelName    模型名称
     * @param aigcTaskList 任务列表
     * @return 任务信息
     */
    private Map<Long, Integer> getQueueRankMap(String taskType, String modelName, List<AigcTask> aigcTaskList) {
        if (!StringUtils.hasText(taskType) || !StringUtils.hasText(modelName)) {
            return Collections.emptyMap();
        }

        List<Long> ids = aigcTaskList.stream()
                .filter(v -> Objects.equals(v.getTaskState(), TaskStateEnum.WAITE.getCode()))
                .map(AigcTask::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        if (ids.size() < 2) {
            Integer rank = aigcTaskQueueService.getRank(taskType, modelName, ids.get(0));
            if (Objects.nonNull(rank)) {
                return Map.of(ids.get(0), rank);
            }
        }

        return aigcTaskQueueService.getRankMap(taskType, modelName, ids);
    }


    /**
     * 获取任务排名
     *
     * @param taskType     任务类型
     * @param modelName    模型名称
     * @param batchOpen    批量开关
     * @param aigcTaskList 任务列表
     * @return 任务信息
     */
    private Map<String, Integer> getRankMap(String taskType, String modelName, boolean batchOpen, List<AigcTask> aigcTaskList) {
        if (!StringUtils.hasText(taskType) || !StringUtils.hasText(modelName)) {
            return Collections.emptyMap();
        }

        if (batchOpen) {
            // 独立、批量单独计算排名
            Map<Integer, List<String>> taskBatchMap = aigcTaskList.stream()
                    .filter(v -> Objects.equals(v.getTaskState(), TaskStateEnum.WAITE.getCode()))
                    .collect(Collectors.groupingBy(
                            AigcTask::getTaskBatch,
                            Collectors.mapping(AigcTask::getAigcTaskId, Collectors.toList()))
                    );

            if (CollectionUtils.isEmpty(taskBatchMap)) {
                return Collections.emptyMap();
            }

            Map<String, Integer> rankMap = new HashMap<>();
            taskBatchMap.forEach((taskBatch, aigcTaskIds) -> {
                Map<String, Integer> batchRankMap = getWaitTaskRank(aigcTaskIds, taskType, modelName, taskBatch);
                rankMap.putAll(batchRankMap);
            });
            return rankMap;
        }

        List<String> waiteAigcTaskIds = aigcTaskList.stream()
                .filter(v -> Objects.equals(v.getTaskState(), TaskStateEnum.WAITE.getCode()))
                .map(AigcTask::getAigcTaskId)
                .collect(Collectors.toList());

        return getWaitTaskRank(waiteAigcTaskIds, taskType, modelName);
    }


    /**
     * 获取队列排名
     *
     * @param aigcTaskIds 任务id
     * @param taskType    任务类型
     * @param modelName   模型名称
     * @return 任务排名
     */
    public Map<String, Integer> getWaitTaskRank(List<String> aigcTaskIds, String taskType, String modelName) {
        return getWaitTaskRank(aigcTaskIds, taskType, modelName, null);
    }

    /**
     * 获取队列排名
     *
     * @param aigcTaskIds 任务id
     * @param taskType    任务类型
     * @param modelName   模型名称
     * @param taskBatch   是否批量
     * @return 任务排名
     */
    public Map<String, Integer> getWaitTaskRank(List<String> aigcTaskIds, String taskType, String modelName, Integer taskBatch) {
        if (CollectionUtils.isEmpty(aigcTaskIds) || !StringUtils.hasText(taskType) || !StringUtils.hasText(modelName)) {
            return new HashMap<>();
        }

        AigcTaskCondition taskCondition = AigcTaskCondition.builder()
                .aigcTaskIds(aigcTaskIds)
                .taskType(taskType)
                .modelName(modelName)
                .taskBatch(taskBatch)
                .taskState(TaskStateEnum.WAITE.getCode())
                .orderByPriority(orderByPriority)
                .build();
        return aigcTaskDao.getTaskRankByMemory(taskCondition);
    }


    /**
     * 取消任务
     *
     * @param aigcTask 任务信息
     * @return 是否取消成功
     */
    @Override
    public boolean cancelAiTask(AigcTask aigcTask) {
        if (Objects.isNull(aigcTask)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS, MessageUtils.getMessage("business.task.not.exists"));
        }

        if (Objects.equals(aigcTask.getTaskState(), TaskStateEnum.CANCEL.getCode())) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.task.cancelled"));
        }

        if (!Objects.equals(aigcTask.getTaskState(), TaskStateEnum.WAITE.getCode())
                && !Objects.equals(aigcTask.getTaskState(), TaskStateEnum.RUNNING.getCode())) {
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.task.status.not.allow.cancel"));
        }

        LocalDateTime now = LocalDateTime.now();
        boolean updated = aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.CANCEL, now);
        if (updated) {
            aigcTaskQueueService.batchRemove(aigcTask.getTaskType(), aigcTask.getModelName(), List.of(aigcTask.getId()));
        }
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return updated;
    }

    /**
     * 重启任务
     *
     * @param aigcTask 任务信息
     * @return 是否重启成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restartTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        boolean restarted = aigcTaskDao.restartTask(aigcTask, TaskStateEnum.WAITE, now);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        if (restarted) {
            aigcTaskQueueService.add(aigcTask);
        }
        return restarted;
    }

    /**
     * 开始任务
     *
     * @param aigcTask 任务信息
     * @return 是否开始成功
     */
    @Override
    public boolean startTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        boolean r = aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.RUNNING, now);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return r;
    }

    /**
     * 任务成功
     *
     * @param aigcTask 任务信息
     */
    @Override
    public void succTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        if (Objects.equals(aigcTask.getTaskState(), TaskStateEnum.TIMEOUT_FAILED.getCode())) {
            aigcTask.setFailMessage("超时成功");
        }
        aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.SUCC, now);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
    }

    /**
     * 任务失败
     *
     * @param aigcTask 任务信息
     */
    @Override
    public boolean failTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        if (Objects.equals(aigcTask.getTaskState(), TaskStateEnum.TIMEOUT_FAILED.getCode())) {
            aigcTask.setFailMessage("超时失败：" + aigcTask.getFailMessage());
        }

        boolean r = aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.FAILED, now);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return r;
    }

    /**
     * 任务进度
     *
     * @param aigcTask 任务信息
     */
    @Override
    public boolean progressTask(AigcTask aigcTask) {
        if (!Objects.equals(aigcTask.getTaskState(), TaskStateEnum.RUNNING.getCode())) {
            log.info("callback. task is not running, taskId: {}, reqProgress: {}", aigcTask.getTaskId(), aigcTask.getTaskProgress());
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        return aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.of(aigcTask.getTaskState()), now);
    }

    /**
     * 超时失败任务
     *
     * @param aigcTask 任务信息
     * @return 是否超时失败
     */
    @Override
    public boolean timeoutFailTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        boolean r = aigcTaskDao.updateTaskState(aigcTask, TaskStateEnum.TIMEOUT_FAILED, now);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        return r;
    }

    /**
     * 重试任务
     *
     * @param aigcTask 任务信息
     * @return 是否重试成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean retryTask(AigcTask aigcTask) {
        LocalDateTime now = LocalDateTime.now();
        boolean restarted = aigcTaskDao.restartTask(aigcTask, TaskStateEnum.WAITE, now, Boolean.TRUE);
        eventPublisher.publishEvent(new AigcTaskEvent(aigcTask));
        if (restarted) {
            aigcTaskQueueService.add(aigcTask);
        }
        return restarted;
    }

    /**
     * 查询模型参数
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 模型参数
     */
    @Override
    public String queryModelParam(String taskType, String modelName) {
        return aigcTaskDao.queryModelParam(taskType, modelName);
    }

    /**
     * 删除任务
     *
     * @param businessId 业务id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void actualDelByBusinessId(String businessId) {
        AigcTaskCondition condition = AigcTaskCondition.builder()
                .businessId(businessId)
                .build();
        List<AigcTask> aigcTasks = queryList(condition);
        if (CollectionUtils.isEmpty(aigcTasks)) {
            return;
        }

        List<Long> allIds = CollStreamUtil.toList(aigcTasks, AigcTask::getId);
        aigcTaskDao.actualDelById(allIds);
        eventPublisher.publishEvent(new AigcTaskBatchEvent(allIds).setDelete(true));
        Map<Map.Entry<String, String>, List<Long>> groupedTasks = aigcTasks.stream()
                .filter(task -> Objects.equals(task.getTaskState(), TaskStateEnum.WAITE.getCode()))
                .collect(Collectors.groupingBy(v -> Map.entry(v.getTaskType(), v.getModelName()),
                        Collectors.mapping(AigcTask::getId, Collectors.toList()))
                );
        if (CollectionUtils.isEmpty(groupedTasks)) {
            return;
        }

        groupedTasks.forEach((model, ids) -> aigcTaskQueueService.batchRemove(model.getKey(), model.getValue(), ids));
    }

    @Override
    public void actualDelById(List<Long> ids) {
        aigcTaskDao.actualDelById(ids);
        eventPublisher.publishEvent(new AigcTaskBatchEvent(ids).setDelete(true));
    }

    @Override
    public Page<AigcTask> queryPage(AigcTaskCondition condition) {
        // 查询字段
        condition.setSelectColumns(List.of(AigcTask::getId));
        /*Page<AigcTask> aigcTaskPage = aigcTaskDao.queryPage(condition);
                if (!CollectionUtils.isEmpty(aigcTaskPage.getRecords())) {
            Set<Long> ids = CollStreamUtil.toSet(aigcTaskPage.getRecords(), AigcTask::getId);
            List<AigcTask> aigcTasks = aigcTaskDao.listByIds(ids);
            Collections.reverse(aigcTasks);
            aigcTaskPage.setRecords(aigcTasks);
        }*/

        Page<AigcTaskDoc> aigcTaskDocPage = aigcTaskESDao.queryPage(condition);
        Page<AigcTask> aigcTaskPage = Page.of(aigcTaskDocPage.getCurrent(), aigcTaskDocPage.getSize(), aigcTaskDocPage.getTotal());
        if (!CollectionUtils.isEmpty(aigcTaskDocPage.getRecords())) {
            Set<Long> ids = CollStreamUtil.toSet(aigcTaskDocPage.getRecords(), AigcTaskDoc::getTableId);
            List<AigcTask> aigcTasks = aigcTaskDao.listByIds(ids);
            Collections.reverse(aigcTasks);
            aigcTaskPage.setRecords(aigcTasks);
        }
        return aigcTaskPage;
    }

    @Override
    public List<AigcTask> queryList(AigcTaskCondition condition) {
        return aigcTaskDao.list(condition.buildQuery());
    }

    @Override
    public AigcTask getByAigcTaskId(String aigcTaskId) {
        return aigcTaskDao.getByAigcTaskId(aigcTaskId);
    }

    @Override
    public boolean updateAigcTask(AigcTaskCreateReq req) {
        AigcTask aigcTask = getByAigcTaskId(req.getAigcTaskId());
        BaseBizException.isTrue(Objects.nonNull(aigcTask), CustomErrorCode.DATA_NOT_EXISTS);
        BaseBizException.isTrue(Objects.equals(aigcTask.getTaskState(), TaskStateEnum.WAITE.getCode()), CustomErrorCode.DATA_CANNOT_EDIT, "任务状态不是待执行状态，无法修改");

        AigcTask updateAigcTask = new AigcTask();
        updateAigcTask.setId(aigcTask.getId());
        updateAigcTask.setTaskState(aigcTask.getTaskState());
        updateAigcTask.setTaskType(req.getTaskType());
        updateAigcTask.setModelName(req.getModelName());
        updateAigcTask.setModelParams(JSON.toJSONString(req.getParams()));
        updateAigcTask.setTaskPriority(aigcTask.getTaskPriority());

        boolean updated = aigcTaskDao.updateAigcTask(updateAigcTask);
        if (updated) {
            aigcTaskQueueService.batchUpdate(List.of(aigcTask));
            if (!Objects.equals(aigcTask.getTaskType(), req.getTaskType()) || !Objects.equals(aigcTask.getModelName(), req.getModelName())) {
                aigcTaskQueueService.batchRemove(aigcTask.getTaskType(), aigcTask.getModelName(), List.of(aigcTask.getId()));
            }
        }

        return updated;
    }
}
