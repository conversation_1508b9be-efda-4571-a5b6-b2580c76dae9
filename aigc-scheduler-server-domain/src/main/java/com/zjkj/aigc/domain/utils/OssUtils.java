package com.zjkj.aigc.domain.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.zjkj.aigc.common.exception.UploadOssException;
import com.zjkj.aigc.domain.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

@Slf4j
public class OssUtils {
    private static OSS downloadOSSClient = getClient();

    public static OSS getClient() {
        String ossUrl = SpringUtils.getProperties("oss.oss_url");
        String accessKeyId = SpringUtils.getProperties("oss.accesskey_id");
        String accessKeySecret = SpringUtils.getProperties("oss.accesskey_secret");
        if (StrUtil.isBlank(ossUrl) || StrUtil.isBlank(accessKeyId) || StrUtil.isBlank(accessKeySecret)) {
            throw new UploadOssException("缺少阿里云OSS参数[endpoint|accessKeyId|secretAccessKey]配置，请检查");
        }
        return new OSSClientBuilder().build(ossUrl, accessKeyId, accessKeySecret);
    }

    public static OSS getAOneClient() {
        String ossUrl = SpringUtils.getProperties("aone-oss.oss_url");
        String accessKeyId = SpringUtils.getProperties("aone-oss.accesskey_id");
        String accessKeySecret = SpringUtils.getProperties("aone-oss.accesskey_secret");
        if (StrUtil.isBlank(ossUrl) || StrUtil.isBlank(accessKeyId) || StrUtil.isBlank(accessKeySecret)) {
            throw new UploadOssException("缺少阿里云OSS参数[endpoint|accessKeyId|secretAccessKey]配置，请检查");
        }
        return new OSSClientBuilder().build(ossUrl, accessKeyId, accessKeySecret);
    }

    /**
     * 上传oss
     *
     * @param inputStream inputStream
     * @param fileName    文件名
     * @return oss文件地址
     */
    public static String uploadFile(InputStream inputStream, String fileName) {
        log.info("上传oss 参数: fileName: {}", fileName);
        String bucketName = SpringUtils.getProperties("oss.bucket_name");
        String packName = SpringUtils.getProperties("oss.pack_name");
        OSS ossClient = getClient();
        try {
            fileName = StrUtil.isEmpty(packName) ? fileName : packName + "/" + fileName;
            // 创建PutObjectRequest对象
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader("x-oss-forbid-overwrite", "false");
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream);
            putObjectRequest.setMetadata(metadata);
            // 创建PutObject请求
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            log.info("上传oss 返回: result: {}", JSON.toJSONString(result));
            return fileName;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }

    /**
     * 上传oss
     *
     * @param inputStream inputStream
     * @param fileName    文件名
     * @return oss文件地址
     */
    public static String uploadAOneFile(InputStream inputStream, String fileName) {
        log.info("上传oss 参数: fileName: {}", fileName);
        String bucketName = SpringUtils.getProperties("aone-oss.bucket_name");
        String packName = SpringUtils.getProperties("aone-oss.pack_name");
        OSS ossClient = getAOneClient();
        try {
            fileName = StrUtil.isEmpty(packName) ? fileName : packName + "/" + fileName;
            // 创建PutObjectRequest对象
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader("x-oss-forbid-overwrite", "false");
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream);
            putObjectRequest.setMetadata(metadata);
            // 创建PutObject请求
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            log.info("上传oss 返回: result: {}", JSON.toJSONString(result));
            return SpringUtils.getProperties("aone-oss.vite_ossip") + "/" + fileName;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }

    public static String uploadPrivateFile(InputStream inputStream, String fileName) {
        log.info("上传oss 参数: fileName: {}", fileName);
        String bucketName = SpringUtils.getProperties("oss.bucket_name");
        String packName = SpringUtils.getProperties("oss.pack_name");
        OSS ossClient = getClient();
        try {
            fileName = StrUtil.isEmpty(packName) ? fileName : packName + "/" + fileName;
            // 创建PutObjectRequest对象
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.Private);
            metadata.setHeader("x-oss-forbid-overwrite", "false");
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream);
            putObjectRequest.setMetadata(metadata);
            // 创建PutObject请求
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            log.info("上传oss 返回: result: {}", JSON.toJSONString(result));
            return fileName;
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            log.error("Error Message:" + oe.getErrorMessage());
            log.error("Error Code:" + oe.getErrorCode());
            log.error("Request ID:" + oe.getRequestId());
            log.error("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            log.error("Error Message:" + ce.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return "";
    }

    public static synchronized InputStream getDownloadStream(String fileName) {
        String bucketName = SpringUtils.getProperties("oss.bucket_name");
        OSSObject ossObject;
        try {
            ossObject = downloadOSSClient.getObject(bucketName, fileName);
        } catch (Exception e) {
            //可能连接断开了，重新连接，再有问题就不管了
            if (downloadOSSClient != null) {
                downloadOSSClient.shutdown();
            }
            downloadOSSClient = getClient();
            ossObject = downloadOSSClient.getObject(bucketName, fileName);
        }
        return ossObject.getObjectContent();
    }
}
