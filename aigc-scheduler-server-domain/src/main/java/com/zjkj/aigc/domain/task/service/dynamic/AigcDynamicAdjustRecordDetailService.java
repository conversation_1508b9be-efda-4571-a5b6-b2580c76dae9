package com.zjkj.aigc.domain.task.service.dynamic;

import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordDetailCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;

import java.util.Collection;
import java.util.List;

/**
 * 动态调整记录明细表(AigcDynamicAdjustRecordDetail)服务接口
 *
 * <AUTHOR>
 * @since 2025-04-18 09:52:52
 */
public interface AigcDynamicAdjustRecordDetailService {

    /**
     * 查询动态调整记录明细列表
     *
     * @param condition 查询条件
     * @return 动态调整记录明细列表
     */
    List<AigcDynamicAdjustRecordDetail> queryList(AigcDynamicAdjustRecordDetailCondition condition);

    /**
     * 更新动态调整记录明细状态
     *
     * @param ids        动态调整记录明细id列表
     * @param statusEnum 动态调整状态
     */
    void updateStatusById(Collection<Long> ids, DynamicAdjustStatusEnum statusEnum);

    /**
     * 保存回退动态调整记录明细
     *
     * @param rollbackRecord 回滚动态调整记录明细
     */
    void saveRollbackRecordDetails(AigcDynamicAdjustRecord rollbackRecord);

    /**
     * 批量更新动态调整记录明细
     *
     * @param recordDetails       动态调整记录明细列表
     */
    void updateBatchById(List<AigcDynamicAdjustRecordDetail> recordDetails);

    /**
     * 批量保存动态调整记录明细
     *
     * @param recordDetails 动态调整记录明细列表
     */
    void saveBatch(List<AigcDynamicAdjustRecordDetail> recordDetails);
}
