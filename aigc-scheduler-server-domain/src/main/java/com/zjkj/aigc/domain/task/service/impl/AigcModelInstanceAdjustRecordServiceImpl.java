package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordReq;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceAdjustRecordService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelInstanceAdjustRecordDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstanceAdjustRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模型实例调整记录实现类
 *
 * <AUTHOR>
 * @since 2025/01/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcModelInstanceAdjustRecordServiceImpl implements AigcModelInstanceAdjustRecordService {

    private final AigcModelInstanceAdjustRecordDao aigcModelInstanceAdjustRecordDao;

    @Override
    public Page<AigcModelInstanceAdjustRecord> queryPage(AigcModelInstanceAdjustRecordCondition condition) {
        return aigcModelInstanceAdjustRecordDao.queryPage(condition);
    }

    @Override
    public void create(AigcModelInstanceAdjustRecordReq req) {
        AigcModelInstanceAdjustRecord record = BeanUtil.copyProperties(req, AigcModelInstanceAdjustRecord.class, "id");
        aigcModelInstanceAdjustRecordDao.save(record);
    }

    @Override
    public void update(AigcModelInstanceAdjustRecordReq req) {
        AigcModelInstanceAdjustRecord record = BeanUtil.copyProperties(req, AigcModelInstanceAdjustRecord.class);
        aigcModelInstanceAdjustRecordDao.updateById(record);
    }

    @Override
    public AigcModelInstanceAdjustRecord queryById(Long id) {
        return aigcModelInstanceAdjustRecordDao.getById(id);
    }

    @Override
    public void delete(Long id) {
        aigcModelInstanceAdjustRecordDao.removeById(id);
    }
}
