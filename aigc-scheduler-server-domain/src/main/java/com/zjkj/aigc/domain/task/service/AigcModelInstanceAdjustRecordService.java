package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.model.AigcModelInstanceAdjustRecordReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval.GpuApplyCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstanceAdjustRecord;

/**
 * 模型实例表(AigcModelInstanceAdjustRecord)服务接口
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface AigcModelInstanceAdjustRecordService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcModelInstanceAdjustRecord> queryPage(AigcModelInstanceAdjustRecordCondition condition);

    /**
     * 创建
     *
     * @param req 创建数据
     */
    void create(AigcModelInstanceAdjustRecordReq req);

    /**
     * 更新
     *
     * @param req 更新数据
     */
    void update(AigcModelInstanceAdjustRecordReq req);

    /**
     * 根据id查询
     *
     * @param id 数据ID
     * @return 详情
     */
    AigcModelInstanceAdjustRecord queryById(Long id);

    /**
     * 删除
     *
     * @param id 数据ID
     */
    void delete(Long id);

}
