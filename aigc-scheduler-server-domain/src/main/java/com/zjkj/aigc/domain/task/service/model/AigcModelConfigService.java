package com.zjkj.aigc.domain.task.service.model;

import com.zjkj.aigc.common.req.model.AigcModelConfigCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;

import java.util.List;

/**
 * 模型配置表(AigcModelConfig)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19 15:37:13
 */
public interface AigcModelConfigService {

    /**
     * 根据模型id获取
     *
     * @param modelId 模型id
     * @return 模型配置
     */
    AigcModelConfig getByModelId(Long modelId);

    /**
     * 刷新模型配置
     *
     * @param modelDeploy 模型部署
     */
    void refreshModelConfig(AigcModelDeploy modelDeploy);

    /**
     * 根据模型id集合获取
     *
     * @param modelIds 模型id集合
     * @return 模型配置集合
     */
    List<AigcModelConfig> getByModelIds(List<Long> modelIds);

    /**
     * 创建模型配置
     *
     * @param req 模型配置
     */
    void createModelConfig(AigcModelConfigCreateReq req);

    /**
     * 更新模型配置
     *
     * @param req 模型配置
     */
    void updateModelConfig(AigcModelConfigCreateReq req);
}
