package com.zjkj.aigc.domain.task.service.impl.approval;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.req.approval.GpuApplyCreateReq;
import com.zjkj.aigc.common.req.approval.GpuApplyStatusReq;
import com.zjkj.aigc.common.util.PreciseTimeNumberGenerator;
import com.zjkj.aigc.domain.task.service.approval.GpuApplyService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.approval.GpuApplyCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.approval.GpuApplyDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.approval.GpuApply;
import com.zjkj.saas.admin.sdk.dto.SsoUserDTO;
import com.zjkj.saas.admin.sdk.util.SaasSsoContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * GPU资源申请表(GpuApply)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GpuApplyServiceImpl implements GpuApplyService {

    private final GpuApplyDao gpuApplyDao;

    @Override
    public Page<GpuApply> queryPage(GpuApplyCondition condition) {
        return gpuApplyDao.queryPage(condition);
    }

    /**
     * 创建GPU资源申请表
     *
     * @param req GPU资源申请数据
     */
    @Override
    public void createGpuApply(GpuApplyCreateReq req) {
        LocalDateTime now = LocalDateTime.now();
        GpuApply gpuApply = BeanUtil.copyProperties(req, GpuApply.class, "id");
        gpuApply.setStatus(ApprovalStatusEnum.PENDING.getStatus());
        gpuApply.setCode(PreciseTimeNumberGenerator.generateNumber());
        gpuApplyDao.save(gpuApply);
    }

    @Override
    public void updateGpuApply(GpuApplyCreateReq req) {
        GpuApply gpuApply = BeanUtil.copyProperties(req, GpuApply.class);
        gpuApplyDao.updateById(gpuApply);
    }

    @Override
    public GpuApply queryById(Long id, boolean nullThrowEx) {
        GpuApply gpuApply = gpuApplyDao.getById(id);
        if (nullThrowEx && Objects.isNull(gpuApply)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS);
        }
        return gpuApply;
    }

    @Override
    public void updateGpuApplyStatus(GpuApplyStatusReq statusReq) {
        GpuApply apply = queryById(statusReq.getId(), Boolean.TRUE);
        apply.setStatus(statusReq.getStatus());
        apply.setSupplyRemark(statusReq.getSupplyRemark());
        gpuApplyDao.updateById(apply);
    }
}
