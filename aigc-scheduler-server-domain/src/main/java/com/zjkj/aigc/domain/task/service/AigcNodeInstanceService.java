package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.RegisterStatusEnum;
import com.zjkj.aigc.common.req.node.AigcNodeHeartbeat;
import com.zjkj.aigc.common.req.node.AigcNodeRegisterReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcNodeInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcNodeInstance;

import java.time.Duration;
import java.util.List;

/**
 * 节点实例表(AigcNodeInstance)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-25 18:49:04
 */
public interface AigcNodeInstanceService {

    /**
     * 节点注册
     *
     * @param register 注册信息
     */
    AigcNodeInstance nodeRegister(AigcNodeRegisterReq register);

    /**
     * 节点心跳
     *
     * @param heartbeat 心跳信息
     * @return boolean
     */
    boolean nodeHeartbeat(AigcNodeHeartbeat heartbeat);

    /**
     * 判断节点是否合法
     *
     * @param nodeIp       节点IP
     * @param podNamespace pod命名空间
     * @return 是否合法
     */
    boolean isLegalNode(String nodeIp, String podNamespace);

    /**
     * 获取集群ID
     *
     * @param nodeIp       节点IP
     * @param podNamespace pod命名空间
     * @return 集群ID
     */
    Long getClusterId(String nodeIp, String podNamespace);

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcNodeInstance> queryPage(AigcNodeInstanceCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcNodeInstance> queryList(AigcNodeInstanceCondition condition);

    /**
     * 根据hostIp改变状态
     *
     * @param hostIps      ip列表
     * @param sourceStatus 原状态
     * @param targetStatus 目标状态
     */
    void changeStatusByHostIp(List<String> hostIps, RegisterStatusEnum.Compliance sourceStatus, RegisterStatusEnum.Compliance targetStatus);

    /**
     *  检查健康状态
     * @param timeout 超时时间
     */
    void checkHealthStatus(Duration timeout);
}
