package com.zjkj.aigc.domain.task.service.resource;

import com.zjkj.aigc.common.req.resource.ResourceBillGroupConfigCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillGroupConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBillGroupConfig;

import java.util.List;

/**
 * 费用分摊信息配置表(ResourceBillGroupConfig)服务接口
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
public interface ResourceBillGroupConfigService {
    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<ResourceBillGroupConfig> queryList(ResourceBillGroupConfigCondition condition);
    /**
     * 保存或更新
     *
     * @param req 费用分摊信息配置数据
     */
    void saveOrUpdateBatch(ResourceBillGroupConfigCreateReq req);
}
