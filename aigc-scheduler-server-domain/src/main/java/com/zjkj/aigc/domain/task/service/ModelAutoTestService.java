package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.ModelTestReportSubmitDTO;
import com.zjkj.aigc.common.req.model.test.ModelTestCreateReq;
import com.zjkj.aigc.common.vo.ModelTestReportInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.ModelAutoTestRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestParam;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.ModelAutoTestReport;

import java.util.List;

/**
 * 模型测试服务接口
 *
 * <AUTHOR>
 * @since 2024-10-21 10:49:49
 */
public interface ModelAutoTestService {

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<ModelAutoTestRecord> queryRecordByPage(Page<ModelAutoTestRecord> page, ModelAutoTestRecordCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    List<ModelAutoTestRecord> queryRecordList(ModelAutoTestRecordCondition condition);

    /**
     * 检查是否存在
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 是否存在
     */
    boolean checkSameModelExist(String taskType, String modelName);

    /**
     * 保存模型测试
     *
     * @param req 请求参数
     */
    void saveModelAutoTest(ModelTestCreateReq req);

    /**
     * 根据测试编号查询
     *
     * @param testNo 测试编号
     * @return 测试数据
     */
    ModelAutoTestRecord queryByTestNo(String testNo);

    /**
     * 根据测试编号查询
     *
     * @param testNo 测试编号
     * @return 测试数据
     */
    List<ModelAutoTestParam> queryParamByTestNo(String testNo);

    /**
     * 根据测试编号查询
     *
     * @param testNo 测试编号
     * @param nullThrowEx 是否抛出异常
     * @return 测试数据
     */
    ModelAutoTestRecord queryByTestNo(String testNo, boolean nullThrowEx);

    /**
     * 更新模型测试
     *
     * @param req    请求参数
     */
    boolean updateModelAutoTest(ModelTestCreateReq req);

    /**
     * 开始模型测试
     * @param testNo 测试编号
     */
    void startModelTest(String testNo);

    /**
     * 取消模型测试
     * @param testNo 测试编号
     */
    void cancelModelTest(String testNo);

    /**
     * 删除模型测试
     * @param testNo 测试编号
     */
    void delModelTest(String testNo);

    /**
     * 查看测试报告
     * @param testNo 测试编号
     * @return 测试报告
     */
    ModelAutoTestReport queryReportByTestNo(String testNo);

    /**
     * 提交测试报告
     * @param reportSubmit 提交信息
     */
    void reportSubmit(ModelTestReportSubmitDTO<ModelTestReportInfo> reportSubmit);

    /**
     * 检查是否完成
     * @param record 测试记录
     */
    void checkDone(ModelAutoTestRecord record);

}
