package com.zjkj.aigc.domain.task.service.impl.model;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.AigcModelConfigCreateReq;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.model.AigcModelConfigDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 模型配置表(AigcModelConfig)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-19 15:37:13
 */
@Service
@RequiredArgsConstructor
public class AigcModelConfigServiceImpl implements AigcModelConfigService {

    private final AigcModelConfigDao aigcModelConfigDao;
    private final AigcModelInfoService aigcModelInfoService;

    @Override
    public AigcModelConfig getByModelId(Long modelId) {
        return aigcModelConfigDao.getByModelId(modelId);
    }

    /**
     * 转换为模型配置
     *
     * @param modelDeploy 模型部署信息
     * @return 模型配置
     */
    private AigcModelConfig covertToModelConfig(AigcModelDeploy modelDeploy) {
        ModelDeployResource resourceInfo = modelDeploy.getResourceInfo();
        return new AigcModelConfig().setModelVersion(modelDeploy.getModelVersion())
                .setProjectName(modelDeploy.getProjectName())
                .setProjectGitUrl(modelDeploy.getProjectGitUrl())
                .setModelVersion(modelDeploy.getModelVersion())
                .setDeploySdkVersion(modelDeploy.getDeploySdkVersion())
                .setModelFileOssUrl(modelDeploy.getModelFileOssUrl())
                .setIsEncrypt(modelDeploy.getIsEncrypt())
                .setMirrorImageUrl(modelDeploy.getMirrorImageUrl())
                .setCpuCore(resourceInfo.getCpuCore())
                .setMemorySize(resourceInfo.getMemorySize())
                .setGpuInfo(resourceInfo.getGpuInfo());
    }

    @Override
    public void refreshModelConfig(AigcModelDeploy modelDeploy) {
        AigcModelConfig aigcModelConfig = covertToModelConfig(modelDeploy);
        Long modelId = modelDeploy.getModelId();
        AigcModelConfig modelConfig = getByModelId(modelDeploy.getModelId());
        if (Objects.isNull(modelConfig)) {
            aigcModelConfig.setModelId(modelId);
            aigcModelConfigDao.save(aigcModelConfig);
            return;
        }

        aigcModelConfig.setId(modelConfig.getId());
        aigcModelConfigDao.updateById(aigcModelConfig);
    }

    @Override
    public List<AigcModelConfig> getByModelIds(List<Long> modelIds) {
        return aigcModelConfigDao.getByModelIds(modelIds);
    }

    @Override
    public void createModelConfig(AigcModelConfigCreateReq req) {
        AigcModelInfo modelInfo = aigcModelInfoService.getById(req.getModelId());
        BaseBizException.isTrue(Objects.nonNull(modelInfo), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.not.exists"));

        AigcModelConfig modelConfig = getByModelId(req.getModelId());
        BaseBizException.isTrue(Objects.isNull(modelConfig), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.config.already.exists"));

        AigcModelConfig aigcModelConfig = BeanUtil.copyProperties(req, AigcModelConfig.class, "id");
        aigcModelConfigDao.save(aigcModelConfig);
    }

    @Override
    public void updateModelConfig(AigcModelConfigCreateReq req) {
        AigcModelConfig modelConfig = aigcModelConfigDao.getById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(modelConfig), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.model.config.not.exists"));

        AigcModelConfig aigcModelConfig = BeanUtil.copyProperties(req, AigcModelConfig.class, "modelId");
        aigcModelConfigDao.updateById(aigcModelConfig);
    }
}
