package com.zjkj.aigc.domain.task.service.impl.model;

import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.common.util.MathUtil;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.domain.task.service.AigcModelInstanceService;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.domain.task.service.model.AigcModelUsageService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInstanceCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.model.AigcModelUsageDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelConfig;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 模型资源占用表(AigcModelUsage)服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-18 15:44:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcModelUsageServiceImpl implements AigcModelUsageService {

    private final AigcModelUsageDao aigcModelUsageDao;
    private final AigcModelInstanceService aigcModelInstanceService;
    private final AigcModelInfoService aigcModelInfoService;
    private final AigcModelConfigService aigcModelConfigService;

    @Override
    public List<AigcModelUsage> queryList(AigcModelUsageCondition condition) {
        return aigcModelUsageDao.queryList(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AigcModelUsage> calculateModelUsed(AigcClusterUsage clusterUsage, List<String> nodeIps) {
        Assert.notEmpty(nodeIps, "节点ip不能为空");
        // 统计模型使用情况
        List<AigcModelUsage> modelUsages = statModelUsage(clusterUsage, nodeIps);

        // 查询已有数据
        AigcModelUsageCondition condition = new AigcModelUsageCondition()
                .setClusterId(clusterUsage.getClusterId())
                .setDataDate(clusterUsage.getDataDate());
        List<AigcModelUsage> extUsages = queryList(condition);

        // 完全新增
        if (!CollectionUtils.isEmpty(modelUsages) && CollectionUtils.isEmpty(extUsages)) {
            aigcModelUsageDao.saveBatch(modelUsages);
            return modelUsages;
        }

        // 删除
        if (CollectionUtils.isEmpty(modelUsages) && !CollectionUtils.isEmpty(extUsages)) {
            List<Long> ids = StreamUtil.mapToList(extUsages, AigcModelUsage::getId);
            aigcModelUsageDao.removeBatchByIds(ids);
            return modelUsages;
        }

        // 更新
        Map<Map.Entry<String, String>, Long> extUsageIdMap = StreamUtil.toMap(extUsages, usage -> Map.entry(usage.getTaskType(), usage.getModelName()), AigcModelUsage::getId);
        List<AigcModelUsage> newList = new ArrayList<>();
        List<AigcModelUsage> updateList = new ArrayList<>();
        modelUsages.forEach(modelUsage -> {
            Map.Entry<String, String> modelKey = Map.entry(modelUsage.getTaskType(), modelUsage.getModelName());
            Long id = extUsageIdMap.get(modelKey);
            if (Objects.isNull(id)) {
                newList.add(modelUsage);
            } else {
                modelUsage.setId(id);
                updateList.add(modelUsage);
                extUsageIdMap.remove(modelKey);
            }
        });

        if (!CollectionUtils.isEmpty(newList)) {
            aigcModelUsageDao.saveBatch(newList);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcModelUsageDao.updateBatchById(updateList);
        }

        if (!CollectionUtils.isEmpty(extUsageIdMap)) {
            aigcModelUsageDao.removeBatchByIds(extUsageIdMap.values());
        }

        return modelUsages;
    }


    /**
     * 统计模型使用情况
     * @param clusterUsage 集群使用情况
     * @param nodeIps 节点ip
     * @return 模型使用情况
     */
    private List<AigcModelUsage> statModelUsage(AigcClusterUsage clusterUsage, List<String> nodeIps) {
        AigcModelInstanceCondition condition = new AigcModelInstanceCondition()
                .setNodeIps(nodeIps);
        // 查询在线模型实例
        Map<Map.Entry<String, String>, List<AigcModelInstance>> instanceMap = aigcModelInstanceService.queryInstance(condition);
        if (CollectionUtils.isEmpty(instanceMap)) {
            return List.of();
        }

        Set<String> taskTypes = new HashSet<>();
        Set<String> modelNames = new HashSet<>();
        instanceMap.keySet().forEach(entry -> {
            taskTypes.add(entry.getKey());
            modelNames.add(entry.getValue());
        });

        AigcModelInfoCondition modelInfoCondition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        // 查询模型基础信息
        List<AigcModelInfo> modelInfos = aigcModelInfoService.queryList(modelInfoCondition);
        if (CollectionUtils.isEmpty(modelInfos)) {
            return List.of();
        }

        Map<Map.Entry<String, String>, AigcModelInfo> modelInfoMap = StreamUtil.toMap(modelInfos, modelInfo -> Map.entry(modelInfo.getTaskType(), modelInfo.getModelName()));
        // 模型发布配置
        List<Long> modelIds = StreamUtil.mapToList(modelInfos, AigcModelInfo::getId);
        List<AigcModelConfig> modelConfigs = aigcModelConfigService.getByModelIds(modelIds);
        if (CollectionUtils.isEmpty(modelConfigs)) {
            return List.of();
        }

        Map<Long, AigcModelConfig> modelConfigMap = StreamUtil.toMap(modelConfigs, AigcModelConfig::getModelId);
        List<AigcModelUsage> modelUsages = new ArrayList<>(instanceMap.size());
        AigcModelUsage aigcModelUsage;
        for (Map.Entry<String, String> model : instanceMap.keySet()) {
            AigcModelInfo modelInfo = modelInfoMap.get(model);
            if (Objects.isNull(modelInfo)) {
                continue;
            }

            AigcModelConfig modelConfig = modelConfigMap.get(modelInfo.getId());
            if (Objects.isNull(modelConfig)) {
                continue;
            }

            aigcModelUsage = new AigcModelUsage();
            aigcModelUsage.setModelNameZh(modelInfo.getName());
            aigcModelUsage.setTaskType(modelInfo.getTaskType());
            aigcModelUsage.setModelName(modelInfo.getModelName());
            aigcModelUsage.setModelType(modelInfo.getType());
            aigcModelUsage.setClusterId(clusterUsage.getClusterId());
            aigcModelUsage.setDataDate(clusterUsage.getDataDate());

            int podCount = instanceMap.getOrDefault(model, List.of()).size();
            aigcModelUsage.setPodCount((long) podCount);

            // gpu
            GpuInfo gpuInfo = modelConfig.getGpuInfo();
            aigcModelUsage.setGpuMemorySize(((long) podCount * gpuInfo.getMemorySize()));
            BigDecimal gpuRatio = MathUtil.percentage(clusterUsage.getTotalGpuMemorySize(), aigcModelUsage.getGpuMemorySize());
            aigcModelUsage.setGpuMemorySizeRatio(gpuRatio);

            // cpu
            aigcModelUsage.setCpuCore(((long) podCount * modelConfig.getCpuCore()));
            BigDecimal cpuRatio = MathUtil.percentage(clusterUsage.getTotalCpuCore(), aigcModelUsage.getCpuCore());
            aigcModelUsage.setCpuCoreRatio(cpuRatio);

            // 内存
            aigcModelUsage.setMemorySize(((long) podCount * modelConfig.getMemorySize()));
            BigDecimal memoryRatio = MathUtil.percentage(clusterUsage.getTotalMemorySize(), aigcModelUsage.getMemorySize());
            aigcModelUsage.setMemorySizeRatio(memoryRatio);
            modelUsages.add(aigcModelUsage);
        }

        return modelUsages;
    }
}
