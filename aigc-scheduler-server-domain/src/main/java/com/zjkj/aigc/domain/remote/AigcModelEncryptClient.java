package com.zjkj.aigc.domain.remote;

import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@FeignClient(name = "AigcModelEncryptClient",
        url = "${ai.model.encrypt.endpoint:}",
        path = "/api/v1",
        configuration = FeignClientConfig.class)
public interface AigcModelEncryptClient {

    /**
     * 模型加密
     * @param modelEncrypt 模型加密参数
     * @return 信息
     */
    @PostMapping("/oss/upload_finish")
    DataResponse<String> encryptModel(@RequestBody ModelEncryptDTO modelEncrypt);
}
