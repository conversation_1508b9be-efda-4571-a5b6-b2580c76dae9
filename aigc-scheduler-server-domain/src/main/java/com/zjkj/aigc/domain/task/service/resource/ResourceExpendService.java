package com.zjkj.aigc.domain.task.service.resource;

import com.zjkj.aigc.common.req.resource.ResourceExpendCreateReq;
import com.zjkj.aigc.common.req.resource.ResourceMidPlanCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceExpendCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceMidPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceExpend;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;

import java.util.List;

/**
 * 资源支出(ResourceExpend)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface ResourceExpendService {

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<ResourceExpend> queryList(ResourceExpendCondition condition);

    /**
     * 批量创建支出
     *
     * @param reqs 支出数据
     */
    void createExpendBath(List<ResourceExpendCreateReq.Expend> reqs);

    /**
     * 批量更新支出
     *
     * @param req 支出数据
     */
    void updateExpendBath(List<ResourceExpendCreateReq.Expend> reqs);

}
