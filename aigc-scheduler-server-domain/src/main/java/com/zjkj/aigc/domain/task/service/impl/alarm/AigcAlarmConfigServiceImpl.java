package com.zjkj.aigc.domain.task.service.impl.alarm;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.alarm.AigcAlarmConfigCreateReq;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmConfigCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmConfigDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型告警配置(AigcAlarmConfig)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcAlarmConfigServiceImpl implements AigcAlarmConfigService {

    private final AigcAlarmConfigDao configDao;

    @Override
    public Page<AigcAlarmConfig> queryPage(AigcAlarmConfigCondition condition) {
        return configDao.queryPage(condition);
    }

    @Override
    public void createAigcAlarmConfig(AigcAlarmConfigCreateReq req) {
        AigcAlarmConfig config = BeanUtil.copyProperties(req, AigcAlarmConfig.class, "id");
        configDao.save(config);
    }

    @Override
    public AigcAlarmConfig queryById(Long id) {
        return configDao.getById(id);
    }

    @Override
    public void updateAigcAlarmConfig(AigcAlarmConfigCreateReq req) {
        AigcAlarmConfig config = BeanUtil.copyProperties(req, AigcAlarmConfig.class);
        configDao.updateById(config);
    }

    @Override
    public void updateAigcAlarmConfigStatus(Long id, Integer status) {
        AigcAlarmConfig config = new AigcAlarmConfig();
        config.setId(id);
        config.setStatus(status);
        configDao.updateById(config);
    }

    @Override
    public void deleteById(Long id) {
        configDao.removeById(id);
    }
}
