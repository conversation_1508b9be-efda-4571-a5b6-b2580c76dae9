package com.zjkj.aigc.domain.task.service.impl.dynamic;

import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicScaleTypeEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordDetailService;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordDetailCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic.AigcDynamicAdjustRecordDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 动态调整记录表(AigcDynamicAdjustRecord)服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-16 11:23:06
 */
@Service
@RequiredArgsConstructor
public class AigcDynamicAdjustRecordServiceImpl implements AigcDynamicAdjustRecordService {

    private final AigcDynamicAdjustRecordDao dynamicAdjustRecordDao;
    private final AigcDynamicAdjustRecordDetailService dynamicAdjustRecordDetailService;

    @Override
    public Page<AigcDynamicAdjustRecord> queryPage(AigcDynamicAdjustRecordCondition condition) {
        return dynamicAdjustRecordDao.page(condition.getPage(), condition.buildQuery());
    }

    @Override
    public List<AigcDynamicAdjustRecord> queryList(AigcDynamicAdjustRecordCondition condition) {
        List<AigcDynamicAdjustRecord> records = dynamicAdjustRecordDao.list(condition.buildQuery());
        if (!CollectionUtils.isEmpty(records) && condition.isIncludeDetail()) {
            List<Long> ids = CollStreamUtil.toList(records, AigcDynamicAdjustRecord::getId);
            // 查询明细
            AigcDynamicAdjustRecordDetailCondition detailCondition = new AigcDynamicAdjustRecordDetailCondition()
                    .setAdjustRecordIds(ids);
            List<AigcDynamicAdjustRecordDetail> details = dynamicAdjustRecordDetailService.queryList(detailCondition);
            Map<Long, List<AigcDynamicAdjustRecordDetail>> detailMap = CollStreamUtil.groupByKey(details, AigcDynamicAdjustRecordDetail::getAdjustRecordId);
            records.forEach(record -> {
                List<AigcDynamicAdjustRecordDetail> recordDetails = detailMap.getOrDefault(record.getId(), List.of());
                record.setRecordDetails(recordDetails);
            });
        }
        return records;
    }

    @Override
    public List<AigcDynamicAdjustRecord> queryEffectiveRecord(Collection<Long> modelIds) {
        AigcDynamicAdjustRecordDetailCondition detailCondition = new AigcDynamicAdjustRecordDetailCondition()
                .setScaleType(DynamicScaleTypeEnum.SCALE_IN.getType())
                .setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setModelIds(modelIds)
                ;
        // 查询明细
        List<AigcDynamicAdjustRecordDetail> recordDetails = dynamicAdjustRecordDetailService.queryList(detailCondition);
        if (CollectionUtils.isEmpty(recordDetails)) {
            return List.of();
        }

        // 查询记录
        List<Long> recordIds = CollStreamUtil.toList(recordDetails, AigcDynamicAdjustRecordDetail::getAdjustRecordId);
        AigcDynamicAdjustRecordCondition recordCondition = new AigcDynamicAdjustRecordCondition()
                .setScaleType(DynamicScaleTypeEnum.SCALE_OUT.getType())
                .setScaleStatus(DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setIncludeDetail(Boolean.TRUE);
        recordCondition.setIds(recordIds);
        return queryList(recordCondition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rollbackInstanceRecord(AigcDynamicAdjustRecord record, String remark) {
        if (!Objects.equals(record.getStatus(),  DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                || !Objects.equals(record.getScaleStatus(), DynamicAdjustScaleStatusEnum.SUCCESS.getStatus())) {
            return false;
        }

        boolean changed = dynamicAdjustRecordDao.changeStatus(record.getId(), record.getStatus(), DynamicAdjustStatusEnum.END.getStatus());
        if (!changed) {
            return false;
        }

        AigcDynamicAdjustRecord rollbackRecord = new AigcDynamicAdjustRecord()
                .setRelatedId(record.getId())
                .setScaleType(DynamicScaleTypeEnum.SCALE_IN.getType())
                .setScaleStatus(DynamicAdjustScaleStatusEnum.PENDING.getStatus())
                .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus())
                .setModelId(record.getModelId())
                .setModelNameZh(record.getModelNameZh())
                .setDynamicConfigId(record.getDynamicConfigId())
                .setRemark(remark)
                .setRecordDetails(record.getRecordDetails());
        dynamicAdjustRecordDao.save(rollbackRecord);
        dynamicAdjustRecordDetailService.saveRollbackRecordDetails(rollbackRecord);
        return true;
    }

    @Override
    public void updateById(AigcDynamicAdjustRecord record) {
        dynamicAdjustRecordDao.updateById(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveScaleOutRecord(List<AigcDynamicAdjustRecord> adjustRecords) {
       dynamicAdjustRecordDao.saveBatch(adjustRecords);

        //保存明细
        List<AigcDynamicAdjustRecordDetail> recordDetails = adjustRecords.stream()
                .flatMap(record -> record.getRecordDetails().stream()
                        .map(detail -> detail.setAdjustRecordId(record.getId())))
                .collect(Collectors.toList());
        dynamicAdjustRecordDetailService.saveBatch(recordDetails);
    }

    @Override
    public boolean changeScaleStatus(Long id, DynamicAdjustScaleStatusEnum source, DynamicAdjustScaleStatusEnum target) {
        return dynamicAdjustRecordDao.changeScaleStatus(id, source.getStatus(), target.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelAdjust(AigcDynamicAdjustRecord record) {
        BaseBizException.isTrue(
                Objects.equals(record.getScaleStatus(), DynamicAdjustScaleStatusEnum.PENDING.getStatus()) && Objects.equals(record.getStatus(), DynamicAdjustStatusEnum.EFFECTIVE.getStatus()),
                CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.dynamic.adjust.status.cannot.cancel"));

        boolean changed = dynamicAdjustRecordDao.changeScaleStatus(record.getId(), record.getScaleStatus(), DynamicAdjustScaleStatusEnum.CANCEL.getStatus());
        BaseBizException.isTrue(changed, CustomErrorCode.DATA_CHANGED);

        AigcDynamicAdjustRecord update = new AigcDynamicAdjustRecord()
                .setScaleStatus(DynamicAdjustScaleStatusEnum.CANCEL.getStatus())
                .setStatus(DynamicAdjustStatusEnum.CANCEL.getStatus());
        update.setId(record.getId());
        dynamicAdjustRecordDao.updateById(update);

        List<AigcDynamicAdjustRecordDetail> updateDetails = record.getRecordDetails().stream()
                .map(recordDetail -> {
                    AigcDynamicAdjustRecordDetail detail = new AigcDynamicAdjustRecordDetail()
                            .setScaleStatus(DynamicAdjustScaleStatusEnum.CANCEL.getStatus())
                            .setStatus(DynamicAdjustStatusEnum.CANCEL.getStatus());
                    detail.setId(recordDetail.getId());
                    return detail;
                }).collect(Collectors.toList());
        dynamicAdjustRecordDetailService.updateBatchById(updateDetails);
    }
}
