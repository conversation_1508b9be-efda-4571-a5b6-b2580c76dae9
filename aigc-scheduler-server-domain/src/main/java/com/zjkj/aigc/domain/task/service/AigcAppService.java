package com.zjkj.aigc.domain.task.service;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcApp;

import java.util.List;

/**
 * 应用表(AigcApp)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-04 11:44:11
 */
public interface AigcAppService {

    /**
     * 查询启用的应用
     * @return 应用列表
     */
    List<AigcApp> enableApp();

    /**
     * 查询指定时间之后活跃的应用
     *
     * @param currentTime 当前时间
     * @return 应用列表
     */
    List<AigcApp> activeAfterTime(String currentTime);

    /**
     * 刷新活跃应用
     *
     * @param aigcAppList 活跃任务列表
     */
    void refreshActiveApp(List<AigcApp> aigcAppList);
}
