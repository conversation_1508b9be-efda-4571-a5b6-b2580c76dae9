package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptCallbackDTO;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;

import java.util.List;

/**
 * 模型发布记录表(AigcModelDeploy)服务接口
 *
 * <AUTHOR>
 * @since 2024-11-11 18:30:07
 */
public interface AigcModelDeployService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcModelDeploy> queryPage(AigcModelDeployCondition condition);

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcModelDeploy> queryList(AigcModelDeployCondition condition);

    /**
     * 根据id查询
     *
     * @param id id
     * @return 详情
     */
    AigcModelDeploy queryById(Long id);

    /**
     * 根据id查询
     *
     * @param id id
     * @param nullThrowEx 不存在是否抛出异常
     * @return 详情
     */
    AigcModelDeploy queryById(Long id, boolean nullThrowEx);

    /**
     * 存在指定状态的发布记录
     *
     * @param modelId    模型id
     * @param statusList 状态列表
     * @return 是否存在
     */
    boolean existAppointStatusSameDeploy(Long modelId, List<Integer> statusList);

    /**
     * 创建模型发布
     *
     * @param req 保存参数
     */
    void createAigcModelDeploy(AigcModelDeployCreateReq req);

    /**
     * 更新模型发布
     * @param req 更新参数
     */
    boolean updateAigcModelDeploy(AigcModelDeployCreateReq req);

    /**
     * 删除模型发布
     * @param id 模型发布id
     */
    void deleteAigcModelDeploy(Long id);

    /**
     * 请求部署
     * @param id 模型发布id
     */
    AigcModelDeploy requestDeploy(Long id);

    /**
     * 加密回调
     * @param callback 加密回调参数
     * @return 更新后的模型发布
     */
    AigcModelDeploy encryptCallback(ModelEncryptCallbackDTO callback);

    /**
     * 完成模型发布
     * @param id 模型发布id
     */
    AigcModelDeploy deployDone(Long id);
}
