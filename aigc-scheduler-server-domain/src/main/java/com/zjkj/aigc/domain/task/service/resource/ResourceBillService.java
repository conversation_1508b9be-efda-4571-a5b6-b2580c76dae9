package com.zjkj.aigc.domain.task.service.resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.dto.resource.BillImportDTO;
import com.zjkj.aigc.common.enums.resource.ResourceBillTypeEnum;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资源账单表(ResourceBill)服务接口
 *
 * <AUTHOR>
 * @since 2025-01-08 10:37:52
 */
public interface ResourceBillService {

    /**
     * 导入账单
     *
     * @param typeEnum       账单类型
     * @param platform       平台
     * @param billImportList 账单列表
     * @return 数量
     */
    Integer importBill(ResourceBillTypeEnum typeEnum, String platform, List<BillImportDTO> billImportList);

    /**
     * 查询账单
     *
     * @param condition 查询条件
     * @return 账单列表
     */
    Page<ResourceBill> queryPage(ResourceBillCondition condition);

    /**
     * 账单总额
     *
     * @param condition 查询条件
     * @return 总额
     */
    BigDecimal billTotal(ResourceBillCondition condition);

    /**
     * 查询账单集合
     *
     * @param condition 查询条件
     * @return 账单集合
     */
    List<ResourceBill> queryList(ResourceBillCondition condition);
}
