package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.model.AigcModelInfoEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.AigcModelInfoCreateReq;
import com.zjkj.aigc.common.util.EnvUtil;
import com.zjkj.aigc.domain.task.service.AigcModelInfoService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelInfoCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelInfoDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInstance;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 模型基础信息表(AigcModelInfo)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-11 18:30:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcModelInfoServiceImpl implements AigcModelInfoService {

    private final AigcModelInfoDao aigcModelInfoDao;

    @Override
    public Page<AigcModelInfo> queryPage(AigcModelInfoCondition condition) {
        return aigcModelInfoDao.queryPage(condition);
    }

    @Override
    public List<AigcModelInfo> queryList(AigcModelInfoCondition condition) {
        List<AigcModelInfo> aigcModelInfos = aigcModelInfoDao.queryList(condition);
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            return aigcModelInfos;
        }

        if (StringUtils.hasText(condition.getEnvType()) && Objects.equals(condition.getOnline(), Boolean.TRUE)) {
            return aigcModelInfos.stream()
                    .filter(v -> v.getInstanceByEnv(condition.getEnvType()) > 0)
                    .collect(Collectors.toList());
        }
        return aigcModelInfos;
    }

    @Override
    public AigcModelInfo getById(Long id) {
        return aigcModelInfoDao.getById(id);
    }

    @Override
    public AigcModelInfo queryByTaskTypeAndModelName(String taskType, String modelName) {
        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskType(taskType)
                .modelName(modelName)
                .limit(1)
                .build();
        List<AigcModelInfo> aigcModelInfoList = queryList(condition);
        return CollectionUtils.firstElement(aigcModelInfoList);
    }

    @Override
    public void saveModelInfo(AigcModelInfoCreateReq req) {
        AigcModelInfo existModelInfo = queryByTaskTypeAndModelName(req.getTaskType(), req.getModelName());
        BaseBizException.isTrue(Objects.isNull(existModelInfo), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.model.already.exists"));

        AigcModelInfo modelInfo = BeanUtil.copyProperties(req, AigcModelInfo.class, "id");
        modelInfo.setStatus( AigcModelInfoEnum.ENABLE.getStatus());
        aigcModelInfoDao.save(modelInfo);
    }

    @Override
    public void updateModelInfo(AigcModelInfoCreateReq req) {
        AigcModelInfo modelInfo = getById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(modelInfo), CustomErrorCode.DATA_NOT_EXISTS);
        // 判断是否修改了任务类型和模型名称
        if (!Objects.equals(modelInfo.getTaskType(), req.getTaskType()) || !Objects.equals(modelInfo.getModelName(), req.getModelName())) {
            AigcModelInfo existed = queryByTaskTypeAndModelName(req.getTaskType(), req.getModelName());
            BaseBizException.isTrue((Objects.isNull(existed) || Objects.equals(existed.getId(), req.getId())), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.model.test.not.allow.edit"));
        }

        AigcModelInfo updateModelInfo = BeanUtil.copyProperties(req, AigcModelInfo.class);
        aigcModelInfoDao.updateById(updateModelInfo);
    }

    @Override
    public void deleteModelInfo(Long id) {
        AigcModelInfo modelInfo = getById(id);
        BaseBizException.isTrue(Objects.nonNull(modelInfo), CustomErrorCode.DATA_NOT_EXISTS);

        aigcModelInfoDao.delById(id);
    }

    @Override
    public void changeStatus(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids) || Objects.isNull(status)) {
            return;
        }

        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .idList(ids)
                .limit(ids.size())
                .build();
        List<AigcModelInfo> modelInfoList = queryList(condition);
        if (CollectionUtils.isEmpty(modelInfoList)) {
            return;
        }

        ids = modelInfoList.stream()
                .map(AigcModelInfo::getId)
                .collect(Collectors.toList());
        aigcModelInfoDao.changeStatus(ids, status);
    }

    @Override
    public void refreshActiveTime(Map<Map.Entry<String, String>, LocalDateTime> statMap) {
        if (CollectionUtils.isEmpty(statMap)) {
            return;
        }

        Set<String> taskTypes = Sets.newHashSetWithExpectedSize(statMap.size());
        Set<String> modelNames = Sets.newHashSetWithExpectedSize(statMap.size());
        statMap.keySet().forEach(entry -> {
            taskTypes.add(entry.getKey());
            modelNames.add(entry.getValue());
        });

        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        List<AigcModelInfo> aigcModelInfos = queryList(condition);
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            return;
        }

        List<AigcModelInfo> updateList = new ArrayList<>(aigcModelInfos.size());
        aigcModelInfos.forEach(modelInfo -> {
            LocalDateTime lastTime = statMap.get(Map.entry(modelInfo.getTaskType(), modelInfo.getModelName()));
            if (!Objects.equals(lastTime, modelInfo.getLastActiveTime())) {
                AigcModelInfo updateInfo = new AigcModelInfo();
                updateInfo.setId(modelInfo.getId());
                updateInfo.setLastActiveTime(lastTime);
                updateList.add(updateInfo);
            }
        });

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcModelInfoDao.updateBatchById(updateList);
        }
    }

    @Override
    public void refreshInstance(Map<Map.Entry<String, String>, List<AigcModelInstance>> modelInstanceMap) {
        // 分环境统计
        Map<Map.Entry<String, String>, Map<String, Long>> instanceMap = modelInstanceMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().stream()
                                .collect(Collectors.groupingBy(
                                        AigcModelInstance::getEnvType,
                                        Collectors.counting()
                                ))
                ));

        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .online(CollectionUtils.isEmpty(instanceMap) ? true : null)
                .build();
        List<AigcModelInfo> aigcModelInfos = queryList(condition);
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            return;
        }

        // 无在线实例，重置存量数据
        if (CollectionUtils.isEmpty(instanceMap)) {
            aigcModelInfos.forEach(info -> {
                info.setOnline(false);
                info.setEnvInstance(Map.of());
                info.setPreThreshold(0L);
            });
            aigcModelInfoDao.updateBatchById(aigcModelInfos);
            return;
        }

        // 过滤出需要更新的模型信息
        List<AigcModelInfo> updateList = new ArrayList<>(aigcModelInfos.size());
        String currentEnvType = EnvUtil.getCurrentEnvType();
        aigcModelInfos.forEach(model -> {
            Map<String, Long> envInstance = instanceMap.getOrDefault(Map.entry(model.getTaskType(), model.getModelName()), Map.of());
            if (!Objects.equals(envInstance, model.getEnvInstance())) {
                AigcModelInfo updateModel = new AigcModelInfo();
                updateModel.setId(model.getId());
                updateModel.setEnvInstance(envInstance);
                updateModel.setOnline(!envInstance.isEmpty());
                // 实例变更，更新预测负载
                long sourceInstance = model.getInstanceByEnv(currentEnvType);
                long newInstance = updateModel.getInstanceByEnv(currentEnvType);
                if (newInstance != sourceInstance) {
                    long calPreThreshold = model.calPreThreshold(model.getPreQuantity(), newInstance, model.getAvgElapsedTime());
                    updateModel.setPreThreshold(calPreThreshold);
                }
                updateList.add(updateModel);
            }
        });

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcModelInfoDao.updateBatchById(updateList);
        }
    }

    @Override
    public void refreshAvgElapsedTime(List<AigcTaskStatDay> aigcTaskStatDays) {
        if (CollectionUtils.isEmpty(aigcTaskStatDays)) {
            return;
        }

        // 过滤掉平均耗时为空的数据
        aigcTaskStatDays = aigcTaskStatDays.stream()
                .filter(v -> Objects.nonNull(v.getAvgElapsedTime()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aigcTaskStatDays)) {
            return;
        }

        Set<String> taskTypes = CollStreamUtil.toSet(aigcTaskStatDays, AigcTaskStatDay::getTaskType);
        Set<String> modelNames = CollStreamUtil.toSet(aigcTaskStatDays, AigcTaskStatDay::getModelName);
        AigcModelInfoCondition condition = AigcModelInfoCondition.builder()
                .taskTypes(taskTypes)
                .modelNames(modelNames)
                .build();
        List<AigcModelInfo> aigcModelInfos = queryList(condition);
        if (CollectionUtils.isEmpty(aigcModelInfos)) {
            return;
        }

        Map<Map.Entry<String, String>, AigcTaskStatDay> statMap = CollStreamUtil.toIdentityMap(aigcTaskStatDays, v -> Map.entry(v.getTaskType(), v.getModelName()));
        List<AigcModelInfo> updateList = new ArrayList<>(aigcModelInfos.size());
        aigcModelInfos.forEach(model -> {
            AigcTaskStatDay stat = statMap.get(Map.entry(model.getTaskType(), model.getModelName()));
            if (Objects.isNull(stat) || Objects.isNull(stat.getAvgElapsedTime())) {
                return;
            }

            AigcModelInfo updateModel = new AigcModelInfo();
            updateModel.setId(model.getId());
            updateModel.setAvgElapsedTime(stat.getAvgElapsedTime());
            updateList.add(updateModel);
        });

        if (!CollectionUtils.isEmpty(updateList)) {
            aigcModelInfoDao.updateBatchById(updateList);
            log.info("refreshAvgElapsedTime. model size:{}", updateList.size());
        }

    }

    @Override
    public void updateBatchById(List<AigcModelInfo> updateList) {
        aigcModelInfoDao.updateBatchById(updateList);
    }
}
