package com.zjkj.aigc.domain.task.service.impl.alarm;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelCreateReq;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmChannelService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmChannelCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmChannelDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型告警(AigcAlarm)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcAlarmChannelServiceImpl implements AigcAlarmChannelService {

    private final AigcAlarmChannelDao channelDao;

    @Override
    public List<AigcAlarmChannel> query(AigcAlarmChannelCondition condition) {
        return channelDao.list(condition.buildQuery());
    }

    @Override
    public AigcAlarmChannel getById(Long id) {
        return channelDao.getById(id);
    }

    @Override
    public void saveAigcAlarmChannel(AigcAlarmChannelCreateReq req) {
        AigcAlarmChannel channel = BeanUtil.copyProperties(req, AigcAlarmChannel.class, "id");
        channelDao.save(channel);
    }

    @Override
    public void updateAigcAlarmChannel(AigcAlarmChannelCreateReq req) {
        AigcAlarmChannel channel = BeanUtil.copyProperties(req, AigcAlarmChannel.class);
        channelDao.updateById(channel);
    }

    @Override
    public void deleteAigcAlarmChannel(Long id) {
        channelDao.removeById(id);
    }
}
