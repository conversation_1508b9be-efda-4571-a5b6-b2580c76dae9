package com.zjkj.aigc.domain.task.service.resource;

import com.zjkj.aigc.common.req.resource.ResourceMidPlanCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceMidPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;

import java.util.List;

/**
 * 中间件资源规划表(ResourceMidPlan)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface ResourceMidPlanService {

    /**
     * 列表查询
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<ResourceMidPlan> queryList(ResourceMidPlanCondition condition);

    /**
     * 创建中间件资源规划表
     *
     * @param req 中间资源规划数据
     */
    void createMidPlan(ResourceMidPlanCreateReq req);

    /**
     * 更新中间件资源规划表
     *
     * @param req 中间件资源规划数据
     */
    void saveOrUpdateMidPlan(ResourceMidPlanCreateReq req);

    /**
     * 条件删除中间件资源规划表
     *
     * @param condition
     */
    void deleteMidPlan(ResourceMidPlanCondition condition);
}
