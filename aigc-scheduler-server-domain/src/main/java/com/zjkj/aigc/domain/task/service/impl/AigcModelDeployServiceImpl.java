package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.base.dto.DataResponse;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptCallbackDTO;
import com.zjkj.aigc.common.dto.model.encrypt.ModelEncryptDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.model.ModelDeployEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.model.AigcModelDeployCreateReq;
import com.zjkj.aigc.domain.config.propertie.ModelEncryptProperties;
import com.zjkj.aigc.domain.remote.AigcModelEncryptClient;
import com.zjkj.aigc.domain.task.service.AigcModelDeployService;
import com.zjkj.aigc.domain.task.service.model.AigcModelConfigService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelDeployCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcModelDeployDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelDeploy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 模型发布记录表(AigcModelDeploy)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-11 18:30:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcModelDeployServiceImpl implements AigcModelDeployService {

    private final ModelEncryptProperties modelEncryptProperties;
    private final AigcModelDeployDao aigcModelDeployDao;
    private final AigcModelEncryptClient aigcModelEncryptClient;
    private final AigcModelConfigService aigcModelConfigService;

    @Override
    public Page<AigcModelDeploy> queryPage(AigcModelDeployCondition condition) {
        return aigcModelDeployDao.queryPage(condition);
    }

    @Override
    public List<AigcModelDeploy> queryList(AigcModelDeployCondition condition) {
        return aigcModelDeployDao.queryList(condition);
    }

    @Override
    public AigcModelDeploy queryById(Long id) {
        return aigcModelDeployDao.getById(id);
    }

    @Override
    public AigcModelDeploy queryById(Long id, boolean nullThrowEx) {
        AigcModelDeploy aigcModelDeploy = queryById(id);
        if (nullThrowEx && Objects.isNull(aigcModelDeploy)) {
            throw new BaseBizException(CustomErrorCode.DATA_NOT_EXISTS);
        }
        return aigcModelDeploy;
    }

    @Override
    public boolean existAppointStatusSameDeploy(Long modelId, List<Integer> statusList) {
        AigcModelDeploy modelDeploy = queryAppointStatusSameDeploy(modelId, statusList);
        return Objects.nonNull(modelDeploy);
    }

    /**
     * 查询指定状态的发布记录
     *
     * @param modelId    模型id
     * @param statusList 状态列表
     * @return 发布记录
     */
    public AigcModelDeploy queryAppointStatusSameDeploy(Long modelId, List<Integer> statusList) {
        AigcModelDeployCondition condition = AigcModelDeployCondition.builder()
                .modelId(modelId)
                .statusList(statusList)
                .build();
        List<AigcModelDeploy> aigcModelDeploys = queryList(condition);
        return CollectionUtils.firstElement(aigcModelDeploys);
    }

    @Override
    public void createAigcModelDeploy(AigcModelDeployCreateReq req) {
        AigcModelDeploy aigcModelDeploy = BeanUtil.copyProperties(req, AigcModelDeploy.class, "id");
        aigcModelDeployDao.save(aigcModelDeploy);
    }

    @Override
    public boolean updateAigcModelDeploy(AigcModelDeployCreateReq req) {
        AigcModelDeploy modelDeploy = queryById(req.getId(), true);

        boolean allowStatus = Objects.equals(modelDeploy.getStatus(), ModelDeployEnum.Status.DRAFT.getStatus());
        BaseBizException.isTrue(allowStatus, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.deploy.draft.only.edit"));

        AigcModelDeploy updateModelDeploy = BeanUtil.copyProperties(req, AigcModelDeploy.class, "modelId");
        updateModelDeploy.setStatus(modelDeploy.getStatus());
        return aigcModelDeployDao.updateModelDeploy(updateModelDeploy);
    }

    @Override
    public void deleteAigcModelDeploy(Long id) {
        AigcModelDeploy modelDeploy = queryById(id, true);

        boolean notAllowed = Objects.equals(modelDeploy.getStatus(), ModelDeployEnum.Status.DEPLOYING.getStatus());
        BaseBizException.isTrue(!notAllowed, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.deploy.deploying.cannot.delete"));

        aigcModelDeployDao.removeById(id);
    }

    @Override
    public AigcModelDeploy requestDeploy(Long id) {
        AigcModelDeploy modelDeploy = queryById(id, true);
        boolean allowStatus = Objects.equals(modelDeploy.getStatus(), ModelDeployEnum.Status.DRAFT.getStatus());
        BaseBizException.isTrue(allowStatus, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.deploy.draft.only.request"));

        if (modelDeploy.getIsEncrypt()) {
            boolean modelFileChanged = modelDeploy.getChanged().isModelFileChanged();
            if (modelFileChanged) {
                boolean success = notifyEncryptCallback(modelDeploy);
                if (!success) {
                    log.info("requestDeploy(). 通知模型加密失败，deployId:{}", modelDeploy.getId());
                    throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.notify.model.encrypt.failed"));
                }
                modelDeploy.setEncryptStatus(ModelDeployEnum.EncryptStatus.ENCRYPTING.getStatus());
            } else {
                modelDeploy.setEncryptStatus(ModelDeployEnum.EncryptStatus.ENCRYPTED.getStatus());
            }
        }

        modelDeploy.setStatus(ModelDeployEnum.Status.DEPLOYING.getStatus());
        modelDeploy.setRevisedTime(LocalDateTime.now());
        aigcModelDeployDao.updateById(modelDeploy);
        return modelDeploy;
    }

    /**
     * 通知模型加密
     * @param modelDeploy 模型发布记录
     * @return 是否成功
     */
    public boolean notifyEncryptCallback(AigcModelDeploy modelDeploy) {
        ModelEncryptDTO modelEncrypt = new ModelEncryptDTO()
                .setDeployId(modelDeploy.getId())
                .setPatch(modelDeploy.getModelFileOssUrl())
                .setNotifyUrl(modelEncryptProperties.getCallbackUrl());
        DataResponse<String> resp = aigcModelEncryptClient.encryptModel(modelEncrypt);
        return resp.isSuccessful();
    }

    @Override
    public AigcModelDeploy encryptCallback(ModelEncryptCallbackDTO callback) {
        AigcModelDeploy modelDeploy = queryById(callback.getDeployId(), true);
        if (!Objects.equals(modelDeploy.getEncryptStatus(), ModelDeployEnum.EncryptStatus.ENCRYPTING.getStatus())) {
            log.info("encryptCallback(). 非【加密中】状态的发布记录，deployId:{}", modelDeploy.getId());
            throw new BaseBizException(CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.not.encrypting.status"));
        }

        if (Objects.equals(callback.getStatus(), ModelDeployEnum.EncryptCallbackStatus.SUCCESS.getStatus())) {
            modelDeploy.setEncryptStatus(ModelDeployEnum.EncryptStatus.ENCRYPTED.getStatus());
        } else {
            modelDeploy.setEncryptStatus(ModelDeployEnum.EncryptStatus.ENCRYPT_FAIL.getStatus());
            String message = StrUtil.sub(callback.getMessage(), 0, 200);
            modelDeploy.setMessage(message);
            modelDeploy.setStatus(ModelDeployEnum.Status.DEPLOY_FAIL.getStatus());
        }

        modelDeploy.setRevisedTime(LocalDateTime.now());
        aigcModelDeployDao.updateById(modelDeploy);
        return modelDeploy;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AigcModelDeploy deployDone(Long id) {
        AigcModelDeploy modelDeploy = queryById(id, true);
        boolean allowStatus = Objects.equals(modelDeploy.getStatus(), ModelDeployEnum.Status.DEPLOYING.getStatus());
        BaseBizException.isTrue(allowStatus, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.deploy.status.only.deploying"));
        
        modelDeploy.setStatus(ModelDeployEnum.Status.DEPLOY_SUCCESS.getStatus());
        modelDeploy.setRevisedTime(LocalDateTime.now());
        aigcModelDeployDao.updateById(modelDeploy);

        // 刷新模型配置
        aigcModelConfigService.refreshModelConfig(modelDeploy);
        return modelDeploy;
    }
}
