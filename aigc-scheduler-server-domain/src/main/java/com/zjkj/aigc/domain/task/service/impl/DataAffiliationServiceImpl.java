package com.zjkj.aigc.domain.task.service.impl;

import com.zjkj.aigc.domain.task.service.DataAffiliationService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.DataAffiliationDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.DataAffiliation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据所属表(DataAffiliation)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-11 15:32:42
 */
@Service
@RequiredArgsConstructor
public class DataAffiliationServiceImpl implements DataAffiliationService {

    private final DataAffiliationDao dataAffiliationDao;

    @Override
    public List<DataAffiliation> queryByDataTypeId(String dataType, List<String> dataIds) {
        return dataAffiliationDao.queryByDataTypeId(dataType, dataIds);
    }

    @Override
    public void create(DataAffiliation dataAffiliation) {
        dataAffiliationDao.save(dataAffiliation);
    }
}
