package com.zjkj.aigc.domain.task.service.impl.dynamic;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.OptionalDouble;
import java.util.stream.Collectors;

/**
 * 动态预测
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
public class AigcDynamicPredict {

    /**
     * 预测配置参数
     */
    @Data
    @Accessors(chain = true)
    public static class PredictConfig {
        private double todayTrendWeight = 5.0;
        private double yesterdayWeight = 3.0;
        private double lastWeekWeight = 2.0;
        private double nightFactor = 0.7;
        private int nightStartHour = 1;
        private int nightEndHour = 5;
        private int recentHoursWindow = 3;
        private double defaultConfidence = 0.5;
        // 标准差倍数，用于异常检测
        private double outlierThreshold = 3.0;
        // 指数平滑因子
        private double smoothingAlpha = 0.3;
        // 95%置信区间
        private double confidenceInterval = 0.95;
    }

    @Getter
    @AllArgsConstructor
    public enum DataType {
        TODAY(1.2),
        YESTERDAY(1.0),
        LAST_WEEK(0.9);
        private final double weight;
    }

    // 使用默认配置
    private static final PredictConfig DEFAULT_CONFIG = new PredictConfig();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskStat {
        /**
         * 时间小时
         */
        private Integer timeHour;
        /**
         * 数量
         */
        private Long quantity;
    }

    @Data
    @Accessors(chain = true)
    public static class TaskPrediction {
        /**
         * 预测的小时
         */
        private int preHour;
        /**
         * 预测的数量
         */
        private long preQuantity;
        /**
         * 置信度分数
         */
        private double confidenceScore;
        /**
         * 预测区间下限
         */
        private long lowerBound;
        /**
         * 预测区间上限
         */
        private long upperBound;
    }

    /**
     * 基于三组时间维度数据的任务预测
     *
     * @param todayData     当前模型小时统计数据
     * @param time          当前时间
     * @param yesterdayData 昨天的历史数据
     * @param lastWeekData  上周同一天的历史数据
     * @param predictHours  需要预测的小时数
     * @return 预测结果
     */
    public static List<TaskPrediction> predictWithTimePatterns(List<TaskStat> todayData,
                                                               List<TaskStat> yesterdayData,
                                                               List<TaskStat> lastWeekData,
                                                               LocalDateTime time,
                                                               int predictHours) {
        return predictWithTimePatterns(todayData, yesterdayData, lastWeekData, time, predictHours, DEFAULT_CONFIG);
    }

    /**
     * 基于三组时间维度数据的任务预测
     *
     * @param todayData     当前模型小时统计数据
     * @param time          当前时间
     * @param yesterdayData 昨天的历史数据
     * @param lastWeekData  上周同一天的历史数据
     * @param predictHours  需要预测的小时数
     * @param config        预测配置参数
     * @return 预测结果
     */
    public static List<TaskPrediction> predictWithTimePatterns(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            LocalDateTime time,
            int predictHours,
            PredictConfig config) {
        if (config == null) {
            config = DEFAULT_CONFIG;
        }

        int currentHour = time.getHour();
        // 参数验证
        validateInputParameters(currentHour, predictHours);

        // 数据预处理：清理异常值
        List<TaskStat> cleanedModelStatList = removeOutliers(todayData, config.getOutlierThreshold());
        List<TaskStat> cleanedYesterdayData = removeOutliers(yesterdayData, config.getOutlierThreshold());
        List<TaskStat> cleanedLastWeekData = removeOutliers(lastWeekData, config.getOutlierThreshold());

        List<TaskPrediction> predictions = new ArrayList<>();
        // 对未来几个小时进行预测
        for (int i = 1; i <= predictHours; i++) {
            int futureHour = (currentHour + i) % 24;

            // 选择合适的预测模型
            TaskPrediction prediction = predictWithModelSelection(
                    cleanedModelStatList, cleanedYesterdayData, cleanedLastWeekData,
                    currentHour, futureHour, time, config);

            // 计算预测区间
            calculatePredictionInterval(prediction, cleanedModelStatList, cleanedYesterdayData,
                    cleanedLastWeekData, futureHour, config);

            predictions.add(prediction);
        }

        return predictions;
    }

    /**
     * 根据不同条件选择预测模型
     */
    private static TaskPrediction predictWithModelSelection(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int currentHour,
            int futureHour,
            LocalDateTime time,
            PredictConfig config) {

        // 检查是否是特殊时间段
        if (isNightHour(futureHour, config)) {
            // 深夜时段使用特殊模型
            return predictNightHourQuantity(todayData, yesterdayData, lastWeekData, currentHour, futureHour, config);
        } else if (isPeakHour(futureHour, time)) {
            // 高峰时段使用特殊模型
            return predictPeakHourQuantity(todayData, yesterdayData, lastWeekData, currentHour, futureHour, config);
        } else {
            // 普通时段
            return predictNormalHourQuantity(todayData, yesterdayData, lastWeekData, currentHour, futureHour, config);
        }
    }

    /**
     * 预测普通时段任务量
     */
    private static TaskPrediction predictNormalHourQuantity(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int currentHour,
            int futureHour,
            PredictConfig config) {

        TaskPrediction prediction = new TaskPrediction()
                .setPreHour(futureHour);

        double todayTrend = calculateRecentTrend(todayData, currentHour, config);
        OptionalDouble yesterdayQuantity = getHourQuantity(yesterdayData, futureHour);
        OptionalDouble lastWeekQuantity = getHourQuantity(lastWeekData, futureHour);

        WeightedPrediction weightedPrediction = new WeightedPrediction();

        // 添加今天的趋势
        if (todayTrend > 0) {
            double adjustedWeight = adjustWeight(config.getTodayTrendWeight(), todayData, DataType.TODAY);
            weightedPrediction.addValue(todayTrend, adjustedWeight);
        }

        // 添加昨天同时段数据
        if (yesterdayQuantity.isPresent()) {
            double yesterdayValue = yesterdayQuantity.getAsDouble();
            double adjustedWeight = adjustWeight(config.getYesterdayWeight(), yesterdayData, DataType.YESTERDAY);
            weightedPrediction.addValue(yesterdayValue, adjustedWeight);
        }

        // 添加上周同时段数据
        if (lastWeekQuantity.isPresent()) {
            double lastWeekValue = lastWeekQuantity.getAsDouble();
            double adjustedWeight = adjustWeight(config.getLastWeekWeight(), lastWeekData, DataType.LAST_WEEK);
            weightedPrediction.addValue(lastWeekValue, adjustedWeight);
        }

        // 如果没有有效数据，使用基础预测
        if (weightedPrediction.isEmpty()) {
            long basicValue = basicPrediction(todayData, yesterdayData, lastWeekData);
            prediction.setPreQuantity(basicValue);
        } else {
            // 使用加权预测
            double finalPrediction = weightedPrediction.getWeightedAverage();

            // 应用小时因子
            double hourlyFactor = getHourlyFactor(yesterdayData, lastWeekData, futureHour);
            finalPrediction *= hourlyFactor;

            // 设置最终预测值
            prediction.setPreQuantity(Math.round(finalPrediction));
        }

        // 计算置信度分数
        double confidenceScore = calculateConfidenceScore(todayData, yesterdayData, lastWeekData, currentHour, futureHour, config);
        prediction.setConfidenceScore(confidenceScore);

        return prediction;
    }

    /**
     * 预测高峰时段任务量
     */
    private static TaskPrediction predictPeakHourQuantity(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int currentHour,
            int futureHour,
            PredictConfig config) {

        // 高峰时段增加上周数据的权重
        PredictConfig peakConfig = new PredictConfig()
                .setTodayTrendWeight(config.getTodayTrendWeight())
                .setYesterdayWeight(config.getYesterdayWeight())
                .setLastWeekWeight(config.getLastWeekWeight() * 1.5)
                .setNightFactor(config.getNightFactor())
                .setNightStartHour(config.getNightStartHour())
                .setNightEndHour(config.getNightEndHour())
                .setRecentHoursWindow(config.getRecentHoursWindow())
                .setDefaultConfidence(config.getDefaultConfidence())
                .setOutlierThreshold(config.getOutlierThreshold())
                .setSmoothingAlpha(config.getSmoothingAlpha());

        return predictNormalHourQuantity(
                todayData, yesterdayData, lastWeekData, currentHour, futureHour, peakConfig);
    }

    /**
     * 预测深夜时段任务量
     */
    private static TaskPrediction predictNightHourQuantity(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int currentHour,
            int futureHour,
            PredictConfig config) {

        // 先使用普通预测
        TaskPrediction prediction = predictNormalHourQuantity(
                todayData, yesterdayData, lastWeekData, currentHour, futureHour, config);

        // 应用夜间因子
        double nightFactor = getNightFactor(futureHour, config);
        long nightQuantity = Math.round(prediction.getPreQuantity() * nightFactor);

        // 更新预测值
        prediction.setPreQuantity(nightQuantity);
        return prediction;
    }

    /**
     * 计算预测区间
     */
    private static void calculatePredictionInterval(
            TaskPrediction prediction,
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int targetHour,
            PredictConfig config) {

        // 收集相关小时的历史数据
        List<Long> historicalValues = new ArrayList<>();

        // 从今天数据中获取
        if (!CollectionUtils.isEmpty(todayData)) {
            todayData.stream()
                    .filter(stat -> Math.abs(stat.getTimeHour() - targetHour) <= 2)
                    .forEach(stat -> historicalValues.add(stat.getQuantity()));
        }

        // 从昨天数据中获取目标小时
        yesterdayData.stream()
                .filter(stat -> stat.getTimeHour() == targetHour)
                .forEach(stat -> historicalValues.add(stat.getQuantity()));

        // 从上周数据中获取目标小时
        lastWeekData.stream()
                .filter(stat -> stat.getTimeHour() == targetHour)
                .forEach(stat -> historicalValues.add(stat.getQuantity()));

        // 如果没有足够的历史数据，使用预测值的百分比作为区间
        if (historicalValues.size() < 3) {
            long baseValue = prediction.getPreQuantity();
            double variationFactor = 0.3 / Math.max(0.1, prediction.getConfidenceScore());

            prediction.setLowerBound(Math.max(0, Math.round(baseValue * (1 - variationFactor))));
            prediction.setUpperBound(Math.round(baseValue * (1 + variationFactor)));
            return;
        }

        // 计算标准差
        double mean = historicalValues.stream().mapToLong(Long::longValue).average().orElse(0);
        double sumSquaredDiff = historicalValues.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .sum();
        double stdDev = Math.sqrt(sumSquaredDiff / historicalValues.size());

        // 使用z分数计算置信区间
        double z = getZScore(config.getConfidenceInterval());
        long margin = Math.round(z * stdDev);

        prediction.setLowerBound(Math.max(0, prediction.getPreQuantity() - margin));
        prediction.setUpperBound(prediction.getPreQuantity() + margin);
    }

    /**
     * 获取z分数（用于置信区间计算）
     */
    private static double getZScore(double confidenceLevel) {
        // 常用置信水平的z分数
        if (confidenceLevel >= 0.99) {
            return 2.576;
        }
        if (confidenceLevel >= 0.98) {
            return 2.326;
        }
        if (confidenceLevel >= 0.95) {
            return 1.96;
        }
        if (confidenceLevel >= 0.90) {
            return 1.645;
        }
        return 1.0;
    }

    /**
     * 根据数据质量调整权重
     */
    private static double adjustWeight(double baseWeight, List<TaskStat> data, DataType dataType) {
        if (CollectionUtils.isEmpty(data)) {
            return baseWeight * 0.5;
        }

        // 计算数据的方差作为质量指标
        double variance = calculateVariance(data);
        double qualityFactor = 1.0;

        if (variance > 0) {
            // 方差越大，数据波动越大，质量越低
            qualityFactor = 1.0 / (1.0 + Math.log10(variance) * 0.1);
        }

        // 数据近期性调整
        qualityFactor *= dataType.getWeight();

        return baseWeight * qualityFactor;
    }

    /**
     * 计算数据方差
     */
    private static double calculateVariance(List<TaskStat> data) {
        if (CollectionUtils.isEmpty(data) || data.size() < 2) {
            return 0;
        }

        double mean = data.stream()
                .mapToLong(TaskStat::getQuantity)
                .average()
                .orElse(0);

        double sumSquaredDiff = data.stream()
                .mapToDouble(stat -> Math.pow(stat.getQuantity() - mean, 2))
                .sum();

        return sumSquaredDiff / data.size();
    }

    /**
     * 检测并移除异常值
     */
    private static List<TaskStat> removeOutliers(List<TaskStat> data, double threshold) {
        if (CollectionUtils.isEmpty(data) || data.size() < 4) {
            return data;
        }

        // 计算均值和标准差
        double mean = data.stream()
                .mapToLong(TaskStat::getQuantity)
                .average()
                .orElse(0);

        double sumSquaredDiff = data.stream()
                .mapToDouble(stat -> Math.pow(stat.getQuantity() - mean, 2))
                .sum();

        double stdDev = Math.sqrt(sumSquaredDiff / data.size());

        // 过滤掉超出阈值的异常值
        return data.stream()
                .filter(stat -> Math.abs(stat.getQuantity() - mean) <= threshold * stdDev)
                .collect(Collectors.toList());
    }

    /**
     * 判断是否是高峰时段
     */
    private static boolean isPeakHour(int hour, LocalDateTime time) {
        // 工作日的9-11点和14-16点视为高峰
        DayOfWeek dayOfWeek = time.getDayOfWeek();
        boolean isWeekend = dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

        if (isWeekend) {
            // 周末的高峰时段可能不同
            return (hour >= 10 && hour <= 12) || (hour >= 15 && hour <= 18);
        } else {
            // 工作日高峰
            return (hour >= 9 && hour <= 11) || (hour >= 14 && hour <= 16);
        }
    }

    /**
     * 判断是否是深夜时段
     */
    private static boolean isNightHour(int hour, PredictConfig config) {
        return hour >= config.getNightStartHour() && hour <= config.getNightEndHour();
    }

    /**
     * 获取夜间因子
     */
    private static double getNightFactor(int hour, PredictConfig config) {
        if (!isNightHour(hour, config)) {
            return 1.0;
        }

        // 根据小时深度调整夜间因子
        int nightStartHour = config.getNightStartHour();
        int nightEndHour = config.getNightEndHour();
        double nightFactor = config.getNightFactor();

        // 计算深度调整值(0-1范围)，越靠近凌晨3点，值越低
        int midNight = (nightStartHour + nightEndHour) / 2;
        double depthAdjustment = 1.0 - Math.abs(hour - midNight) / (double) (nightEndHour - nightStartHour + 1);

        // 调整后的夜间因子
        return nightFactor + (1.0 - nightFactor) * (1.0 - depthAdjustment);
    }

    /**
     * 参数验证
     */
    private static void validateInputParameters(int currentHour, int predictHours) {
        if (currentHour < 0 || currentHour >= 24) {
            throw new IllegalArgumentException("Current hour must be between 0 and 23");
        }
        if (predictHours <= 0) {
            throw new IllegalArgumentException("Predict hours must be positive");
        }
    }

    /**
     * 使用指数平滑计算趋势
     */
    private static double calculateExponentialSmoothing(List<TaskStat> data, double alpha) {
        if (CollectionUtils.isEmpty(data) || data.size() < 2) {
            return 0;
        }

        // 按时间排序
        List<TaskStat> sortedData = data.stream()
                .sorted(Comparator.comparing(TaskStat::getTimeHour))
                .collect(Collectors.toList());

        // 初始值
        double smoothed = sortedData.get(0).getQuantity();

        // 应用指数平滑
        for (int i = 1; i < sortedData.size(); i++) {
            smoothed = alpha * sortedData.get(i).getQuantity() + (1 - alpha) * smoothed;
        }

        return smoothed;
    }

    /**
     * 计算最近的任务数趋势
     */
    private static double calculateRecentTrend(List<TaskStat> todayData, int currentHour, PredictConfig config) {
        if (CollectionUtils.isEmpty(todayData) || todayData.size() < 2) {
            return 0;
        }

        // 获取最近几个小时的数据
        List<TaskStat> recentData = todayData.stream()
                .filter(d -> d.getTimeHour() <= currentHour &&
                        d.getTimeHour() >= currentHour - config.getRecentHoursWindow())
                .sorted(Comparator.comparing(TaskStat::getTimeHour))
                .collect(Collectors.toList());

        if (recentData.size() < 2) {
            // 数据不足，无法计算趋势
            return 0;
        }

        // 计算简单的线性趋势
        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
        int n = recentData.size();

        for (TaskStat stat : recentData) {
            double x = stat.getTimeHour();
            double y = stat.getQuantity();
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumXX += x * x;
        }

        // 计算趋势斜率
        double slope = 0;
        if (n * sumXX - sumX * sumX != 0) {
            slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        }

        // 如果斜率无法计算或异常，使用指数平滑
        if (Double.isNaN(slope) || Double.isInfinite(slope)) {
            return calculateExponentialSmoothing(recentData, config.getSmoothingAlpha());
        }

        // 获取最后一个数据点
        TaskStat lastStat = recentData.get(n - 1);
        double lastQuantity = lastStat.getQuantity();

        // 使用线性外推
        double linearPrediction = lastQuantity + slope;

        // 使用指数平滑作为辅助预测
        double smoothedPrediction = calculateExponentialSmoothing(recentData, config.getSmoothingAlpha());

        // 融合两种预测结果
        if (slope > 0) {
            // 上升趋势，取较为保守的值
            return Math.min(linearPrediction, smoothedPrediction * 1.1);
        } else {
            // 下降趋势，取较为保守的值
            return Math.max(linearPrediction, smoothedPrediction * 0.9);
        }
    }

    /**
     * 获取特定小时的任务数
     */
    private static OptionalDouble getHourQuantity(List<TaskStat> data, int hour) {
        if (CollectionUtils.isEmpty(data)) {
            return OptionalDouble.empty();
        }

        return data.stream()
                .filter(d -> d.getTimeHour() == hour)
                .mapToLong(TaskStat::getQuantity)
                .average();
    }

    /**
     * 计算小时因子
     */
    private static double getHourlyFactor(List<TaskStat> yesterdayData, List<TaskStat> lastWeekData, int hour) {
        // 合并两天的数据来计算更准确的小时因子
        List<TaskStat> combinedData = new ArrayList<>();

        if (!CollectionUtils.isEmpty(yesterdayData)) {
            combinedData.addAll(yesterdayData);
        }

        if (!CollectionUtils.isEmpty(lastWeekData)) {
            combinedData.addAll(lastWeekData);
        }

        if (combinedData.isEmpty()) {
            // 默认因子
            return 1.0;
        }

        // 计算目标小时的平均值
        OptionalDouble hourAvg = combinedData.stream()
                .filter(d -> d.getTimeHour() == hour)
                .mapToLong(TaskStat::getQuantity)
                .average();

        // 计算所有小时的总平均值
        OptionalDouble totalAvg = combinedData.stream()
                .mapToLong(TaskStat::getQuantity)
                .average();

        if (hourAvg.isEmpty() || totalAvg.isEmpty() || totalAvg.getAsDouble() == 0) {
            return 1.0;
        }

        // 计算小时因子，但限制在合理范围内
        double factor = hourAvg.getAsDouble() / totalAvg.getAsDouble();

        // 限制因子范围，防止极端值
        return Math.max(0.5, Math.min(factor, 2.0));
    }

    /**
     * 基础预测（当没有足够历史数据时）
     */
    private static long basicPrediction(List<TaskStat> todayData, List<TaskStat> yesterdayData, List<TaskStat> lastWeekData) {
        List<Long> allValues = new ArrayList<>();

        // 收集所有可用数据
        if (!CollectionUtils.isEmpty(todayData)) {
            allValues.addAll(todayData.stream()
                    .map(TaskStat::getQuantity)
                    .collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(yesterdayData)) {
            allValues.addAll(yesterdayData.stream()
                    .map(TaskStat::getQuantity)
                    .collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(lastWeekData)) {
            allValues.addAll(lastWeekData.stream()
                    .map(TaskStat::getQuantity)
                    .collect(Collectors.toList()));
        }

        if (allValues.isEmpty()) {
            return 0;
        }

        // 计算平均值和中位数
        double avg = allValues.stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0);

        // 使用最近的非零值和平均值的加权平均
        long lastNonZeroValue = 0;

        if (!CollectionUtils.isEmpty(todayData)) {
            lastNonZeroValue = todayData.get(todayData.size() - 1).getQuantity();
        } else if (!CollectionUtils.isEmpty(yesterdayData)) {
            lastNonZeroValue = yesterdayData.get(yesterdayData.size() - 1).getQuantity();
        }

        if (lastNonZeroValue > 0) {
            return Math.round(0.7 * avg + 0.3 * lastNonZeroValue);
        } else {
            return Math.round(avg);
        }
    }

    /**
     * 计算预测的置信度分数
     */
    private static double calculateConfidenceScore(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int currentHour,
            int futureHour,
            PredictConfig config) {

        double baseScore = config.getDefaultConfidence();

        // 数据质量评分
        int dataQualityScore = calculateDataQualityScore(todayData, yesterdayData, lastWeekData);
        baseScore += 0.1 * dataQualityScore;

        // 数据一致性评分
        double consistencyScore = calculateDataConsistencyScore(todayData, yesterdayData, lastWeekData, futureHour);
        baseScore += 0.1 * consistencyScore;

        // 时间距离衰减
        double timeDistanceFactor = calculateTimeDistanceFactor(currentHour, futureHour);

        // 最终置信度，限制在0-1范围内
        return Math.min(1.0, Math.max(0.1, baseScore * timeDistanceFactor));
    }

    /**
     * 计算数据一致性评分
     */
    private static double calculateDataConsistencyScore(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData,
            int hour) {

        List<Double> hourValues = new ArrayList<>();

        // 收集指定小时的值
        OptionalDouble todayValue = todayData.stream()
                .filter(d -> d.getTimeHour() == hour)
                .mapToLong(TaskStat::getQuantity)
                .average();

        OptionalDouble yesterdayValue = yesterdayData.stream()
                .filter(d -> d.getTimeHour() == hour)
                .mapToLong(TaskStat::getQuantity)
                .average();

        OptionalDouble lastWeekValue = lastWeekData.stream()
                .filter(d -> d.getTimeHour() == hour)
                .mapToLong(TaskStat::getQuantity)
                .average();

        todayValue.ifPresent(hourValues::add);
        yesterdayValue.ifPresent(hourValues::add);
        lastWeekValue.ifPresent(hourValues::add);

        if (hourValues.size() < 2) {
            return 0;
        }

        // 计算变异系数(CV)作为一致性指标，CV越小表示一致性越高
        double mean = hourValues.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        if (mean == 0) {
            return 0;
        }

        double sumSquaredDiff = hourValues.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .sum();

        double stdDev = Math.sqrt(sumSquaredDiff / hourValues.size());
        double cv = stdDev / mean;

        // 转换为分数(0-1)，CV越小分数越高
        return Math.max(0, 1 - cv);
    }

    /**
     * 计算数据质量评分
     */
    private static int calculateDataQualityScore(
            List<TaskStat> todayData,
            List<TaskStat> yesterdayData,
            List<TaskStat> lastWeekData) {

        int score = 0;

        if (!CollectionUtils.isEmpty(todayData)) {
            score++;
        }

        if (!CollectionUtils.isEmpty(yesterdayData)) {
            score++;
        }

        if (!CollectionUtils.isEmpty(lastWeekData)) {
            score++;
        }

        return score;
    }

    /**
     * 计算时间距离因子
     */
    private static double calculateTimeDistanceFactor(int currentHour, int futureHour) {
        // 计算时间差，考虑跨天情况
        int hourDiff = futureHour >= currentHour ?
                futureHour - currentHour : futureHour + 24 - currentHour;

        // 时间越远，可信度越低，使用非线性衰减
        return Math.exp(-0.05 * hourDiff);
    }

    /**
     * 加权预测帮助类
     */
    private static class WeightedPrediction {
        private double sum = 0;
        private double weightSum = 0;

        public void addValue(double value, double weight) {
            sum += value * weight;
            weightSum += weight;
        }

        public boolean isEmpty() {
            return weightSum == 0;
        }

        public double getWeightedAverage() {
            return isEmpty() ? 0 : sum / weightSum;
        }
    }
}