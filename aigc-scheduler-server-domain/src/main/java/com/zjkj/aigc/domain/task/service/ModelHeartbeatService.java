package com.zjkj.aigc.domain.task.service;

import com.zjkj.aigc.common.req.model.HeartbeatCheckReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.HeartBeatCheckDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.HeartbeatCheck;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelHeartbeatService {

    private final HeartBeatCheckDao heartbeatCheckDao;

    /**
     * 保存心跳检查
     * @param req 心跳检查请求
     */
    public void saveHeartbeatCheck(HeartbeatCheckReq req) {
        HeartbeatCheck heartbeatCheck = new HeartbeatCheck();
        heartbeatCheck.setTaskType(req.getTaskType());
        heartbeatCheck.setContainerId(req.getContainerId());
        heartbeatCheck.setModelName(req.getModelName());
        heartbeatCheck.setStatus(req.getStatus());
        heartbeatCheckDao.save(heartbeatCheck);
    }
}
