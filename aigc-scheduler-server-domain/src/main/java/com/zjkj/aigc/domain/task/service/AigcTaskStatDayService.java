package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.task.TaskStatSummaryReq;
import com.zjkj.aigc.common.vo.TaskSummaryStatResp;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskSummaryCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.TaskSummaryStatCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;

import java.time.LocalDate;
import java.util.List;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:25
 */
public interface AigcTaskStatDayService {

    List<TaskSummaryStatResp> summary(TaskSummaryStatCondition condition);

    /**
     * 按模型分组分页查询
     *
     * @param page  分页对象
     * @param query 查询条件
     * @return 分页数据
     */
    Page<AigcTaskStatDay> pageByGroupModel(Page<AigcTaskStatDay> page, TaskStatSummaryReq query);

    /**
     * 按天统计
     *
     * @param appId 应用ID
     * @param date  日期
     */
    void statDay(String appId, LocalDate date);

    /**
     * 按天合计
     *
     * @param query 查询条件
     * @return 趋势数据
     */
    List<AigcTaskStatDay> sumByGroupStateDate(AigcTaskStatCondition query);

    /**
     * 按模型合计
     * @param query 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByModel(TaskStatSummaryReq query);

    /**
     * 查询未完成的任务统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 未完成的任务统计
     */
    List<AigcTaskStatDay> queryIncompleteStat(String startDate, String endDate);

    /**
     * 重新计算统计
     * @param taskStatDay 任务统计
     */
    void recalculateStat(AigcTaskStatDay taskStatDay);

    /**
     * 按模型类型合计
     * @param summaryReq 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByModelType(TaskStatSummaryReq summaryReq);

    /**
     * 合计
     * @param query 查询条件
     * @return 合计数据
     */
    AigcTaskStatDay sumTotal(TaskStatSummaryReq query);

    /**
     * 根据模型日期查询
     * @param condition
     * @return
     */
    List<AigcTaskStatDay> listByModelStateDate(AigcTaskSummaryCondition condition);

    /**
     * 按模型、数据日期分组合计
     * @param condition 查询条件
     * @return 合计数据
     */
    List<AigcTaskStatDay> sumByGroupModelStatDate(AigcTaskStatCondition condition);
}
