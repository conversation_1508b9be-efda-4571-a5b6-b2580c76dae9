package com.zjkj.aigc.domain.task.service.impl.alarm;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.alarm.AigcAlarmCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmUpdateReq;
import com.zjkj.aigc.domain.task.service.alarm.AigcAlarmService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.alarm.AigcAlarmDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 模型告警(AigcAlarm)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcAlarmServiceImpl implements AigcAlarmService {

    private final AigcAlarmDao aigcAlarmDao;

    @Override
    public Page<AigcAlarm> queryPage(AigcAlarmCondition condition) {
        return aigcAlarmDao.queryPage(condition);
    }

    @Override
    public void createAigcAlarm(AigcAlarmCreateReq req) {
        AigcAlarm alarm =  BeanUtil.copyProperties(req, AigcAlarm.class);
        aigcAlarmDao.save(alarm);
    }

    @Override
    public AigcAlarm queryById(Long id) {
        return aigcAlarmDao.getById(id);
    }

    @Override
    public void updateAigcAlarm(AigcAlarmUpdateReq req) {
        AigcAlarm alarm =  BeanUtil.copyProperties(req, AigcAlarm.class);
        aigcAlarmDao.updateById(alarm);
    }
}
