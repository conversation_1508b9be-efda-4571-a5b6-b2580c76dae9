package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.cluster.AigcClusterUsageCreateReq;
import com.zjkj.aigc.common.util.MathUtil;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcClusterUsageService;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.domain.task.service.model.AigcModelUsageService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcClusterUsageDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 集群资源利用服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcClusterUsageServiceImpl implements AigcClusterUsageService {

    private final AigcClusterUsageDao aigcClusterUsageDao;
    private final AigcClusterService aigcClusterService;
    private final AigcGpuService aigcGpuService;
    private final AigcModelUsageService aigcModelUsageService;

    @Override
    public Page<AigcClusterUsage> page(AigcClusterUsageCondition condition) {
        return aigcClusterUsageDao.queryPage(condition);
    }

    @Override
    public List<AigcClusterUsage> queryList(AigcClusterUsageCondition condition) {
        return aigcClusterUsageDao.queryList(condition);
    }

    @Override
    public AigcClusterUsage queryById(Long id) {
        return aigcClusterUsageDao.getById(id);
    }


    @Override
    public AigcClusterUsage queryByClusterIdAndDataDate(Long clusterId, LocalDate dataDate) {
        AigcClusterUsageCondition condition = new AigcClusterUsageCondition()
                .setClusterId(clusterId)
                .setDataDate(dataDate);
        List<AigcClusterUsage> aigcClusterUsages = aigcClusterUsageDao.queryList(condition);
        return CollectionUtils.firstElement(aigcClusterUsages);
    }

    @Override
    public void createAigcClusterUsage(AigcClusterUsageCreateReq createReq) {
        AigcClusterUsage existClusterUsage = queryByClusterIdAndDataDate(createReq.getClusterId(), createReq.getDataDate());
        BaseBizException.isTrue(Objects.isNull(existClusterUsage), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.cluster.date.record.exists"));

        AigcCluster aigcCluster = aigcClusterService.queryById(createReq.getClusterId());
        AigcClusterUsage aigcClusterUsage = BeanUtil.copyProperties(createReq, AigcClusterUsage.class, "id");
        aigcClusterUsage.setClusterName(aigcCluster.getName());
        aigcClusterUsage.setPlatform(aigcCluster.getPlatform());
        aigcClusterUsage.setServiceType(aigcCluster.getServiceType());
        aigcClusterUsageDao.save(aigcClusterUsage);
    }

    @Override
    public void updateAigcClusterUsage(AigcClusterUsageCreateReq req) {
        AigcClusterUsage aigcClusterUsage = queryById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(aigcClusterUsage), CustomErrorCode.DATA_NOT_EXISTS);
        if (!Objects.equals(aigcClusterUsage.getDataDate(), req.getDataDate())
                || !Objects.equals(aigcClusterUsage.getClusterId(), req.getClusterId())) {
            AigcClusterUsage existClusterUsage = queryByClusterIdAndDataDate(req.getClusterId(), req.getDataDate());
            BaseBizException.isTrue(Objects.isNull(existClusterUsage), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.cluster.date.record.exists.cannot.update"));
        }

        AigcClusterUsage updateUsage = BeanUtil.copyProperties(req, AigcClusterUsage.class);
        if (!Objects.equals(req.getClusterId(), aigcClusterUsage.getClusterId())) {
            AigcCluster aigcCluster = aigcClusterService.queryById(req.getClusterId());
            updateUsage.setClusterName(aigcCluster.getName());
            updateUsage.setPlatform(aigcCluster.getPlatform());
            updateUsage.setServiceType(aigcCluster.getServiceType());
        }
        aigcClusterUsageDao.updateById(updateUsage);
    }

    @Override
    public void deleteAigcClusterUsage(Long id) {
        AigcClusterUsage aigcClusterUsage = queryById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcClusterUsage), CustomErrorCode.DATA_NOT_EXISTS);
        aigcClusterUsageDao.delById(id);
    }

    @Override
    public List<AigcClusterUsage> statByDate(AigcClusterUsageCondition condition) {
        List<AigcClusterUsage> aigcClusterUsages = aigcClusterUsageDao.statByDate(condition);
        aigcClusterUsages.forEach(aigcClusterUsage -> {
            // 计算gpu使用率
            aigcClusterUsage.setGpuUsageRate(MathUtil.percentage(aigcClusterUsage.getTotalGpuMemorySize(), aigcClusterUsage.getUsedGpuMemorySize()));
            // 计算cpu使用率
            aigcClusterUsage.setCpuUsageRate(MathUtil.percentage(aigcClusterUsage.getTotalCpuCore(), aigcClusterUsage.getUsedCpuCore()));
            // 计算内存使用率
            aigcClusterUsage.setMemoryUsageRate(MathUtil.percentage(aigcClusterUsage.getTotalMemorySize(), aigcClusterUsage.getUsedMemorySize()));
        });
        return aigcClusterUsages;
    }

    /**
     * 计算集群资源
     * @param cluster 集群
     * @return 集群资源
     */
    private AigcClusterUsage calculateTotal(AigcCluster cluster) {
        List<Long> gpuIds = StreamUtil.mapToList(cluster.getNodes(), AigcClusterNode::getGpuId);
        List<AigcGpu> aigcGpus = aigcGpuService.queryByIds(gpuIds);
        Map<Long, AigcGpu> gpuCardCountMap = StreamUtil.toMap(aigcGpus, AigcGpu::getId);

        // 计算gpu数量和总显存
        long gpuCount = 0, totalGpuMemorySize = 0;
        Set<String> gpuModel = new HashSet<>();
        for (Long gpuId : gpuIds) {
            AigcGpu aigcGpu = gpuCardCountMap.get(gpuId);
            if (Objects.isNull(aigcGpu)) {
                continue;
            }
            gpuModel.add(aigcGpu.getModel());
            gpuCount += aigcGpu.getCardCount();
            int gpuMemorySize = aigcGpu.getSingleCardMemory() * aigcGpu.getCardCount();
            totalGpuMemorySize += gpuMemorySize;
        }

        // 计算cpu核数和总内存
        long totalCpuCore = 0, totalMemorySize = 0;
        for (AigcClusterNode node : cluster.getNodes()) {
            totalCpuCore += node.getCpuCore();
            totalMemorySize += node.getMemorySize();
        }

        return new AigcClusterUsage()
                .setGpuCount(gpuCount)
                .setGpuModel(String.join(StringPool.COMMA, gpuModel))
                .setNodeCount((long) cluster.getNodes().size())
                .setTotalGpuMemorySize(totalGpuMemorySize)
                .setTotalCpuCore(totalCpuCore)
                .setTotalMemorySize(totalMemorySize);
    }

    /**
     * 计算已使用资源
     * @param usage 集群资源
     */
    private void calculateUsed(AigcCluster cluster, AigcClusterUsage usage) {
        List<String> nodeIps = StreamUtil.mapToList(cluster.getNodes(), AigcClusterNode::getNodeIp);
        List<AigcModelUsage> modelUsages = aigcModelUsageService.calculateModelUsed(usage, nodeIps);

        // 使用
        long usedGpuMemorySize = 0, usedCpuCore = 0, usedMemorySize = 0;
        for (AigcModelUsage modelUsage : modelUsages) {
            usedGpuMemorySize += modelUsage.getGpuMemorySize();
            usedCpuCore += modelUsage.getCpuCore();
            usedMemorySize += modelUsage.getMemorySize();
        }
        usage.setUsedGpuMemorySize(usedGpuMemorySize);
        usage.setUsedCpuCore(usedCpuCore);
        usage.setUsedMemorySize(usedMemorySize);

        // 计算利用率
        usage.setGpuUsageRate(MathUtil.percentage(usage.getTotalGpuMemorySize(), usage.getUsedGpuMemorySize()));
        usage.setCpuUsageRate(MathUtil.percentage(usage.getTotalCpuCore(), usage.getUsedCpuCore()));
        usage.setMemoryUsageRate(MathUtil.percentage(usage.getTotalMemorySize(), usage.getUsedMemorySize()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clusterUsageStat(AigcCluster cluster, LocalDate dataDa) {
        AigcClusterUsage updateUsage = Optional.ofNullable(calculateTotal(cluster))
                .orElse(new AigcClusterUsage());
        updateUsage.setClusterId(cluster.getId());
        updateUsage.setDataDate(dataDa);
        updateUsage.setClusterName(cluster.getName());
        updateUsage.setPlatform(cluster.getPlatform());
        updateUsage.setServiceType(cluster.getServiceType());

        // 计算已使用资源
        calculateUsed(cluster, updateUsage);

        // 查询是否已有记录
        AigcClusterUsage extClusterUsage = queryByClusterIdAndDataDate(updateUsage.getClusterId(), dataDa);
        if (Objects.nonNull(extClusterUsage)) {
            updateUsage.setId(extClusterUsage.getId());
            aigcClusterUsageDao.updateById(updateUsage);
            return;
        }
        aigcClusterUsageDao.save(updateUsage);
    }
}
