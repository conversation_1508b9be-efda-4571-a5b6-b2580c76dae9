package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.cluster.AigcClusterCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;

import java.util.Collection;
import java.util.List;

/**
 * 集群服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcClusterService {

    /**
     * 分页查询
     *
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<AigcCluster> queryPage(AigcClusterCondition condition);

    /**
     * 根据ID查询
     * @param ids 集群ID
     * @return 集群信息
     */
    List<AigcCluster> listById(Collection<Long> ids);

    /**
     * 查询列表
     *
     * @param condition 查询条件
     * @return 列表数据
     */
    List<AigcCluster> queryList(AigcClusterCondition condition);

    /**
     * 根据ID查询
     *
     * @param id 主键
     * @return 集群信息
     */
    AigcCluster queryIncludeNodeById(Long id);

    /**
     * 根据ID查询
     *
     * @param id 主键
     * @return 集群信息
     */
    AigcCluster queryById(Long id);

    /**
     * 创建集群
     *
     * @param createReq 创建请求
     */
    void createAigcCluster(AigcClusterCreateReq createReq);

    /**
     * 更新集群
     *
     * @param updateReq 更新请求
     */
    void updateAigcCluster(AigcClusterCreateReq updateReq);

    /**
     * 删除集群
     *
     * @param id 主键
     */
    void deleteAigcCluster(Long id);

    /**
     * 启用集群
     *
     * @param id 主键
     */
    void enableAigcCluster(Long id);

    /**
     * 禁用集群
     *
     * @param id 主键
     */
    void disableAigcCluster(Long id);
}
