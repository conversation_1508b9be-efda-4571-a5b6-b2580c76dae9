package com.zjkj.aigc.domain.task.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.zjkj.aigc.common.req.resource.ResourceGpuPlanCreateReq;
import com.zjkj.aigc.domain.task.service.resource.ResourceGpuPlanService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceGpuPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource.ResourceGpuPlanDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceGpuPlan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * GPU资源规划(ResourceGpuPlan)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceGpuPlanServiceImpl implements ResourceGpuPlanService {

    private final ResourceGpuPlanDao gpuPlanDao;

    @Override
    public List<ResourceGpuPlan> queryList(ResourceGpuPlanCondition condition) {
        return gpuPlanDao.list(condition.buildQuery());
    }

    @Override
    public void createGpuPlanBath(List<ResourceGpuPlanCreateReq> reqList) {
        List<ResourceGpuPlan> gpuPlanList = BeanUtil.copyToList(reqList,
                ResourceGpuPlan.class, CopyOptions.create().setIgnoreProperties("id"));
        gpuPlanDao.saveBatch(gpuPlanList);
    }

    @Override
    public void saveOrUpdateBatch(List<ResourceGpuPlanCreateReq> reqList) {
        List<ResourceGpuPlan> gpuPlanList = BeanUtil.copyToList(reqList, ResourceGpuPlan.class);
        gpuPlanDao.saveOrUpdateBatch(gpuPlanList);
    }

    @Override
    public void deleteGpuPlan(ResourceGpuPlanCondition condition) {
        gpuPlanDao.update(condition.buildDelete());
    }
}
