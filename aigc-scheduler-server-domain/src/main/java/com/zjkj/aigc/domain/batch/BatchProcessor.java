package com.zjkj.aigc.domain.batch;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 通用批处理器，用于批量处理数据
 *
 * <AUTHOR>
 * @since 2024/11/21
 */
public class BatchProcessor<T> {
    /**
     * 批处理的大小
     */
    private final int batchSize;

    /**
     * 批处理执行器
     */
    private final Consumer<List<T>> batchConsumer;

    /**
     * 数据缓存列表
     */
    private final List<T> batch;

    /**
     * 已处理的总数量
     */
    private final AtomicInteger totalProcessed = new AtomicInteger(0);

    /**
     * @param batchSize     批量大小
     * @param batchConsumer 批处理执行器
     */
    public BatchProcessor(int batchSize, Consumer<List<T>> batchConsumer) {
        this.batchSize = batchSize;
        this.batchConsumer = batchConsumer;
        this.batch = new ArrayList<>(batchSize);
    }

    /**
     * 添加元素，达到批量大小时自动执行批处理
     */
    public void add(T item) {
        batch.add(item);
        if (batch.size() >= batchSize) {
            flush();
        }
    }

    /**
     * 处理剩余数据
     */
    public void flush() {
        if (!batch.isEmpty()) {
            batchConsumer.accept(batch);
            totalProcessed.addAndGet(batch.size());
            batch.clear();
        }
    }

    /**
     * 获取已处理的总数量
     */
    public int getTotalProcessed() {
        return totalProcessed.get();
    }
}
