package com.zjkj.aigc.domain.task.service;

import com.zjkj.aigc.common.req.cluster.AigcClusterNodeCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterNodeCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;

import java.util.List;

/**
 * 集群节点服务接口
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface AigcClusterNodeService {

    /**
     * 根据集群ID查询节点列表
     *
     * @param clusterId 集群ID
     * @return 节点列表
     */
    List<AigcClusterNode> listByClusterId(Long clusterId);

    /**
     * 根据集群ID查询节点列表
     *
     * @param clusterIds 集群ID
     * @return 节点列表
     */
    List<AigcClusterNode> listByClusterId(List<Long> clusterIds);

    /**
     * 批量创建节点
     *
     * @param clusterId 集群ID
     * @param nodeReqs 节点创建请求列表
     */
    void batchCreate(Long clusterId, List<AigcClusterNodeCreateReq> nodeReqs);

    /**
     * 批量更新节点
     *
     * @param aigcCluster 集群信息
     * @param nodeReqs 节点更新请求列表
     */
    void batchUpdate(AigcCluster aigcCluster, List<AigcClusterNodeCreateReq> nodeReqs);

    /**
     * 根据集群ID删除节点
     *
     * @param clusterId 集群ID
     */
    void removeByClusterId(Long clusterId);

    /**
     * gpu是否被使用
     * @param gpuId gpuId
     * @return 是否被使用
     */
    boolean gpuIsInUsed(Long gpuId);

    /**
     * 查询节点列表
     *
     * @param condition 查询条件
     * @return 节点列表
     */
    List<AigcClusterNode> queryList(AigcClusterNodeCondition condition);

}
