package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.host.HostStatusEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.host.AigcHostCreateReq;
import com.zjkj.aigc.domain.task.service.AigcHostButterflyEffectService;
import com.zjkj.aigc.domain.task.service.AigcHostService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcHostCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcHostDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcHost;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 主机表(AigcHost)服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-25 14:08:02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcHostServiceImpl implements AigcHostService {

    private final AigcHostDao aigcHostDao;
    private final AigcHostButterflyEffectService aigcHostButterflyEffectService;

    @Override
    public boolean isLegalHost(String nodeIp) {
        AigcHost aigcHost = aigcHostDao.getByNodeIp(nodeIp);
        return Objects.nonNull(aigcHost) && Objects.equals(aigcHost.getStatus(), HostStatusEnum.ENABLE.getCode());
    }

    @Override
    public Page<AigcHost> queryPage(AigcHostCondition condition) {
        return aigcHostDao.queryPage(condition);
    }

    @Override
    public List<AigcHost> queryList(AigcHostCondition condition) {
        return aigcHostDao.queryList(condition);
    }

    @Override
    public AigcHost queryByHostIp(String hostIp) {
        return aigcHostDao.queryByHostIp(hostIp);
    }

    @Override
    public AigcHost queryById(Long id) {
        return aigcHostDao.getById(id);
    }

    @Override
    public void createAigcHost(AigcHostCreateReq createReq) {
        AigcHost aigcHost = queryByHostIp(createReq.getHostIp());
        BaseBizException.isTrue(Objects.isNull(aigcHost), CustomErrorCode.DATA_ALREADY_EXISTS, MessageUtils.getMessage("business.host.ip.already.exists"));

        aigcHost = BeanUtil.copyProperties(createReq, AigcHost.class, "id");
        aigcHostDao.save(aigcHost);
    }

    @Override
    public void updateAigcHost(AigcHostCreateReq req) {
        AigcHost aigcHost = queryById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(aigcHost), CustomErrorCode.DATA_NOT_EXISTS);

        AigcHost updateHost = BeanUtil.copyProperties(req, AigcHost.class);
        // 不允许修改主机IP
        updateHost.setHostIp(null);
        updateHost.setId(aigcHost.getId());
        aigcHostDao.updateById(updateHost);
    }

    @Override
    public void deleteAigcHost(Long id) {
        AigcHost aigcHost = queryById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcHost), CustomErrorCode.DATA_NOT_EXISTS);

        boolean unAllowed = Objects.equals(aigcHost.getStatus(), HostStatusEnum.ENABLE.getCode());
        BaseBizException.isTrue(!unAllowed, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.host.enabled.cannot.delete"));
        aigcHostDao.delById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(List<Long> ids, HostStatusEnum sourceStatus, HostStatusEnum targetStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        AigcHostCondition condition = new AigcHostCondition();
        condition.setIds(ids);
        condition.setStatus(sourceStatus.getCode());
        List<String> hostIps = queryList(condition).stream()
                .map(AigcHost::getHostIp)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hostIps)) {
            return;
        }

        aigcHostDao.changeStatus(AigcHost::getId, ids, AigcHost::getStatus, targetStatus.getCode());
        switch (targetStatus) {
            case ENABLE:
                aigcHostButterflyEffectService.enableHost(hostIps);
                break;
            case DISABLE:
                aigcHostButterflyEffectService.disableHost(hostIps);
            default:
                log.info("未知状态");
        }
    }
}
