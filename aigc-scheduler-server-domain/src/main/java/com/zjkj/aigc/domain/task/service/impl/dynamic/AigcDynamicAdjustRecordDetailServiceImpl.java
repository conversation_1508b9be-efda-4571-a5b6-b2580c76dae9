package com.zjkj.aigc.domain.task.service.impl.dynamic;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustScaleStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicAdjustStatusEnum;
import com.zjkj.aigc.common.enums.dynamic.DynamicScaleTypeEnum;
import com.zjkj.aigc.domain.task.service.dynamic.AigcDynamicAdjustRecordDetailService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.dynamic.AigcDynamicAdjustRecordDetailCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.dynamic.AigcDynamicAdjustRecordDetailDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecordDetail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 动态调整记录明细表(AigcDynamicAdjustRecordDetail)服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-18 09:52:52
 */
@Service
@RequiredArgsConstructor
public class AigcDynamicAdjustRecordDetailServiceImpl implements AigcDynamicAdjustRecordDetailService {

    private final AigcDynamicAdjustRecordDetailDao recordDetailDao;

    @Override
    public List<AigcDynamicAdjustRecordDetail> queryList(AigcDynamicAdjustRecordDetailCondition condition) {
        return recordDetailDao.list(condition.buildQuery());
    }

    @Override
    public void updateStatusById(Collection<Long> ids, DynamicAdjustStatusEnum statusEnum) {
        LambdaUpdateWrapper<AigcDynamicAdjustRecordDetail> updateWrapper = Wrappers.<AigcDynamicAdjustRecordDetail>lambdaUpdate()
                .set(AigcDynamicAdjustRecordDetail::getStatus, statusEnum.getStatus())
                .set(AigcDynamicAdjustRecordDetail::getRevisedTime, LocalDateTime.now())
                .in(AigcDynamicAdjustRecordDetail::getId, ids);
        recordDetailDao.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRollbackRecordDetails(AigcDynamicAdjustRecord rollbackRecord) {
        List<Long> sourceDetailIds = new ArrayList<>();
        List<AigcDynamicAdjustRecordDetail> rollbackDetails = new ArrayList<>();
        rollbackRecord.getRecordDetails().forEach(recordDetail -> {
            rollbackDetails.add(new AigcDynamicAdjustRecordDetail()
                    .setAdjustRecordId(rollbackRecord.getId())
                    .setModelId(recordDetail.getModelId())
                    .setModelNameZh(recordDetail.getModelNameZh())
                    .setScaleType(DynamicScaleTypeEnum.reverse(recordDetail.getScaleType()))
                    .setSourceReplica(recordDetail.getTargetReplica())
                    .setTargetReplica(recordDetail.getSourceReplica())
                    .setDiffReplica(recordDetail.getDiffReplica())
                    .setGpuMemorySize(recordDetail.getGpuMemorySize())
                    .setEnvName(recordDetail.getEnvName())
                    .setProjectName(recordDetail.getProjectName())
                    .setServiceName(recordDetail.getServiceName())
                    .setScaleStatus(DynamicAdjustScaleStatusEnum.PENDING.getStatus())
                    .setStatus(DynamicAdjustStatusEnum.EFFECTIVE.getStatus()));
            sourceDetailIds.add(recordDetail.getId());
        });

        if (!rollbackDetails.isEmpty()) {
            recordDetailDao.saveBatch(rollbackDetails);
            updateStatusById(sourceDetailIds, DynamicAdjustStatusEnum.END);
        }
    }

    @Override
    public void updateBatchById(List<AigcDynamicAdjustRecordDetail> recordDetails) {
        recordDetailDao.updateBatchById(recordDetails);
    }

    @Override
    public void saveBatch(List<AigcDynamicAdjustRecordDetail> recordDetails) {
        recordDetailDao.saveBatch(recordDetails);
    }
}
