package com.zjkj.aigc.domain.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.gpu.AigcGpuCreateReq;
import com.zjkj.aigc.domain.task.service.AigcClusterNodeService;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcGpuCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.AigcGpuDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * GPU服务实现类
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcGpuServiceImpl implements AigcGpuService {

    private final AigcGpuDao aigcGpuDao;
    private final AigcClusterNodeService aigcClusterNodeService;

    @Override
    public Page<AigcGpu> queryPage(AigcGpuCondition condition) {
        return aigcGpuDao.queryPage(condition);
    }

    @Override
    public List<AigcGpu> queryList(AigcGpuCondition condition) {
        return aigcGpuDao.queryList(condition);
    }

    @Override
    public AigcGpu queryById(Long id) {
        return aigcGpuDao.getById(id);
    }

    @Override
    public List<AigcGpu> queryByIds(List<Long> ids) {
        return aigcGpuDao.listByIds(ids);
    }

    @Override
    public void createAigcGpu(AigcGpuCreateReq createReq) {
        AigcGpu aigcGpu = BeanUtil.copyProperties(createReq, AigcGpu.class, "id");
        aigcGpuDao.save(aigcGpu);
    }

    @Override
    public void updateAigcGpu(AigcGpuCreateReq req) {
        AigcGpu aigcGpu = queryById(req.getId());
        BaseBizException.isTrue(Objects.nonNull(aigcGpu), CustomErrorCode.DATA_NOT_EXISTS);
        AigcGpu updateGpu = BeanUtil.copyProperties(req, AigcGpu.class);
        aigcGpuDao.updateById(updateGpu);
    }

    @Override
    public void deleteAigcGpu(Long id) {
        AigcGpu aigcGpu = queryById(id);
        BaseBizException.isTrue(Objects.nonNull(aigcGpu), CustomErrorCode.DATA_NOT_EXISTS);

        // 使用中的则无法删除
        boolean used = aigcClusterNodeService.gpuIsInUsed(id);
        BaseBizException.isTrue(!used, CustomErrorCode.DATA_CANNOT_EDIT, MessageUtils.getMessage("business.gpu.in.use.cannot.delete"));
        aigcGpuDao.removeById(id);
    }
}
