package com.zjkj.aigc.domain.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.zjkj.aigc.common.exception.UploadOssException;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

@Slf4j
public class S3Utils {
    
    private static AmazonS3 downloadS3Client = getClient();
    
    public static AmazonS3 getClient() {
        String endpoint = SpringUtils.getProperties("s3.endpoint");
        String region = SpringUtils.getProperties("s3.region");
        String accessKey = SpringUtils.getProperties("s3.access_key");
        String secretKey = SpringUtils.getProperties("s3.secret_key");

        if (StrUtil.isBlank(accessKey) || StrUtil.isBlank(secretKey)) {
            throw new UploadOssException("缺少S3参数[accessKey|secretKey]配置，请检查");
        }

        BasicAWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);

        AmazonS3ClientBuilder builder = AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials));

        // 如果配置了endpoint，使用endpoint；否则使用region
        if (StrUtil.isNotBlank(endpoint)) {
            // 确保endpoint是完整的URL
            String fullEndpoint = endpoint.startsWith("http") ? endpoint : "https://" + endpoint;
            builder.withEndpointConfiguration(
                new com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration(
                    fullEndpoint, region != null ? region : "us-west-2"));
        } else if (StrUtil.isNotBlank(region)) {
            builder.withRegion(region);
        } else {
            builder.withRegion("us-west-2"); // 默认region
        }

        return builder.build();
    }
    
    /**
     * 上传S3
     *
     * @param inputStream inputStream
     * @param fileName    文件名
     * @return S3文件地址
     */
    public static String uploadFile(InputStream inputStream, String fileName) {
        log.info("上传S3 参数: fileName: {}", fileName);
        String bucketName = SpringUtils.getProperties("s3.bucket_name");
        String pathPrefix = SpringUtils.getProperties("s3.path_prefix");
        AmazonS3 s3Client = getClient();
        
        try {
            fileName = StrUtil.isEmpty(pathPrefix) ? fileName : pathPrefix + "/" + fileName;
            
            // 创建ObjectMetadata
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/octet-stream");
            
            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileName, inputStream, metadata);
            
            // 上传文件
            PutObjectResult result = s3Client.putObject(putObjectRequest);
            log.info("上传S3 返回: result: {}", JSON.toJSONString(result));
            
            // 返回文件访问路径
            return fileName;
            
        } catch (Exception e) {
            log.error("S3文件上传失败", e);
            throw new UploadOssException("S3文件上传失败: " + e.getMessage());
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("关闭输入流失败", e);
            }
        }
    }
    
    /**
     * 获取S3文件下载流
     *
     * @param fileName 文件名
     * @return 文件流
     */
    public static synchronized InputStream getDownloadStream(String fileName) {
        String bucketName = SpringUtils.getProperties("s3.bucket_name");
        S3Object s3Object;
        
        try {
            s3Object = downloadS3Client.getObject(bucketName, fileName);
        } catch (Exception e) {
            // 可能连接断开了，重新连接，再有问题就不管了
            if (downloadS3Client != null) {
                downloadS3Client.shutdown();
            }
            downloadS3Client = getClient();
            s3Object = downloadS3Client.getObject(bucketName, fileName);
        }
        
        return s3Object.getObjectContent();
    }
    
    /**
     * 检查S3文件是否存在
     *
     * @param fileName 文件名
     * @return 是否存在
     */
    public static boolean doesObjectExist(String fileName) {
        String bucketName = SpringUtils.getProperties("s3.bucket_name");
        AmazonS3 s3Client = getClient();
        
        try {
            return s3Client.doesObjectExist(bucketName, fileName);
        } catch (Exception e) {
            log.error("检查S3文件存在性失败: {}", fileName, e);
            return false;
        }
    }
}
