package com.zjkj.aigc.domain.task.service.model;

import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcModelUsageCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterUsage;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelUsage;

import java.util.List;

/**
 * 模型资源占用表(AigcModelUsage)服务接口
 *
 * <AUTHOR>
 * @since 2025-02-18 15:44:53
 */
public interface AigcModelUsageService {

    /**
     * 查询模型使用情况
     * @param condition 查询条件
     * @return 模型使用情况
     */
    List<AigcModelUsage> queryList(AigcModelUsageCondition condition);

    /**
     * 计算模型使用情况
     *
     * @param clusterUsage 集群使用情况
     * @param nodeIps      节点ip
     * @return 模型使用情况
     */
    List<AigcModelUsage> calculateModelUsed(AigcClusterUsage clusterUsage, List<String> nodeIps);
}
