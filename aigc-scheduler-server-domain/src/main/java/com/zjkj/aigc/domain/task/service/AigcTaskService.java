package com.zjkj.aigc.domain.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.req.task.AigcTaskCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcTaskCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import com.zjkj.aigc.task.dto.req.BatchGetAiTaskReq;
import com.zjkj.aigc.task.dto.req.CreateAiTaskReq;
import com.zjkj.aigc.task.dto.req.GetAiTaskReq;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * aigc任务服务
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
public interface AigcTaskService {

    /**
     * 转换任务
     *
     * @param req 请求参数
     * @param <T> 参数类型
     * @return 任务信息
     */
    <T> AigcTask covertToAigcTask(CreateAiTaskReq<T> req);

    /**
     * 保存任务
     *
     * @param req 请求任务信息
     */
    <T> AigcTask saveAigcTask(CreateAiTaskReq<T> req);

    /**
     * 批量保存任务
     *
     * @param reqList 请求参数
     * @param <T>     参数类型
     * @return 任务列表
     */
    <T> List<AigcTask> batchSaveAigcTask(List<CreateAiTaskReq<T>> reqList);

    /**
     * 获取任务信息
     *
     * @param req 请求参数
     * @return 任务信息
     */
    AigcTask getAiTask(GetAiTaskReq req);

    /**
     * 获取任务排名列表
     *
     * @param req 请求参数
     * @return 任务排名列表
     */
    List<AigcTask> getAigcTaskRankList(BatchGetAiTaskReq req);

    /**
     * 取消任务
     *
     * @param aigcTask 任务信息
     * @return 是否取消成功
     */
    boolean cancelAiTask(AigcTask aigcTask);

    /**
     * 重启任务
     *
     * @param aigcTask 任务信息
     * @return 是否重启成功
     */
    boolean restartTask(AigcTask aigcTask);

    /**
     * 开始任务
     *
     * @param aigcTask 任务信息
     * @return 是否开始成功
     */
    boolean startTask(AigcTask aigcTask);

    /**
     * 任务成功
     *
     * @param aigcTask 任务信息
     */
    void succTask(AigcTask aigcTask);

    /**
     * 任务失败
     *
     * @param aigcTask 任务信息
     */
    boolean failTask(AigcTask aigcTask);

    /**
     * 任务进度
     *
     * @param aigcTask 任务信息
     */
    boolean progressTask(AigcTask aigcTask);

    /**
     * 超时失败任务
     *
     * @param aigcTask 任务信息
     * @return 是否超时失败
     */
    boolean timeoutFailTask(AigcTask aigcTask);

    /**
     * 重试任务
     *
     * @param aigcTask 任务信息
     * @return 是否重试成功
     */
    boolean retryTask(AigcTask aigcTask);

    /**
     * 查询模型参数
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 模型参数
     */
    String queryModelParam(String taskType, String modelName);

    /**
     * 删除任务
     * 物理删除
     *
     * @param businessId 业务id
     */
    void actualDelByBusinessId(String businessId);

    /**
     * 删除任务
     * 物理删除
     *
     * @param ids 任务id
     */
    void actualDelById(List<Long> ids);

    /**
     * 查询任务
     *
     * @param condition  查询条件
     * @return 任务分页信息
     */
    Page<AigcTask> queryPage(AigcTaskCondition condition);

    /**
     * 查询任务列表
     * @param condition 查询条件
     * @return 任务列表
     */
    List<AigcTask> queryList(AigcTaskCondition condition);
    /**
     * 根据任务id查询任务
     *
     * @param aigcTaskId 任务id
     * @return 任务信息
     */
    AigcTask getByAigcTaskId(String aigcTaskId);

    /**
     * 更新任务
     * @param aigcTask 任务信息
     * @return 是否更新成功
     */
    boolean updateAigcTask(AigcTaskCreateReq aigcTask);

    /**
     * 填充任务排名
     * @param taskType 任务类型
     * @param modelName 模型名称
     * @param batchOpen 是否批量开启
     * @param aigcTaskList 任务列表
     */
    void fillAigcTaskRank(String taskType, String modelName, boolean batchOpen, List<AigcTask> aigcTaskList);

    /**
     * 更新任务优先级
     *
     * @param ids            任务id集合
     * @param targetPriority 目标优先级
     * @return 更新成功的数量
     */
    boolean updateTaskPriority(Collection<Long> ids, int targetPriority);

    /**
     * 刷新任务缓存队列
     * @param endTime 结束时间
     */
    void taskRefreshQueue(LocalDateTime endTime);
}
