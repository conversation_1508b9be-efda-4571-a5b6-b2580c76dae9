package com.zjkj.aigc.domain.task.service.sys;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.enums.GeneralEnum;
import com.zjkj.aigc.common.req.dict.SysDictDataCreateReq;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.sys.SysDictDataCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.sys.SysDictData;

import java.util.List;

/**
 * 字典数据领域服务接口
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
public interface SysDictDataService {

    /**
     * 分页查询
     * @param condition 查询条件
     * @return 分页数据
     */
    Page<SysDictData> queryPage(SysDictDataCondition condition);
    /**
     * 查询字典数据列表
     *
     * @param condition 查询条件
     * @return 字典数据列表
     */
    List<SysDictData> queryList(SysDictDataCondition condition);
    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 字典数据信息
     */
    SysDictData queryById(Long id);
    /**
     * 创建字典数据
     *
     * @param req 创建请求
     */
    void createSysDictData(SysDictDataCreateReq req);

    /**
     * 更新字典数据
     *
     * @param req 更新请求
     */
    void updateSysDictData(SysDictDataCreateReq req);

    /**
     * 删除字典数据
     *
     * @param id 主键
     */
    void deleteSysDictData(Long id);

    /**
     * 更改状态
     *
     * @param ids    主键集合
     * @param source 源状态
     * @param target 目标状态
     */
    void changeStatus(List<Long> ids, GeneralEnum.SWITCH source, GeneralEnum.SWITCH target);

    /**
     * 根据字典id删除字典数据
     *
     * @param dictId 字典id
     */
    void removeByDictId(Long dictId);
}
