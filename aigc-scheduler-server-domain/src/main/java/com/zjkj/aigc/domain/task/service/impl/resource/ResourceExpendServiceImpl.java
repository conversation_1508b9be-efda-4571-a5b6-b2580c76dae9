package com.zjkj.aigc.domain.task.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.zjkj.aigc.common.req.resource.ResourceExpendCreateReq;
import com.zjkj.aigc.domain.task.service.resource.ResourceExpendService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceExpendCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource.ResourceExpendDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceExpend;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 资源支出(ResourceExpend)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceExpendServiceImpl implements ResourceExpendService {

    private final ResourceExpendDao expendDao;

    @Override
    public List<ResourceExpend> queryList(ResourceExpendCondition condition) {
        return expendDao.list(condition.buildQuery());
    }

    @Override
    public void createExpendBath(List<ResourceExpendCreateReq.Expend> reqs) {
        List<ResourceExpend> expendList = BeanUtil.copyToList(reqs,
                ResourceExpend.class, CopyOptions.create().setIgnoreProperties("id"));
        expendDao.saveBatch(expendList);
    }

    @Override
    public void updateExpendBath(List<ResourceExpendCreateReq.Expend> reqs) {
        List<ResourceExpend> expendList = BeanUtil.copyToList(reqs, ResourceExpend.class);
        expendDao.updateBatchById(expendList);
    }
}
