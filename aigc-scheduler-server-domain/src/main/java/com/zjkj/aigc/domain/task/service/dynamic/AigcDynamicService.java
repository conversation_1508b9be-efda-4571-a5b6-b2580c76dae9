package com.zjkj.aigc.domain.task.service.dynamic;

import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcModelInfo;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTaskStatDay;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicAdjustRecord;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.dynamic.AigcDynamicConfigGlobal;
import com.zjkj.aigc.zadig.client.ZadigClient;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/4/3
 */
public interface AigcDynamicService {
    /**
     * 模型预测负载
     *
     * @param aigcModelInfos 模型信息列表
     */
    void modelPreThreshold(List<AigcModelInfo> aigcModelInfos);

    /**
     * 动态调度扩容
     *
     * @param globalConfig 全局动态配置
     * @param statDayList  统计数据列表
     */
    void scaleOutByTask(AigcDynamicConfigGlobal globalConfig, List<AigcTaskStatDay> statDayList);

    /**
     * 动态调度缩容
     *
     * @param zadigClient Zadig客户端
     * @param record      动态调整记录
     */
    void scaleInstance(ZadigClient zadigClient, AigcDynamicAdjustRecord record);

    /**
     * 动态调度缩容-低负载剥夺的
     *
     * @param statModeMap 模型统计
     * @param records     动态调整记录
     */
    void scaleInByLowThresholdDeprived(Map<Map.Entry<String, String>, Long> statModeMap, List<AigcDynamicAdjustRecord> records);
}
