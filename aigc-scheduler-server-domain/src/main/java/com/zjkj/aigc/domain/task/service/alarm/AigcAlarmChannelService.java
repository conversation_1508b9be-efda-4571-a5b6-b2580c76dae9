package com.zjkj.aigc.domain.task.service.alarm;

import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelCreateReq;
import com.zjkj.aigc.common.req.alarm.AigcAlarmChannelQuery;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.alarm.AigcAlarmChannelCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.alarm.AigcAlarmChannel;

import java.util.List;

/**
 * 告警渠道配置表(AigcAlarmChannel)服务接口
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface AigcAlarmChannelService {
    /**
     * 查询所有
     *
     * @return 数据
     */
    List<AigcAlarmChannel> query(AigcAlarmChannelCondition condition);
    /**
     * 告警渠道配置详情
     *
     * @param id 数据ID
     */
    AigcAlarmChannel getById(Long id);
    /**
     * 新增告警渠道配置
     *
     * @param req 新增参数
     */
    void saveAigcAlarmChannel(AigcAlarmChannelCreateReq req);

    /**
     * 更新告警渠道配置
     *
     * @param req 更新参数
     */
    void updateAigcAlarmChannel(AigcAlarmChannelCreateReq req);

    /**
     * 删除告警渠道配置
     *
     * @param id 数据ID
     */
    void deleteAigcAlarmChannel(Long id);
}
