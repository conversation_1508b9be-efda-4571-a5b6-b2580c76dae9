package com.zjkj.aigc.domain.task.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import com.zjkj.aigc.common.req.resource.ResourceMidPlanCreateReq;
import com.zjkj.aigc.domain.task.service.resource.ResourceMidPlanService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceMidPlanCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.resource.ResourceMidPlanDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceMidPlan;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中间件资源规划(ResourceMidPlan)服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceMidPlanServiceImpl implements ResourceMidPlanService {

    private final ResourceMidPlanDao midPlanDao;

    @Override
    public List<ResourceMidPlan> queryList(ResourceMidPlanCondition condition) {
        return midPlanDao.list(condition.buildQuery());
    }

    @Override
    public void createMidPlan(ResourceMidPlanCreateReq req) {
        ResourceMidPlan midPlan = BeanUtil.copyProperties(req, ResourceMidPlan.class,"id");
        midPlanDao.save(midPlan);
    }

    @Override
    public void saveOrUpdateMidPlan(ResourceMidPlanCreateReq req) {
        ResourceMidPlan midPlan = BeanUtil.copyProperties(req, ResourceMidPlan.class);
        midPlanDao.saveOrUpdate(midPlan);
    }

    @Override
    public void deleteMidPlan(ResourceMidPlanCondition condition) {
        midPlanDao.update(condition.buildDelete());
    }
}
