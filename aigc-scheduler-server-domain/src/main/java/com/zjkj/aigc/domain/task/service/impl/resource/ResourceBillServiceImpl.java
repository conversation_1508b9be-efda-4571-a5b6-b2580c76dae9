package com.zjkj.aigc.domain.task.service.impl.resource;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.dto.resource.BillImportDTO;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.PlatformEnum;
import com.zjkj.aigc.common.enums.resource.ResourceBillTypeEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.util.StreamUtil;
import com.zjkj.aigc.domain.task.service.AigcClusterService;
import com.zjkj.aigc.domain.task.service.AigcGpuService;
import com.zjkj.aigc.domain.task.service.resource.ResourceBillService;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.AigcClusterCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.condition.resource.ResourceBillCondition;
import com.zjkj.aigc.infrastructure.mybatis.aigc.dao.ResourceBillDao;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcCluster;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcClusterNode;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcGpu;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.resources.ResourceBill;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资源账单表(ResourceBill)服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08 10:37:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResourceBillServiceImpl implements ResourceBillService {

    private final AigcClusterService aigcClusterService;
    private final AigcGpuService aigcGpuService;
    private final ResourceBillDao resourceBillDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer importBill(ResourceBillTypeEnum typeEnum, String platform, List<BillImportDTO> billImportList) {
        // 参数校验
        if (Objects.isNull(typeEnum) || CollectionUtils.isEmpty(billImportList) || !StringUtils.hasText(platform)) {
            log.info("importBill() 导入账单类型/列表/云平台为空");
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.bill.import.data.empty"));
        }

        AigcClusterCondition condition = new AigcClusterCondition()
                .setPlatform(platform);
        List<AigcCluster> aigcClusters = aigcClusterService.queryList(condition);
        if (Objects.equals(typeEnum, ResourceBillTypeEnum.GPU)) {
            return importGpu(platform, billImportList, aigcClusters);
        }

        if (Objects.equals(typeEnum, ResourceBillTypeEnum.MID)) {
            return importMid(platform, billImportList, aigcClusters);
        }

        return 0;
    }

    /**
     * 导入中间件MID账单
     *
     * @param platform       平台
     * @param billImportList 账单列表
     * @param aigcClusters   集群列表
     * @return 数量
     */
    private Integer importMid(String platform, List<BillImportDTO> billImportList, List<AigcCluster> aigcClusters) {
        Set<String> instanceIds = new HashSet<>();
        if (!Objects.equals(PlatformEnum.APP.getName(), platform)) {
            aigcClusters.stream()
                    .flatMap(cluster -> cluster.getNodes().stream())
                    .map(AigcClusterNode::getInstanceId)
                    .filter(StringUtils::hasText)
                    .forEach(instanceIds::add);
        }
        Map<String, List<BillImportDTO>> groupMap = billImportList.stream()
                .filter(v -> instanceIds.isEmpty() || !instanceIds.contains(v.getInstanceId()))
                .collect(Collectors.groupingBy(v -> v.getProductCode() + v.getBillingType() + v.getPeriodMonth()));
        BaseBizException.isTrue(!CollectionUtils.isEmpty(groupMap), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.bill.no.matching.data"));

        // 转换为账单实体
        List<ResourceBill> resourceBills = groupMap.values().stream().map(bills -> {
            BillImportDTO billImportDTO = bills.get(0);
            ResourceBill resourceBill = BeanUtil.copyProperties(billImportDTO, ResourceBill.class);
            resourceBill.setMonth(billImportDTO.getResultMonth());
            resourceBill.setInstanceId(StringPool.EMPTY);
            resourceBill.setPlatform(platform);
            resourceBill.setType(ResourceBillTypeEnum.MID.getType());
            // 金额合计
            BigDecimal paymentAmount = bills.stream()
                    .map(BillImportDTO::getPaymentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            resourceBill.setPaymentAmount(paymentAmount);

            return resourceBill;
        }).collect(Collectors.toList());

        // 处理账单保存和更新
        return processBillsSaveAndUpdate(ResourceBillTypeEnum.MID, platform, resourceBills);
    }

    /**
     * 导入GPU账单
     *
     * @param platform       平台
     * @param billImportList 账单列表
     * @param aigcClusters   集群列表
     * @return 数量
     */
    private Integer importGpu(String platform, List<BillImportDTO> billImportList, List<AigcCluster> aigcClusters) {
        BaseBizException.isTrue(!CollectionUtils.isEmpty(aigcClusters), CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.no.cluster.nodes"));
        // Map<instanceId, gpuId>
        Map<String, Long> instanceMap = aigcClusters.stream()
                .flatMap(cluster -> cluster.getNodes().stream())
                .filter(v -> StringUtils.hasText(v.getInstanceId()))
                .collect(Collectors.toMap(
                        AigcClusterNode::getInstanceId,
                        AigcClusterNode::getGpuId,
                        (v1, v2) -> v2
                ));

        // 过滤和验证账单数据
        List<BillImportDTO> validBillList = validateAndFilterGpuBills(billImportList, instanceMap);
        Map<Long, String> gpuModeMap = aigcGpuService.queryByIds(new ArrayList<>(instanceMap.values()))
                .stream()
                .collect(Collectors.toMap(AigcGpu::getId, AigcGpu::getModel));
        // 转换为账单实体
        String type = ResourceBillTypeEnum.GPU.getType();
        List<ResourceBill> resourceBills = validBillList.stream().map(v -> {
            ResourceBill resourceBill = BeanUtil.copyProperties(v, ResourceBill.class);
            resourceBill.setMonth(v.getResultMonth());
            resourceBill.setPlatform(platform);
            resourceBill.setType(type);
            Long gpuId = instanceMap.get(v.getInstanceId());
            String gpuModel = gpuModeMap.get(gpuId);
            resourceBill.setGpuModel(gpuModel);
            return resourceBill;
        }).collect(Collectors.toList());

        // 处理账单保存和更新
        return processBillsSaveAndUpdate(ResourceBillTypeEnum.GPU, platform, resourceBills);
    }

    @Override
    public Page<ResourceBill> queryPage(ResourceBillCondition condition) {
        return resourceBillDao.page(condition.getPage(), condition.buildQuery());
    }

    @Override
    public BigDecimal billTotal(ResourceBillCondition condition) {
        return resourceBillDao.billTotal(condition);
    }

    @Override
    public List<ResourceBill> queryList(ResourceBillCondition condition) {
        return resourceBillDao.queryList(condition);
    }

    /**
     * 过滤和验证账单数据
     *
     * @param billImportList 账单导入列表
     * @param instanceMap    实例信息Map<instanceId, gpuId>
     * @return 有效的账单列表
     */
    private List<BillImportDTO> validateAndFilterGpuBills(List<BillImportDTO> billImportList, Map<String, Long> instanceMap) {
        List<BillImportDTO> validBills = billImportList.stream()
                .filter(v -> instanceMap.containsKey(v.getInstanceId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validBills)) {
            log.info("importBill() 无匹配的集群节点，原始账单数量:{}", billImportList.size());
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.bill.no.matching.data"));
        }

        // 检查同帐期同实例id是否重复
        validBills.stream()
                .collect(Collectors.toMap(
                                v -> Map.entry(v.getPeriodMonth(), v.getInstanceId()),
                                v -> 1, Integer::sum
                        )
                ).forEach((entry, count) -> {
                    if (count > 1) {
                        log.info("importBill() 同帐期同实例id数据重复, periodMonth:{}, instanceId:{}, count:{}", entry.getKey(), entry.getValue(), count);
                        throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.bill.period.instance.duplicate", new Object[]{entry.getKey(), entry.getValue()}));
                    }
                });

        return validBills;
    }

    /**
     * 处理账单保存和更新
     *
     * @param typeEnum      账单类型
     * @param platform      云平台
     * @param resourceBills 账单列表
     * @return 账单数量
     */
    private Integer processBillsSaveAndUpdate(ResourceBillTypeEnum typeEnum, String platform, List<ResourceBill> resourceBills) {

        // 查询已存在的账单
        Map<Map.Entry<String, Integer>, Long> existBillMap = queryExistingBills(typeEnum, platform, resourceBills);

        // 分类新增和更新的账单
        List<ResourceBill> newBills = new ArrayList<>();
        List<ResourceBill> updateBills = new ArrayList<>();
        boolean isGpu = Objects.equals(typeEnum, ResourceBillTypeEnum.GPU);
        resourceBills.forEach(bill -> {
            Map.Entry<String, Integer> key = isGpu ? Map.entry(bill.getInstanceId(), bill.getMonth()) : Map.entry(bill.getProductCode() + bill.getBillingType(), bill.getMonth());
            Long id = existBillMap.get(key);
            if (Objects.isNull(id)) {
                newBills.add(bill);
            } else {
                bill.setId(id);
                updateBills.add(bill);
            }
        });

        // 批量保存新账单
        if (!CollectionUtils.isEmpty(newBills)) {
            resourceBillDao.saveBatch(newBills);
            log.info("importBill() type:{}, platform:{} 新增账单数量:{}", typeEnum.getType(), platform, newBills.size());
        }

        // 批量更新已存在账单
        if (!CollectionUtils.isEmpty(updateBills)) {
            resourceBillDao.updateBatchById(updateBills);
            log.info("importBill() type:{}, platform:{} 更新账单数量:{}", typeEnum.getType(), platform, updateBills.size());
        }

        Set<Long> updateIds = StreamUtil.mapToSet(updateBills, ResourceBill::getId);
        List<Long> delIds = existBillMap.values().stream()
                .filter(v -> !updateIds.contains(v))
                .collect(Collectors.toList());

        // 批量删除账单
        if (!CollectionUtils.isEmpty(delIds)) {
            resourceBillDao.removeByIds(delIds);
            log.info("importBill() type:{}, platform:{} 删除账单数量:{}", typeEnum.getType(), platform, delIds.size());
        }

        return resourceBills.size();
    }

    /**
     * 查询已存在的账单
     *
     * @param typeEnum      账单类型
     * @param platform      云平台
     * @param resourceBills 账单列表
     * @return 账单映射
     */
    private Map<Map.Entry<String, Integer>, Long> queryExistingBills(ResourceBillTypeEnum typeEnum, String platform, List<ResourceBill> resourceBills) {
        boolean isGpu = Objects.equals(typeEnum, ResourceBillTypeEnum.GPU);

        // 提取账单关键信息
        Set<Integer> months = StreamUtil.mapToSet(resourceBills, ResourceBill::getMonth);
        ResourceBillCondition billCondition = new ResourceBillCondition()
                .setType(typeEnum.getType())
                .setPlatform(platform)
                .setMonths(months);
        return resourceBillDao.queryList(billCondition).stream()
                .collect(Collectors.toMap(
                        v -> isGpu ? Map.entry(v.getInstanceId(), v.getMonth()) : Map.entry(v.getProductCode() + v.getBillingType(), v.getMonth()),
                        ResourceBill::getId,
                        (v1, v2) -> v2
                ));
    }
}
