package com.zjkj.aigc.domain.task.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.zjkj.aigc.infrastructure.mybatis.aigc.entity.AigcTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AigcTaskQueueService {
    private final static String MODEL_KEY = "MODEL_TASK:%s@%s";
    private final RedissonClient redissonClient;

    /**
     * 获取模型key
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 模型key
     */
    private String getModelKey(String taskType, String modelName) {
        return String.format(MODEL_KEY, taskType, modelName);
    }

    /**
     * 计算任务优先级分数
     *
     * @param id       id
     * @param priority 优先级
     * @return 计算结果 优先级越小排名越靠前，同优先级时ID越小越靠前
     */
    private double getTaskScore(long id, int priority) {
        double scale = 1e12;
        return priority * scale + id;
    }

    /**
     * 添加
     *
     * @param aigcTask 任务信息
     */
    public void add(AigcTask aigcTask) {
        double taskScore = getTaskScore(aigcTask.getId(), aigcTask.getTaskPriority());
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(aigcTask.getTaskType(), aigcTask.getModelName()));
        boolean add = sortedSet.add(taskScore, aigcTask.getId());
        log.info("添加任务到队列, taskId:{}, id:{}, add:{}", aigcTask.getTaskId(), aigcTask.getId(), add);
    }

    /**
     * 转换任务信息
     *
     * @param aigcTaskList 任务信息列表
     * @return 转换结果
     */
    private Map<Map.Entry<String, String>, Map<Long, Double>> covertTaskMap(Collection<AigcTask> aigcTaskList) {
        if (CollectionUtils.isEmpty(aigcTaskList)) {
            return Map.of();
        }

        return aigcTaskList.stream()
                .collect(Collectors.groupingBy(
                        v -> Map.entry(v.getTaskType(), v.getModelName()),
                        Collectors.toMap(
                                AigcTask::getId,
                                v -> getTaskScore(v.getId(), v.getTaskPriority()), (v1, v2) -> v2
                        )
                ));
    }

    /**
     * 批量添加
     *
     * @param aigcTaskList 任务信息列表
     */
    public void batchAdd(Collection<AigcTask> aigcTaskList) {
        covertTaskMap(aigcTaskList).forEach((model, taskMap) -> {
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(model.getKey(), model.getValue()));
            int addAll = sortedSet.addAll(taskMap);
            log.info("批量添加任务到队列 taskMap:{}, addAll:{}", JSON.toJSONString(taskMap), addAll);
        });
    }

    /**
     * 添加
     *
     * @param aigcTask 任务信息
     */
    public void addIfAbsent(AigcTask aigcTask) {
        String key = getModelKey(aigcTask.getTaskType(), aigcTask.getModelName());
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(key);
        double taskScore = getTaskScore(aigcTask.getId(), aigcTask.getTaskPriority());
        boolean added = sortedSet.addIfAbsent(taskScore, aigcTask.getId());
        log.info("添加任务到队列. key:{}, id:{}, added:{}", key, aigcTask.getId(), added);
    }

    /**
     * 批量更新
     *
     * @param aigcTaskList 任务信息列表
     */
    public void batchUpdate(Collection<AigcTask> aigcTaskList) {
        covertTaskMap(aigcTaskList).forEach((model, taskMap) -> {
            RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(model.getKey(), model.getValue()));
            int allIfExist = sortedSet.addAllIfExist(taskMap);
            log.info("批量更新任务到队列 taskMap:{}, allIfExist:{}", JSON.toJSONString(taskMap), allIfExist);
        });
    }

    /**
     * 获取
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 获取模型任务
     */
    public Long pop(String taskType, String modelName) {
        String modelKey = getModelKey(taskType, modelName);
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(modelKey);
        Long first = sortedSet.pollFirst();
        if (Objects.nonNull(first)) {
           log.info("获取队列任务, taskType:{}, modelName:{}, id:{}", taskType, modelName, first);
        }
        return first;
    }

    /**
     * 批量移除
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @param ids       ids
     */
    public void batchRemove(String taskType, String modelName, Collection<Long> ids) {
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(taskType, modelName));
        sortedSet.removeAll(ids);
    }

    /**
     * 获取排名
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @param id        ID
     * @return 排名
     */
    public Integer getRank(String taskType, String modelName, Long id) {
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(taskType, modelName));
        Integer rank = sortedSet.rank(id);
        return Objects.isNull(rank) ? null : rank + 1;
    }

    /**
     * 获取所有任务
     *
     * @param taskType  任务类型
     * @param modelName 模型名称
     * @return 所有任务
     */
    public Map<Long, Integer> getRankMap(String taskType, String modelName, Collection<Long> ids) {
        RScoredSortedSet<Long> sortedSet = redissonClient.getScoredSortedSet(getModelKey(taskType, modelName));
        int totalSize = sortedSet.size();
        if (totalSize == 0) {
            return Map.of();
        }

        List<Integer> revRanks = sortedSet.revRank(ids);
        Map<Long, Integer> rankMap = Maps.newHashMapWithExpectedSize(ids.size());
        int i = 0;
        for (Long id : ids) {
            Integer rank = revRanks.get(i++);
            if (Objects.nonNull(rank)) {
                rankMap.put(id, totalSize - rank);
            }
        }
        return rankMap;
    }
}
