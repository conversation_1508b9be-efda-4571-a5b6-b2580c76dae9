package com.zjkj.aigc.domain.config.propertie;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "ai.model.deploy")
public class ModelDeployProperties {
    /**
     * 页面地址
     */
    private String detailPage;
    /**
     * 钉钉机器人地址
     */
    private String dingHook;

}
