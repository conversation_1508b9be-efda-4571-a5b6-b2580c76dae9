package com.zjkj.aigc.common.i18n;

import com.zjkj.aigc.common.enums.CustomErrorCode;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 国际化测试类
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class I18nTest {

    @Test
    public void testDefaultMessages() {
        // 测试默认语言（英文）
        LocaleContextHolder.setLocale(Locale.US);

        System.out.println("=== Default (English) Messages ===");
        System.out.println("SUCCESS: " + CustomErrorCode.SUCCESS.getMessage());
        System.out.println("DATA_NOT_EXISTS: " + CustomErrorCode.DATA_NOT_EXISTS.getMessage());
        System.out.println("UNAUTHORIZED: " + CustomErrorCode.UNAUTHORIZED.getMessage());
    }

    @Test
    public void testChineseMessages() {
        // 设置中文环境
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);

        System.out.println("=== Chinese Messages ===");
        System.out.println("SUCCESS: " + CustomErrorCode.SUCCESS.getMessage());
        System.out.println("DATA_NOT_EXISTS: " + CustomErrorCode.DATA_NOT_EXISTS.getMessage());
        System.out.println("UNAUTHORIZED: " + CustomErrorCode.UNAUTHORIZED.getMessage());
    }

    @Test
    public void testEnglishMessages() {
        // 设置英文环境（显式）
        LocaleContextHolder.setLocale(Locale.US);

        System.out.println("=== English Messages ===");
        System.out.println("SUCCESS: " + CustomErrorCode.SUCCESS.getMessage());
        System.out.println("DATA_NOT_EXISTS: " + CustomErrorCode.DATA_NOT_EXISTS.getMessage());
        System.out.println("UNAUTHORIZED: " + CustomErrorCode.UNAUTHORIZED.getMessage());
    }

    @Test
    public void testJapaneseMessages() {
        // 设置日文环境
        LocaleContextHolder.setLocale(Locale.JAPAN);
        
        // 测试错误码消息
        System.out.println("Japanese test:");
        System.out.println("SUCCESS: " + CustomErrorCode.SUCCESS.getMessage());
        System.out.println("DATA_NOT_EXISTS: " + CustomErrorCode.DATA_NOT_EXISTS.getMessage());
        System.out.println("UNAUTHORIZED: " + CustomErrorCode.UNAUTHORIZED.getMessage());
    }

    @Test
    public void testMessageWithParams() {
        // 测试带参数的消息
        LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        
        String message = CustomErrorCode.TIME_RANGE_TO_LONG.getMessage("30天");
        System.out.println("带参数的消息: " + message);
    }
}
