# CustomErrorCode文件
success=请求成功！
data.not.exists=数据不存在
data.already.exists=数据已存在
data.cannot.edit=数据无法修改
data.changed=数据发生变化，请重试
db.data.already.exists=数据库数据重复
data.error=数据异常
dashvector.create.collection=向量数据库创建集合错误：{0}
dashvector.create.doc=向量数据库创建文档错误：{0}
dashvector.delete.doc=向量数据库删除文档错误：{0}
dashvector.query.doc=向量数据库查询文档错误：{0}
dashvector.query.params=检索ID和检索向量数组不能同时为空
unauthorized=未授权！
param.error=客户端参数错误
not.supported=NOT_SUPPORTED
unknown.error=未知错误，请联系管理员
feign.invoke=远程外部服务调用失败！
time.range.too.long=时间跨度超过{0}

# Validation related
validation.id.required=id不能为空
validation.model.name.required=模型名称不能为空
validation.task.type.required=任务类型不能为空
validation.platform.required=平台不能为空
validation.cloud.platform.required=云平台不能为空
validation.model.id.required=模型id不能为空
validation.model.version.required=模型版本不能为空
validation.status.required=状态不能为空
validation.memory.size.required=内存大小不能为空
validation.cpu.core.required=cpu核数不能为空
validation.type.required=类型不能为空
validation.name.required=名称不能为空
validation.month.required=月份不能为空
validation.tag.required=标签不能为空
validation.model.type.required=型号不能为空

# Batch operate validation
batch.operate.ids.required=操作id不能为空

# Alarm channel validation
alarm.channel.tag.length.exceeded=标签不能超过{max}个字符
alarm.channel.type.required=渠道类型不能为空
alarm.channel.webhook.required=Webhook地址不能为空
alarm.channel.webhook.length.exceeded=Webhook地址不能超过{max}个字符
alarm.channel.sign.length.exceeded=签名密钥不能超过{max}个字符

# Task validation
task.params.required=模型参数不能为空

# Task batch operate validation
task.batch.operate.task.ids.required=平台任务id不能为空

# Task priority validation
task.priority.business.id.required=业务id不能为空

# Task stat summary validation
task.stat.summary.start.time.required=开始时间不能为空
task.stat.summary.end.time.required=结束时间不能为空

# Model deploy validation
model.deploy.title.required=标题不能为空
model.deploy.title.length.exceeded=标题长度不能超过{max}个字符
model.deploy.project.name.required=项目工程名称不能为空
model.deploy.project.name.length.exceeded=项目工程名称长度不能超过{max}个字符
model.deploy.project.git.url.required=项目git地址不能为空
model.deploy.project.git.url.length.exceeded=项目git地址长度不能超过{max}个字符
model.deploy.version.length.exceeded=模型版本长度不能超过{max}个字符
model.deploy.sdk.version.required=部署sdk版本不能为空
model.deploy.sdk.version.length.exceeded=部署sdk版本长度不能超过{max}个字符
model.deploy.oss.url.required=模型文件OSS地址不能为空
model.deploy.oss.url.length.exceeded=模型文件OSS地址长度不能超过{max}个字符
model.deploy.mirror.image.url.required=基础镜像地址不能为空
model.deploy.mirror.image.url.length.exceeded=基础镜像地址长度不能超过{max}个字符
model.deploy.resource.info.required=资源信息不能为空
model.deploy.encrypt.required=是否加密不能为空
model.deploy.remark.length.exceeded=备注长度不能超过{max}个字符
model.deploy.config.changed.required=模型配置变更项不能为空

# Dict validation
dict.id.required=主键不能为空
dict.name.required=字典名称不能为空
dict.name.length.exceeded=字典名称长度不能超过{max}个字符
dict.type.required=字典类型不能为空
dict.type.length.exceeded=字典类型长度不能超过{max}个字符
dict.remark.length.exceeded=备注长度不能超过{max}个字符

# Dict data validation
dict.data.id.required=主键不能为空
dict.data.dict.id.required=字典类型id不能为空
dict.data.label.required=字典标签不能为空
dict.data.label.length.exceeded=字典标签长度不能超过{max}个字符
dict.data.value.required=字典键值不能为空
dict.data.value.length.exceeded=字典键值长度不能超过{max}个字符
dict.data.css.class.length.exceeded=样式属性长度不能超过{max}个字符
dict.data.color.class.length.exceeded=颜色属性长度不能超过{max}个字符
dict.data.remark.length.exceeded=备注长度不能超过{max}个字符

# Model info validation
model.info.name.length.exceeded=名称长度不能超过{max}个字符
model.info.type.length.exceeded=类型长度不能超过{max}个字符
model.info.task.type.length.exceeded=任务类型长度不能超过{max}个字符
model.info.model.name.length.exceeded=模型名称长度不能超过{max}个字符
model.info.remark.length.exceeded=备注长度不能超过{max}个字符

# Model resource validation
model.resource.gpu.info.required=gpu信息不能为空


# Alarm update validation
alarm.update.remark.length.exceeded=处理备注不能超过{max}个字符

# Alarm config validation
alarm.config.tag.length.exceeded=标签长度不能超过{max}个字符
alarm.config.model.id.required=关联模型不能为空
alarm.config.alarm.type.required=告警类型不能为空
alarm.config.threshold.required=告警阈值配置不能为空
alarm.config.time.interval.required=告警时间间隔不能为空
alarm.config.time.interval.unit.required=告警时间间隔单位不能为空
alarm.config.receiver.required=接收人配置不能为空

# Heartbeat validation
heartbeat.status.required=任务状态不能为空

# Model register validation
model.register.node.ip.required=节点IP地址不能为空
model.register.pod.name.required=pod名称不能为空
model.register.pod.namespace.required=pod命名空间不能为空

# GPU apply status validation
gpu.apply.status.remark.length.exceeded=审批说明不能超过{max}个字符

# Resource expend validation
resource.expend.month.required=支出月份不能为空
resource.expend.list.required=支出数据不能为空
resource.expend.remark.length.exceeded=备注说明不能超过{max}个字符
resource.expend.budget.diff.remark.length.exceeded=预算差异说明不能超过{max}个字符

# Resource bill validation
resource.bill.redundancy.mid.price.required=中间件费用冗余比例不能为空
resource.bill.redundancy.gpu.price.required=GPU费用冗余比例不能为空

# Resource mid plan validation
resource.mid.plan.price.remark.length.exceeded=价格说明不能超过{max}个字符
resource.mid.plan.remark.length.exceeded=备注说明不能超过{max}个字符

# Resource bill group config validation
resource.bill.group.config.redundancy.list.required=冗余比例集合不能为空
resource.bill.group.config.mid.price.redundancy.required=中间件费用冗余比例不能为空
resource.bill.group.config.gpu.price.redundancy.required=GPU费用冗余比例不能为空

# Resource gpu plan validation
resource.gpu.plan.model.length.exceeded=型号长度不能超过{max}个字符
resource.gpu.plan.remark.length.exceeded=备注说明不能超过{max}个字符

# Resource plan validation
resource.plan.month.required=规划月份不能为空
resource.plan.platform.length.exceeded=云平台长度不能超过{max}个字符

# Resource plan query validation
resource.plan.query.month.required=规划月份不能为空

# Resource expend query validation
resource.expend.query.month.required=支出月份不能为空

# GPU apply validation
gpu.apply.type.required=审批类型不能为空
gpu.apply.type.length.exceeded=审批类型长度不能超过{max}个字符
gpu.apply.cluster.business.required=集群/业务不能为空
gpu.apply.cluster.business.length.exceeded=集群/业务长度不能超过{max}个字符
gpu.apply.platform.length.exceeded=云平台长度不能超过{max}个字符
gpu.apply.gpu.model.required=GPU型号不能为空
gpu.apply.gpu.model.length.exceeded=GPU型号长度不能超过{max}个字符
gpu.apply.gpu.num.required=GPU卡数不能为空
gpu.apply.remark.length.exceeded=补充说明不能超过{max}个字符
gpu.apply.supply.remark.length.exceeded=审批说明不能超过{max}个字符

# Cluster validation
cluster.platform.length.exceeded=平台长度不能超过{max}个字符
cluster.name.length.exceeded=名称长度不能超过{max}个字符
cluster.service.type.required=服务类型不能为空
cluster.nodes.required=节点列表不能为空

# Cluster node validation
cluster.node.instance.id.length.exceeded=实例id长度不能超过{max}个字符
cluster.node.ip.required=节点IP不能为空
cluster.node.ip.length.exceeded=节点IP长度不能超过{max}个字符
cluster.node.cost.required=成本不能为空
cluster.node.cost.min.value=成本必须大于等于{value}
cluster.node.gpu.id.required=GPU ID不能为空

# Cluster usage validation
cluster.usage.cluster.id.required=集群id不能为空
cluster.usage.date.required=日期不能为空
cluster.usage.total.gpu.memory.required=总gpu显存大小不能为空
cluster.usage.used.gpu.memory.required=已使用gpu显存大小不能为空
cluster.usage.gpu.usage.rate.required=gpu使用率不能为空
cluster.usage.gpu.usage.rate.min=gpu使用率不能小于{value}
cluster.usage.gpu.usage.rate.max=gpu使用率不能大于{value}
cluster.usage.remark.length.exceeded=备注长度不能超过{max}个字符

# Cluster model type usage validation
cluster.model.type.usage.start.date.required=开始日期不能为空
cluster.model.type.usage.end.date.required=结束日期不能为空

# Cluster usage stat validation
cluster.usage.stat.data.date.required=数据日期不能为空

# Cluster model usage validation
cluster.model.usage.cluster.id.required=集群id不能为空
cluster.model.usage.data.date.required=数据日期不能为空

# Dynamic config validation
dynamic.config.min.replica.required=最小副本数不能为空
dynamic.config.min.replica.min.value=最小副本数最小值为{value}
dynamic.config.max.replica.required=最大副本数不能为空
dynamic.config.max.replica.max.value=最大副本数不能超过{value}
dynamic.config.cooldown.minute.min.value=冷却分钟数最小值为{value}
dynamic.config.scale.out.minute.required=扩容维度不能为空
dynamic.config.scale.out.minute.min.value=扩容维度最小值为{value}
dynamic.config.scale.out.threshold.required=扩容负载不能为空
dynamic.config.scale.out.threshold.range=扩容负载范围在1-100%之间
dynamic.config.scale.in.minute.required=缩容维度不能为空
dynamic.config.scale.in.minute.min.value=缩容维度最小值为{value}
dynamic.config.scale.in.threshold.required=缩容负载不能为空
dynamic.config.scale.in.threshold.range=缩容负载范围在1-100%之间

# Dynamic config global validation
dynamic.config.global.zadig.endpoint.length.exceeded=Zadig endpoint长度不能超过{max}个字符
dynamic.config.global.zadig.api.token.length.exceeded=Zadig api token长度不能超过{max}个字符

# Host validation
host.ip.required=主机ip不能为空
host.ip.length.exceeded=主机ip长度不能超过{max}个字符
host.platform.length.exceeded=平台长度不能超过{max}个字符
host.region.required=区域不能为空
host.region.length.exceeded=区域长度不能超过{max}个字符
host.remark.length.exceeded=备注长度不能超过{max}个字符

# GPU validation
gpu.card.count.required=卡数不能为空
gpu.card.count.min.value=卡数不能小于{value}
gpu.single.card.memory.required=单卡显存不能为空
gpu.single.card.memory.min.value=单卡显存不能小于{value}

# Model config validation
model.config.project.name.required=项目工程名称不能为空
model.config.project.name.length.exceeded=项目工程名称长度不能超过{max}个字符
model.config.project.git.url.required=项目git地址不能为空
model.config.project.git.url.length.exceeded=项目git地址长度不能超过{max}个字符
model.config.version.length.exceeded=模型版本长度不能超过{max}个字符
model.config.sdk.version.required=部署sdk版本不能为空
model.config.sdk.version.length.exceeded=部署sdk版本长度不能超过{max}个字符
model.config.oss.url.required=模型文件OSS地址不能为空
model.config.oss.url.length.exceeded=模型文件OSS地址长度不能超过{max}个字符
model.config.encrypt.required=是否加密不能为空
model.config.mirror.image.url.required=基础镜像地址不能为空
model.config.mirror.image.url.length.exceeded=基础镜像地址长度不能超过{max}个字符
model.config.memory.size.required=内存大小GB不能为空
model.config.gpu.info.required=gpu信息不能为空

# Model instance adjust validation
model.instance.adjust.model.info.required=调整模型信息不能为空

# Task model callback validation
task.model.callback.task.id.required=任务ID不能为空

# Model test validation
model.test.name.length.exceeded=名称长度不能超过{max}个字符
model.test.task.type.length.exceeded=任务类型长度不能超过{max}个字符
model.test.model.name.length.exceeded=模型名称长度不能超过{max}个字符
model.test.param.type.required=参数类型不能为空
model.test.param.type.length.exceeded=参数类型长度不能超过{max}个字符
model.test.model.params.required=模型参数不能为空
model.test.batch.task.count.required=任务批次数不能为空
model.test.batch.task.count.min.value=任务批次数不能小于{value}
model.test.batch.task.count.max.value=任务批次数不能超过{value}

# Model test report validation
model.test.report.test.no.required=测试编号不能为空
model.test.report.service.name.required=服务名不能为空
model.test.report.info.required=报告信息不能为空

# Model encrypt callback validation
model.encrypt.callback.deploy.id.required=部署发布id不能为空

# Model heartbeat validation
model.heartbeat.register.id.required=注册id不能为空


# Node validation
node.heartbeat.id.required=注册id不能为空
node.register.node.ip.required=节点IP地址不能为空
node.register.hostname.required=主机名不能为空

validation.task.ids.empty=任务id集合不能为空
validation.params.null=参数不能为空
validation.task.batch.range=任务批量标识必须是0或1

# Business error messages
business.sort.field.not.exists=排序字段[{0}]不存在
business.model.param.type.error=类型错误
business.model.param.format.error.object=模型参数格式错误, 需为{}
business.model.param.format.error.array=模型参数格式错误, 需为[{},{}]
business.file.upload.empty=上传文件的不能为空
business.file.name.empty=文件名不能为空
business.file.format.unsupported=仅支持文件格式{0}
business.file.upload.size.exceeded=上传文件大小不能超过{0}
business.file.upload.failed=文件上传失败
business.file.download.failed=文件下载失败
business.file.not.found=文件不存在
business.file.path.empty=文件路径不能为空
business.file.s3.upload.failed=S3文件上传失败
business.file.s3.download.failed=S3文件下载失败
business.file.s3.config.missing=缺少S3配置参数
business.platform.unsupported=不支持的云平台[{0}]
business.file.content.column.empty=r[{0}]文件内容[{1}]列不能为空
business.file.date.format.error=r[{0}]{1}格式为yyyy-MM，如：2025-06
business.bill.type.unsupported=不支持的账单类型[{0}]
business.bill.import.data.empty=导入账单类型/列表/云平台为空
business.bill.no.matching.data=无匹配的账单数据，请检查账单数据
business.task.info.empty=任务信息为空
business.task.create.failed=任务创建失败
business.task.model.name.ids.empty=modelName/taskIds不能为空
business.task.id.model.name.empty=taskId/modelName不能为空
business.task.id.empty=taskId 不能为空
business.compare.type.empty=比较类型不能为空
business.task.type.model.name.empty=任务类型/模型名称不能为空
business.model.param.format.error=模型参数格式错误
business.model.param.empty=模型参数不能为空
business.dict.type.exists=该字典类型已存在，无法更新
business.model.test.status.cannot.operate=非【{0}】状态无法操作，请刷新后重试
business.model.test.report.generated=测试报告已生成，请勿重复提交
business.dynamic.adjust.status.cannot.cancel=动态调整记录状态不支持取消
business.task.ids.business.id.empty=taskIds/businessId 不能为空
business.batch.task.count.limit=批量任务数不能超过200
business.bill.period.instance.duplicate=帐期[{0}]实例id[{1}]重复，请检查账单数据
business.dict.type.already.exists=该字典类型已存在
business.model.test.already.exists=该模型测试已存在，请先执行或等待执行完成
business.compare.type.unsupported=比较类型不支持
business.resource.expend.month.data.exists=当前月份支出数据已存在
business.node.ip.duplicate=节点IP重复:{0}
business.deploy.status.only.deploying=只允许发布中状态的发布记录
business.model.already.exists=该模型已存在
business.model.not.supported.contact.admin=不支持的模型，请联系管理员
business.service.not.configured.contact.admin=服务未配置，请联系管理员
business.request.key.mismatch=请求key不匹配
business.cluster.disable.before.delete=请先禁用集群,再删除
business.approval.completed=审批已完成，无法再次执行该操作
business.upload.file.content.empty=上传文件的内容不能为空
business.service.type.not.match=【{0}】集群标记的服务类型【{1}】与【{2}】模型注册信息类型【{3}】不一致
business.current.month.plan.data.exists=当前月份规划数据已存在，请先删除再添加
business.task.not.exists=任务不存在
business.param.together.empty=taskIds&taskType&modelName 不能同时为空
business.notify.model.encrypt.failed=通知模型加密失败
business.not.encrypting.status=非【加密中】状态的发布记录
business.task.cancelled=任务已取消，无法重复取消
business.task.status.not.allow.cancel=任务状态不允许取消
business.model.test.not.allow.edit=该模型测试已存在，无法修改
business.test.task.running=测试任务正在执行中，请先取消后删除
business.not.allow.retry=无符合重试的任务
business.retry.result=重试成功：{0}，重试失败：{1}
business.not.allow.cancel=无符合取消的任务
business.model.deploy.already.exists=该模型发布已存在，请先发布或等待发布完成
business.model.not.exists=该模型不存在
business.current.status.cannot.rollback=当前状态无法回退/数据发生变化，请重试
business.cluster.date.record.exists=该集群在此日期已有记录
business.cluster.date.record.exists.cannot.update=该集群在此日期已有记录，无法更新
business.gpu.in.use.cannot.delete=GPU使用中，无法删除
business.host.ip.already.exists=主机IP已存在
business.host.enabled.cannot.delete=启用状态，不允许删除，请先禁用
business.deploy.draft.only.edit=只能修改草稿状态的发布记录
business.deploy.deploying.cannot.delete=发布中的记录无法删除
business.deploy.draft.only.request=只能申请发布草稿状态的发布记录
business.model.type.error=类型错误
business.test.record.not.exists=测试记录不存在
business.model.config.not.exists=该模型配置不存在
business.model.config.already.exists=模型配置已存在
business.no.cluster.nodes=无集群节点，请检查集群配置
business.template.placeholder.missing=JSON模板中的占位符在动态参数列表中未找到: {0}
business.template.parameter.unused=动态参数列表中的参数在JSON模板中未使用: {0}
business.template.validation.failed=模板参数校验失败 - {0}

