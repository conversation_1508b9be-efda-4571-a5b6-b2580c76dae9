package com.zjkj.aigc.common.req.task;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/11
 */
@Data
public class AigcTaskPriorityReq implements Serializable {
    private static final long serialVersionUID = 6418488516064863050L;

    /**
     * 业务id
     */
    @NotBlank(message = "{task.priority.business.id.required}")
    private String businessId;
    /**
     * 任务id列表
     */
    private List<String> taskIds;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 目标优先级
     */
    private int targetPriority = -1;
}
