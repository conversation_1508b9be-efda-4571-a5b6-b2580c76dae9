package com.zjkj.aigc.common.vo.dynamic;

import com.zjkj.aigc.common.vo.BaseVO;
import com.zjkj.aigc.common.vo.model.AigcModelInfoBrieflyVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcDynamicConfigVO extends BaseVO {
    private static final long serialVersionUID = 160093604556368436L;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型信息
     */
    private AigcModelInfoBrieflyVO modelInfo;
    /**
     * 最小副本数
     */
    private Integer minReplica;
    /**
     * 最大副本数
     */
    private Integer maxReplica;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 冷却分钟数
     */
    private Integer cooldownMinute;
    /**
     * 扩容维度:分钟数
     */
    private Integer scaleOutMinute;
    /**
     * 扩容负载: 阈值1-100%
     */
    private Integer scaleOutThreshold;
    /**
     * 优先剥夺的模型id
     */
    private List<Long> deprivedModelIds;
    /**
     * 缩容维度:分钟数
     */
    private Integer scaleInMinute;
    /**
     * 缩容负载:阈值1-100%
     */
    private Integer scaleInThreshold;
    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;
}
