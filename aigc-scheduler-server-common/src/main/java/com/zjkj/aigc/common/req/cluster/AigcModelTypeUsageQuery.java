package com.zjkj.aigc.common.req.cluster;

import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Data
public class AigcModelTypeUsageQuery implements Serializable {
    private static final long serialVersionUID = 5473761563161194432L;
    /**
     * 平台
     */
    private String platform;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 开始日期
     */
    @NotNull(message = "{cluster.model.type.usage.start.date.required}")
    private LocalDate startDate;
    /**
     * 结束日期
     */
    @NotNull(message = "{cluster.model.type.usage.end.date.required}")
    private LocalDate endDate;

    /**
     * 检查时间范围
     *
     */
    public void checkParams() {
        if (ChronoUnit.MONTHS.between(this.startDate, this.endDate) > 3) {
            throw new BaseBizException(CustomErrorCode.TIME_RANGE_TO_LONG, String.format(CustomErrorCode.TIME_RANGE_TO_LONG.getMessage(), "3个月"));
        }
    }
}
