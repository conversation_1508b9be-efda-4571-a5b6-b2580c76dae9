package com.zjkj.aigc.common.vo.stat;

import com.zjkj.aigc.common.util.TimeFormatter;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/22 18:23
 */
@Setter
@Getter
public class AigcTaskStatDayVO implements Serializable {

    private static final long serialVersionUID = -5683114572246847023L;
    /**
     * 模型ID
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 成功数量
     */
    private Long successAmount;

    /**
     * 失败数量
     */
    private Long failAmount;

    /**
     * 处理中数量
     */
    private Long runningAmount;

    /**
     * 等待中数量
     */
    private Long waitingAmount;

    /**
     * 取消数量
     */
    private Long cancelAmount;

    /**
     * 总数量
     */
    private Long totalAmount;
    /**
     * 平均耗时ms
     */
    private Long avgElapsedTime;

    /**
     * 平均耗时
     */
    private String formatAvgElapsedTime;
    /**
     * 预计完成时间
     */
    private LocalDateTime expectFinishedTime;
    /**
     * 统计日期
     */
    private LocalDate statDate;

    public String getFormatAvgElapsedTime() {
        if (Objects.nonNull(this.avgElapsedTime)) {
            return TimeFormatter.formatTime(this.avgElapsedTime);
        }
        return null;
    }

    /**
     * 初始化为0
     * @return AigcTaskStatDayVO
     */
    public static AigcTaskStatDayVO initZero() {
        AigcTaskStatDayVO taskStatDay = new AigcTaskStatDayVO();
        long zero = BigDecimal.ZERO.longValue();
        taskStatDay.setSuccessAmount(zero);
        taskStatDay.setFailAmount(zero);
        taskStatDay.setRunningAmount(zero);
        taskStatDay.setWaitingAmount(zero);
        taskStatDay.setCancelAmount(zero);
        taskStatDay.setTotalAmount(zero);
        return taskStatDay;
    }
}
