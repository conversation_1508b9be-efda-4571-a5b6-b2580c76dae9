package com.zjkj.aigc.common.vo.sys;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 字典视图对象
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysDictVO extends BaseVO {
    private static final long serialVersionUID = 2480984117056731860L;

    /**
     * 字典名称
     */
    private String name;

    /**
     * 字典类型
     */
    private String type;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 字典数据
     */
    private List<SysDictDataVO> dictDataList;
}
