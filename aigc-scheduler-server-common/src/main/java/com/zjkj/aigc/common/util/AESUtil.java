package com.zjkj.aigc.common.util;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
public class AESUtil {
    /**
     * GCM模式的IV长度固定为12字节
     */
    private static final int GCM_IV_LENGTH = 12;
    /**
     * GCM认证标签长度为128位(16字节)
     */
    private static final int GCM_TAG_LENGTH = 16 * 8;
    private static final String MODEL_GCM = "AES/GCM/NoPadding";

    /**
     * AES GCM加密
     *
     * @param plaintext 明文
     * @param key       密钥（建议16、24或32字节）
     * @return Base64编码的密文
     */
    public static String encrypt(String plaintext, byte[] key) {
        try {
            // 生成随机IV(初始化向量)
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom random = new SecureRandom();
            random.nextBytes(iv);

            // 创建并初始化加密器
            Cipher cipher = Cipher.getInstance(MODEL_GCM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(key), parameterSpec);

            // 执行加密
            byte[] ciphertext = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 使用ByteBuffer拼接IV和密文
            ByteBuffer byteBuffer = ByteBuffer.allocate(iv.length + ciphertext.length);
            byteBuffer.put(iv);
            byteBuffer.put(ciphertext);

            // 转换为Base64编码
            return Base64.getEncoder().encodeToString(byteBuffer.array());
        } catch (Exception e) {
            throw new RuntimeException("AES encryption failed", e);
        }
    }

    /**
     * AES GCM解密
     *
     * @param encryptedBase64 Base64编码的密文
     * @param key             密钥
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedBase64, byte[] key) {
        try {
            // 从Base64解码
            byte[] decoded = Base64.getDecoder().decode(encryptedBase64);

            // 使用ByteBuffer提取IV和密文
            ByteBuffer buffer = ByteBuffer.wrap(decoded);
            byte[] iv = new byte[GCM_IV_LENGTH];
            buffer.get(iv);
            byte[] ciphertext = new byte[buffer.remaining()];
            buffer.get(ciphertext);

            // 创建并初始化解密器
            Cipher cipher = Cipher.getInstance(MODEL_GCM);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(key), parameterSpec);

            // 执行解密
            byte[] plaintext = cipher.doFinal(ciphertext);
            return new String(plaintext, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AES decryption failed", e);
        }
    }

    /**
     * 获取SecretKeySpec
     *
     * @param key 密钥
     * @return SecretKeySpec
     */
    public static SecretKeySpec getSecretKey(byte[] key) {
        return new SecretKeySpec(key, "AES");
    }
}
