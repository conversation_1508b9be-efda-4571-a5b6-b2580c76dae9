package com.zjkj.aigc.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zjkj.aigc.common.serializer.TimestampSerializer;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Data
public class ModelTestBriefDTO implements Serializable {
    private static final long serialVersionUID = 1178651724836627810L;
    /**
     * 测试编号
     */
    private String testNo;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 次数
     */
    private Long batchCount;
    /**
     * 开始时间
     */
    @JsonSerialize(using = TimestampSerializer.class)
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @JsonSerialize(using = TimestampSerializer.class)
    private LocalDateTime endTime;
    /**
     * 模型参数
     */
    private String modelParams;
}
