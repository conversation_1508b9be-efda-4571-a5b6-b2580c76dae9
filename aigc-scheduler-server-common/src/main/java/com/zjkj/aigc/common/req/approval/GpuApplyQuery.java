package com.zjkj.aigc.common.req.approval;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
@Builder
public class GpuApplyQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -8189172552977942327L;
    /**
     * 审批编号
     */
    private String code;
    /**
     * 审批类型
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalTypeEnum}
     */
    private String type;
    /**
     * 审批状态
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum}
     */
    private Integer status;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 发起人
     */
    private String creatorName;
    /**
     * 是否查询当前用户
     */
    private Boolean currentUser;
}
