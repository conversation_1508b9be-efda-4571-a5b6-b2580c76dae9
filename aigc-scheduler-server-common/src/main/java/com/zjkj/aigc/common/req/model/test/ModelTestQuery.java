package com.zjkj.aigc.common.req.model.test;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ModelTestQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = 6697480983519542670L;
    /**
     * 名称
     */
    private String name;
    /**
     * 测试编号
     */
    private String testNo;
    /**
     *  状态
     */
    private Integer status;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
}
