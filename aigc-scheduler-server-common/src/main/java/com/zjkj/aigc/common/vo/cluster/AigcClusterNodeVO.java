package com.zjkj.aigc.common.vo.cluster;

import com.zjkj.aigc.common.vo.BaseVO;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 集群节点视图对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcClusterNodeVO extends BaseVO {

    private static final long serialVersionUID = 2053534632236476629L;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 节点ip
     */
    private String nodeIp;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * gpu主键id
     */
    private Long gpuId;
    /**
     * gpu
     */
    private AigcGpuVO gpu;
    /**
     * cpu核数
     */
    private Integer cpuCore;
    /**
     * 内存大小GB
     */
    private Integer memorySize;
    /**
     * 在线状态
     */
    private boolean online;
}
