package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelDeployQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -3728452648873047635L;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 加密状态:0-未加密,1-加密中,2-加密完成,3-加密失败
     */
    private List<Integer> encryptStatusList;
    /**
     * 是否加密
     */
    private Boolean isEncrypt;
    /**
     * 状态: 0-草稿,1-发布中,2-发布成功,3-发布失败
     */
    private List<Integer> statusList;
}
