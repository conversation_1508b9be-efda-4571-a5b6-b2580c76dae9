package com.zjkj.aigc.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:21
 */
@Setter
@Getter
public class TaskSummaryStatResp implements Serializable {
    private static final long serialVersionUID = 1L;
    private String modelName;
    private String taskType;
    private Long totalCount = 0L;
    private Long successCount = 0L;
    private Long failCount = 0L;
    private Long runningCount = 0L;
    private Long waitingCount = 0L;
    private Long cancelCount = 0L;
    private Long avgTimeMs;
    private String statDate;
}
