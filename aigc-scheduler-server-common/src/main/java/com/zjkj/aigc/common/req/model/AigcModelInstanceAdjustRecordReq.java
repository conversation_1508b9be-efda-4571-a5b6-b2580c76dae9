package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.dto.ModelInstanceAdjustDTO;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/01/17
 */
@Data
public class AigcModelInstanceAdjustRecordReq implements Serializable {
    private static final long serialVersionUID = -1115537671433512563L;
    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 模型信息
     */
    @NotEmpty(message = "{model.instance.adjust.model.info.required}")
    private List<ModelInstanceAdjustDTO> modelInfo;
}
