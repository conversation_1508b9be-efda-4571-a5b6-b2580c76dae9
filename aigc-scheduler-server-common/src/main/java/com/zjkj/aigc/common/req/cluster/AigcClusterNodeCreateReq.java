package com.zjkj.aigc.common.req.cluster;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 集群节点创建请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class AigcClusterNodeCreateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 实例id
     */
    @Length(max = 64, message = "{cluster.node.instance.id.length.exceeded}")
    private String instanceId;

    /**
     * 节点ip
     */
    @NotBlank(message = "{cluster.node.ip.required}")
    @Length(max = 128, message = "{cluster.node.ip.length.exceeded}")
    private String nodeIp;

    /**
     * 成本
     */
    @NotNull(message = "{cluster.node.cost.required}")
    @DecimalMin(value = "0", message = "{cluster.node.cost.min.value}")
    private BigDecimal cost;

    /**
     * gpu主键id
     */
    @NotNull(message = "{cluster.node.gpu.id.required}")
    private Long gpuId;
    /**
     * cpu核数
     */
    @NotNull(message = "{validation.cpu.core.required}")
    private Integer cpuCore;
    /**
     * 内存大小
     */
    @NotNull(message = "{validation.memory.size.required}")
    private Integer memorySize;
}
