package com.zjkj.aigc.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@Data
public class PageVO<T> implements Serializable {
    private static final long serialVersionUID = 4531036252606333369L;
    /**
     * 页码
     */
    private Long pageNum;
    /**
     * 页大小
     */
    private Long pageSize;
    /**
     * 总数
     */
    private Long total;
    /**
     * 页数
     */
    private Long pages;
    /**
     * 数据
     */
    private List<T> records;
}
