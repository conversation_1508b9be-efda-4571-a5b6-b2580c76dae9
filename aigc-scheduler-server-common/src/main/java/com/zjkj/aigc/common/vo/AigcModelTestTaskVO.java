package com.zjkj.aigc.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * ai任务表(AigcTask)实体类
 *
 * <AUTHOR>
 * @since 2024-05-30
 */
@Data
@Accessors(chain = true)
public class AigcModelTestTaskVO implements Serializable {
    private static final long serialVersionUID = 436124028580969512L;

    private Long id;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 测试编码
     */
    private String testCode;
    /**
     * 任务进度
     */
    private Integer taskProgress;
    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消
     */
    private Integer taskState;
    /**
     * 任务开始时间
     */
    private LocalDateTime taskStartTime;
    /**
     * 任务取消时间
     */
    private LocalDateTime taskCancelTime;
    /**
     * 任务完成时间
     */
    private LocalDateTime taskCompletionTime;
    /**
     * AI模型
     */
    private Long modelId;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 任务类型，一般与AI模型对应
     */
    private String taskType;
    /**
     * 生成风格
     */
    private String styleModel;
    /**
     * 灵感图
     */
    private String refImgUrl;
    /**
     * 模型参数，与任务类型有关
     */
    private String modelParams;
    /**
     * 输出图片{多张以英文逗号分隔}
     */
    private String modelOutput;
    /**
     * 输出图片{多张以英文逗号分隔}
     */
    private String modelNewOutput;
    /**
     * 评价人id
     */
    private Long evaluator_id;
    /**
     * 评价人
     */
    private String evaluator;
    /**
     * 是否可接受,-1:未评价，0:否，1:是
     */
    private Integer isAcceptable;
    /**
     * 可接受的结果
     */
    private String acceptOutput;
    /**
     * 评价详情
     */
    private String evaluation;
    /**
     * 运行容器id
     */
    private String containerId;
    /**
     * 失败信息
     */
    private String failMessage;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime revisedTime;
    /**
     * 逻辑删除 0 否 1是
     */
    private Integer isDeleted;

}

