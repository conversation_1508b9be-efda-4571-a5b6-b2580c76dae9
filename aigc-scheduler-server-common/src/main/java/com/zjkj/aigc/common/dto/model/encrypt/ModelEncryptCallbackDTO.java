package com.zjkj.aigc.common.dto.model.encrypt;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/13
 */
@Data
public class ModelEncryptCallbackDTO implements Serializable {
    private static final long serialVersionUID = -8097633478518865201L;

    /**
     * 部署发布id
     */
    @NotNull(message = "{model.encrypt.callback.deploy.id.required}")
    @JsonAlias("deploy_id")
    private Long deployId;
    /**
     * 1-加密完成, 2-加密失败
     */
    @NotNull(message = "{validation.status.required}")
    private Integer status;
    /**
     * 失败信息
     */
    private String message;
}
