package com.zjkj.aigc.common.exception;

import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.ErrorCode;
import lombok.Getter;

/**
 * 自定义异常类
 *
 * <AUTHOR>
 * @since 2024/5/30
 */
@Getter
public class WithErrorCodeException extends RuntimeException {

    private static final long serialVersionUID = 572297362219928448L;

    private final ErrorCode errorCode;

    public WithErrorCodeException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public WithErrorCodeException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public WithErrorCodeException(ErrorCode errorCode, String message, Throwable throwable) {
        super(message, throwable);
        this.errorCode = errorCode;
    }
}
