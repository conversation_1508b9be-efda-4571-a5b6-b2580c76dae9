package com.zjkj.aigc.common.req.gpu;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GPU查询请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcGpuQuery extends PageBaseReq {

    private static final long serialVersionUID = 8743171679342374987L;
    /**
     * 平台
     */
    private String platform;

    /**
     * 型号
     */
    private String model;

    /**
     * GPU卡数
     */
    private Integer cardCount;

    /**
     * 单卡显存
     */
    private Integer singleCardMemory;
}
