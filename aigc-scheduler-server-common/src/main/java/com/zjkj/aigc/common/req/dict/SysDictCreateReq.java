package com.zjkj.aigc.common.req.dict;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 字典创建请求
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class SysDictCreateReq {
    /**
     * 主键
     */
    @NotNull(message = "{validation.id.required}", groups = ValidationGroup.Update.class)
    private Long id;

    /**
     * 字典名称
     */
    @NotBlank(message = "{dict.name.required}")
    @Length(max = 100, message = "{dict.name.length.exceeded}")
    private String name;

    /**
     * 字典类型
     */
    @NotBlank(message = "{dict.type.required}")
    @Length(max = 100, message = "{dict.type.length.exceeded}")
    private String type;

    /**
     * 备注
     */
    @Length(max = 250, message = "{dict.remark.length.exceeded}")
    private String remark;
}
