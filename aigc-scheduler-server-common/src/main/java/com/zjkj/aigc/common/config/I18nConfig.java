package com.zjkj.aigc.common.config;

import com.zjkj.aigc.common.i18n.LocaleUtils;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.nio.charset.StandardCharsets;

/**
 * 国际化基础配置类
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Configuration
public class I18nConfig {

    /**
     * 消息源配置
     * Spring Boot会自动查找以下文件：
     * - messages.properties (默认)
     * - messages_zh_CN.properties (中文)
     * - messages_en_US.properties (英文)
     * - messages_ja_JP.properties (日文)
     */
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();

        // 设置消息文件基础名称（不包含语言后缀）
        messageSource.setBasenames("i18n/messages");

        // 设置编码为UTF-8，支持中文
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.name());

        // 设置缓存时间（秒），生产环境建议设置较长时间
        messageSource.setCacheSeconds(3600);

        // 当找不到消息时，使用消息代码作为默认消息
        messageSource.setUseCodeAsDefaultMessage(true);

        // 不回退到系统默认语言，使用我们定义的默认语言
        messageSource.setFallbackToSystemLocale(false);

        // 设置默认语言环境（当找不到对应语言文件时使用）
        messageSource.setDefaultLocale(LocaleUtils.DEFAULT_LOCALE);

        return messageSource;
    }
}
