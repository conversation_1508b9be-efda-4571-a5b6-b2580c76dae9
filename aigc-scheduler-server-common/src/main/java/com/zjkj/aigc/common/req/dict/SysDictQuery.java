package com.zjkj.aigc.common.req.dict;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典查询请求
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictQuery extends PageBaseReq {
    private static final long serialVersionUID = 3198252659059636794L;
    /**
     * 字典类型
     */
    private String type;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;
}
