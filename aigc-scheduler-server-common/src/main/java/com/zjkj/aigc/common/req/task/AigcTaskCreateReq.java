package com.zjkj.aigc.common.req.task;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/5
 */
@Data
public class AigcTaskCreateReq implements Serializable {
    private static final long serialVersionUID = 8476200943371774213L;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;
    /**
     * 模型参数
     */
    @NotEmpty(message = "{task.params.required}")
    private Map<String, Object> params;
    /**
     * 应用ID
     */
    @JsonIgnore
    private String appId;
    /**
     * 平台任务ID
     */
    @JsonIgnore
    private String aigcTaskId;

    /**
     * 优先级（可选）
     * 数字越小优先级越高，默认 0
     */
    private Integer taskPriority;
}
