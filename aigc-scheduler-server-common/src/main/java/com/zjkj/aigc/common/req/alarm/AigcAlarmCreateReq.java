package com.zjkj.aigc.common.req.alarm;

import com.zjkj.aigc.common.dto.alarm.AlarmSnapshotDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmCreateReq implements Serializable {
    private static final long serialVersionUID = 9026675440219785208L;
    /**
     * 告警编号
     */
    private String alarmCode;
    /**
     * 报警快照
     */
    private AlarmSnapshotDTO alarmSnapshot;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型类型
     * {@link com.zjkj.aigc.common.enums.model.AigcModelTypeEnum}
     */
    private String modelType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;
}
