package com.zjkj.aigc.common.dto.node;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@NoArgsConstructor
@Data
public class NodeSystemInfo implements Serializable {
    private static final long serialVersionUID = -9114890274456847199L;
    /**
     * 系统信息
     */
    @JsonAlias("os_info")
    private OsInfo osInfo;
    /**
     * CPU信息
     */
    @JsonAlias("cpu_info")
    private CpuInfo cpuInfo;
    /**
     * 内存信息
     */
    @JsonAlias("memory_info")
    private MemoryInfo memoryInfo;
    /**
     * 网络信息
     */
    @JsonAlias("network_info")
    private NetworkInfo networkInfo;
    /**
     * Docker信息
     */
    @JsonAlias("docker_info")
    private DockerInfo dockerInfo;

    @Data
    public static class OsInfo {
        /**
         * 操作系统信息
         */
        @JsonAlias("os_type")
        private String osType;
        /**
         * 系统版本号
         */
        @JsonAlias("os_version")
        private String osVersion;
        /**
         * 内核版本
         */
        private String kernel;
        /**
         * 系统架构
         */
        private String architecture;
    }

    @Data
    public static class CpuInfo {
        /**
         * CPU型号
         */
        private String model;
        /**
         * CPU核心数
         */
        private String cores;
        /**
         * CPU架构
         */
        private String architecture;
    }

    @Data
    public static class MemoryInfo {
        /**
         * 总内存
         */
        private String total;
        /**
         * 可用内存
         */
        private String available;
    }

    @Data
    public static class NetworkInfo {
        /**
         * 主机MAC地址
         */
        @JsonAlias("host_mac")
        private String hostMac;
    }

    @Data
    public static class DockerInfo {
        /**
         * Docker版本
         */
        private String version;
    }
}
