package com.zjkj.aigc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
public class RegisterStatusEnum {

    @Getter
    @AllArgsConstructor
    public enum HealthStatus {
        OFFLINE(0, "离线"),
        ONLINE(1, "在线");
        private final Integer code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum Compliance {
        // 状态: 0-非法，1-合法
        ILLEGAL(0, "非法"),
        LEGAL(1, "合法");
        private final Integer code;
        private final String desc;
    }

}
