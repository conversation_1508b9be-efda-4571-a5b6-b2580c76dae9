package com.zjkj.aigc.common.req.node;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcNodeInstanceQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -2431123947261650728L;

    /**
     * 主机名
     */
    private String hostname;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 合法状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 健康状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 命名空间
     */
    private String namespace;
}
