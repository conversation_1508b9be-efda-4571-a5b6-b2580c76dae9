package com.zjkj.aigc.common.vo.node;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/27
 */
@Data
public class AigcNodeInstanceBrieflyVO implements Serializable {
    private static final long serialVersionUID = 6952598792462034914L;
    private Long id;
    /**
     * 主机名
     */
    private String hostname;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 命名空间
     */
    private List<String> namespace;
}
