package com.zjkj.aigc.common.vo.dashboard;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/01/14
 */
@Data
public class AigcClusterResourceDashboardVO implements Serializable {
    private static final long serialVersionUID = 7410256283310786766L;
    /**
     * GPU使用率
     */
    private Usage gpuUsage;
    /**
     * CPU使用率
     */
    private Usage cpuUsage;
    /**
     * 内存使用率
     */
    private Usage memoryUsage;
    /**
     * 集群信息
     */
    private List<Cluster> clusterList;

    @Data
    public static class Usage implements Serializable {
        private static final long serialVersionUID = 6987846394071429290L;
        /**
         * 已使用
         */
        private Integer used;
        /**
         * 总共
         */
        private Integer total;
        /**
         * 使用率
         */
        private BigDecimal usedPercent;
    }


    @Data
    public static class ModelInfo implements Serializable {
        private static final long serialVersionUID = 2547750306498199459L;
        /**
         * 模型ID
         */
        private Long modelId;
        /**
         * 模型名称
         */
        private String modelName;
        /**
         * 任务类型
         */
        private String taskType;
        /**
         * 总任务数量
         */
        private Long totalAmount;
        /**
         * 待处理数量
         */
        private Long waitingAmount;
        /**
         * 任务处理速率
         */
        private BigDecimal taskSpeed;
        /**
         * POD数
         */
        private Integer podCount;
        /**
         * GPU占用
         */
        private Integer gpuUsage;
        /**
         * CPU占用
         */
        private Integer cpuUsage;
        /**
         * 内存占用
         */
        private Integer memoryUsage;
        /**
         * 预计完成时间
         */
        private LocalDateTime expectFinishedTime;
    }

    @Data
    public static class Cluster implements Serializable {
        private static final long serialVersionUID = 8269497737293571610L;
        /**
         * 集群ID
         */
        private Long clusterId;
        /**
         * 集群名称
         */
        private String clusterName;
        /**
         * 环境类型
         */
        private List<String> envType;
        /**
         * GPU使用率
         */
        private Usage gpuUsage;
        /**
         * CPU使用率
         */
        private Usage cpuUsage;
        /**
         * 内存使用率
         */
        private Usage memoryUsage;
        /**
         * 活跃模型
         */
        private List<ModelInfo> activeModels;
        /**
         * 空闲模型
         */
        private List<ModelInfo> freeModels;
    }

    public static AigcClusterResourceDashboardVO init() {
        AigcClusterResourceDashboardVO vo = new AigcClusterResourceDashboardVO();
        Usage gpuUsage = new Usage();
        gpuUsage.setUsed(0);
        gpuUsage.setTotal(0);
        gpuUsage.setUsedPercent(BigDecimal.ZERO);
        Usage cpuUsage = new Usage();
        cpuUsage.setUsed(0);
        cpuUsage.setTotal(0);
        cpuUsage.setUsedPercent(BigDecimal.ZERO);
        Usage memoryUsage = new Usage();
        memoryUsage.setUsed(0);
        memoryUsage.setTotal(0);
        memoryUsage.setUsedPercent(BigDecimal.ZERO);
        vo.setGpuUsage(gpuUsage);
        vo.setCpuUsage(cpuUsage);
        vo.setMemoryUsage(memoryUsage);
        return vo;
    }

    public static Cluster initCluster() {
        AigcClusterResourceDashboardVO.Cluster vo = new AigcClusterResourceDashboardVO.Cluster();
        Usage gpuUsage = new Usage();
        gpuUsage.setUsed(0);
        gpuUsage.setTotal(0);
        gpuUsage.setUsedPercent(BigDecimal.ZERO);
        Usage cpuUsage = new Usage();
        cpuUsage.setUsed(0);
        cpuUsage.setTotal(0);
        cpuUsage.setUsedPercent(BigDecimal.ZERO);
        Usage memoryUsage = new Usage();
        memoryUsage.setUsed(0);
        memoryUsage.setTotal(0);
        memoryUsage.setUsedPercent(BigDecimal.ZERO);
        vo.setGpuUsage(gpuUsage);
        vo.setCpuUsage(cpuUsage);
        vo.setMemoryUsage(memoryUsage);
        return vo;
    }

    public void calculate() {
        if (CollUtil.isNotEmpty(clusterList)) {
            for (Cluster cluster : clusterList) {
                calculateUsage(cluster.getGpuUsage());
                calculateUsage(cluster.getCpuUsage());
                calculateUsage(cluster.getMemoryUsage());
                gpuUsage.setTotal(gpuUsage.getTotal() + cluster.getGpuUsage().getTotal());
                gpuUsage.setUsed(gpuUsage.getUsed() + cluster.getGpuUsage().getUsed());
                cpuUsage.setTotal(cpuUsage.getTotal() + cluster.getCpuUsage().getTotal());
                cpuUsage.setUsed(cpuUsage.getUsed() + cluster.getCpuUsage().getUsed());
                memoryUsage.setTotal(memoryUsage.getTotal() + cluster.getMemoryUsage().getTotal());
                memoryUsage.setUsed(memoryUsage.getUsed() + cluster.getMemoryUsage().getUsed());
            }
        }
        calculateUsage(gpuUsage);
        calculateUsage(cpuUsage);
        calculateUsage(memoryUsage);
    }

    private void calculateUsage(Usage usage) {
        if (usage.getTotal() > 0) {
            usage.setUsedPercent(
                    BigDecimal.valueOf(usage.getUsed())
                            .multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(usage.getTotal()), 2, BigDecimal.ROUND_HALF_UP)
            );
        }
    }
}
