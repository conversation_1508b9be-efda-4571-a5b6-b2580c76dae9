package com.zjkj.aigc.common.req.model.test;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@Data
public class EvaluateReq implements Serializable {
    private static final long serialVersionUID = 6697480983519542670L;

    /**
     *
     */
    private Long id;

    /**
     *
     */
    private Integer isAcceptable;

    /**
     *
     */
    private String acceptOutput;

    /**
     *
     */
    private String evaluation;

}
