package com.zjkj.aigc.common.vo.stat;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Data
@Accessors(chain = true)
public class AigcTaskTotalStatVO implements Serializable {
    private static final long serialVersionUID = 6552552451604810695L;

    /**
     * 总数量
     */
    private Long totalAmount;
    /**
     * 日环比-增长率
     */
    private BigDecimal dayChainGrowth;
    /**
     * 周环比-增长率
     */
    private BigDecimal weekChainGrowth;
}
