package com.zjkj.aigc.common.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/21 15:52
 */
@Setter
@Getter
public class PageBaseReq implements Serializable {
    private static final long serialVersionUID = -4561585430898658171L;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 创建人
     */
    private String creatorName;
    /**
     * 排序字段
     */
    private String sortField;
    /**
     * 是否升序
     */
    private boolean sortAsc;
}
