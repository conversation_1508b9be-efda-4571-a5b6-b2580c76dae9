package com.zjkj.aigc.common.vo.host;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcHostVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 4709497719154228605L;
    /**
     * 主机ip
     */
    private String hostIp;
    /**
     * 平台
     */
    private String platform;
    /**
     * 区域
     */
    private String region;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 在线节点数
     */
    private int onlineNodeCrt;
}
