package com.zjkj.aigc.common.req.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcModelHeartbeat implements Serializable {
    private static final long serialVersionUID = -4612526318254769660L;

    /**
     * 注册id
     */
    @NotNull(message = "{model.heartbeat.register.id.required}")
    private Long id;
    /**
     * 状态：0-空闲，1-处理任务中
     */
    @NotNull(message = "{validation.status.required}")
    private Integer status;
}
