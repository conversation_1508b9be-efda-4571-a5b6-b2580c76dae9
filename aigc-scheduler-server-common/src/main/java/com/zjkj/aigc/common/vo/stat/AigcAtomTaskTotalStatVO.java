package com.zjkj.aigc.common.vo.stat;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@Data
public class AigcAtomTaskTotalStatVO implements Serializable {

    private static final long serialVersionUID = 2058635322139674676L;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 待处理数量
     */
    private Long waitingAmount;
    /**
     * 任务处理速率
     */
    private BigDecimal taskSpeed;
    /**
     * 预计完成时间
     */
    private LocalDateTime expectFinishedTime;


    public static AigcAtomTaskTotalStatVO initZero() {
        AigcAtomTaskTotalStatVO vo = new AigcAtomTaskTotalStatVO();
        vo.setWaitingAmount(0L);
        vo.setTaskSpeed(BigDecimal.ZERO);
        return vo;
    }
}
