package com.zjkj.aigc.common.vo.cluster;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Data
@Accessors(chain = true)
public class AigcClusterModelUsageVO implements Serializable {
    private static final long serialVersionUID = -6326958812083986626L;
    /**
     * 模型类型使用列表
     */
    List<AigcModelTypeUsageVO> modelTypeUsages;
    /**
     * 模型资源使用列表
     */
    List<AigcModelUsageVO> modelUsages;

    public static AigcClusterModelUsageVO buildDefault() {
        return new AigcClusterModelUsageVO()
                .setModelUsages(List.of())
                .setModelTypeUsages(List.of());
    }
}
