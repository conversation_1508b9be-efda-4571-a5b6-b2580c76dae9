package com.zjkj.aigc.common.msg;

import com.zjkj.aigc.common.msg.dto.EmailMsgDTO;
import com.zjkj.aigc.common.msg.dto.MsgResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Properties;
import java.util.UUID;
import javax.mail.*;
import javax.mail.internet.*;

/**
 * 邮件推送
 *
 * <AUTHOR>
 * @since 2024/12/20
 */
@Slf4j
@Service
public class EmailService {

    @Value("${mail.smtp.host:smtpdm.aliyun.com}")
    private String host;

    @Value("${mail.smtp.port:80}")
    private String port;

    @Value("${mail.user:<EMAIL>}")
    private String user;

    @Value("${mail.password:musetech}")
    private String password;

    /**
     * 发送邮件
     *
     * @param msgDTO 消息载体
     * @return false-失败，true-成功
     */
    public MsgResponseDTO send(EmailMsgDTO emailMsgDTO) {
        // 配置发送邮件的环境属性
        final Properties props = new Properties();
        // 表示SMTP发送邮件，需要进行身份验证
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.host", host);
        //设置端口：
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.from", user);
        // 发件人的账号（在控制台创建的发信地址）
        props.put("mail.user", user);
        // 发信地址的smtp密码（在控制台选择发信地址进行设置）
        props.put("mail.password", password);
        //构建授权信息，用于进行SMTP进行身份验证
        Authenticator authenticator = new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 用户名、密码
                String userName = props.getProperty("mail.user");
                String password = props.getProperty("mail.password");
                return new PasswordAuthentication(userName, password);
            }
        };
        //使用环境属性和授权信息，创建邮件会话
        Session mailSession = Session.getInstance(props, authenticator);
        //创建邮件消息
        MimeMessage message = new MimeMessage(mailSession) {
            @Override
            protected void updateMessageID() throws MessagingException {
                //设置自定义Message-ID值
                setHeader("Message-ID", genMessageID(props.getProperty("mail.user")));
            }
        };
        MsgResponseDTO response = new MsgResponseDTO();
        try {
            // 设置发件人邮件地址和名称。填写控制台配置的发信地址。和上面的mail.user保持一致。名称用户可以自定义填写。
            InternetAddress from = new InternetAddress(props.getProperty("mail.user"), "调度平台");
            message.setFrom(from);
            // 设置收件人邮件地址
            InternetAddress to = new InternetAddress(emailMsgDTO.getEmail());
            message.setRecipient(MimeMessage.RecipientType.TO, to);
            //设置时间
            message.setSentDate(new Date());
            //设置邮件标题
            message.setSubject(emailMsgDTO.getTitle());
            //发送附件和内容：
            BodyPart messageBodyPart = new MimeBodyPart();
            // 纯文本："text/plain;charset=UTF-8" 设置邮件的内容
            messageBodyPart.setContent(emailMsgDTO.getContent(), "text/html;charset=UTF-8");
            //创建多重消息
            Multipart multipart = new MimeMultipart();
            //设置文本消息部分
            multipart.addBodyPart(messageBodyPart);
            //发送含有附件的完整消息
            message.setContent(multipart);
            // 发送邮件
            Transport.send(message);
            response.setIsSuccess(Boolean.TRUE);
        } catch (Exception e) {
            log.error("sendEmailError {}:", e);
            response.setIsSuccess(Boolean.FALSE);
            response.setErrorMsg(e.toString());
        }
        return response;
    }

    /**
     * 生成消息ID
     *
     * @param mailFrom
     * @return 消息ID
     */
    private String genMessageID(String mailFrom) {
        // message-id 必须符合 first-part@last-part
        String[] mailInfo = mailFrom.split("@");
        String domain = mailFrom;
        int index = mailInfo.length - 1;
        if (index >= 0) {
            domain = mailInfo[index];
        }
        UUID uuid = UUID.randomUUID();
        StringBuffer messageId = new StringBuffer();
        messageId.append('<').append(uuid.toString()).append('@').append(domain).append('>');
        return messageId.toString();
    }

}
