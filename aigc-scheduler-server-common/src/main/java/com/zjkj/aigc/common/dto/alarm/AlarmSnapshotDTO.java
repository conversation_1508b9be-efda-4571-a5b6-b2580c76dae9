package com.zjkj.aigc.common.dto.alarm;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author:zhongyuji
 * @Description:
 * @DATE: 2024/12/18
 */
@Data
public class AlarmSnapshotDTO implements Serializable {
    private static final long serialVersionUID = -8213320782300247196L;
    /**
     * 配置ID
     */
    private Long configId;
    /**
     * 积压数量阈值
     */
    private Integer overstackNumThreshold;
    /**
     * 成功率阈值
     */
    private Integer successRateThreshold;
    /**
     * 积压数量
     */
    private Long overstackNum;
    /**
     * 成功率
     */
    private Integer successRate;
    /**
     * 总数量
     */
    private Long taskTotalAmount;
    /**
     * 失败数量
     */
    private Long taskFailAmount;
}
