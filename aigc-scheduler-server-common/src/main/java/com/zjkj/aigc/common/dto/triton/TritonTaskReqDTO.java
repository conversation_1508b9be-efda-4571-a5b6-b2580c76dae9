package com.zjkj.aigc.common.dto.triton;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
@Accessors(chain = true)
public class TritonTaskReqDTO<T> implements Serializable {
    private static final long serialVersionUID = 3583627912587628952L;
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    /**
     * app-name
     */
    @JsonProperty("app_name")
    private String appName;
    /**
     * 类型
     */
    private String type;
    /**
     * 模型参数
     */
    private T params;
}
