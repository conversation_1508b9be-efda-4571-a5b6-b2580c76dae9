package com.zjkj.aigc.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/2/8
 */
public class MathUtil {
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);
    private static final int DIVISION_SCALE = 4;
    private static final int RESULT_SCALE = 2;

    private MathUtil() {
    }

    /**
     * 占比
     *
     * @param denominator 分母
     * @param numerator   分子
     * @return 使用率
     */
    public static BigDecimal percentage(long denominator, long numerator) {
        if (denominator == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), DIVISION_SCALE, RoundingMode.HALF_UP)
                .multiply(HUNDRED)
                .setScale(RESULT_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 占比
     *
     * @param denominator 分母
     * @param numerator   分子
     * @param scale       保留小数位数
     * @return 使用率
     */
    public static BigDecimal percentage(long denominator, long numerator, int scale) {
        if (denominator == 0) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), scale + 2, RoundingMode.HALF_UP)
                .multiply(HUNDRED)
                .setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * 是否有小于等于的值
     *
     * @param compareValue 比较的值
     * @param sourceValue  原值
     * @return 是否有小于等于的值
     */
    public static boolean anyLessOrEqual(long compareValue, Long... sourceValue) {
        if (Objects.isNull(sourceValue)) {
            return true;
        }
        for (Long value : sourceValue) {
            if (Objects.isNull(value) || value <= compareValue) {
                return true;
            }
        }

        return false;
    }
    /**
     * 是否有小于等于的值
     *
     * @param compareValue 比较的值
     * @param sourceValue  原值
     * @return 是否有小于等于的值
     */
    public static boolean anyLessOrEqual(int compareValue, Integer... sourceValue) {
        if (Objects.isNull(sourceValue)) {
            return true;
        }
        for (Integer value : sourceValue) {
            if (Objects.isNull(value) || value <= compareValue) {
                return true;
            }
        }

        return false;
    }
}
