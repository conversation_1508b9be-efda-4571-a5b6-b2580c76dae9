package com.zjkj.aigc.common.vo.approval;

import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GpuApplyVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 6273738155944964236L;
    /**
     * id
     */
    private Long id;
    /**
     * 审批编号
     */
    private String code;
    /**
     * 审批类型
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalTypeEnum}
     */
    private String type;
    /**
     * 集群/业务
     */
    private String clusterBusiness;
    /**
     * 云平台
     */
    private String platform;
    /**
     * GPU型号
     */
    private String gpuModel;
    /**
     * GPU卡数
     */
    private Integer gpuNum;
    /**
     * 单卡显存
     */
    @NotNull
    @Min(1)
    private Integer singleCardMemory;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum}
     */
    private Integer status;
    /**
     * 操作说明
     */
    private String supplyRemark;
    /**
     * 补充说明
     */
    private String remark;
    /**
     * 删除版本号
     */
    private String delVersion;
}
