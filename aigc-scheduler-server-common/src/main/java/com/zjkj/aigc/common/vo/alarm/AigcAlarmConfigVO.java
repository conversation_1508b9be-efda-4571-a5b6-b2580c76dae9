package com.zjkj.aigc.common.vo.alarm;

import com.alibaba.fastjson2.JSONObject;
import com.zjkj.aigc.common.dto.alarm.AlarmConfigThresholdDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmReceiverDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmConfigVO implements Serializable {
    private static final long serialVersionUID = 8869312468901373074L;
    /**
     * 数据ID
     */
    private Long id;
    /**
     * 标签
     */
    private String tag;
    /**
     * 关联模型ID集合
     */
    private List<String> modelId;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 告警阈值配置
     */
    private AlarmConfigThresholdDTO alarmConfigThreshold;
    /**
     * 告警时间间隔
     */
    private Integer alarmTimeInterval;
    /**
     * 告警时间间隔单位
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTimeIntervalUnitEnum}
     */
    private Integer alarmTimeIntervalUnit;
    /**
     * 接收人配置
     */
    private AlarmReceiverDTO alarmReceiver;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum}
     */
    private Integer status;
}
