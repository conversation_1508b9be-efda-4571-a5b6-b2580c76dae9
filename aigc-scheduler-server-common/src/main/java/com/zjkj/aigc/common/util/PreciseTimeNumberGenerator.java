package com.zjkj.aigc.common.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
public class PreciseTimeNumberGenerator {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    private static final Random RANDOM = new Random();

    /**
     * 生成精确到毫秒的编号
     * @return 编号
     */
    public static String generateNumber() {
        LocalDateTime now = LocalDateTime.now();
        String formattedDate = now.format(DATE_FORMATTER);
        int nano = now.getNano();
        // 生成 100-999 的随机数
        int randomSequence = RANDOM.nextInt(900) + 100;

        return String.format("%s%03d%03d", formattedDate, nano / 1_000_000, randomSequence);
    }
}
