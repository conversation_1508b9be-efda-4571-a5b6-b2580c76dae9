package com.zjkj.aigc.common.dto.file;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 * @since 2025/7/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 文件路径（OSS中的路径）
     */
    private String filePath;
    
    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * OSS文件名
     */
    private String ossFileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型/MIME类型
     */
    private String contentType;
    
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    
    /**
     * 文件访问URL（如果是公开文件）
     */
    private String accessUrl;
    
    /**
     * 构造方法 - 基本信息
     * 
     * @param filePath 文件路径
     * @param originalFileName 原始文件名
     */
    public FileUploadDTO(String filePath, String originalFileName) {
        this.filePath = filePath;
        this.originalFileName = originalFileName;
        this.uploadTime = LocalDateTime.now();
    }
    
    /**
     * 构造方法 - 包含文件大小
     * 
     * @param filePath 文件路径
     * @param originalFileName 原始文件名
     * @param fileSize 文件大小
     */
    public FileUploadDTO(String filePath, String originalFileName, Long fileSize) {
        this.filePath = filePath;
        this.originalFileName = originalFileName;
        this.fileSize = fileSize;
        this.uploadTime = LocalDateTime.now();
    }
    
    /**
     * 构造方法 - 完整信息
     * 
     * @param filePath 文件路径
     * @param originalFileName 原始文件名
     * @param fileSize 文件大小
     * @param contentType 内容类型
     */
    public FileUploadDTO(String filePath, String originalFileName, Long fileSize, String contentType) {
        this.filePath = filePath;
        this.originalFileName = originalFileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.uploadTime = LocalDateTime.now();
    }
}
