package com.zjkj.aigc.common.enums.task;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/25
 */
@Getter
@RequiredArgsConstructor
public enum TaskBatchEnum {

    /**
     * 单个
     */
    SINGLE(0, "单个"),

    /**
     * 批量
     */
    BATCH(1, "批量"),

    ;

    private final Integer code;
    private final String desc;

    public Integer code() {
        return code;
    }

    /**
     * 对立code
     * @param currentCode 当前code
     * @return 切换后的code
     */
    public static Integer opposite(Integer currentCode) {
        return Objects.equals(BATCH.code(), currentCode) ? SINGLE.code() : BATCH.code();
    }
}
