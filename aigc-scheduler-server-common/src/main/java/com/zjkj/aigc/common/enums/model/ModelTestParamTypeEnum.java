package com.zjkj.aigc.common.enums.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
@Getter
@AllArgsConstructor
public enum ModelTestParamTypeEnum {

    SINGLE("single", "单个"),
    BATCH("batch", "批量");

    private final String code;
    private final String desc;

    public static ModelTestParamTypeEnum getByCode(String code) {
        for (ModelTestParamTypeEnum value : ModelTestParamTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
