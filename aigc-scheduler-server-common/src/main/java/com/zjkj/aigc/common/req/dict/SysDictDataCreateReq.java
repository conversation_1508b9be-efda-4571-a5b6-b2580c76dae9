package com.zjkj.aigc.common.req.dict;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 字典数据创建请求
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@Data
public class SysDictDataCreateReq {
    /**
     * 主键
     */
    @NotNull(message = "{validation.id.required}", groups = ValidationGroup.Update.class)
    private Long id;

    /**
     * 字典类型id
     */
    @NotNull(message = "{dict.data.dict.id.required}")
    private Long dictId;

    /**
     * 字典标签
     */
    @NotBlank(message = "{dict.data.label.required}")
    @Length(max = 100, message = "{dict.data.label.length.exceeded}")
    private String label;

    /**
     * 字典键值
     */
    @NotBlank(message = "{dict.data.value.required}")
    @Length(max = 100, message = "{dict.data.value.length.exceeded}")
    private String value;

    /**
     * 字典排序
     */
    private Integer dictSort;
    /**
     * 样式属性
     */
    @Length(max = 100, message = "{dict.data.css.class.length.exceeded}")
    private String cssClass;
    /**
     * 颜色属性
     */
    @Length(max = 100, message = "{dict.data.color.class.length.exceeded}")
    private String colorClass;
    /**
     * 备注
     */
    @Length(max = 250, message = "{dict.data.remark.length.exceeded}")
    private String remark;
}
