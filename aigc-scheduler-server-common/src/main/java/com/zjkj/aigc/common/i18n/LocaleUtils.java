package com.zjkj.aigc.common.i18n;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.util.StringUtils;

import java.util.Locale;

/**
 * 语言环境工具类
 * 
 * <AUTHOR>
 * @since 2025/1/15
 */
public class LocaleUtils {

    /**
     * 默认语言环境（中文，对应messages.properties）
     */
    public static final Locale DEFAULT_LOCALE = Locale.SIMPLIFIED_CHINESE;

    /**
     * 支持的语言环境
     */
    public static final Locale[] SUPPORTED_LOCALES = {
            Locale.SIMPLIFIED_CHINESE,  // zh_CN
            Locale.US
    };

    /**
     * 获取当前语言环境
     *
     * @return 当前语言环境
     */
    public static Locale getCurrentLocale() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale != null ? locale : DEFAULT_LOCALE;
    }

    /**
     * 设置当前语言环境
     *
     * @param locale 语言环境
     */
    public static void setCurrentLocale(Locale locale) {
        LocaleContextHolder.setLocale(locale);
    }

    /**
     * 根据语言标识解析语言环境
     *
     * @param lang 语言标识 (如: zh_CN, en_US, ja_JP)
     * @return 语言环境
     */
    public static Locale parseLocale(String lang) {
        if (!StringUtils.hasText(lang)) {
            return DEFAULT_LOCALE;
        }

        try {
            // 将 en-US 格式转换为标准的 Locale
            // Locale.forLanguageTag() 可以正确处理 en-US 格式
            return Locale.forLanguageTag(lang.replace("_", "-"));
        } catch (Exception e) {
            return DEFAULT_LOCALE;
        }
    }

    /**
     * 检查是否支持指定的语言环境
     *
     * @param locale 语言环境
     * @return 是否支持
     */
    public static boolean isSupported(Locale locale) {
        if (locale == null) {
            return false;
        }

        for (Locale supportedLocale : SUPPORTED_LOCALES) {
            if (supportedLocale.equals(locale)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取语言环境的字符串表示
     *
     * @param locale 语言环境
     * @return 字符串表示
     */
    public static String getLocaleString(Locale locale) {
        if (locale == null) {
            return DEFAULT_LOCALE.toString();
        }
        return locale.toString();
    }
}
