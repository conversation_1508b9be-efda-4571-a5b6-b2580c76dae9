package com.zjkj.aigc.common.vo.cluster;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 集群资源利用视图对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcClusterUsageVO extends BaseVO {

    private static final long serialVersionUID = 1L;
    /**
     * 平台
     */
    private String platform;
    /**
     * 服务类型
     */
    private List<String> serviceType;
    /**
     * 集群主键id
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;
    /**
     * 数据日期
     */
    private LocalDate dataDate;
    /**
     * 节点数量
     */
    private Long nodeCount;
    /**
     * gpu型号
     */
    private String gpuModel;
    /**
     * gpu卡数
     */
    private Long gpuCount;
    /**
     * 总gpu显存大小
     */
    private Long totalGpuMemorySize;
    /**
     * 已使用gpu显存大小
     */
    private Long usedGpuMemorySize;
    /**
     * gpu使用率
     */
    private BigDecimal gpuUsageRate;
    /**
     * 总cpu核数
     */
    private Long totalCpuCore;
    /**
     * 已使用cpu核数
     */
    private Long usedCpuCore;
    /**
     * cpu使用率
     */
    private BigDecimal cpuUsageRate;
    /**
     * 总内存大小
     */
    private Long totalMemorySize;
    /**
     * 已使用内存大小
     */
    private Long usedMemorySize;
    /**
     * 内存使用率
     */
    private BigDecimal memoryUsageRate;
    /**
     * 备注
     */
    private String remark;
}
