package com.zjkj.aigc.common.vo.cluster;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelUsageVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -1734302233824479244L;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型类型
     */
    private String modelType;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 数据日期
     */
    private LocalDate dataDate;
    /**
     * pod数量
     */
    private Long podCount;
    /**
     * gpu显存大小GB
     */
    private Long gpuMemorySize;
    /**
     * 集群gpu显存占比
     */
    private BigDecimal gpuMemorySizeRatio;
    /**
     * cpu核数
     */
    private Long cpuCore;
    /**
     * 集群cpu核数占比
     */
    private BigDecimal cpuCoreRatio;
    /**
     * 总内存大小GB
     */
    private Long memorySize;
    /**
     * 集群内存占比
     */
    private BigDecimal memorySizeRatio;
}
