package com.zjkj.aigc.common.req.resource;

import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/03/04
 */
@Data
@Builder
public class ResourceBillTypeQuery implements Serializable {
    private static final long serialVersionUID = -132210733558643219L;
    /**
     * 0-预算费用，1-实际费用
     */
    private Integer type;
    /**
     * 月份
     */
    @NotNull(message = "{validation.month.required}")
    private Integer month;
    /**
     * 冗余比例集合
     */
    private List<Redundancy> redundancyList;

    @Data
    public static class Redundancy {
        /**
         * 平台
         */
        @NotEmpty(message = "{validation.platform.required}")
        private String platform;
        /**
         * 中间件费用冗余比例
         */
        @NotNull(message = "{resource.bill.redundancy.mid.price.required}")
        private BigDecimal midPriceRedundancy;
        /**
         * 中间件调整费用
         */
        private BigDecimal midAdjPrice;
        /**
         * GPU费用冗余比例
         */
        @NotNull(message = "{resource.bill.redundancy.gpu.price.required}")
        private BigDecimal gpuPriceRedundancy;
        /**
         * GPU调整费用
         */
        private BigDecimal gpuAdjPrice;
    }

    public BigDecimal midPriceRedundancy(String platform, BigDecimal midTotal) {
        if (!CollectionUtils.isEmpty(redundancyList)) {
            for (Redundancy redundancy : redundancyList) {
                if (platform.equals(redundancy.getPlatform())) {
                    midTotal = midTotal.multiply(redundancy.getMidPriceRedundancy()).setScale(2, 2);
                    if (Objects.nonNull(redundancy.getMidAdjPrice())) {
                        midTotal = midTotal.add(redundancy.getMidAdjPrice());
                    }
                    break;
                }
            }
        }
        return midTotal;
    }


    public BigDecimal gpuPriceRedundancy(String platform, BigDecimal gpuTotal) {
        if (!CollectionUtils.isEmpty(redundancyList)) {
            for (Redundancy redundancy : redundancyList) {
                if (platform.equals(redundancy.getPlatform())) {
                    gpuTotal = gpuTotal.multiply(redundancy.getGpuPriceRedundancy()).setScale(2, 2);
                    if (Objects.nonNull(redundancy.getGpuAdjPrice())) {
                        gpuTotal = gpuTotal.add(redundancy.getGpuAdjPrice());
                    }
                    break;
                }
            }
        }
        return gpuTotal;
    }

    public Integer getType() {
        return type == null ? 0 : type;
    }


}
