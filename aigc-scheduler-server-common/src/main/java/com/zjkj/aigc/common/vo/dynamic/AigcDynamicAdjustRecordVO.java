package com.zjkj.aigc.common.vo.dynamic;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcDynamicAdjustRecordVO extends BaseVO {
    private static final long serialVersionUID = -6877825271903329356L;
    /**
     * 动态配置id
     */
    private Long dynamicConfigId;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String scaleType;
    /**
     * 伸缩状态: PENDING-待伸缩,SCALING-伸缩中,SUCCESS-伸缩成功,FAILED-伸缩失败,CANCEL-取消,ROLLBACK-伸缩回退
     */
    private String scaleStatus;
    /**
     * 状态：1-生效中 2-结束 3-取消
     */
    private Integer status;
    /**
     * 失败信息
     */
    private String message;
    /**
     * 动态配置快照
     */
    private String dynamicConfigSnapshot;
    /**
     * 关联的记录id
     */
    private Long relatedId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 动态调整记录明细
     */
    private List<aigcDynamicAdjustRecordDetailVO> recordDetails;
}
