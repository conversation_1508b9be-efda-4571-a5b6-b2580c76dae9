package com.zjkj.aigc.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
@Data
public class BaseVO implements Serializable {
    private static final long serialVersionUID = 5295071824560551446L;
    private Long id;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime revisedTime;
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
}
