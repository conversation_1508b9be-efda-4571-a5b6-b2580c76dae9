package com.zjkj.aigc.common.req.dict;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据查询请求
 *
 * <AUTHOR>
 * @since 2024/12/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysDictDataQuery extends PageBaseReq {

    private static final long serialVersionUID = 5436175424738357002L;
    /**
     * 字典类型id
     */
    private Long dictId;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;
}
