package com.zjkj.aigc.common.enums.dynamic;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/4/10
 */
@Getter
@AllArgsConstructor
public enum DynamicScaleTypeEnum {

    /**
     * 扩容
     */
    SCALE_OUT("SCALE_OUT"),
    /**
     * 缩容
     */
    SCALE_IN("SCALE_IN");

    private final String type;

    /**
     * 伸缩类型取反
     *
     * @param type 伸缩类型
     * @return 伸缩类型
     */
    public static String reverse(String type) {
        if (SCALE_OUT.getType().equals(type)) {
            return SCALE_IN.getType();
        }

        if (SCALE_IN.getType().equals(type)) {
            return SCALE_OUT.getType();
        }
        throw new IllegalArgumentException("不支持的类型: " + type);
    }
}
