package com.zjkj.aigc.common.req.approval;

import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
public class GpuApplyCreateReq implements Serializable {

    private static final long serialVersionUID = 7207306596141029069L;

    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 审批类型
     */
    @NotBlank(message = "{gpu.apply.type.required}")
    @Length(max = 20, message = "{gpu.apply.type.length.exceeded}")
    private String type;
    /**
     * 集群/业务
     */
    @NotBlank(message = "{gpu.apply.cluster.business.required}")
    @Length(max = 20, message = "{gpu.apply.cluster.business.length.exceeded}")
    private String clusterBusiness;
    /**
     * 云平台
     */
    @NotBlank(message = "{validation.cloud.platform.required}")
    @Length(max = 20, message = "{gpu.apply.platform.length.exceeded}")
    private String platform;
    /**
     * GPU型号
     */
    @NotBlank(message = "{gpu.apply.gpu.model.required}")
    @Length(max = 20, message = "{gpu.apply.gpu.model.length.exceeded}")
    private String gpuModel;
    /**
     * GPU卡数
     */
    @NotNull(message = "{gpu.apply.gpu.num.required}")
    private Integer gpuNum;
    /**
     * 单卡显存
     */
    @NotNull
    @Min(1)
    private Integer singleCardMemory;
    /**
     * 补充说明
     */
    @Length(max = 255, message = "{gpu.apply.remark.length.exceeded}")
    private String remark;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum}
     */
    private Integer status;
    /**
     * 操作说明
     */
    @Length(max = 255, message = "{gpu.apply.supply.remark.length.exceeded}")
    private String supplyRemark;
}
