package com.zjkj.aigc.common.vo.resource;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/9
 */
@Data
@Accessors(chain = true)
public class ResourceBillPlatform implements Serializable {
    private static final long serialVersionUID = -3459898284178806806L;

    /**
     * 云平台
     */
    private String platform;
    /**
     * 标题
     */
    private List<String> headers;
    /**
     * 文件扩展名
     */
    private List<String> extensions;
}
