package com.zjkj.aigc.common.dto.node;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
@Accessors(chain = true)
public class AigcRegisterBackDTO implements Serializable {
    private static final long serialVersionUID = -5892934860096674278L;

    private Long id;
    /**
     * 节点IP地址
     */
    private String nodeIp;

    /**
     * 时间戳13位
     */
    private Long ts;
}
