package com.zjkj.aigc.common.req.model.test;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DevModelTestQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = 6697480983519542670L;

    /**
     * 测试编码
     */
    private String testCode;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * style
     */
    private String styleModel;

    /**
     *
     */
    private Integer isAcceptable;

}
