package com.zjkj.aigc.common.vo.cluster;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@Data
@Accessors(chain = true)
public class AigcClusterUsageStatVO implements Serializable {
    private static final long serialVersionUID = -3861436258937945228L;
    /**
     * 总gpu显存大小
     */
    private Long totalGpuMemorySize;
    /**
     * 已使用gpu显存大小
     */
    private Long usedGpuMemorySize;
    /**
     * gpu使用率
     */
    private BigDecimal gpuUsageRate;
    /**
     * 模型占比
     */
    private List<ModelProportion> modelProportions;
    /**
     * gpu使用率
     */
    private List<GpuUsageRate> gpuUsageRates;

    @Data
    @Accessors(chain = true)
    public static class GpuUsageRate {
        /**
         * 数据日期
         */
        private LocalDate dataDate;
        /**
         * 总gpu显存大小
         */
        private Long totalGpuMemorySize;
        /**
         * 已使用gpu显存大小
         */
        private Long usedGpuMemorySize;
        /**
         * gpu使用率
         */
        private BigDecimal gpuUsageRate;
        /**
         * 总cpu核数
         */
        private Long totalCpuCore;
        /**
         * 已使用cpu核数
         */
        private Long usedCpuCore;
        /**
         * cpu使用率
         */
        private BigDecimal cpuUsageRate;
        /**
         * 总内存大小
         */
        private Long totalMemorySize;
        /**
         * 已使用内存大小
         */
        private Long usedMemorySize;
        /**
         * 内存使用率
         */
        private BigDecimal memoryUsageRate;

        /**
         * 构建默认值
         * @param dataDate 数据日期
         * @return 默认值
         */
        public static GpuUsageRate buildDefault(LocalDate dataDate){
            return new GpuUsageRate()
                    .setDataDate(dataDate)
                    .setTotalGpuMemorySize(0L)
                    .setUsedGpuMemorySize(0L)
                    .setGpuUsageRate(BigDecimal.ZERO)
                    .setTotalCpuCore(0L)
                    .setUsedCpuCore(0L)
                    .setCpuUsageRate(BigDecimal.ZERO)
                    .setTotalMemorySize(0L)
                    .setUsedMemorySize(0L)
                    .setMemoryUsageRate(BigDecimal.ZERO);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class ModelProportion {
        /**
         * 类型
         */
        private String type;
        /**
         * gpu数量
         */
        private Long gpuCount;
        /**
         * gpu显存大小
         */
        private Long gpuMemorySize;
        /**
         * gpu占比
         */
        private BigDecimal gpuProportionRate;
        /**
         * gpu使用率
         */
        private BigDecimal gpuUsageRate;
        /**
         * 模型数量
         */
        private Long modelCount;
        /**
         * 总任务数
         */
        private Long totalTaskCount;
    }

    /**
     * 构建默认值
     *
     * @return 默认值
     */
    public static AigcClusterUsageStatVO buildDefault(){
        return new AigcClusterUsageStatVO()
                .setTotalGpuMemorySize(0L)
                .setUsedGpuMemorySize(0L)
                .setGpuUsageRate(BigDecimal.ZERO)
                .setModelProportions(List.of())
                .setGpuUsageRates(List.of());
    }
}
