package com.zjkj.aigc.common.vo.cluster;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Data
@Accessors(chain = true)
public class AigcModelTypeUsageVO implements Serializable {
    private static final long serialVersionUID = 7892617245288723580L;
    /**
     * 模型类型
     */
    private String type;
    /**
     * 模型数量
     */
    private Long modelCount;
    /**
     * gpu显存大小GB
     */
    private Long gpuMemorySize;
    /**
     * 占比总gpu显存
     */
    private BigDecimal gpuMemorySizeRatio;
    /**
     * 总任务数
     */
    private Long totalTaskCount;

    public static AigcModelTypeUsageVO buildDefault(String type) {
        return new AigcModelTypeUsageVO()
                .setType(type)
                .setModelCount(0L)
                .setGpuMemorySize(0L)
                .setGpuMemorySizeRatio(BigDecimal.ZERO)
                .setTotalTaskCount(0L);
    }
}
