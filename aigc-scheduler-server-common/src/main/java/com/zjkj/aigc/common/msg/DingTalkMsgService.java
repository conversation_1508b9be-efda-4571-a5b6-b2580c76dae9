package com.zjkj.aigc.common.msg;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zjkj.aigc.common.enums.msg.DingTalkMsgTypeEnum;
import com.zjkj.aigc.common.msg.dto.DingTalkMsgDTO;
import com.zjkj.aigc.common.msg.dto.MsgResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

/**
 * 钉钉消息推送
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
public class DingTalkMsgService {

    /**
     * 发送群消息
     *
     * @param msgDTO 消息载体
     * @return 发送结果
     */
    public MsgResponseDTO sendWebHook(DingTalkMsgDTO msgDTO) {
        MsgResponseDTO response = new MsgResponseDTO();
        String webHookUrl = msgDTO.getWebHookUrl();
        //加签
        if (StrUtil.isNotEmpty(msgDTO.getSecret())) {
            webHookUrl = signWebHook(webHookUrl, msgDTO.getSecret());
        }
        JSONObject at = new JSONObject();
        at.put("atMobiles",msgDTO.getAtMobiles());
        at.put("isAtAll",msgDTO.getIsAtAll());
        JSONObject body = new JSONObject();
        body.put("msgtype", msgDTO.getType());
        body.put("at", at);
        DingTalkMsgTypeEnum type = DingTalkMsgTypeEnum.of(msgDTO.getType());
        switch (type) {
            case TEXT:
                body.put("content", msgDTO.getText());
                break;
            case LINK:
                body.put("link", msgDTO.getLink());
                break;
            case MARKDOWN:
                body.put("markdown", msgDTO.getMarddown());
                break;
            case ACTIONCARD:
               body.put("actionCard", msgDTO.getActionCard());
                break;
            default:
                response.setIsSuccess(Boolean.FALSE);
                response.setErrorMsg("Error msg type");
                return response;
        }
        HttpResponse rsp = HttpUtil.createPost(webHookUrl).body(body.toJSONString()).execute();
        response.setIsSuccess(rsp.isOk());
        if (!rsp.isOk()) {
            response.setErrorMsg(rsp.body());
        }
        return response;
    }

    /**
     * webhook加签
     *
     * @param webHookUrl 地址
     * @param secret     密钥
     * @return
     */
    private String signWebHook(String webHookUrl, String secret) {
        try {
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            return webHookUrl + String.format("&sign=%s&timestamp=%s", sign, timestamp);
        } catch (Exception e) {
            throw new RuntimeException(e.toString());
        }
    }
}
