package com.zjkj.aigc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Getter
@AllArgsConstructor
public enum TaskStatCompareTypeEnum {
    DAY("day"),
    WEEK("week"),
    MONTH("month");

    private final String type;

    public static TaskStatCompareTypeEnum of(String type) {
        if (!StringUtils.hasText(type)) {
            return null;
        }

        for (TaskStatCompareTypeEnum value : values()) {
            if (Objects.equals(value.getType(), type)) {
                return value;
            }
        }

        return null;
    }
}
