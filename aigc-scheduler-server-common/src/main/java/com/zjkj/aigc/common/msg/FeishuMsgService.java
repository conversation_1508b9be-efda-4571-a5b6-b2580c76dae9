package com.zjkj.aigc.common.msg;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.zjkj.aigc.common.enums.msg.DingTalkMsgTypeEnum;
import com.zjkj.aigc.common.enums.msg.FeishuMsgTypeEnum;
import com.zjkj.aigc.common.msg.dto.FeishuMsgDTO;
import com.zjkj.aigc.common.msg.dto.MsgResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 飞书消息推送
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
public class FeishuMsgService {

    /**
     * 发送群消息
     *
     * @param msgDTO 消息载体
     * @return 发送结果
     */
    public MsgResponseDTO sendWebHook(FeishuMsgDTO msgDTO) {
        MsgResponseDTO response = new MsgResponseDTO();
        String webHookUrl = msgDTO.getWebHookUrl();
        JSONObject body = new JSONObject();
        //加签
        if (StrUtil.isNotEmpty(msgDTO.getSecret())) {
            int timestamp = (int) (System.currentTimeMillis() / 1000);
            String sign = genSign(msgDTO.getSecret(), timestamp);
            body.put("timestamp", timestamp);
            body.put("sign", sign);
        }
        body.put("msg_type", msgDTO.getType());
        FeishuMsgTypeEnum type = FeishuMsgTypeEnum.of(msgDTO.getType());
        switch (type) {
            case TEXT:
                body.put("content", msgDTO.getText());
                break;
            case POST:
                JSONObject postBody = new JSONObject();
                postBody.put("zh_cn", msgDTO.getPost());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("post", postBody);
                body.put("content", jsonObject);
                break;
            case SHARE_CHAT:
                body.put("content", msgDTO.getShareChat());
                break;
            case IMAGE:
                body.put("content", msgDTO.getImage());
                break;
            case INTERACTIVE:
                body.put("card", msgDTO.getCard());
                break;
            default:
                response.setIsSuccess(Boolean.FALSE);
                response.setErrorMsg("Msg type error");
                return response;
        }
        HttpResponse rsp = HttpUtil.createPost(webHookUrl).body(body.toJSONString()).execute();
        response.setIsSuccess(rsp.isOk());
        if (!rsp.isOk()) {
            response.setErrorMsg(rsp.body());
        }
        return response;
    }

    /**
     * webhook加签
     *
     * @param webHookUrl 地址
     * @param secret     密钥
     * @return 签名
     */
    private String genSign(String secret, int timestamp) {
        try {
            //把timestamp+"\n"+密钥当做签名字符串
            String stringToSign = timestamp + "\n" + secret;
            //使用HmacSHA256算法计算签名
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(new byte[]{});
            return new String(Base64.encodeBase64(signData));
        } catch (Exception e) {
            throw new RuntimeException(e.toString());
        }
    }
}
