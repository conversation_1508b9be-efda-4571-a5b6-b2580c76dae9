package com.zjkj.aigc.common.dto.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
public class DingMessageDTO {

    private static final String ACTION_URL = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=false";
    /**
     * 获取PC端侧边栏链接
     * @param url 链接
     * @return PC端侧边栏链接
     */
    public static String getPcOutSlideUrl(String url) {
        return String.format(ACTION_URL, URLEncoder.encode(url, StandardCharsets.UTF_8));
    }

    /**
     * 构建请求
     *
     * @param message 消息
     * @return 请求
     */
    public static Map<String, Object> buildRequest(Object message) {
        Map<String, Object> reqMap = new HashMap<>();
        String className = message.getClass().getSimpleName();
        String msgType = Character.toLowerCase(className.charAt(0)) + className.substring(1);
        reqMap.put("msgtype", msgType);
        reqMap.put(msgType, message);
        return reqMap;
    }


    @Data
    @Builder
    public static class ActionCard {
        /**
         * 首屏会话透出的展示内容
         */
        private String title;
        /**
         * markdown格式的消息
         */
        private String text;
        /**
         * 0：按钮竖直排列
         * 1：按钮横向排列
         */
        private String btnOrientation;
        /**
         * 单个按钮的标题
         */
        private String singleTitle;
        /**
         * 点击singleTitle按钮触发的URL
         */
        @JsonProperty("singleURL")
        private String singleUrl;

    }
}
