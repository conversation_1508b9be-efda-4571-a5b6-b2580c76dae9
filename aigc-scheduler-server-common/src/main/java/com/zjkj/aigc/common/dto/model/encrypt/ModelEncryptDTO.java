package com.zjkj.aigc.common.dto.model.encrypt;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/14
 */
@Accessors(chain = true)
@Data
public class ModelEncryptDTO implements Serializable {
    private static final long serialVersionUID = 9059056776522466099L;

    /**
     * 部署发布id
     */
    @JsonProperty(value = "deploy_id")
    private Long deployId;

    /**
     * 路径
     */
    @JsonProperty(value = "model_ver_path")
    private String patch;

    /**
     * 通知地址
     */
    @JsonProperty(value = "notify_url")
    private String notifyUrl;
}
