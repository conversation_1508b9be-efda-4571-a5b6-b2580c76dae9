package com.zjkj.aigc.common.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ModelTestReportVO extends ModelTestReportInfo implements Serializable {
    private static final long serialVersionUID = 4486665942355782966L;

    /**
     * 测试编码
     */
    private String testNo;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * AI模型名称
     */
    private String modelName;
    /**
     * 模型服务名称
     */
    private String serviceName;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 状态，0：待生成；1：已生成
     */
    private Integer reportStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 生成时间
     */
    private LocalDateTime genTime;
}
