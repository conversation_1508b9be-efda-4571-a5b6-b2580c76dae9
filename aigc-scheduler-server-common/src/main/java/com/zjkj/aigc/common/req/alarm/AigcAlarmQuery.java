package com.zjkj.aigc.common.req.alarm;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Builder
public class AigcAlarmQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -2468560441373101365L;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型类型
     * {@link com.zjkj.aigc.common.enums.model.AigcModelTypeEnum}
     */
    private String modelType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
