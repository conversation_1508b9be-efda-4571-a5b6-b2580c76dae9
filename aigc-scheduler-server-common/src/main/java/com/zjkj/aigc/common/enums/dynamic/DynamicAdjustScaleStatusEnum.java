package com.zjkj.aigc.common.enums.dynamic;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/16
 */
@Getter
@AllArgsConstructor
public enum DynamicAdjustScaleStatusEnum {
    PENDING("PENDING", "待伸缩"),
    SCALING("SCALING", "伸缩中"),
    SUCCESS("SUCCESS", "伸缩成功"),
    FAILED("FAILED", "伸缩失败"),
    CANCEL("CANCEL", "取消"),
    ROLLBACK("ROLLBACK", "伸缩回退");

    public static final Set<String> TO_SCALE_STATUS_SET = Set.of(
            PENDING.getStatus(),
            SCALING.getStatus()
    );

    private final String status;
    private final String desc;
}
