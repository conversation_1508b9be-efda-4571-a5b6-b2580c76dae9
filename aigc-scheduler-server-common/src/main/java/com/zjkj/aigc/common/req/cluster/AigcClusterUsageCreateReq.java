package com.zjkj.aigc.common.req.cluster;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 集群资源利用创建请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class AigcClusterUsageCreateReq implements Serializable {

    private static final long serialVersionUID = -4806303042923710964L;
    /**
     * 数据id
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;

    /**
     * 集群id
     */
    @NotNull(message = "{cluster.usage.cluster.id.required}")
    private Long clusterId;

    /**
     * 数据日期
     */
    @NotNull(message = "{cluster.usage.date.required}")
    private LocalDate dataDate;

    /**
     * 总gpu显存大小
     */
    @NotNull(message = "{cluster.usage.total.gpu.memory.required}")
    private Long totalGpuMemorySize;
    /**
     * 已使用gpu显存大小
     */
    @NotNull(message = "{cluster.usage.used.gpu.memory.required}")
    private Long usedGpuMemorySize;
    /**
     * gpu使用率
     */
    @NotNull(message = "{cluster.usage.gpu.usage.rate.required}")
    @DecimalMin(value = "0.00", message = "{cluster.usage.gpu.usage.rate.min}")
    @DecimalMax(value = "100.00", message = "{cluster.usage.gpu.usage.rate.max}")
    private BigDecimal gpuUsageRate;

    /**
     * 备注
     */
    @Length(max = 200, message = "{cluster.usage.remark.length.exceeded}")
    private String remark;
}
