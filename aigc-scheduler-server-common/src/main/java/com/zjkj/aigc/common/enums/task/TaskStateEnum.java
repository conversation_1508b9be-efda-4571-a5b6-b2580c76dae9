package com.zjkj.aigc.common.enums.task;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;


/**
 * 任务状态
 *
 * @Description
 * @Company 广州致景科技有限公司
 * <AUTHOR>
 * @Date 2023/12/13 14:18
 * @Version 1.0.0
 */
@Getter
@RequiredArgsConstructor
public enum TaskStateEnum {

    /**
     * 待运行
     */
    WAITE(0, "待运行"),

    /**
     * 运行中
     */
    RUNNING(1, "运行中"),

    /**
     * 已完成
     */
    SUCC(2, "已完成"),

    /**
     * 失败
     */
    FAILED(3, "失败"),

    /**
     * 取消
     */
    CANCEL(4, "取消"),

    /**
     * 超时失败
     */
    TIMEOUT_FAILED(5, "超时失败"),

    ;

    private final Integer code;
    private final String desc;

    public static final Map<Integer, String> CODE_MAP =  new HashMap<>();

    static {
        for (TaskStateEnum value : TaskStateEnum.values()) {
            CODE_MAP.put(value.getCode(), value.getDesc());
        }
    }

    public static TaskStateEnum of(Integer code) {
        for (TaskStateEnum value : TaskStateEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isFailed(Integer code) {
        return TaskStateEnum.FAILED.getCode().equals(code);
    }

    public static boolean isTimeoutFailed(Integer code) {
        return TaskStateEnum.TIMEOUT_FAILED.getCode().equals(code);
    }

    public static boolean isSucc(Integer code) {
        return TaskStateEnum.SUCC.getCode().equals(code);
    }

    public static boolean isComplete(Integer code) {
        return isSucc(code) || isFailed(code);
    }

    public static boolean isComplete(TaskStateEnum e) {
        return e != null && isComplete(e.getCode());
    }

    public static boolean isStatFailed(Integer code) {
        return isFailed(code) || isTimeoutFailed(code);
    }

    public static boolean isWait(Integer code) {
        return TaskStateEnum.WAITE.getCode().equals(code);
    }

    public static boolean isProcess(Integer code) {
        return TaskStateEnum.RUNNING.getCode().equals(code);
    }

    public static boolean isCancel(Integer code) {
        return TaskStateEnum.CANCEL.getCode().equals(code);
    }
}
