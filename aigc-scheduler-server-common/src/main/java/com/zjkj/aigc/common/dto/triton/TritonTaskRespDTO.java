package com.zjkj.aigc.common.dto.triton;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/3/25
 */
@Data
@Accessors(chain = true)
public class TritonTaskRespDTO<T> implements Serializable {
    private static final long serialVersionUID = 6663160075814396760L;

    /**
     *  状态码: 0-成功
     */
    private Integer code;

    private String message;
    /**
     * 进度: 0-100
     */
    private Integer process;
    /**
     * 结果
     */
    private T result;
    /**
     *  耗时
     */
    private BigDecimal costTime;
    /**
     * 请求ID
     */
    @JsonProperty("req_id")
    private String reqId;

    public static TritonTaskRespDTO<Object> defaultFail(String message) {
        return new TritonTaskRespDTO<Object>()
                .setCode(-9)
                .setMessage("Request Inference Error: " + message)
                .setProcess(0);
    }

}
