package com.zjkj.aigc.common.req.resource;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/09
 */
@Data
public class ResourceBillGroupConfigCreateReq implements Serializable {
    private static final long serialVersionUID = 6175737930636341531L;
    /**
     * 月份
     */
    @NotNull(message = "{validation.month.required}")
    private Integer month;
    /**
     * 冗余比例集合
     */
    @NotEmpty(message = "{resource.bill.group.config.redundancy.list.required}")
    private List<Redundancy> redundancyList;

    @Data
    public static class Redundancy {
        /**
         * 月份
         */
        private Integer month;
        /**
         * 平台
         */
        @NotEmpty(message = "{validation.platform.required}")
        private String platform;
        /**
         * 中间件费用冗余比例
         */
        @NotNull(message = "{resource.bill.group.config.mid.price.redundancy.required}")
        private BigDecimal midPriceRedundancy;
        /**
         * 中间件调整费用
         */
        private BigDecimal midAdjPrice;
        /**
         * GPU费用冗余比例
         */
        @NotNull(message = "{resource.bill.group.config.gpu.price.redundancy.required}")
        private BigDecimal gpuPriceRedundancy;
        /**
         * GPU调整费用
         */
        private BigDecimal gpuAdjPrice;

        /**
         * 备注
         */
        private String marks;
    }
}
