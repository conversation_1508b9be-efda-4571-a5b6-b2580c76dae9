package com.zjkj.aigc.common.dto.file;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;
import java.io.Serializable;

/**
 * 文件下载DTO
 * 
 * <AUTHOR>
 * @since 2025/7/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileDownloadDTO implements Serializable {
    
    private static final long serialVersionUID = 1333334L;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型/MIME类型
     */
    private String contentType;
    
    /**
     * 文件输入流
     */
    private InputStream inputStream;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 构造方法 - 仅包含输入流和文件名
     * 
     * @param fileName 文件名
     * @param inputStream 文件输入流
     */
    public FileDownloadDTO(String fileName, InputStream inputStream) {
        this.fileName = fileName;
        this.inputStream = inputStream;
    }
    
    /**
     * 构造方法 - 包含文件名、输入流和内容类型
     * 
     * @param fileName 文件名
     * @param inputStream 文件输入流
     * @param contentType 内容类型
     */
    public FileDownloadDTO(String fileName, InputStream inputStream, String contentType) {
        this.fileName = fileName;
        this.inputStream = inputStream;
        this.contentType = contentType;
    }
}
