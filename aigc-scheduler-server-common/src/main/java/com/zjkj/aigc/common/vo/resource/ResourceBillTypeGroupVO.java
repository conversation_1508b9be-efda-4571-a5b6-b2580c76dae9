package com.zjkj.aigc.common.vo.resource;

import cn.hutool.core.collection.CollUtil;
import com.zjkj.aigc.common.constant.StringPool;
import com.zjkj.aigc.common.enums.PlatformEnum;
import com.zjkj.aigc.common.enums.model.AigcModelTypeEnum;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/03/03
 */
@Data
public class ResourceBillTypeGroupVO implements Serializable {
    private static final long serialVersionUID = 6878758428851143058L;
    /**
     * 月份
     */
    private Integer month;
    /**
     * GPU卡类型
     */
    private List<String> gpuTypeList;
    /**
     * 平台分组
     */
    private List<PlatformGroup> platformGroupList;
    /**
     * 模型分组
     */
    private List<ModelGroup> modelGroupList;
    /**
     * GPU预算费用
     */
    private BigDecimal gpuPrice;
    /**
     * 中间件预算费用
     */
    private BigDecimal midPrice;
    /**
     * 合计费用
     */
    private BigDecimal totalPrice;
    /**
     * GPU卡数
     */
    private BigDecimal gpuCount;

    /**
     * GPU台数
     */
    private BigDecimal gpuNum;

    @Data
    public static class PlatformGroup implements Serializable {
        private static final long serialVersionUID = -4629141857542749988L;
        /**
         * 平台
         */
        private String platform;
        /**
         * 模型类型集合
         */
        private List<Model> modelList;
        /**
         * GPU预算费用
         */
        private BigDecimal gpuPrice;
        /**
         * 中间件预算费用
         */
        private BigDecimal midPrice;
    }

    @Data
    @Builder
    public static class ModelGroup implements Serializable {
        private static final long serialVersionUID = -6383692293900854127L;
        /**
         * 模型类型
         */
        private String type;
        /**
         * 平台费用集合
         */
        private List<PlatformPrice> platformPriceList;
        /**
         * 合计费用
         */
        private BigDecimal totalPrice;
        /**
         * 费用占比
         */
        private BigDecimal totalPriceRate;
        /**
         * GPU卡数
         */
        private BigDecimal gpuCount;
        /**
         * GPU台数
         */
        private BigDecimal gpuNum;

        public void setGpuCount(BigDecimal gpuCount) {
            this.gpuCount = gpuCount;
            this.gpuNum = (gpuCount != null) ? gpuCount.divide(BigDecimal.valueOf(8), 2, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;
        }
    }

    @Data
    @Builder
    public static class Model implements Serializable {
        private static final long serialVersionUID = -7606043674079853863L;
        /**
         * 模型类型
         */
        private String type;
        /**
         * Gpu使用占比
         */
        private BigDecimal gpuProportionRate;
        /**
         * Gpu费用
         */
        private BigDecimal gpuPrice;
        /**
         * 中间件费用
         */
        private BigDecimal midPrice;
        /**
         * 合计费用
         */
        private BigDecimal totalPrice;
        /**
         * Gpu集合
         */
        private List<Gpu> gpuList;
        /**
         * GPU卡数合计
         */
        private BigDecimal gpuCount;
        /**
         * GPU台数
         */
        private BigDecimal gpuNum;

        public void setGpuCount(BigDecimal gpuCount) {
            this.gpuCount = gpuCount;
            this.gpuNum = (gpuCount != null) ? gpuCount.divide(BigDecimal.valueOf(8), 2, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;
        }
    }

    @Data
    @Builder
    public static class Gpu implements Serializable {
        private static final long serialVersionUID = 190141766078864064L;
        /**
         * GPU类型
         */
        private String type;
        /**
         * GPU卡数
         */
        private BigDecimal num;
    }

    @Data
    public static class PlatformPrice implements Serializable {
        private static final long serialVersionUID = 2276405892735855548L;
        /**
         * 平台
         */
        private String platform;
        /**
         * GPU预算费用
         */
        private BigDecimal gpuPrice;
        /**
         * 中间件预算费用
         */
        private BigDecimal midPrice;
        /**
         * 合计费用
         */
        private BigDecimal totalPrice;
    }

    public void fillModelGroupList() {
        modelGroupList = new ArrayList<>();
        Set<String> gpuTypes = new HashSet<>();
        gpuCount = BigDecimal.ZERO;
        Map<String, ModelGroup> modelGroupMap = new HashMap<>();
        for (PlatformGroup platformGroup : platformGroupList) {
            for (Model model : platformGroup.getModelList()) {
                modelGroupMap.putIfAbsent(model.getType(), ModelGroup.builder()
                        .type(model.getType()).gpuCount(BigDecimal.ZERO).totalPrice(BigDecimal.ZERO).build());
                ModelGroup group = modelGroupMap.get(model.getType());
                group.setGpuCount(group.getGpuCount().add(model.getGpuCount()));
                group.setTotalPrice(group.getTotalPrice().add(model.getTotalPrice()));
                group.setTotalPriceRate(group.getTotalPrice().divide(totalPrice, 4, RoundingMode.HALF_UP));

                PlatformPrice platformPrice = new PlatformPrice();
                platformPrice.setPlatform(platformGroup.getPlatform());
                platformPrice.setGpuPrice(model.getGpuPrice());
                platformPrice.setMidPrice(model.getMidPrice());
                platformPrice.setTotalPrice(model.getTotalPrice());
                if (CollectionUtils.isEmpty(group.getPlatformPriceList())) {
                    group.setPlatformPriceList(new ArrayList<>());
                }
                group.getPlatformPriceList().add(platformPrice);

                gpuCount = gpuCount.add(model.getGpuCount());
                for (Gpu gpu : model.getGpuList()) {
                    gpuTypes.add(gpu.getType());
                }
            }
        }

        this.gpuNum = (gpuCount != null) ? gpuCount.divide(BigDecimal.valueOf(8), 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        for (ModelGroup gpuGroup : modelGroupMap.values()) {
            //普通应用平台放最后
            gpuGroup.getPlatformPriceList().sort(Comparator
                    .comparing((PlatformPrice mg) -> mg.getPlatform().equals(PlatformEnum.APP.getName()))
                    .thenComparing(PlatformPrice::getPlatform));
            modelGroupList.add(gpuGroup);
        }
        gpuTypeList = new ArrayList<>(gpuTypes);
        gpuTypeList.sort(Comparator.naturalOrder());

        //其他放最后
        modelGroupList.sort(Comparator
                .comparing((ModelGroup mg) -> mg.getType().equals(StringPool.OTHER))
                .thenComparing(ModelGroup::getType)
        );

        //普通应用平台放最后
        platformGroupList.sort(Comparator
                .comparing((PlatformGroup mg) -> mg.getPlatform().equals(PlatformEnum.APP.getName()))
                .thenComparing(PlatformGroup::getPlatform)
        );

    }

}
