package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Data
public class AigcModelInfoCreateReq implements Serializable {
    private static final long serialVersionUID = 833339647549978681L;

    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 模型中文名称
     */
    @NotBlank(message = "{validation.name.required}")
    @Length(max = 100, message = "{model.test.name.length.exceeded}")
    private String name;
    /**
     * 类型: BIZ-业务模型,ATOM-原子模型
     */
    @NotBlank(message = "{validation.type.required}")
    @Length(max = 60, message = "{model.info.type.length.exceeded}")
    private String type;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    @Length(max = 60, message = "{model.info.task.type.length.exceeded}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    @Length(max = 60, message = "{model.info.model.name.length.exceeded}")
    private String modelName;
    /**
     * 备注
     */
    @Length(max = 255, message = "{model.info.remark.length.exceeded}")
    private String remark;
}
