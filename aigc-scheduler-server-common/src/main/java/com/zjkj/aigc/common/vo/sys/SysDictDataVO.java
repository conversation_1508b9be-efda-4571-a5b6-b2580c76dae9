package com.zjkj.aigc.common.vo.sys;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysDictDataVO extends BaseVO {
    private static final long serialVersionUID = 6464788263153164368L;

    /**
     * 字典类型id
     */
    private Long dictId;

    /**
     * 字典标签
     */
    private String label;

    /**
     * 字典键值
     */
    private String value;

    /**
     * 字典排序
     */
    private Integer dictSort;
    /**
     * 样式属性
     */
    private String cssClass;
    /**
     * 颜色属性
     */
    private String colorClass;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
