package com.zjkj.aigc.common.req.cluster;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 集群查询请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcClusterQuery extends PageBaseReq {

    private static final long serialVersionUID = 1L;

    /**
     * 平台
     */
    private String platform;

    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;

    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 节点ip
     */
    private String nodeIp;
}
