package com.zjkj.aigc.common.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 16:21
 */
@Setter
@Getter
public class TaskSummaryStatDTO {
    private String modelName;
    private String taskType;
    private Integer taskState;
    private Long amount;
    private BigDecimal avgTimeSec;
    private LocalDateTime lastTime;
    private String businessId;
    private Integer taskTimeHour;
}
