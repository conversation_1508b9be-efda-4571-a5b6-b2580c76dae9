package com.zjkj.aigc.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/30
 */
public class Jackson {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        // 配置 ObjectMapper
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        // 注册 JavaTimeModule 以支持 Java 8 日期/时间类型
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
    }

    private Jackson() {
        // 私有构造函数，防止实例化
    }

    /**
     * 对象转 JSON 字符串
     *
     * @param object 对象
     * @return JSON 字符串
     */
    public static String toJSONString(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Convert object to JSON string failed", e);
        }
    }

    /**
     * JSON 字符串转对象
     *
     * @param text  JSON 字符串
     * @param clazz 对象类型
     * @param <T>   对象类型
     * @return 对象
     */
    public static <T> T parseObject(String text, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(text, clazz);
        } catch (IOException e) {
            throw new RuntimeException("Parse JSON string to object failed", e);
        }
    }

    /**
     * JSON 字符串转对象
     *
     * @param text          JSON 字符串
     * @param typeReference 对象类型引用
     * @param <T>           对象类型
     * @return 对象
     */
    public static <T> T parseObject(String text, TypeReference<T> typeReference) {
        try {
            JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructType(typeReference);
            return OBJECT_MAPPER.readValue(text, javaType);
        } catch (IOException e) {
            throw new RuntimeException("Parse JSON string to object failed", e);
        }
    }

    /**
     * JSON 字符串转 List
     *
     * @param text  JSON 字符串
     * @param clazz List 中元素的类型
     * @param <T>   List 中元素的类型
     * @return List
     */
    public static <T> List<T> parseArray(String text, Class<T> clazz) {

        try {
            JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz);
            return OBJECT_MAPPER.readValue(text, javaType);
        } catch (IOException e) {
            throw new RuntimeException("Parse JSON string to List failed", e);
        }
    }

    /**
     * JSON 字符串转 JsonNode
     *
     * @param text JSON 字符串
     * @return JsonNode
     */
    public static JsonNode parseObject(String text) {
        try {
            return OBJECT_MAPPER.readTree(text);
        } catch (IOException e) {
            throw new RuntimeException("Parse JSON string to JsonNode failed", e);
        }
    }

    /**
     * JSON 字符串转 ObjectNode
     *
     * @return ObjectNode
     */
    public static ObjectNode createObjectNode() {
        return OBJECT_MAPPER.createObjectNode();
    }

    /**
     * 获取 ObjectMapper 实例
     *
     * @return ObjectMapper 实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
}