package com.zjkj.aigc.common.req.resource;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
public class ResourceMidPlanCreateReq implements Serializable {

    private static final long serialVersionUID = 6580963822160387797L;

    /**
     * 数据ID
     */
    private Long id;

    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 价格说明
     */
    @Length(max = 255, message = "{resource.mid.plan.price.remark.length.exceeded}")
    private String priceRemark;
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    /**
     * 备注
     */
    @Length(max = 255, message = "{resource.mid.plan.remark.length.exceeded}")
    private String remark;
}
