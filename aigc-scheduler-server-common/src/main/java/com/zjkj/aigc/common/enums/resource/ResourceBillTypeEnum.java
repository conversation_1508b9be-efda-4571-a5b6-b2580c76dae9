package com.zjkj.aigc.common.enums.resource;

import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/2/21
 */
@Getter
@AllArgsConstructor
public enum ResourceBillTypeEnum {

    /**
     * 账单类型
     */
    GPU("gpu", "GPU账单"),
    MID("mid", "中间件账单");

    private final String type;
    private final String desc;

    public static ResourceBillTypeEnum getByType(String type) {
        for (ResourceBillTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.bill.type.unsupported", new Object[]{type}));
    }
}
