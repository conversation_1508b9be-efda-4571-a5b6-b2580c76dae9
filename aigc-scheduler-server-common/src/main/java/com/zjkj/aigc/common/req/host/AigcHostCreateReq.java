package com.zjkj.aigc.common.req.host;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcHostCreateReq implements Serializable {
    private static final long serialVersionUID = 70946607983388917L;

    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 主机ip
     */
    @NotNull(message = "{host.ip.required}")
    @Length(max = 128, message = "{host.ip.length.exceeded}")
    private String hostIp;
    /**
     * 平台
     */
    @NotBlank(message = "{validation.platform.required}")
    @Length(max = 50, message = "{host.platform.length.exceeded}")
    private String platform;
    /**
     * 区域
     */
    @NotBlank(message = "{host.region.required}")
    @Length(max = 50, message = "{host.region.length.exceeded}")
    private String region;
    /**
     * 备注
     */
    @Length(max = 60, message = "{host.remark.length.exceeded}")
    private String remark;
}
