package com.zjkj.aigc.common.req.node;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcNodeHeartbeat implements Serializable {
    private static final long serialVersionUID = -4612526318254769660L;

    /**
     * 注册id
     */
    @NotNull(message = "{node.heartbeat.id.required}")
    private Long id;
}
