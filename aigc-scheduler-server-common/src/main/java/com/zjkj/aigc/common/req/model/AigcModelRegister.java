package com.zjkj.aigc.common.req.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcModelRegister implements Serializable {
    private static final long serialVersionUID = 5477803574724768092L;
    /**
     * 节点IP地址
     */
    @NotBlank(message = "{model.register.node.ip.required}")
    private String nodeIp;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;
    /**
     * pod名称
     */
    @NotBlank(message = "{model.register.pod.name.required}")
    private String podName;
    /**
     * pod命名空间
     */
    @NotBlank(message = "{model.register.pod.namespace.required}")
    private String podNamespace;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * sdk版本
     */
    private String sdkVersion;
    /**
     * 来源ip
     */
    private String sourceIp;
    /**
     * 环境类型
     */
    private String envType;
}
