package com.zjkj.aigc.common.req.approval;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/09
 */
@Data
public class GpuApplyStatusReq implements Serializable {
    private static final long serialVersionUID = -7953891772456844406L;
    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}")
    private Long id;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.approval.ApprovalStatusEnum}
     */
    private Integer status;
    /**
     * 操作说明
     */
    @Length(max = 255, message = "{gpu.apply.status.remark.length.exceeded}")
    private String supplyRemark;
}
