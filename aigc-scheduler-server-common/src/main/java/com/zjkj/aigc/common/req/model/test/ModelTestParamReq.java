package com.zjkj.aigc.common.req.model.test;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Data
public class ModelTestParamReq implements Serializable {
    private static final long serialVersionUID = 2451202136468334217L;
    /**
     * 动态参数名
     */
    private String parmaName;


    /**
     * 动态参数值的Oss地址
     */
    private String parmaFilePath;
}
