package com.zjkj.aigc.common.vo.cluster;

import com.zjkj.aigc.common.vo.BaseVO;
import com.zjkj.aigc.common.vo.gpu.AigcGpuVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 集群视图对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcClusterVO extends BaseVO {

    private static final long serialVersionUID = 5673897834470151808L;
    /**
     * 平台
     */
    private String platform;

    /**
     * 名称
     */
    private String name;

    /**
     * 服务类型
     */
    private List<String> serviceType;

    /**
     * 环境类型
     */
    private List<String> envType;

    /**
     * 成本
     */
    private BigDecimal cost;

    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;

    /**
     * gpu使用率
     */
    private BigDecimal gpuUsageRate;

    /**
     * 节点列表
     */
    private List<AigcClusterNodeVO> nodes;

    /**
     * GPU列表
     */
    private List<AigcGpuVO> gpus;

}
