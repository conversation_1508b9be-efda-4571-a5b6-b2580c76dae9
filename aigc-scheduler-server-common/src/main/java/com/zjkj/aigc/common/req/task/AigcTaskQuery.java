package com.zjkj.aigc.common.req.task;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcTaskQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -1168542287908055723L;
    /**
     * 业务任务ID
     */
    private String taskId;
    /**
     * 平台任务ID
     */
    private String aigcTaskId;
    /**
     * 平台任务ID
     */
    private List<String> aigcTaskIds;
    /**
     * 应用ID/业务系统ID
     */
    private String appId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消，5：超时失败
     */
    private List<Integer> taskStates;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
