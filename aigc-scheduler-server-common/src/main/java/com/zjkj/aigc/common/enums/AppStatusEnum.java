package com.zjkj.aigc.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@Getter
@AllArgsConstructor
public enum AppStatusEnum {

    /**
     * 未激活
     */
    INACTIVE(0, "未激活"),
    /**
     * 激活
     */
    ENABLE(1, "激活"),
    /**
     * 禁用
     */
    DISABLE(2, "禁用");

    private final Integer code;
    private final String desc;

    public Integer code() {
        return this.code;
    }
}
