package com.zjkj.aigc.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Getter
@RequiredArgsConstructor
public enum ModelAutoTestStatusEnum {

    /**
     * 待运行
     */
    WAITE(0, "待运行"),

    /**
     * 运行中
     */
    RUNNING(1, "运行中"),
    /**
     * 已完成
     */
    SUCCESS(2, "已完成"),
    /**
     * 报告已生成
     */
    REPORTED(3, "报告已生成"),
    /**
     * 取消
     */
    CANCEL(4, "取消"),
    /**
     * 失败
     */
    FAIL(5, "失败");

    private final Integer code;
    private final String desc;

    public static ModelAutoTestStatusEnum getByCode(Integer code) {
        for (ModelAutoTestStatusEnum value : ModelAutoTestStatusEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }

}
