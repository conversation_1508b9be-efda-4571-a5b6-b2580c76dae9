package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.dto.ModelConfigChanged;
import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/13
 */
@Data
public class AigcModelDeployCreateReq implements Serializable {
    private static final long serialVersionUID = 1180785879548922795L;
    /**
     * 标题
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 标题
     */
    @NotBlank(message = "{model.deploy.title.required}")
    @Length(max = 100, message = "{model.deploy.title.length.exceeded}")
    private String title;
    /**
     * 模型id
     */
    @NotNull(message = "{validation.model.id.required}")
    private Long modelId;
    /**
     * 项目工程名称
     */
    @NotBlank(message = "{model.deploy.project.name.required}")
    @Length(max = 60, message = "{model.deploy.project.name.length.exceeded}")
    private String projectName;
    /**
     * 项目git地址
     */
    @NotBlank(message = "{model.deploy.project.git.url.required}")
    @Length(max = 255, message = "{model.deploy.project.git.url.length.exceeded}")
    private String projectGitUrl;
    /**
     * 模型版本
     */
    @NotBlank(message = "{validation.model.version.required}")
    @Length(max = 60, message = "{model.deploy.version.length.exceeded}")
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    @NotBlank(message = "{model.deploy.sdk.version.required}")
    @Length(max = 60, message = "{model.deploy.sdk.version.length.exceeded}")
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    @NotBlank(message = "{model.deploy.oss.url.required}")
    @Length(max = 255, message = "{model.deploy.oss.url.length.exceeded}")
    private String modelFileOssUrl;
    /**
     * 基础镜像地址
     */
    @NotBlank(message = "{model.deploy.mirror.image.url.required}")
    @Length(max = 255, message = "{model.deploy.mirror.image.url.length.exceeded}")
    private String mirrorImageUrl;
    /**
     * 资源信息,GUP、CPU、内存等信息描述
     */
    @Valid
    @NotNull(message = "{model.deploy.resource.info.required}")
    private ModelDeployResource resourceInfo;
    /**
     * 是否加密
     */
    @NotNull(message = "{model.deploy.encrypt.required}")
    private Boolean isEncrypt;

    /**
     * 备注
     */
    @Length(max = 255, message = "{model.deploy.remark.length.exceeded}")
    private String remark;

    /**
     * 模型配置变更项
     */
    @NotNull(message = "{model.deploy.config.changed.required}")
    private ModelConfigChanged changed;
}
