package com.zjkj.aigc.common.req.task;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AigcTaskBatchOperateReq implements Serializable {
    private static final long serialVersionUID = -4696755439609351712L;
    /**
     * 平台任务id
     */
    @NotEmpty(message = "{task.batch.operate.task.ids.required}")
    private List<String> aigcTaskIds;
}
