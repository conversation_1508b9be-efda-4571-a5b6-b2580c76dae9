package com.zjkj.aigc.common.util;

import java.text.DecimalFormat;
import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/11/1
 */
public class TimeFormatter {

    /**
     * 格式化毫秒为可读时间字符
     *
     * @param milliseconds 毫秒数(必须大于等于0)
     * @return 格式化后的时间字符串
     */
    public static String formatTime(long milliseconds) {
        if (milliseconds < 0) {
            return "-秒";
        }

        if (milliseconds < 1) {
            return "0秒";
        }

        Duration duration = Duration.ofMillis(milliseconds);
        long hours = duration.toHours();
        long minutes = duration.toMinutesPart();
        long seconds = duration.toSecondsPart();
        long remainingMillis = duration.toMillisPart();

        StringBuilder result = new StringBuilder();
        if (hours > 0) {
            result.append(hours).append("小时");
        }

        if (minutes > 0) {
            result.append(minutes).append("分");
        }

        if (remainingMillis == 0) {
            // 没有毫秒，显示整数秒
            if (seconds > 0 || (hours == 0 && minutes == 0)) {
                result.append(seconds).append("秒");
            }
        } else {
            // 有毫秒，格式化为带小数的秒
            double secondsWithFraction = seconds + (remainingMillis / 1000.0);

            // 如果总时间小于1秒
            if (hours == 0 && minutes == 0 && seconds == 0) {
                result.append(formatDecimal(secondsWithFraction)).append("秒");
            } else {
                // 总时间超过1秒，且有毫秒部分
                result.append(formatDecimal(secondsWithFraction)).append("秒");
            }
        }

        return result.toString();
    }

    /**
     * 格式化小数，去除末尾多余的0和小数点
     * @param value 小数
     * @return 格式化后的字符串
     */
    private static String formatDecimal(double value) {
        // 最多保留3位小数
        DecimalFormat df = new DecimalFormat("0.###");
        return df.format(value);
    }

}
