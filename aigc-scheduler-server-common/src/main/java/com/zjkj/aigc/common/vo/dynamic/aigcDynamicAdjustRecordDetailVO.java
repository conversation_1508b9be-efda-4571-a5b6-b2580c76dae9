package com.zjkj.aigc.common.vo.dynamic;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class aigcDynamicAdjustRecordDetailVO extends BaseVO {
    private static final long serialVersionUID = 796817048231541988L;
    /**
     * 调整记录id
     */
    private Long adjustRecordId;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String scaleType;
    /**
     * 原副本数
     */
    private Integer sourceReplica;
    /**
     * 目标副本数
     */
    private Integer targetReplica;
    /**
     * 差异副本数
     */
    private Integer diffReplica;
    /**
     * gpu内存大小GB
     */
    private Integer gpuMemorySize;
    /**
     * 环境名称
     */
    private String envName;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 伸缩状态: PENDING-待伸缩,SCALING-伸缩中,SUCCESS-伸缩成功,FAILED-伸缩失败,CANCEL-取消,ROLLBACK-伸缩回退
     */
    private String scaleStatus;
    /**
     * 状态：1-生效中 2-结束
     */
    private Integer status;
    /**
     * 伸缩时间
     */
    private LocalDateTime scaleTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 重试次数
     */
    private Integer retryCount;
}
