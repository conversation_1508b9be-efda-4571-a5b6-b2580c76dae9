package com.zjkj.aigc.common.req.dynamic;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcDynamicAdjustRecordQuery extends PageBaseReq {
    private static final long serialVersionUID = -4473369643687654120L;
    /**
     * 动态配置id
     */
    private Long dynamicConfigId;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 伸缩类型:SCALE_OUT/SCALE_IN
     */
    private String scaleType;
    /**
     * 伸缩状态: PENDING-待伸缩,SCALING-伸缩中,SUCCESS-伸缩成功,FAILED-伸缩失败,CANCEL-取消,ROLLBACK-伸缩回退
     */
    private List<String> scaleStatusList;
    /**
     * 状态：1-生效中 2-结束 3-取消
     */
    private List<Integer> statusList;
}
