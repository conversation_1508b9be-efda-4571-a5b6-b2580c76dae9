package com.zjkj.aigc.common.dto.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.zjkj.aigc.common.constant.StringPool;
import lombok.Data;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class BillImportDTO implements Serializable {
    private static final long serialVersionUID = -52619647250000444L;
    /**
     * 帐期 yyyy-MM
     */
    private String periodMonth;
    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 计费类型
     */
    private String billingType;

    /**
     * 付款时间
     */
    private LocalDateTime paymentTime;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品
     */
    private String productName;

    /**
     * 设置字段值
     * @param fieldName 字段名
     * @param value 值
     */
    public void setFieldValue(String fieldName, Object value) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            Class<?> fieldType = field.getType();

            Object convertedValue;
            if (LocalDateTime.class.equals(fieldType)) {
                convertedValue = Convert.toLocalDateTime(value);
            } else if (BigDecimal.class.equals(fieldType)) {
                convertedValue = Convert.toBigDecimal(value);
            } else {
                convertedValue = Convert.convert(fieldType, value);
            }

            BeanUtil.setFieldValue(this, fieldName, convertedValue);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException("字段[" + fieldName + "]不存在");
        } catch (Exception e) {
            throw new RuntimeException("数据格式转换异常");
        }
    }

    public Integer getResultMonth() {
        String result = this.periodMonth.replace(StringPool.DASH, StringPool.EMPTY);
        return Integer.valueOf(result);
    }

    /**
     *  设置百度云帐期
     */
    public void setBaidu() {
        // 百度云帐期 2024-12-01 00:00:00-2024-12-31 23:59:59
        String paymentTimeStr = StrUtil.sub(this.periodMonth, 0, 19);
        this.paymentTime = DateUtil.parse(paymentTimeStr).toLocalDateTime();
        this.periodMonth = StrUtil.sub(this.periodMonth, 0, 7);
        this.productCode = this.productName;
    }
}
