package com.zjkj.aigc.common.msg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Author:z<PERSON><PERSON><PERSON>
 * @Description:
 * @DATE: 2024/12/19
 */
@Data
@Builder
public class DingTalkMsgDTO {
    /**
     * 消息类型 text,link,markdown,actionCard
     *
     * {@link https://open.dingtalk.com/document/orgapp/custom-bot-send-message-type?spm=ding_open_doc.document.0.0.ae823665yXMDeE}
     */
    private String type;
    /**
     * webhookUrl
     */
    private String webHookUrl;
    /**
     * 签名密钥
     */
    private String secret;
    /**
     * 被@人的手机号。
     */
    private List<String> atMobiles;
    /**
     * isAtAll
     */
    private Boolean isAtAll;
    /**
     * 文本类型消息
     */
    private Text text;
    /**
     * 链接类型
     */
    private Link link;
    /**
     * Marddown 类型
     */
    private Marddown marddown;
    /**
     * ActionCard类型消息
     */
    private ActionCard actionCard;


    @Data
    @Builder
    public static class Text {
        /**
         * 内容
         */
        private String content;
    }

    @Data
    @Builder
    public static class Link {
        /**
         * 消息内容。如果太长只会部分展示。
         */
        private String text;
        /**
         * 消息标题。
         */
        private String title;
        /**
         * 图片URL。
         */
        private String picUrl;
        /**
         * 点击消息跳转的URL
         */
        private String messageUrl;
    }

    @Data
    @Builder
    public static class Marddown {
        /**
         * markdown格式的消息。
         */
        private String text;
        /**
         * 首屏会话透出的展示内容。
         */
        private String title;
    }

    @Data
    @Builder
    public static class ActionCard {
        /**
         * 首屏会话透出的展示内容
         */
        private String title;
        /**
         * markdown格式的消息
         */
        private String text;
        /**
         * 0：按钮竖直排列
         * 1：按钮横向排列
         */
        private String btnOrientation;
        /**
         * 单个按钮的标题
         */
        private String singleTitle;
        /**
         * 点击singleTitle按钮触发的URL
         */
        @JsonProperty("singleURL")
        private String singleUrl;
    }

    private static final String ACTION_URL = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=false";

    public static String getPcOutSlideUrl(String url) {
        return String.format(ACTION_URL, URLEncoder.encode(url, StandardCharsets.UTF_8));
    }
}
