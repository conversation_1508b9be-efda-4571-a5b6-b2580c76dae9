package com.zjkj.aigc.common.enums.msg;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/19
 * @desc
 */
@Getter
@AllArgsConstructor
public enum DingTalkMsgTypeEnum {

	TEXT("text","文本"),
	LINK("link", "链接"),
	MARKDOWN("markdown", "markdown"),
	ACTIONCARD("actionCard", "actionCard");
	private final String code;
	private final String desc;

	public static final Map<String, String> CODE_MAP =  new HashMap<>();

	static {
		for (DingTalkMsgTypeEnum value : DingTalkMsgTypeEnum.values()) {
			CODE_MAP.put(value.getCode(), value.getDesc());
		}
	}

	public static DingTalkMsgTypeEnum of(String code) {
		for (DingTalkMsgTypeEnum value : DingTalkMsgTypeEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}

}
