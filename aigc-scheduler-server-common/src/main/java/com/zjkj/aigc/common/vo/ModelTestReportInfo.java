package com.zjkj.aigc.common.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@Data
public class ModelTestReportInfo implements Serializable {
    private static final long serialVersionUID = -3495475324704686150L;

    /**
     * 任务信息耗时
     */
    private TaskInfo taskInfo;
    /**
     * 显卡型号
     */
    private String gpuType;
    /**
     * 显卡调度类型：独占、虚拟
     */
    private String gpuDispatchType;
    /**
     * CPU信息
     */
    private MetricData cpuInfo;
    /**
     * 内存信息
     */
    private MetricData memoryInfo;
    /**
     * GPU信息
     */
    private MetricData gpuInfo;
    /**
     * GPU信息详情
     */
    private List<MetricData> gpuInfoDetails;
    /**
     * 资源限制
     */
    private ResourceLimit resourceLimits;

    @Data
    public static class ResourceLimit implements Serializable {
        private static final long serialVersionUID = -5052128709175297076L;
        /**
         * CPU限制
         */
        @JsonAlias(value = "CPU_Limit")
        private MetricData.UsageData cpu;
        /**
         * 内存限制
         */
        @JsonAlias(value = "Memory_Limit")
        private MetricData.UsageData memory;
        /**
         * GPU限制
         */
        @JsonAlias(value = "GPU_Memory_Limit")
        private MetricData.UsageData gpu;
    }

    @Data
    public static class TaskInfo implements Serializable {
        private static final long serialVersionUID = 2735849199265622352L;
        /**
         * 任务耗时
         */
        @JsonAlias(value = "model_cost_stats")
        private TimeCost timeCost;
        /**
         * 开始时间
         */
        @JsonAlias(value = "start_time")
        private String startTime;
        /**
         * 结束时间
         */
        @JsonAlias(value = "end_time")
        private String endTime;

        @Data
        public static class TimeCost implements Serializable {
            private static final long serialVersionUID = 7074833243602594967L;
            /**
             * 平均耗时（毫秒）
             */
            @JsonAlias(value = "mean_ms")
            private String meanMs;
            /**
             * 1h内执行的平均任务数量
             */
            @JsonAlias(value = "mean_1h_task_num")
            private String meanOneHourTaskNum;
        }
    }

    @Data
    public static class MetricData implements Serializable {
        private static final long serialVersionUID = 6732596750922150689L;
        /**
         * 编码
         */
        private String code;
        /**
         * 使用率
         */
        @JsonAlias(value = "Usage")
        private UsageData usage;
        /**
         * 使用率超过80%时间占比
         */
        @JsonAlias(value = "Utilization_Over_80")
        private String utilizationOver80;
        /**
         * 使用率超过90%时间占比
         */
        @JsonAlias(value = "Utilization_Over_90")
        private String utilizationOver90;

        @Data
        public static class UsageData implements Serializable {
            private static final long serialVersionUID = 6732596750922150689L;
            /**
             * 最大值
             */
            @JsonAlias(value = "max_value")
            private String maxValue;
            /**
             * 平均值
             */
            @JsonAlias(value = "mean_value")
            private String meanValue;
        }
    }

}
