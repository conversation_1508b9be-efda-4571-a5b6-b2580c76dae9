package com.zjkj.aigc.common.req.alarm;

import com.alibaba.fastjson2.JSONObject;
import com.zjkj.aigc.common.dto.alarm.AlarmConfigThresholdDTO;
import com.zjkj.aigc.common.dto.alarm.AlarmReceiverDTO;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmConfigCreateReq implements Serializable {
    private static final long serialVersionUID = -2612854125279501469L;
    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 标签
     */
    @NotBlank(message = "{validation.tag.required}")
    @Length(max = 30, message = "{alarm.config.tag.length.exceeded}")
    private String tag;
    /**
     * 关联模型ID集合
     */
    @NotEmpty(message = "{alarm.config.model.id.required}")
    private List<String> modelId;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    @NotNull(message = "{alarm.config.alarm.type.required}")
    private Integer alarmType;
    /**
     * 告警阈值配置
     */
    @NotNull(message = "{alarm.config.threshold.required}")
    private AlarmConfigThresholdDTO alarmConfigThreshold;
    /**
     * 告警时间间隔
     */
    @NotNull(message = "{alarm.config.time.interval.required}")
    private Integer alarmTimeInterval;
    /**
     * 告警时间间隔单位
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTimeIntervalUnitEnum}
     */
    @NotNull(message = "{alarm.config.time.interval.unit.required}")
    private Integer alarmTimeIntervalUnit;
    /**
     * 接收人配置
     */
    @NotNull(message = "{alarm.config.receiver.required}")
    private AlarmReceiverDTO alarmReceiver;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum}
     */
    private Integer status;
}
