package com.zjkj.aigc.common.req.resource;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Data
public class ResourceExpendCreateReq implements Serializable {

    private static final long serialVersionUID = 4595482764109481044L;

    /**
     * 支出月份
     */
    @NotNull(message = "{resource.expend.month.required}", groups = {ValidationGroup.Create.class})
    private Integer month;

    /**
     * 支出数据集合
     */
    @Valid
    @NotEmpty(message = "{resource.expend.list.required}")
    private List<Expend> expendList;

    @Data
    public static class Expend implements Serializable {
        private static final long serialVersionUID = 3164297689194500890L;
        /**
         * 数据ID
         */
        @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
        private Long id;
        /**
         * 支出月份
         */
        private Integer month;
        /**
         * 云平台
         */
        private String platform;
        /**
         * 账号
         */
        private String account;
        /**
         * 上月实际消费
         */
        private BigDecimal lastMonthCost;
        /**
         * 上月余额
         */
        private BigDecimal lastMonthRemain;
        /**
         * 本月已充值
         */
        private BigDecimal thisMonthRecharge;
        /**
         * 备注说明
         */
        @Length(max = 255, message = "{resource.expend.remark.length.exceeded}")
        private String remark;
        /**
         * 预算差异说明
         */
        @Length(max = 255, message = "{resource.expend.budget.diff.remark.length.exceeded}")
        private String budgetDiffRemark;
        /**
         * 状态
         * {@link com.zjkj.aigc.common.enums.resource.ResourceExpendStatusEnum}
         */
        private Integer status;
    }
}
