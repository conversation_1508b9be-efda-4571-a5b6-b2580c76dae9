package com.zjkj.aigc.common.exception;

import com.zjkj.aigc.common.enums.CustomErrorCode;
import lombok.Getter;

/**
 * 自定义异常类
 *
 * <AUTHOR>
 * @since 2024/5/29
 */
@Getter
public class BaseBizException extends WithErrorCodeException {

    private static final long serialVersionUID = 5689820143411423228L;

    public BaseBizException(CustomErrorCode errorCode) {
        super(errorCode);
    }

    public BaseBizException(CustomErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public BaseBizException(CustomErrorCode errorCode, String message, Throwable throwable) {
        super(errorCode, message, throwable);
    }

    public static void isTrue(boolean expression, CustomErrorCode errorCode) {
        if (!expression) {
            throw new BaseBizException(errorCode);
        }
    }

    public static void isTrue(boolean expression, CustomErrorCode errorCode, String message) {
        if (!expression) {
            throw new BaseBizException(errorCode, message);
        }
    }
}
