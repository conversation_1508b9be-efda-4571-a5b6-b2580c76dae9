package com.zjkj.aigc.common.req.alarm;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
@Builder
public class AigcAlarmConfigQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -4082270143644257363L;
    /**
     * 标签
     */
    private String tag;
    /**
     * 关联模型ID集合
     */
    private List<String> modelId;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmConfigStatusEnum}
     */
    private Integer status;
}
