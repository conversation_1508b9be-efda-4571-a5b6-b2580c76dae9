package com.zjkj.aigc.common.vo.resource;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResourceBillVO extends BaseVO {
    private static final long serialVersionUID = -1559577339337466783L;
    /**
     * 月份
     */
    private Integer month;
    /**
     * 账单类型
     */
    private String type;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * GPU型号
     */
    private String gpuModel;
    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 计费类型
     */
    private String billingType;
    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;
    /**
     * 付款时间
     */
    private LocalDateTime paymentTime;
}
