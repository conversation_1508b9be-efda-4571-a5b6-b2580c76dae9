package com.zjkj.aigc.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zjkj.aigc.common.serializer.AbstractTaskDTOSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */

@Data
@EqualsAndHashCode(callSuper = true)
@JsonSerialize(using = AbstractTaskDTOSerializer.class)
public abstract class AbstractTaskDTO extends HashMap<String, Object> implements Serializable {

    private static final long serialVersionUID = -2511461000649399275L;
    /**
     * 任务ID aigc_task_id
     */
    private String taskId;
    /**
     * 业务ID task_id
     */
    private String businessTaskId;
    /**
     * ai配置
     */
    private Object aiConfig;
}
