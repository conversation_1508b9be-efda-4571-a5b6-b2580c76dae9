package com.zjkj.aigc.common.req.alarm;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmChannelCreateReq implements Serializable {
    private static final long serialVersionUID = -5727862500203144109L;
    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 标签
     */
    @Length(max = 32, message = "{alarm.channel.tag.length.exceeded}")
    @NotEmpty(message = "{validation.tag.required}")
    private String tag;
    /**
     * 渠道类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmChannelEnum}
     */
    @NotNull(message = "{alarm.channel.type.required}", groups = {ValidationGroup.Create.class})
    private Integer channelType;
    /**
     * Webhook地址
     */
    @Length(max = 128, message = "{alarm.channel.webhook.length.exceeded}")
    @NotEmpty(message = "{alarm.channel.webhook.required}")
    private String webhookUrl;
    /**
     * 签名密钥
     */
    @Length(max = 128, message = "{alarm.channel.sign.length.exceeded}")
    private String sign;
}
