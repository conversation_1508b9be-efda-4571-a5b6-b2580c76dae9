package com.zjkj.aigc.common.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Data
public class BatchOperateReq implements Serializable {
    private static final long serialVersionUID = 7955467685527142933L;

    @NotEmpty(message = "{batch.operate.ids.required}")
    private List<Long> ids;
}
