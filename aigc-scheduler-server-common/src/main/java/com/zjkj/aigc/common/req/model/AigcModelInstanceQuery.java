package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelInstanceQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -8631830341108151804L;
    /**
     * pod名称
     */
    private String podName;
    /**
     * 节点IP
     */
    private String nodeIp;
    /**
     * pod命名空间
     */
    private List<String> podNamespaces;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 环境类型
     */
    private String envType;
    /**
     * pod状态: 0-空闲，1-运行
     */
    private Integer podStatus;
    /**
     * 合法状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 健康状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 集群id
     */
    private Long clusterId;
}
