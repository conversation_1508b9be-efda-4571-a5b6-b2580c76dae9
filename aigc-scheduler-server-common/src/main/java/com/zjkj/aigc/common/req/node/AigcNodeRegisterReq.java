package com.zjkj.aigc.common.req.node;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.zjkj.aigc.common.dto.node.NodeSystemInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcNodeRegisterReq implements Serializable {
    private static final long serialVersionUID = 5477803574724768092L;
    /**
     * 节点IP地址
     */
    @NotBlank(message = "{node.register.node.ip.required}")
    private String nodeIp;
    /**
     * 主机名
     */
    @NotBlank(message = "{node.register.hostname.required}")
    private String hostname;
    /**
     *  操作系统信息
     */
    @JsonAlias("system_info")
    private NodeSystemInfo systemInfo;
    /**
     * 命名空间
     */
    private List<String> namespace;
    /**
     * 来源ip
     */
    private String sourceIp;
}
