package com.zjkj.aigc.common.enums.msg;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/19
 * @desc
 */
@Getter
@AllArgsConstructor
public enum FeishuMsgTypeEnum {

	TEXT("text","文本"),
	POST("post", "富文本"),
	SHARE_CHAT("share_chat", "分享群聊"),
	IMAGE("image", "图片"),
	INTERACTIVE("interactive", "卡片");

	private final String code;
	private final String desc;


	public static final Map<String, String> CODE_MAP =  new HashMap<>();

	static {
		for (FeishuMsgTypeEnum value : FeishuMsgTypeEnum.values()) {
			CODE_MAP.put(value.getCode(), value.getDesc());
		}
	}

	public static FeishuMsgTypeEnum of(String code) {
		for (FeishuMsgTypeEnum value : FeishuMsgTypeEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}

}
