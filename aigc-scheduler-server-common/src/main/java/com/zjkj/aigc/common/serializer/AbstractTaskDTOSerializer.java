package com.zjkj.aigc.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.zjkj.aigc.common.dto.AbstractTaskDTO;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/4
 */
public class AbstractTaskDTOSerializer extends JsonSerializer<AbstractTaskDTO> {
    @Override
    public void serialize(AbstractTaskDTO dto, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartObject();

        // Serialize fields from the class
        Class<?> current = dto.getClass();
        while (Objects.nonNull(current) && current != HashMap.class && current != Object.class) {
            for (Field field : current.getDeclaredFields()) {
                // Skip the static field
                if (Modifier.isStatic(field.getModifiers())) {
                    continue;
                }

                field.setAccessible(true);
                try {
                    Object value = field.get(dto);
                    if (Objects.nonNull(value)) {
                        dto.put(field.getName(), field.get(dto));
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException("Failed to serialize field: " + field.getName(), e);
                }

            }
            current = current.getSuperclass();
        }

        // Serialize other fields from the HashMap
        for (Map.Entry<String, Object> entry : dto.entrySet()) {
            gen.writeObjectField(entry.getKey(), entry.getValue());
        }

        gen.writeEndObject();
    }
}
