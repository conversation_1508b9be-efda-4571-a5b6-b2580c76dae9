package com.zjkj.aigc.common.req.model.test;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Data
public class ModelTestCreateReq implements Serializable {
    private static final long serialVersionUID = 2451202136468359227L;
    /**
     * 测试编号
     */
    private String testNo;
    /**
     * 名称
     */
    @NotBlank(message = "{validation.name.required}")
    @Length(max = 100, message = "{model.test.name.length.exceeded}")
    private String name;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    @Length(max = 100, message = "{model.test.task.type.length.exceeded}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    @Length(max = 100, message = "{model.test.model.name.length.exceeded}")
    private String modelName;
    /**
     * 参数类型: single-单个，batch-批量
     */
    @NotBlank(message = "{model.test.param.type.required}")
    @Length(max = 64, message = "{model.test.param.type.length.exceeded}")
    private String paramType;
    /**
     * 模型参数
     */
    @NotBlank(message = "{model.test.model.params.required}")
    private String modelParams;
    /**
     * 任务批次数
     */
    @NotNull(message = "{model.test.batch.task.count.required}")
    @Min(value = 1, message = "{model.test.batch.task.count.min.value}")
    @Max(value = 500, message = "{model.test.batch.task.count.max.value}")
    private Long batchTaskCount;
    /**
     * 保留测试任务
     */
    private boolean retainTask;

    /**
     * 动态参数值及列表
     */
    private List<ModelTestParamReq> modelTestParamReqList;

}
