package com.zjkj.aigc.common.vo.dynamic;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AigcDynamicConfigGlobalVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 7358055388244844418L;
    /**
     * 动态调度启用
     */
    private Boolean dynamicEnabled;
    /**
     * 白名单启用
     */
    private Boolean whitelistEnabled;
    /**
     * 模型白名单
     */
    private List<Long> modelWhitelist;
    /**
     * 冷却分钟数
     */
    private Integer cooldownMinute;
    /**
     * zadig 端点地址
     */
    private String zadigEndpoint;
    /**
     * zadig api token
     */
    private String zadigApiToken;
}
