package com.zjkj.aigc.common.vo.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zjkj.aigc.common.util.TimeFormatter;
import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelInfoVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -7926979375823253847L;
    /**
     * 模型中文名称
     */
    private String name;
    /**
     * 类型: BIZ-业务模型,ATOM-原子模型
     */
    private String type;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态: 0-未部署,1-启用,2-禁用
     */
    private Integer status;
    /**
     * 环境实例数
     * envType,实例数
     */
    @JsonIgnore
    private Map<String, Long> envInstance;
    /**
     * 是否在线
     */
    private Boolean online;
    /**
     * 预测负载
     */
    private Integer preThreshold;
    /**
     * 预测数量
     */
    private Long preQuantity;
    /**
     * 平均耗时ms
     */
    private Long avgElapsedTime;
    /**
     * 平均耗时
     */
    private String formatAvgElapsedTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 上次活跃时间
     */
    private LocalDateTime lastActiveTime;
    /**
     * 上次注册时间
     */
    private LocalDateTime lastRegisterTime;
    /**
     * 在线实例数列表
     */
    private List<OnlineInstance> onlineInstanceList;

    public String getFormatAvgElapsedTime() {
        if (Objects.nonNull(this.avgElapsedTime)) {
            return TimeFormatter.formatTime(this.avgElapsedTime);
        }
        return null;
    }

    @Data
    @Accessors(chain = true)
    public static class OnlineInstance implements Serializable {
        private static final long serialVersionUID = -7926979375823253847L;
        /**
         * 环境类型
         */
        private String envType;
        /**
         * 在线实例数
         */
        private Long onlineInstance;

    }
}
