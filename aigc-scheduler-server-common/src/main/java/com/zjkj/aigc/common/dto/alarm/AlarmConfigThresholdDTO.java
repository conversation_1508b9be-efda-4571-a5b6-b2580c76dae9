package com.zjkj.aigc.common.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author:zhongyuji
 * @Description:
 * @DATE: 2024/12/18
 */
@Data
public class AlarmConfigThresholdDTO implements Serializable {
    private static final long serialVersionUID = -346545568161767698L;
    /**
     * 积压数量
     */
    private Integer overstackNum;
    /**
     * 成功率
     */
    private Integer successRate;
}
