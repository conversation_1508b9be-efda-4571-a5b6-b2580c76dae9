package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelInfoQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = 8783360218924363445L;
    private Long modelId;
    /**
     * 类型
     */
    private String type;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态: 0-未部署,1-启用,2-禁用
     */
    private Integer status;
    /**
     * 是否在线
     */
    private Boolean online;
}
