package com.zjkj.aigc.common.dto;

import cn.hutool.core.date.LocalDateTimeUtil;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Data
public class ModelTestReportSubmitDTO<T> implements Serializable {
    private static final long serialVersionUID = 5522230876951392977L;
    /**
     * 测试编号
     */
    @NotBlank(message = "{model.test.report.test.no.required}")
    private String testNo;
    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;
    /**
     * 模型版本
     */
    @NotBlank(message = "{validation.model.version.required}")
    private String modelVersion;
    /**
     * 服务名
     */
    @NotBlank(message = "{model.test.report.service.name.required}")
    private String serviceName;
    /**
     * 报告信息
     */
    @NotNull(message = "{model.test.report.info.required}")
    private T info;

    /**
     * 格式化单个时间字段
     * @param getter 时间获取函数
     * @param setter 时间设置函数
     */
    public static void formatTimeField(Supplier<String> getter, Consumer<String> setter) {
        Optional.ofNullable(getter.get())
                .filter(StringUtils::hasText)
                .map(Long::parseLong)
                .map(LocalDateTimeUtil::of)
                .map(LocalDateTimeUtil::formatNormal)
                .ifPresent(setter);
    }
}
