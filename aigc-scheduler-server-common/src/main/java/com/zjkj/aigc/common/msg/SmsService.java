package com.zjkj.aigc.common.msg;

import com.zjkj.aigc.common.msg.dto.MsgResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * 短信推送
 *
 * <AUTHOR>
 * @since 2024/12/19
 */
@Slf4j
@Service
public class SmsService {

    /**
     * 发送短信
     *
     * @param msgDTO 消息载体
     * @return 发送结果
     */
    public MsgResponseDTO send() {
        MsgResponseDTO response = new MsgResponseDTO();
        return response;
    }

}
