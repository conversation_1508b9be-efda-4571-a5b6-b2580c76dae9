package com.zjkj.aigc.common.vo.alarm;

import com.zjkj.aigc.common.dto.alarm.AlarmSnapshotDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmVO implements Serializable {

    private static final long serialVersionUID = 5066606493824276190L;

    /**
     * 数据ID
     */
    private Long id;
    /**
     * 报警快照
     */
    private AlarmSnapshotDTO alarmSnapshot;
    /**
     * 告警编号
     */
    private String alarmCode;
    /**
     * 模型类型
     * {@link com.zjkj.aigc.common.enums.model.AigcModelTypeEnum}
     */
    private String modelType;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 告警类型
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmTypeEnum}
     */
    private Integer alarmType;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 处理备注
     */
    private String remark;
    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 处理时间
     */
    private LocalDateTime handleTime;
}
