package com.zjkj.aigc.common.vo.node;

import com.zjkj.aigc.common.dto.node.NodeSystemInfo;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcNodeInstanceVO implements Serializable {
    private static final long serialVersionUID = 2700390312933530685L;

    private Long id;
    /**
     * 主机名
     */
    private String hostname;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 系统信息
     */
    private NodeSystemInfo systemInfo;
    /**
     * 命名空间
     */
    private List<String> namespace;
    /**
     * 来源ip
     */
    private String sourceIp;
    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime revisedTime;
}
