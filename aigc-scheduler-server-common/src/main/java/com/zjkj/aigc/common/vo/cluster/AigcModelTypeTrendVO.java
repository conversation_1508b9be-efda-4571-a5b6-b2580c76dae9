package com.zjkj.aigc.common.vo.cluster;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Data
@Accessors(chain = true)
public class AigcModelTypeTrendVO implements Serializable {
    private static final long serialVersionUID = 1293821613407217586L;
    /*
     * 使用趋势
     */
    private List<Trend> trends;
    /*
     * 平均使用
     */
    private List<AvgUsage> avgUsages;

    @Data
    public static class AvgUsage implements Serializable {
        private static final long serialVersionUID = -4306476281250567379L;
        /*
         * 模型类型
         */
        private String type;
        /**
         * gpu显存大小GB
         */
        private Long gpuMemorySize;
        /**
         * 占比总gpu显存
         */
        private BigDecimal gpuMemorySizeRatio;
    }

    @Data
    public static class Trend implements Serializable {
        private static final long serialVersionUID = 3104636136058921148L;
        /*
         * 数据日期
         */
        private LocalDate dataDate;
        /*
         * 模型类型使用列表
         */
        private List<AigcModelTypeUsageVO> modelTypeUsages;

        public static Trend buildDefault(LocalDate dataDate) {
            return new Trend()
                    .setDataDate(dataDate)
                    .setModelTypeUsages(List.of());
        }
    }

}
