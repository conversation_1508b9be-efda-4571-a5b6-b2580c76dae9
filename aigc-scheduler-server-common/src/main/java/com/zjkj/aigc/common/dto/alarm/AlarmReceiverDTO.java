package com.zjkj.aigc.common.dto.alarm;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:zhongyuji
 * @Description:
 * @DATE: 2024/12/20
 */
@Data
public class AlarmReceiverDTO implements Serializable {
    private static final long serialVersionUID = 3432152413539238377L;
    /**
     * 短信
     */
    private List<Sms> sms;
    /**
     * 邮箱
     */
    private List<Email> email;
    /**
     * 钉钉
     */
    private List<WebHook> dingtalk;
    /**
     * 飞书
     */
    private List<WebHook> feishu;

    @Data
    public static class Sms implements Serializable {
        private static final long serialVersionUID = -118253978224248578L;
        /**
         * 姓名
         */
        private String name;
        /**
         * 手机号
         */
        private String phone;
    }

    @Data
    public static class Email implements Serializable {
        private static final long serialVersionUID = -904292588706413824L;
        /**
         * 姓名
         */
        private String name;
        /**
         * 邮箱
         */
        private String email;
    }

    @Data
    public static class WebHook implements Serializable {
        private static final long serialVersionUID = -3769811655669147742L;
        /**
         * 消息渠道ID
         */
        private Long channelId;
    }
}
