package com.zjkj.aigc.common.req.alarm;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
@Data
public class AigcAlarmUpdateReq implements Serializable {
    private static final long serialVersionUID = -305272281832938200L;
    /**
     * 数据ID
     */
    @NotNull(message = "{validation.id.required}")
    private Long id;
    /**
     * 状态
     * {@link com.zjkj.aigc.common.enums.alarm.AlarmStatusEnum}
     */
    private Integer status;
    /**
     * 处理备注
     */
    @Length(max = 255, message = "{alarm.update.remark.length.exceeded}")
    private String remark;
    /**
     * 处理时间
     */
    private String handleTime;
}
