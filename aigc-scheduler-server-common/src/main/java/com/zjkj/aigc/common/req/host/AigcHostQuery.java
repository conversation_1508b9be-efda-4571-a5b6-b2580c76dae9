package com.zjkj.aigc.common.req.host;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcHostQuery extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = -310977190379766469L;
    /**
     * 主机ip
     */
    private String hostIp;
    /**
     * 平台
     */
    private String platform;
    /**
     * 区域
     */
    private String region;
    /**
     * 状态: 1-启用,0-禁用
     */
    private Integer status;
}
