package com.zjkj.aigc.common.req.task;

import cn.hutool.core.date.DateUtil;
import com.zjkj.aigc.common.enums.CustomErrorCode;
import com.zjkj.aigc.common.enums.TaskStatCompareTypeEnum;
import com.zjkj.aigc.common.exception.BaseBizException;
import com.zjkj.aigc.common.i18n.MessageUtils;
import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

/**
 * @Author:YWJ
 * @Description:
 * @DATE: 2024/10/17 17:00
 */
@Setter
@Getter
public class TaskStatSummaryReq extends PageBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 开始时间
     */
    @NotBlank(message = "{task.stat.summary.start.time.required}")
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "{task.stat.summary.end.time.required}")
    private String endTime;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 比较类型 day、week、month
     */
    private String compareType;

    /**
     * 模型类型
     */
    private String modelType;

    /**
     * 检查时间范围
     */
    public void checkTimeRange() {
        LocalDateTime startTime = DateUtil.parse(this.getStartTime()).toLocalDateTime();
        LocalDateTime endTime = DateUtil.parse(this.getEndTime()).toLocalDateTime();
        if (ChronoUnit.MONTHS.between(startTime, endTime) > 3) {
            throw new BaseBizException(CustomErrorCode.TIME_RANGE_TO_LONG, CustomErrorCode.TIME_RANGE_TO_LONG.getMessage("3个月"));
        }
    }

    /**
     * 设置时间范围
     * @param last 上
     */
    public void setDateByCompareType(boolean last, LocalDate now) {
        TaskStatCompareTypeEnum compareType = TaskStatCompareTypeEnum.of(this.getCompareType());
        if (Objects.isNull(compareType)) {
            throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.compare.type.unsupported"));
        }

        String startDate;
        String endDate;
        switch (compareType) {
            case DAY:
                if (last) {
                    // 昨天
                    startDate = now.minusDays(1).toString();
                } else {
                    startDate = now.toString();
                }
                endDate = startDate;
                break;
            case WEEK:
                if (last) {
                    // 上周一、上周日
                    startDate = now.minusWeeks(1).with(DayOfWeek.MONDAY).toString();
                    endDate = now.minusWeeks(1).with(DayOfWeek.SUNDAY).toString();
                } else {
                    // 获取周一
                    startDate = now.with(DayOfWeek.MONDAY).toString();
                    endDate = now.with(DayOfWeek.SUNDAY).toString();
                }
                break;
            case MONTH:
                if (last) {
                    // 上个月第一天、上个月最后一天
                    LocalDate lastMonthDate = now.minusMonths(1);
                    startDate = lastMonthDate.withDayOfMonth(1).toString();
                    endDate = lastMonthDate.withDayOfMonth(lastMonthDate.lengthOfMonth()).toString();
                } else {
                    // 本月第一天
                    startDate = now.withDayOfMonth(1).toString();
                    endDate = now.withDayOfMonth(now.lengthOfMonth()).toString();
                }
                break;
            default:
                throw new BaseBizException(CustomErrorCode.PARAM_ERROR, MessageUtils.getMessage("business.compare.type.unsupported"));
        }
        this.startTime = startDate;
        this.endTime = endDate;
    }

}
