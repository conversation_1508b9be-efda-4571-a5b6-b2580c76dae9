package com.zjkj.aigc.common.context;

import cn.hutool.core.util.IdUtil;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
public class TraceId {

    public static final String KEY_TRACE_ID = "X-Trace-Id";
    private static final String TRACE_ID = "traceId";
    private static final String CREATOR = "creator";

    public static String newTraceId() {
        return IdUtil.fastSimpleUUID();
    }

    public static void setId(String traceId) {
        MDC.put(TRACE_ID, traceId);
    }
    public static void setCreator(String creator) {
        MDC.put(CREATOR, creator);
    }
    public static void getId() {
        MDC.get(TRACE_ID);
    }

    public static void removeId() {
        MDC.remove(TRACE_ID);
    }
    public static void removeCreator() {
        MDC.remove(CREATOR);
    }

    public static void clear() {
        MDC.clear();
    }
}
