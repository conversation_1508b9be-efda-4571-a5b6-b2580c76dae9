package com.zjkj.aigc.common.vo;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ModelTestParamVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -4953356432965915251L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 名称
     */
    private String paramName;
    /**
     * 测试编号
     */
    private String testNo;

    /**
     * 任务类型
     */
    private String paramFileUrl;
}
