package com.zjkj.aigc.common.req.cluster;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Data
public class AigcClusterUsageStatQuery implements Serializable {
    private static final long serialVersionUID = 1156968297135467810L;

    /**
     * 集群主键id
     */
    private Long clusterId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 数据日期
     */
    @NotNull(message = "{cluster.usage.stat.data.date.required}")
    private LocalDate dataDate;
}
