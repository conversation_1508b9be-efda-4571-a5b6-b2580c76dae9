package com.zjkj.aigc.common.req.resource;

import cn.hutool.core.collection.CollUtil;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
public class ResourcePlanCreateReq implements Serializable {

    private static final long serialVersionUID = 1025375628728909041L;
    /**
     * 规划月份
     */
    @NotNull(message = "{resource.plan.month.required}")
    private Integer month;
    /**
     * 云平台
     */
    @NotBlank(message = "{validation.cloud.platform.required}")
    @Length(max = 20, message = "{resource.plan.platform.length.exceeded}")
    private String platform;
    /**
     * GPU资源规划
     */
    @Valid
    private List<ResourceGpuPlanCreateReq> gpuList;
    /**
     * 中间件资源规划
     */
    @Valid
    private ResourceMidPlanCreateReq mid;
}
