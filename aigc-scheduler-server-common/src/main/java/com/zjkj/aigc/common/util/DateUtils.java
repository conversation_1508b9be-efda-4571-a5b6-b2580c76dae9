package com.zjkj.aigc.common.util;

import cn.hutool.core.date.DatePattern;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.time.LocalDate;

@Slf4j
public class DateUtils {

    /**
     * 获取上个月的年月组合（YYYYMM格式）
     *
     * @param yearMonth 当前年月 (合法格式示例：202301)
     * @return 上个月的YYYYMM格式数值
     * @throws IllegalArgumentException 输入格式非法时抛出异常
     */
    public static int getPreviousYearMonth(Integer yearMonth) {
        // 参数格式校验
        if (yearMonth == null || yearMonth < 100 || String.valueOf(yearMonth).length() != 6) {
            throw new IllegalArgumentException("Invalid yearMonth format, must be YYYYMM");
        }
        // 拆分年月
        int year = yearMonth / 100;
        int month = yearMonth % 100;
        // 构造日期并计算
        LocalDate date = LocalDate.of(year, month, 1).minusMonths(1);
        return date.getYear() * 100 + date.getMonthValue();
    }

    /**
     * 获取当前月份的最后一天日期
     *
     * @param yearMonth 当前年月 (合法格式示例：202303)
     * @return 最后一天的 LocalDate 对象
     * @throws IllegalArgumentException 输入格式非法或日期无效时抛出异常
     */
    public static LocalDate getLastDay(int yearMonth) {
        // 格式校验
        String ymStr = String.valueOf(yearMonth);
        if (ymStr.length() != 6 || !ymStr.matches("\\d{6}")) {
            throw new IllegalArgumentException("Invalid format, must be YYYYMM");
        }
        // 拆分年月
        int year = Integer.parseInt(ymStr.substring(0, 4));
        int month = Integer.parseInt(ymStr.substring(4, 6));
        // 月份有效性校验
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Invalid month value: " + month);
        }
        LocalDate firstDayOfCurrentMonth = LocalDate.of(year, month, 1);
        return firstDayOfCurrentMonth.plusMonths(1).minusDays(1);
    }

    /**
     * @param str
     * @return
     */
    public static boolean isNormMonth(String str) {
        try {
            DatePattern.NORM_MONTH_FORMAT.parse(str);
            return true;
        } catch (ParseException e) {
            log.warn("date format is wrong, str[{}], format[{}]", str, DatePattern.NORM_MONTH_PATTERN);
        }
        return false;
    }

}
