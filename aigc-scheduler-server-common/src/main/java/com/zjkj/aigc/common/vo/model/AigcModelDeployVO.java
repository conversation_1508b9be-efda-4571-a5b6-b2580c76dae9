package com.zjkj.aigc.common.vo.model;

import com.zjkj.aigc.common.dto.ModelConfigChanged;
import com.zjkj.aigc.common.dto.model.ModelDeployResource;
import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelDeployVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 2638279264054930752L;
    /**
     * 标题
     */
    private String title;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 模型中文名称
     */
    private String modelNameZh;
    /**
     * 项目工程名称
     */
    private String projectName;
    /**
     * 项目git地址
     */
    private String projectGitUrl;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    private String modelFileOssUrl;
    /**
     * 是否加密
     */
    private Boolean isEncrypt;
    /**
     * 基础镜像地址
     */
    private String mirrorImageUrl;
    /**
     * 资源信息,GUP、CPU、内存等信息描述
     */
    private ModelDeployResource resourceInfo;
    /**
     * 加密状态: 0-未加密,1-加密中,2-加密完成,3-加密失败
     */
    private Integer encryptStatus;
    /**
     * 状态: 0-草稿,1-发布中,2-发布成功,3-发布失败
     */
    private Integer status;
    /**
     * 信息
     */
    private String message;
    /**
     * 备注
     */
    private String remark;
    /**
     * 模型配置变更项
     */
    private ModelConfigChanged changed;
}
