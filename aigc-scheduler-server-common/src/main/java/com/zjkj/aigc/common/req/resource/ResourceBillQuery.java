package com.zjkj.aigc.common.req.resource;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResourceBillQuery extends PageBaseReq {
    private static final long serialVersionUID = -7331522837304883397L;
    /**
     * 账单类型
     */
    private String type;
    /**
     * 云平台
     */
    private String platform;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 实例id
     */
    private String instanceId;
    /**
     * 产品名称
     */
    private String productName;
}
