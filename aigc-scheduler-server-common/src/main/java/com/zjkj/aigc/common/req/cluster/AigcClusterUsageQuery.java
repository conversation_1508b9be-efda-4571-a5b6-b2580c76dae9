package com.zjkj.aigc.common.req.cluster;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 集群资源利用查询请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcClusterUsageQuery extends PageBaseReq {

    private static final long serialVersionUID = 3092067813995100063L;
    /**
     * 集群主键id
     */
    private Long clusterId;
    /**
     * 平台
     */
    private String platform;
    /**
     * 数据日期
     */
    private LocalDate dataDate;
}
