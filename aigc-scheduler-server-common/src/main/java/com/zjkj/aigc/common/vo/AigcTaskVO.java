package com.zjkj.aigc.common.vo;

import com.zjkj.aigc.common.enums.task.TaskStateEnum;
import com.zjkj.aigc.common.util.TimeFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/11/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcTaskVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 8627913185481865054L;
    /**
     * 业务任务ID
     */
    private String taskId;
    /**
     * 平台任务ID
     */
    private String aigcTaskId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 业务系统ID
     */
    private String businessId;
    /**
     * 通知地址
     */
    private String notifyUrl;
    /**
     * 任务优先级
     * 数字越小优先级越高
     */
    private Integer taskPriority;
    /**
     * 是否任务批量0，1
     */
    private Integer taskBatch;
    /**
     * 任务来源
     */
    private String taskSource;
    /**
     * 任务进度
     */
    private Integer taskProgress;
    /**
     * 任务排名
     */
    private Integer rank;
    /**
     * 状态，0：待执行，1：执行中，2：成功，3：失败，4：取消，5：超时失败
     */
    private Integer taskState;
    /**
     * 任务开始时间
     */
    private LocalDateTime taskStartTime;
    /**
     * 任务取消时间
     */
    private LocalDateTime taskCancelTime;
    /**
     * 任务完成时间
     */
    private LocalDateTime taskCompletionTime;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型参数
     */
    private String modelParams;
    /**
     * 模型输出
     */
    private String modelOutput;

    /**
     * 模型参数图片url
     */
    private List<String> modelParamsImgs;
    /**
     * 模型输出图片url
     */
    private List<String> modelOutputImgs;

    /**
     * 运行容器id
     */
    private String containerId;
    /**
     * 失败信息
     */
    private String failMessage;
    /**
     * 重试次数
     */
    private Integer retryCount;
    /**
     * 任务等待时长
     */
    private String taskWaitTimeFormat;
    /**
     * 任务耗时
     */
    private String taskCostTimeFormat;

    public String getTaskWaitTimeFormat() {
        if (Objects.equals(this.taskState, TaskStateEnum.CANCEL.getCode()) && Objects.isNull(this.taskStartTime)) {
            return null;
        }

        LocalDateTime dateTime = Objects.nonNull(this.taskStartTime) ? this.taskStartTime : LocalDateTime.now();
        long millis = ChronoUnit.MILLIS.between(this.getCreatedTime(), dateTime);
        return TimeFormatter.formatTime(millis);
    }

    public String getTaskCostTimeFormat() {
        if (Objects.nonNull(this.taskStartTime) && Objects.nonNull(this.taskCompletionTime)) {
            long millis = ChronoUnit.MILLIS.between(this.taskStartTime, this.taskCompletionTime);
            return TimeFormatter.formatTime(millis);
        }
        return null;
    }
}
