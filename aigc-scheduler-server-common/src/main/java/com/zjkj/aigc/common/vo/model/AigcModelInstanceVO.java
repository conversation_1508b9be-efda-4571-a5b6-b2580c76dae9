package com.zjkj.aigc.common.vo.model;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/11/25
 */
@Data
public class AigcModelInstanceVO implements Serializable {
    private static final long serialVersionUID = 2700390312933530685L;

    private Long id;
    /**
     * pod名称
     */
    private String podName;
    /**
     * pod命名空间
     */
    private String podNamespace;
    /**
     * 端点地址
     */
    private String endpoint;
    /**
     * 集群id
     */
    private Long clusterId;
    /**
     * 节点ip
     */
    private String nodeIp;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 环境类型
     */
    private String envType;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 状态: 0-空闲，1-运行
     */
    private Integer podStatus;
    /**
     * 状态: 0-非法，1-合法
     */
    private Integer status;
    /**
     * 状态: 0-离线，1-在线
     */
    private Integer healthStatus;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * sdk版本
     */
    private String sdkVersion;
    /**
     * 来源ip
     */
    private String sourceIp;
    /**
     * 最后心跳时间
     */
    private LocalDateTime lastHeartbeatTime;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime revisedTime;
}
