package com.zjkj.aigc.common.enums.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
@Getter
@AllArgsConstructor
public enum AigcModelTypeEnum {

    BIZ("BIZ", "业务模型"),
    ATOM("ATOM", "原子模型"),
    DATA("DATA", "数据模型"),
    DATA_MQ("DATA_MQ", "数据MQ模型"),
    FG("FG", "FG模型"),
    LOGO("LOGO", "Logo印模型"),
    FABR("FABR", "面料识别模型");


    private final String code;
    private final String desc;

    public static AigcModelTypeEnum getByCode(String code) {
        for (AigcModelTypeEnum value : AigcModelTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
