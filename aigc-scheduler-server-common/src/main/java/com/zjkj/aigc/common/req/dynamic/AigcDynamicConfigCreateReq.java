package com.zjkj.aigc.common.req.dynamic;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
public class AigcDynamicConfigCreateReq implements Serializable {
    private static final long serialVersionUID = 311707004486098256L;

    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 模型id
     */
    @NotNull(message = "{validation.model.id.required}")
    private Long modelId;
    /**
     * 最小副本数
     */
    @NotNull(message = "{dynamic.config.min.replica.required}")
    @Min(value = 1, message = "{dynamic.config.min.replica.min.value}")
    private Integer minReplica;
    /**
     * 最大副本数
     */
    @NotNull(message = "{dynamic.config.max.replica.required}")
    @Max(value = 999, message = "{dynamic.config.max.replica.max.value}")
    private Integer maxReplica;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 冷却分钟数
     */
    @Min(value = 0, message = "{dynamic.config.cooldown.minute.min.value}")
    private Integer cooldownMinute;
    /**
     * 扩容维度:分钟数
     */
    @NotNull(message = "{dynamic.config.scale.out.minute.required}")
    @Min(value = 0, message = "{dynamic.config.scale.out.minute.min.value}")
    private Integer scaleOutMinute;
    /**
     * 扩容负载: 阈值1-100%
     */
    @NotNull(message = "{dynamic.config.scale.out.threshold.required}")
    @Range(min = 1, max = 100, message = "{dynamic.config.scale.out.threshold.range}")
    private Integer scaleOutThreshold;
    /**
     * 优先剥夺的模型id
     */
    private List<Long> deprivedModelIds;
    /**
     * 缩容维度:分钟数
     */
    @NotNull(message = "{dynamic.config.scale.in.minute.required}")
    @Min(value = 0, message = "{dynamic.config.scale.in.minute.min.value}")
    private Integer scaleInMinute;
    /**
     * 缩容负载:阈值1-100%
     */
    @NotNull(message = "{dynamic.config.scale.in.threshold.required}")
    @Range(min = 1, max = 100, message = "{dynamic.config.scale.in.threshold.range}")
    private Integer scaleInThreshold;
}
