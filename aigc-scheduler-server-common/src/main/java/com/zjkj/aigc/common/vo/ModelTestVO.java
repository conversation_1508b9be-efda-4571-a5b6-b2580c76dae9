package com.zjkj.aigc.common.vo;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ModelTestVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -4953312495965915251L;
    /**
     * 名称
     */
    private String name;
    /**
     * 测试编号
     */
    private String testNo;

    /**
     * 业务系统ID
     */
    private String businessId;
    /**
     *  状态，0：待执行，1：执行中，2：已完成，3：报告已生成，4:取消，5:失败
     */
    private Integer status;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 参数类型: single-单个，batch-批量
     */
    private String paramType;
    /**
     * 模型参数
     */
    private String modelParams;
    /**
     * 是否保留任务
     */
    private boolean retainTask;
    /**
     * 任务批次数
     */
    private Long batchTaskCount;
    /**
     * 总任务数
     */
    private Long totalTaskCount;
    /**
     * 成功的任务数
     */
    private Long successTaskCount;
    /**
     * 测试开始时间
     */
    private LocalDateTime testStartTime;
    /**
     * 测试完成时间
     */
    private LocalDateTime testCompletionTime;
    /**
     * 测试取消时间
     */
    private LocalDateTime testCancelTime;

    /**
     * 测试取消时间
     */
    private List<ModelTestParamVO> modelTestParamVOList;

    public Long getTotalTaskCount() {
        if (JSON.isValidArray(modelParams)) {
            return JSON.parseArray(modelParams).size() * batchTaskCount;
        }
        return batchTaskCount;
    }
}
