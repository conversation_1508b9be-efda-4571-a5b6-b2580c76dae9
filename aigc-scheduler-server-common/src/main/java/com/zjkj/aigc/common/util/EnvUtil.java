package com.zjkj.aigc.common.util;

import com.zjkj.aigc.common.constant.StringPool;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
public class EnvUtil {
    
    private static final String ACTIVE = System.getProperty("spring.profiles.active");
    private static final String CURRENT_ENV_TYPE;
    
    static {
        if (StringUtils.hasText(ACTIVE)) {
            String[] split = ACTIVE.split(StringPool.DASH);
            CURRENT_ENV_TYPE = split[0];
        } else {
            CURRENT_ENV_TYPE = ACTIVE;
        }
    }

    /**
     * 获取当前环境类型
     *
     * @return 当前环境类型
     */
    public static String getCurrentEnvType() {
        return CURRENT_ENV_TYPE;
    }
}
