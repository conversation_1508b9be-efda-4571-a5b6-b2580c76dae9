package com.zjkj.aigc.common.dto.model;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Data
public class ModelDeployResource implements Serializable {
    private static final long serialVersionUID = -2370256636481577549L;
    /**
     * cpu核数
     */
    @NotNull(message = "{validation.cpu.core.required}")
    private Integer cpuCore;

    /**
     * 内存大小
     */
    @NotNull(message = "{validation.memory.size.required}")
    private Integer memorySize;

    /**
     * gpu信息
     */
    @Valid
    @NotNull(message = "{model.resource.gpu.info.required}")
    private GpuInfo gpuInfo;
}
