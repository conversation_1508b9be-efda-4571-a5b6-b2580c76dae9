package com.zjkj.aigc.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author:zhong<PERSON><PERSON>
 * @Description:
 * @DATE: 2025/01/17
 */
@Data
public class ModelInstanceAdjustDTO implements Serializable {
    private static final long serialVersionUID = -3584805313539422468L;
    /**
     * 模型ID
     */
    private Long modelId;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 当前pod数量
     */
    private Integer podCount;
    /**
     * 调整后pod数量
     */
    private Integer adjustedPodCount;
}
