package com.zjkj.aigc.common.enums;

import com.zjkj.aigc.common.i18n.MessageUtils;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@AllArgsConstructor
public enum CustomErrorCode implements ErrorCode {

    SUCCESS(2000000, "success"),

    DATA_NOT_EXISTS(30000301, "data.not.exists"),
    DATA_ALREADY_EXISTS(30000302, "data.already.exists"),
    DATA_CANNOT_EDIT(30000303, "data.cannot.edit"),
    DATA_CHANGED(30000304, "data.changed"),
    DB_DATA_ALREADY_EXISTS(30000305, "db.data.already.exists"),
    DATA_ERROR(30000306, "data.error"),

    DASHVECTOR_CREATE_COLLECTION_ERROR(30000307, "dashvector.create.collection"),
    DASHVECTOR_CREATE_DOC_ERROR(30000308, "dashvector.create.doc"),
    DASHVECTOR_DELETE_DOC_ERROR(30000309, "dashvector.delete.doc"),
    DASHVECTOR_QUERY_DOC_ERROR(30000310, "dashvector.query.doc"),
    DASHVECTOR_QUERY_PARAMS_ERROR(30000311, "dashvector.query.params"),

    UNAUTHORIZED(401, "unauthorized"),
    PARAM_ERROR(4000000, "param.error"),
    NOT_SUPPORTED(4000003, "not.supported"),

    UNKNOWN_ERROR(5000000, "unknown.error"),
    FEIGN_INVOKE_ERROR(5000501, "feign.invoke"),

    // 自定义页面错误 6000001 开头
    TIME_RANGE_TO_LONG(6000001, "time.range.too.long"),
    ;

    private final Integer code;
    private final String messageKey;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        try {
            return MessageUtils.getMessage(this.messageKey);
        } catch (Exception e) {
            // 如果国际化失败，返回默认消息
            return getDefaultMessage();
        }
    }

    /**
     * 获取带参数的国际化消息
     *
     * @param args 参数
     * @return 国际化消息
     */
    public String getMessage(Object... args) {
        try {
            return MessageUtils.getMessage(this.messageKey, args);
        } catch (Exception e) {
            return getDefaultMessage();
        }
    }

    /**
     * 获取消息键
     *
     * @return 消息键
     */
    public String getMessageKey() {
        return this.messageKey;
    }

    /**
     * 获取默认消息（中文）
     *
     * @return 默认消息
     */
    private String getDefaultMessage() {
        switch (this) {
            case SUCCESS: return "请求成功！";
            case DATA_NOT_EXISTS: return "数据不存在";
            case DATA_ALREADY_EXISTS: return "数据已存在";
            case DATA_CANNOT_EDIT: return "数据无法修改";
            case DATA_CHANGED: return "数据发生变化，请重试";
            case DB_DATA_ALREADY_EXISTS: return "数据库数据重复";
            case DATA_ERROR: return "数据异常";
            case DASHVECTOR_CREATE_COLLECTION_ERROR: return "向量数据库创建集合错误";
            case DASHVECTOR_CREATE_DOC_ERROR: return "向量数据库创建文档错误";
            case DASHVECTOR_DELETE_DOC_ERROR: return "向量数据库删除文档错误";
            case DASHVECTOR_QUERY_DOC_ERROR: return "向量数据库查询文档错误";
            case DASHVECTOR_QUERY_PARAMS_ERROR: return "检索ID和检索向量数组不能同时为空";
            case UNAUTHORIZED: return "未授权！";
            case PARAM_ERROR: return "客户端参数错误";
            case NOT_SUPPORTED: return "NOT_SUPPORTED";
            case UNKNOWN_ERROR: return "未知错误，请联系管理员";
            case FEIGN_INVOKE_ERROR: return "远程外部服务调用失败！";
            case TIME_RANGE_TO_LONG: return "时间跨度过长";
            default: return "系统错误";
        }
    }
}
