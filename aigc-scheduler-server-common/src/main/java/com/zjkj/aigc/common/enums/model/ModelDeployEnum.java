package com.zjkj.aigc.common.enums.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/11/13
 */
public class ModelDeployEnum {

    @Getter
    @AllArgsConstructor
    public enum Status {
        DRAFT(0, "草稿", ""),
        DEPLOYING(1, "申请发布", "\"#3388FF\""),
        DEPLOY_SUCCESS(2, "发布成功", "\"#00CC00\""),
        DEPLOY_FAIL(3, "发布失败", "\"#FF0000\"");

        private final Integer status;
        private final String desc;
        private final String color;

        public static String ofDesc(Integer status) {
            for (Status value : Status.values()) {
                if (value.getStatus().equals(status)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static Status of(Integer status) {
            for (Status value : Status.values()) {
                if (value.getStatus().equals(status)) {
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum EncryptStatus {
        UNENCRYPTED(0, "未加密"),
        ENCRYPTING(1, "加密中"),
        ENCRYPTED(2, "加密完成"),
        ENCRYPT_FAIL(3, "加密失败");

        private final Integer status;
        private final String desc;

        public static String ofDesc(Integer status) {
            for (EncryptStatus value : EncryptStatus.values()) {
                if (value.getStatus().equals(status)) {
                    return value.getDesc();
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum EncryptCallbackStatus {
        SUCCESS(1, "加密完成"),
        FAIL(2, "加密失败");

        private final Integer status;
        private final String desc;
    }
}
