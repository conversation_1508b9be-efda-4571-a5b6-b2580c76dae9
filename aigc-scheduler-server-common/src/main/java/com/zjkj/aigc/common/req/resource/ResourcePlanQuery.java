package com.zjkj.aigc.common.req.resource;

import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Data
@Builder
public class ResourcePlanQuery implements Serializable {
    private static final long serialVersionUID = -8134984009590896310L;
    /**
     * 规划月份
     */
    @NotBlank(message = "{resource.plan.query.month.required}")
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 云平台集合
     */
    private List<String> platforms;
}
