package com.zjkj.aigc.common.vo.model;

import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcModelConfigVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -6111108377084740765L;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 项目工程名称
     */
    private String projectName;
    /**
     * 项目git地址
     */
    private String projectGitUrl;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    private String modelFileOssUrl;
    /**
     * 是否加密
     */
    private Boolean isEncrypt;
    /**
     * 基础镜像地址
     */
    private String mirrorImageUrl;
    /**
     * cpu核数
     */
    private Integer cpuCore;
    /**
     * 内存大小GB
     */
    private Integer memorySize;
    /**
     * gpu信息
     */
    private GpuInfo gpuInfo;
}
