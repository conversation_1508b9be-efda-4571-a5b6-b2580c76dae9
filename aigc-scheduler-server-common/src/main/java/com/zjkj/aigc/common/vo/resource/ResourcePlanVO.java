package com.zjkj.aigc.common.vo.resource;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Data
public class ResourcePlanVO implements Serializable {
    private static final long serialVersionUID = -8553555811974971174L;

    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * GPU资源规划
     */
    private List<Gpu> gpuList;
    /**
     * GPU合计费用
     */
    private GpuSummary gpuSummary;
    /**
     * 中间件资源规划
     */
    private Mid mid;
    /**
     * 合计费用
     */
    private BigDecimal totalPrice;

    @Data
    public static class Gpu implements Serializable {
        private static final long serialVersionUID = -1434507921472445369L;
        /**
         * 数据id
         */
        private String id;
        /**
         * 型号
         */
        private String model;
        /**
         * 业务卡需求
         */
        private Integer businessDemand;
        /**
         * 打标卡需求
         */
        private Integer markDemand;
        /**
         * 训练卡需求
         */
        private Integer trainDemand;
        /**
         * 测试卡需求
         */
        private Integer testDemand;
        /**
         * 运维冗余卡需求
         */
        private Integer devopsDemand;
        /**
         * GPU规格/卡
         */
        private Integer norms;
        /**
         * 长期卡
         */
        private Integer longTerm;
        /**
         * 临时/卡
         */
        private Integer tmp;
        /**
         * 冗余卡
         */
        private Integer backup;
        /**
         * 卡合计
         */
        private Integer total;
        /**
         * 需求台数
         */
        private Integer demandNum;
        /**
         * 单价
         */
        private BigDecimal price;
        /**
         * 总价
         */
        private BigDecimal totalPrice;
        /**
         * 备注
         */
        private String remark;
    }

    @Data
    public static class Mid implements Serializable {
        private static final long serialVersionUID = -26942275814256142L;
        /**
         * 数据id
         */
        private String id;
        /**
         * 价格说明
         */
        private String priceRemark;
        /**
         * 总价
         */
        private BigDecimal totalPrice;
        /**
         * 备注
         */
        private String remark;
    }

    @Data
    public static class GpuSummary implements Serializable {
        private static final long serialVersionUID = -3876697901942283240L;
        /**
         * 业务卡需求
         */
        private Integer businessDemand;
        /**
         * 打标卡需求
         */
        private Integer markDemand;
        /**
         * 训练卡需求
         */
        private Integer trainDemand;
        /**
         * 测试卡需求
         */
        private Integer testDemand;
        /**
         * 运维冗余卡需求
         */
        private Integer devopsDemand;
        /**
         * 长期卡
         */
        private Integer longTerm;
        /**
         * 临时/卡
         */
        private Integer tmp;
        /**
         * 冗余卡
         */
        private Integer backup;
        /**
         * 卡合计
         */
        private Integer total;
        /**
         * 需求台数
         */
        private Integer demandNum;
        /**
         * 总价
         */
        private BigDecimal totalPrice;
    }

    public void calculate() {
        gpuSummary = new GpuSummary();
        totalPrice = new BigDecimal(0);
        if (CollUtil.isNotEmpty(gpuList)) {
            gpuList.stream().forEach(gpu -> {
                gpu.setLongTerm(gpu.getBusinessDemand() + gpu.getMarkDemand()
                        + gpu.getTrainDemand() + gpu.getTestDemand());
                gpu.setBackup(gpu.getDevopsDemand());
                gpu.setTotal(gpu.getLongTerm() + gpu.getTmp() + gpu.getBackup());
                //向上取整
                if (gpu.getNorms() > 0) {
                    gpu.setDemandNum((gpu.getTotal() + gpu.getNorms() - 1) / gpu.getNorms());
                    gpu.setTotalPrice(gpu.getPrice().multiply(BigDecimal.valueOf(gpu.getDemandNum()))
                            .setScale(2, RoundingMode.HALF_UP));
                }else {
                    gpu.setDemandNum(0);
                    gpu.setTotalPrice(BigDecimal.ZERO);
                }
            });
            gpuSummary.setBusinessDemand(gpuList.stream().mapToInt(gpu -> gpu.getBusinessDemand()).sum());
            gpuSummary.setMarkDemand(gpuList.stream().mapToInt(gpu -> gpu.getMarkDemand()).sum());
            gpuSummary.setTrainDemand(gpuList.stream().mapToInt(gpu -> gpu.getTrainDemand()).sum());
            gpuSummary.setTestDemand(gpuList.stream().mapToInt(gpu -> gpu.getTestDemand()).sum());
            gpuSummary.setDevopsDemand(gpuList.stream().mapToInt(gpu -> gpu.getDevopsDemand()).sum());
            gpuSummary.setLongTerm(gpuList.stream().mapToInt(gpu -> gpu.getLongTerm()).sum());
            gpuSummary.setTmp(gpuList.stream().mapToInt(gpu -> gpu.getTmp()).sum());
            gpuSummary.setBackup(gpuList.stream().mapToInt(gpu -> gpu.getBackup()).sum());
            gpuSummary.setTotal(gpuList.stream().mapToInt(gpu -> gpu.getTotal()).sum());
            gpuSummary.setDemandNum(gpuList.stream().mapToInt(gpu -> gpu.getDemandNum()).sum());
            gpuSummary.setTotalPrice(gpuList.stream().map(Gpu::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            totalPrice = totalPrice.add(gpuSummary.getTotalPrice());
        }
        if (Objects.nonNull(mid)) {
            totalPrice = totalPrice.add(mid.getTotalPrice());
        }
    }
}
