package com.zjkj.aigc.common.msg.dto;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Author:zhongyuji
 * @Description:
 * @DATE: 2024/12/19
 */
@Data
@Builder
public class FeishuMsgDTO {
    /**
     * 消息类型 text,post,share_chat,image,interactive
     * <p>
     * {@link https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot}
     */
    private String type;
    /**
     * webhookUrl
     */
    private String webHookUrl;
    /**
     * 签名密钥
     */
    private String secret;
    /**
     * 文本类型消息
     */
    private Text text;
    /**
     * 富文本类型
     */
    private Post post;
    /**
     * 群名片 类型
     */
    private ShareChat shareChat;
    /**
     * 图片类型消息
     */
    private Image image;
    /**
     * 卡片类型消息
     */
    private Card card;


    @Data
    @Builder
    public static class Text {
        /**
         * 内容
         */
        private String text;
    }

    @Data
    @Builder
    public static class Post {
        /**
         * 消息标题。
         */
        private String title;
        /**
         * 内容
         */
        private List<PostContent> content;
    }

    @Data
    @Builder
    public static class PostContent {
        /**
         * 标签，text,a,at，img
         */
        private String tag;
        /**
         * 内容
         */
        private String text;
        /**
         * 链接
         */
        private String href;
        /**
         * 用户ID
         */
        @JsonAlias("user_id")
        private String userId;
        /**
         * 图片的唯一标识
         */
        @JsonAlias("image_key")
        private String imageKey;
    }

    @Data
    @Builder
    public static class ShareChat {
        /**
         * 群 ID。
         */
        @JsonAlias("share_chat_id")
        private String shareChatId;
    }

    @Data
    @Builder
    public static class Image {
        /**
         * 图片Key
         */
        @JsonAlias("image_key")
        private String imageKey;
    }

    @Data
    @Builder
    public static class Card {
        /**
         * 文本内容
         * {@link https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-json-v2-breaking-changes-release-notes}
         */
        private JSONArray elements;
        /**
         * 标题
         */
        private Header header;
    }

    @Data
    @Builder
    public static class Header {
        /**
         * 颜色,可为空 red,blue...
         */
        private String template;
        /**
         *
         */
        private HeaderTitle title;
    }

    @Data
    @Builder
    public static class HeaderTitle {
        /**
         * 标题内容
         */
        private String content;
        /**
         * 标签
         */
        private String tag;
    }

}
