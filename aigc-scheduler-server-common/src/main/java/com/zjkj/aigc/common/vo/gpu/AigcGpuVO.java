package com.zjkj.aigc.common.vo.gpu;

import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * GPU视图对象
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcGpuVO extends BaseVO {

    private static final long serialVersionUID = -123329691236669312L;
    /**
     * 平台
     */
    private String platform;

    /**
     * 型号
     */
    private String model;

    /**
     * 卡数
     */
    private Integer cardCount;

    /**
     * 单卡显存
     */
    private Integer singleCardMemory;
}
