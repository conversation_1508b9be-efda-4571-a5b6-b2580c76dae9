package com.zjkj.aigc.common.req.task;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AigcTaskInitEsReq implements Serializable {


    private AigcTaskQuery query;

    private Boolean initQuery = true;

    private Boolean initAll;

    private Long minId;

}
