package com.zjkj.aigc.common.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author:z<PERSON><PERSON><PERSON>
 * @Description:
 * @DATE: 2024/12/18
 */
@Data
public class TaskModelSummaryVO {
    /**
     * 模型类型
     */
    private String modelType;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 任务类型
     */
    private String taskType;
    /**
     * 等待中的任务数量
     */
    private Long waitTaskCount;
    /**
     * 任务速率
     */
    private BigDecimal taskSpeed;
    /**
     * 在线实例数
     */
    private Long onlineInstance;
}
