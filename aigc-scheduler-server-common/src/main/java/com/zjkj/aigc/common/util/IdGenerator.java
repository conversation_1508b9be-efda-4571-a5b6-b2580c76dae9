package com.zjkj.aigc.common.util;

/**
 * <AUTHOR>
 * @since 2025/6/9
 */
public class IdGenerator {
    private static final IdWorker ID_WORKER = new IdWorker(null);

    /**
     * generate id using snowflake algorithm
     *
     * @return id
     */
    public static long nextId() {
        return ID_WORKER.nextId();
    }

    /**
     * generate id using snowflake algorithm
     *
     * @return id
     */
    public static String nexStrId() {
        return String.valueOf(nextId());
    }

}
