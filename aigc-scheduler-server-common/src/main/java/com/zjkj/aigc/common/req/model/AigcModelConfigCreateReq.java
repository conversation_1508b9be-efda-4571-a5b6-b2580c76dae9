package com.zjkj.aigc.common.req.model;

import com.zjkj.aigc.common.dto.model.GpuInfo;
import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/2/14
 */
@Data
public class AigcModelConfigCreateReq implements Serializable {
    private static final long serialVersionUID = 5001270571835134661L;

    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;
    /**
     * 模型id
     */
    @NotNull(message = "{validation.model.id.required}")
    private Long modelId;
    /**
     * 项目工程名称
     */
    @NotBlank(message = "{model.config.project.name.required}")
    @Length(max = 60, message = "{model.config.project.name.length.exceeded}")
    private String projectName;
    /**
     * 项目git地址
     */
    @NotBlank(message = "{model.config.project.git.url.required}")
    @Length(max = 255, message = "{model.config.project.git.url.length.exceeded}")
    private String projectGitUrl;
    /**
     * 模型版本
     */
    @NotBlank(message = "{validation.model.version.required}")
    @Length(max = 60, message = "{model.config.version.length.exceeded}")
    private String modelVersion;
    /**
     * 部署sdk版本
     */
    @NotBlank(message = "{model.config.sdk.version.required}")
    @Length(max = 60, message = "{model.config.sdk.version.length.exceeded}")
    private String deploySdkVersion;
    /**
     * 模型文件OSS地址
     */
    @NotBlank(message = "{model.config.oss.url.required}")
    @Length(max = 255, message = "{model.config.oss.url.length.exceeded}")
    private String modelFileOssUrl;
    /**
     * 是否加密
     */
    @NotNull(message = "{model.config.encrypt.required}")
    private Boolean isEncrypt;
    /**
     * 基础镜像地址
     */
    @NotBlank(message = "{model.config.mirror.image.url.required}")
    @Length(max = 255, message = "{model.config.mirror.image.url.length.exceeded}")
    private String mirrorImageUrl;
    /**
     * cpu核数
     */
    @NotNull(message = "{validation.cpu.core.required}")
    private Integer cpuCore;
    /**
     * 内存大小GB
     */
    @NotNull(message = "{validation.memory.size.required}")
    private Integer memorySize;
    /**
     * gpu信息
     */
    @Valid
    @NotNull(message = "{model.config.gpu.info.required}")
    private GpuInfo gpuInfo;
}
