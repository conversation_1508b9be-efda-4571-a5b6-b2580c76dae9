package com.zjkj.aigc.common.req.dynamic;

import com.zjkj.aigc.common.req.PageBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AigcDynamicConfigQuery extends PageBaseReq {
    private static final long serialVersionUID = 1821677758428882163L;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 状态:0-停用 1-启用
     */
    private Integer status;
}
