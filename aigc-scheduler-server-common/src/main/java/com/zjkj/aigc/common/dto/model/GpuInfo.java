package com.zjkj.aigc.common.dto.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/12/19
 */
@Data
public class GpuInfo implements Serializable {
    private static final long serialVersionUID = 6300628778757626904L;
    /**
     *  类型：单卡/多卡
     */
    @NotBlank(message = "{validation.type.required}")
    private String type;
    /**
     * 型号
     */
    @NotBlank(message = "{validation.model.type.required}")
    private String model;
    /**
     * 内存大小
     */
    @NotNull(message = "{validation.memory.size.required}")
    private Integer memorySize;
}
