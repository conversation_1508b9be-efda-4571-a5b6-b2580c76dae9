package com.zjkj.aigc.common.req.dynamic;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/1
 */
@Data
public class AigcDynamicConfigGlobalUpdateReq implements Serializable {
    private static final long serialVersionUID = -1861824083166424851L;

    /**
     * 动态调度启用
     */
    private boolean dynamicEnabled;
    /**
     * 白名单启用
     */
    private boolean whitelistEnabled;
    /**
     * 模型id白名单
     */
    private List<Long> modelWhitelist;
    /**
     * 冷却分钟数
     */
    private Integer cooldownMinute;
    /**
     * zadig 端点地址
     */
    @Length(max = 255, message = "{dynamic.config.global.zadig.endpoint.length.exceeded}")
    private String zadigEndpoint;
    /**
     * zadig api token
     */
    @Length(max = 512, message = "{dynamic.config.global.zadig.api.token.length.exceeded}")
    private String zadigApiToken;
}
