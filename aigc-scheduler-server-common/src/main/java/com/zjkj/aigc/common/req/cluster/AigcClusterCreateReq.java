package com.zjkj.aigc.common.req.cluster;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 集群创建请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class AigcClusterCreateReq implements Serializable {

    private static final long serialVersionUID = -2159420758835069993L;
    /**
     * 主键
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;

    /**
     * 平台
     */
    @NotBlank(message = "{validation.platform.required}")
    @Length(max = 64, message = "{cluster.platform.length.exceeded}")
    private String platform;

    /**
     * 名称
     */
    @NotBlank(message = "{validation.name.required}")
    @Length(max = 64, message = "{cluster.name.length.exceeded}")
    private String name;

    /**
     * 服务类型
     */
    @NotEmpty(message = "{cluster.service.type.required}")
    private List<String> serviceType;

    /**
     * 环境类型
     */
    private List<String> envType;

    /**
     * 节点列表
     */
    @NotEmpty(message = "{cluster.nodes.required}")
    @Valid
    private List<AigcClusterNodeCreateReq> nodes;
}
