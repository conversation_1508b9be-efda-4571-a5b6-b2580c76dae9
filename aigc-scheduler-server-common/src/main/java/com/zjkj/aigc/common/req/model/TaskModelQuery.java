package com.zjkj.aigc.common.req.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@Data
public class TaskModelQuery implements Serializable {
    private static final long serialVersionUID = -1039411685554760825L;

    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;

    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;

    /**
     * 容器ID
     */
//    @NotBlank(message = "容器ID不能为空")
    private String containerId;
    /**
     * 是否任务批量0，1
     */
    @JsonIgnore
    private Integer taskBatch;
}
