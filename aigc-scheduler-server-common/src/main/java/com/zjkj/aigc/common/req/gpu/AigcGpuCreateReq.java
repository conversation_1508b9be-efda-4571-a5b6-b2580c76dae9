package com.zjkj.aigc.common.req.gpu;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * GPU创建请求
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class AigcGpuCreateReq implements Serializable {

    private static final long serialVersionUID = 4134169312705423490L;
    /**
     * 主键
     */
    @NotNull(message = "{validation.id.required}", groups = {ValidationGroup.Update.class})
    private Long id;

    /**
     * 平台
     */
    @NotBlank(message = "{validation.platform.required}")
    private String platform;

    /**
     * 型号
     */
    @NotBlank(message = "{validation.model.type.required}")
    private String model;

    /**
     * 卡数
     */
    @NotNull(message = "{gpu.card.count.required}")
    @Min(value = 1, message = "{gpu.card.count.min.value}")
    private Integer cardCount;

    /**
     * 单卡显存
     */
    @NotNull(message = "{gpu.single.card.memory.required}")
    @Min(value = 1, message = "{gpu.single.card.memory.min.value}")
    private Integer singleCardMemory;
}
