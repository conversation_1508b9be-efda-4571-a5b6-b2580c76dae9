package com.zjkj.aigc.common.vo.resource;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/04
 */
@Data
public class ResourceExpendSummaryVO implements Serializable {
    private static final long serialVersionUID = 4595482764109481044L;

    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 支出数据集合
     */
    private List<Expend> expendList;
    /**
     * 合计
     */
    private Summary summary;

    @Data
    public static class Expend implements Serializable {
        private static final long serialVersionUID = -3094854425955528757L;
        /**
         * 数据ID
         */
        private Long id;
        /**
         * 云平台
         */
        private String platform;
        /**
         * 账号
         */
        private String account;
        /**
         * 上月实际消费
         */
        private BigDecimal lastMonthCost;
        /**
         * 上月余额
         */
        private BigDecimal lastMonthRemain;
        /**
         * 本月总预算
         */
        private BigDecimal thisMonthBudget;
        /**
         * 本月待充值
         */
        private BigDecimal thisMonthTodoRecharge;
        /**
         * 本月已充值
         */
        private BigDecimal thisMonthRecharge;
        /**
         * 备注说明
         */
        private String remark;
        /**
         * 预算差异说明
         */
        private String budgetDiffRemark;
        /**
         * 状态
         * {@link com.zjkj.aigc.common.enums.resource.ResourceExpendStatusEnum}
         */
        private Integer status;
    }

    @Data
    public static class Summary implements Serializable {
        private static final long serialVersionUID = -7892714521933113917L;
        /**
         * 上月实际消费
         */
        private BigDecimal lastMonthCost;
        /**
         * 上月余额
         */
        private BigDecimal lastMonthRemain;
        /**
         * 本月总预算
         */
        private BigDecimal thisMonthBudget;
        /**
         * 本月待充值
         */
        private BigDecimal thisMonthTodoRecharge;
        /**
         * 本月已充值
         */
        private BigDecimal thisMonthRecharge;
    }

    public void calculate() {
        summary = new Summary();
        if (CollUtil.isNotEmpty(expendList)) {
            summary.setLastMonthCost(expendList.stream().map(ResourceExpendSummaryVO.Expend::getLastMonthCost).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setLastMonthRemain(expendList.stream().map(ResourceExpendSummaryVO.Expend::getLastMonthRemain).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setThisMonthBudget(expendList.stream().map(ResourceExpendSummaryVO.Expend::getThisMonthBudget).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setThisMonthTodoRecharge(expendList.stream().map(ResourceExpendSummaryVO.Expend::getThisMonthTodoRecharge).reduce(BigDecimal.ZERO, BigDecimal::add));
            summary.setThisMonthRecharge(expendList.stream().map(ResourceExpendSummaryVO.Expend::getThisMonthRecharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }
}
