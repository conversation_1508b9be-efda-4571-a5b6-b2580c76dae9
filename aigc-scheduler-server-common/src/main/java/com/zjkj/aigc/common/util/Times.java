package com.zjkj.aigc.common.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * 提供 Date, LocalDate, LocalTime, LocalDateTime 以及毫秒值的相互转换
 * <p>
 * 类成员变量提供部分时间字符串模版
 *
 * <AUTHOR>
 */
public final class Times {

    public static final DateTimeFormatter YEAR_MOUTH_DAY_HOUR_MINUTES_SECONDS = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter YEAR_MOUTH_DAY_HOUR_MINUTES = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm");

    public static final DateTimeFormatter YEAR_MOUTH_DAY = DateTimeFormatter
            .ofPattern("yyyy-MM-dd");

    private Times() {
        throw new UnsupportedOperationException();
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(YEAR_MOUTH_DAY_HOUR_MINUTES_SECONDS);
    }

    public static String formatLocalDate(LocalDate localDate) {
        return localDate.format(YEAR_MOUTH_DAY);
    }

    public static LocalDate toLocalDate(long millis) {
        return toZoneDateTime(millis).toLocalDate();
    }

    public static LocalDate toLocalDate(Date date) {
        return toZoneDateTime(date).toLocalDate();
    }

    public static LocalTime toLocalTime(long millis) {
        return toZoneDateTime(millis).toLocalTime();
    }

    public static LocalTime toLocalTime(Date date) {
        return toZoneDateTime(date).toLocalTime();
    }

    public static LocalDateTime toLocalDateTime(long millis) {
        return toZoneDateTime(millis).toLocalDateTime();
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return toZoneDateTime(date).toLocalDateTime();
    }


    public static Date toDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(LocalTime localTime) {
        return Date.from(localTime.atDate(LocalDate.of(1970, 1, 1))
                .atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static long toMillis(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long toMillis(LocalTime localTime) {
        return localTime.atDate(LocalDate.of(1970, 1, 1))
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long toMillis(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    private static ZonedDateTime toZoneDateTime(long millis) {
        return Instant.ofEpochMilli(millis).atZone(ZoneOffset.systemDefault());
    }

    private static ZonedDateTime toZoneDateTime(Date date) {
        return Objects.requireNonNull(date, "date 对象不允许为空").toInstant()
                .atZone(ZoneOffset.systemDefault());
    }


    public static LocalDate toLocalDate(String dateString) {
        return LocalDate.parse(dateString, YEAR_MOUTH_DAY);
    }

    public static LocalDateTime toLocalDateTime(String dateString) {
        return LocalDateTime.parse(dateString, YEAR_MOUTH_DAY_HOUR_MINUTES_SECONDS);
    }
}
