package com.zjkj.aigc.common.req.cluster;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Data
public class AigcModelUsageQuery implements Serializable {
    private static final long serialVersionUID = 5473761563161194432L;

    /**
     * 集群主键id
     */
    @NotNull(message = "{cluster.model.usage.cluster.id.required}")
    private Long clusterId;
    /**
     * 数据日期
     */
    @NotNull(message = "{cluster.model.usage.data.date.required}")
    private LocalDate dataDate;
}
