package com.zjkj.aigc.common.req.resource;

import com.zjkj.aigc.common.validate.ValidationGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/12/03
 */
@Data
public class ResourceGpuPlanCreateReq implements Serializable {

    private static final long serialVersionUID = 6580963822160387797L;

    /**
     * 数据ID
     */
    private Long id;

    /**
     * 规划月份
     */
    private Integer month;
    /**
     * 云平台
     */
    private String platform;
    /**
     * 型号
     */
    @Length(max = 20, message = "{resource.gpu.plan.model.length.exceeded}")
    private String model;
    /**
     * 业务卡需求
     */
    private Integer businessDemand;
    /**
     * 打标卡需求
     */
    private Integer markDemand;
    /**
     * 训练卡需求
     */
    private Integer trainDemand;
    /**
     * 测试卡需求
     */
    private Integer testDemand;
    /**
     * 运维冗余卡需求
     */
    private Integer devopsDemand;
    /**
     * GPU规格/卡
     */
    private Integer norms;
    /**
     * 临时/卡
     */
    private Integer tmp;
    /**
     * 单价
     */
    private BigDecimal price;
    /**
     * 备注
     */
    @Length(max = 255, message = "{resource.gpu.plan.remark.length.exceeded}")
    private String remark;
}
