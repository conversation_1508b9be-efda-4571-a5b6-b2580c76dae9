package com.zjkj.aigc.common.req.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@Data
public class HeartbeatCheckReq implements Serializable {

    private static final long serialVersionUID = 3870269399554990036L;
    /**
     * 状态: worker-状态,running-运行中，idle-闲置中
     */
    @NotBlank(message = "{heartbeat.status.required}")
    private String status;

    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;

    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;

    /**
     * 容器ID
     */
//    @NotBlank(message = "当前容器ID不能为空")
    private String containerId;

}
