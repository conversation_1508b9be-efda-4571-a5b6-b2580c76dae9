package com.zjkj.aigc.common.util;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/1/16
 */
public final class StreamUtil {

    private StreamUtil() {
        throw new AssertionError("Utility class should not be instantiated");
    }

    /**
     * 转换为List
     *
     * @param collection 集合
     * @param mapper     转换器
     * @param <T>        集合元素类型
     * @param <R>        转换后的元素类型
     * @return 转换后的List
     */
    public static <T, R> List<R> mapToList(Collection<T> collection, Function<T, R> mapper) {
        if (Objects.isNull(collection)) {
            return List.of();
        }

        return collection.stream()
                .map(mapper)
                .collect(Collectors.toList());

    }

    /**
     * 转换为Set
     *
     * @param collection 集合
     * @param mapper     转换器
     * @param <T>        集合元素类型
     * @param <R>        转换后的元素类型
     * @return 转换后的Set
     */
    public static <T, R> Set<R> mapToSet(Collection<T> collection, Function<T, R> mapper) {
        if (Objects.isNull(collection)) {
            return Set.of();
        }
        return collection.stream()
                .map(mapper)
                .collect(Collectors.toSet());
    }

    /**
     * 根据某个字段分组
     *
     * @param collection 集合
     * @param keyMapper  字段提取器
     * @param <T>        集合元素类型
     * @param <K>        字段类型
     * @return 分组结果
     */
    public static <T, K> Map<K, List<T>> groupBy(Collection<T> collection, Function<T, K> keyMapper) {
        if (Objects.isNull(collection)) {
            return Map.of();
        }

        return collection.stream()
                .collect(Collectors.groupingBy(keyMapper));
    }

    /**
     * 转换为Map
     *
     * @param collection  集合
     * @param keyMapper   key提取器
     * @param valueMapper value提取器
     * @param <T>         集合元素类型
     * @param <K>         key类型
     * @param <V>         value类型
     * @return Map
     */
    public static <T, K, V> Map<K, V> toMap(Collection<T> collection, Function<T, K> keyMapper, Function<T, V> valueMapper) {
        if (Objects.isNull(collection)) {
            return Map.of();
        }

        return collection.stream()
                .collect(Collectors.toMap(keyMapper, valueMapper, (a, b) -> b));
    }

    /**
     * 转换为linkedMap
     *
     * @param collection 集合
     * @param keyMapper  key提取器
     * @param <T>        集合元素类型
     * @param <K>        key类型
     * @return Map
     */
    public static <T, K> Map<K, T> toLinkedMap(Collection<T> collection, Function<T, K> keyMapper) {
        if (Objects.isNull(collection)) {
            return Map.of();
        }

        return collection.stream()
                .collect(Collectors.toMap(keyMapper, Function.identity(), (a, b) -> b, LinkedHashMap::new));
    }

    /**
     * 转换为Map
     *
     * @param collection 集合
     * @param keyMapper  key提取器
     * @param <T>        集合元素类型
     * @param <K>        key类型
     * @return Map
     */
    public static <T, K> Map<K, T> toMap(Collection<T> collection, Function<T, K> keyMapper) {
        if (Objects.isNull(collection)) {
            return Map.of();
        }

        return collection.stream()
                .collect(Collectors.toMap(keyMapper, Function.identity(), (a, b) -> b));
    }

    /**
     * 转换为linkedMap
     *
     * @param collection  集合
     * @param keyMapper   key提取器
     * @param valueMapper value提取器
     * @param <T>         集合元素类型
     * @param <K>         key类型
     * @param <V>         value类型
     * @return Map
     */
    public static <T, K, V> Map<K, V> toLinkedMap(Collection<T> collection, Function<T, K> keyMapper, Function<T, V> valueMapper) {
        if (Objects.isNull(collection)) {
            return Map.of();
        }

        return collection.stream()
                .collect(Collectors.toMap(keyMapper, valueMapper, (a, b) -> b, LinkedHashMap::new));
    }
}
