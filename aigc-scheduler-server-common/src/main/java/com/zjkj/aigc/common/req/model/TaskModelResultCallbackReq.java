package com.zjkj.aigc.common.req.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/3
 */
@Data
public class TaskModelResultCallbackReq<T> implements Serializable {
    private static final long serialVersionUID = 3709768025935819344L;

    /**
     * 任务ID
     */
    @NotBlank(message = "{task.model.callback.task.id.required}")
    private String taskId;

    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;

    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;

    /**
     * 当前容器ID
     */
//    @NotBlank(message = "容器ID不能为空")
    private String containerId;

    /**
     * 模型输出信息
     */
    private T infos;
}
