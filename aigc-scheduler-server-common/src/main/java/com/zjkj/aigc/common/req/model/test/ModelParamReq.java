package com.zjkj.aigc.common.req.model.test;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/10/18
 */
@Data
public class ModelParamReq implements Serializable {

    private static final long serialVersionUID = 7473924766791939683L;

    /**
     * 任务类型
     */
    @NotBlank(message = "{validation.task.type.required}")
    private String taskType;
    /**
     * 模型名称
     */
    @NotBlank(message = "{validation.model.name.required}")
    private String modelName;
}
