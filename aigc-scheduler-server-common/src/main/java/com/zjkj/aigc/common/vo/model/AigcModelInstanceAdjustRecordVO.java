package com.zjkj.aigc.common.vo.model;

import com.zjkj.aigc.common.dto.ModelInstanceAdjustDTO;
import com.zjkj.aigc.common.vo.BaseVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/01/17
 */
@Data
public class AigcModelInstanceAdjustRecordVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 6817850935615933247L;
    /**
     * 数据ID
     */
    private Long id;
    /**
     * 模型信息
     */
    private List<ModelInstanceAdjustDTO> modelInfo;

}
