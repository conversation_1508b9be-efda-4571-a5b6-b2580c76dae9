package com.zjkj.aigc.common.req.resource;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class ResourceBillImportReq implements Serializable {
    private static final long serialVersionUID = -1001725474180024518L;

    /**
     * 云平台
     */
    @NotBlank(message = "{validation.cloud.platform.required}")
    private String platform;
}
